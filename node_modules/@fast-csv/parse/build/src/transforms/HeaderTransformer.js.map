{"version": 3, "file": "HeaderTransformer.js", "sourceRoot": "", "sources": ["../../../src/transforms/HeaderTransformer.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA6C;AAC7C,0EAA2C;AAC3C,8DAA+B;AAC/B,oEAAqC;AAYrC,MAAa,iBAAiB;IACT,aAAa,CAAgB;IAE9C,OAAO,GAAuB,IAAI,CAAC;IAE3B,eAAe,GAAG,KAAK,CAAC;IAEf,iBAAiB,GAAY,KAAK,CAAC;IAE5C,iBAAiB,GAAG,KAAK,CAAC;IAE1B,aAAa,GAAG,CAAC,CAAC;IAET,gBAAgB,CAA2B;IAE5D,YAAmB,aAA4B;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,IAAA,2BAAU,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,OAAO,CAAC;QAClD,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,GAAa,EAAE,EAA2B;QACvD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,YAAY,CAAC,GAAQ;QACzB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACnF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;YACxF,CAAC;YACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,CAAC;iBAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACJ,mGAAmG;gBACnG,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU,CAAC,GAAqB;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,EAAE,GAAG,EAAE,GAAiB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QACrD,CAAC;QACD,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,sBAAsB,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3E,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CACX,sDAAsD,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE,CACxG,CAAC;YACN,CAAC;YACD,OAAO;gBACH,GAAG,EAAE,GAAiB;gBACtB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,oCAAoC,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE;aAC9F,CAAC;QACN,CAAC;QACD,IAAI,aAAa,CAAC,oBAAoB,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACxE,OAAO;gBACH,GAAG,EAAE,GAAiB;gBACtB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,oCAAoC,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE;aAC9F,CAAC;QACN,CAAC;QACD,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACxD,CAAC;IAEO,UAAU,CAAC,GAAqB;QACpC,MAAM,MAAM,GAAW,EAAE,CAAC;QAC1B,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,MAAM,GAAI,OAAoB,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,IAAA,4BAAW,EAAC,MAAM,CAAC,EAAE,CAAC;gBACvB,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnB,6CAA6C;gBAC7C,IAAI,IAAA,4BAAW,EAAC,GAAG,CAAC,EAAE,CAAC;oBACnB,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACJ,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,MAAW,CAAC;IACvB,CAAC;IAEO,UAAU,CAAC,OAAoB;QACnC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;YACzC,OAAO,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;QACH,IAAI,IAAA,qBAAI,EAAC,eAAe,CAAC,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAA,wBAAO,EAAC,eAAe,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;gBACnD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAC7E,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC;IACnD,CAAC;CACJ;AApHD,8CAoHC"}