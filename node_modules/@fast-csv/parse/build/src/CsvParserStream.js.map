{"version": 3, "file": "CsvParserStream.js", "sourceRoot": "", "sources": ["../../src/CsvParserStream.ts"], "names": [], "mappings": ";;;AAAA,mDAA+C;AAC/C,mCAAsD;AAEtD,6CAA0E;AAC1E,qCAAkC;AAGlC,MAAa,eAA8C,SAAQ,kBAAS;IACvD,aAAa,CAAgB;IAE7B,OAAO,CAAgB;IAEvB,MAAM,CAAS;IAEf,iBAAiB,CAAuB;IAExC,uBAAuB,CAAgC;IAEhE,KAAK,GAAG,EAAE,CAAC;IAEX,QAAQ,GAAG,CAAC,CAAC;IAEb,cAAc,GAAG,CAAC,CAAC;IAEnB,eAAe,GAAG,CAAC,CAAC;IAEpB,UAAU,GAAG,KAAK,CAAC;IAEnB,cAAc,GAAG,KAAK,CAAC;IAE/B,YAAmB,aAA4B;QAC3C,KAAK,CAAC,EAAE,UAAU,EAAE,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC,aAAa,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,8BAAiB,CAAC,aAAa,CAAC,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,IAAI,8BAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,uBAAuB,GAAG,IAAI,oCAAuB,EAAE,CAAC;IACjE,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IACvF,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IAChE,CAAC;IAEM,SAAS,CAAC,iBAA6C;QAC1D,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,iBAAiB,CAAC;QAC9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ,CAAC,gBAAgC;QAC5C,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,gBAAgB,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,8DAA8D;IACvD,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;QAC9C,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,QAAgB,EAAE,IAAuB;QACrE,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,EAAE,CAAC;QAClB,CAAC;QACD,MAAM,eAAe,GAAG,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;YACvB,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,IAAuB;QACjC,MAAM,eAAe,GAAG,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/D,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,eAAe,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAChD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,IAAY,EAAE,WAAoB;QAC5C,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;QACd,CAAC;QACD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,IAAgB,EAAE,EAAqB;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,MAAM,OAAO,GAAG,CAAC,CAAS,EAAQ,EAAE;YAChC,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAQ,EAAE;gBACnC,IAAI,GAAG,EAAE,CAAC;oBACN,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;gBACnB,CAAC;gBACD,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;oBAChB,6EAA6E;oBAC7E,YAAY,CAAC,GAAS,EAAE;wBACpB,OAAO,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;oBACH,OAAO,SAAS,CAAC;gBACrB,CAAC;gBACD,OAAO,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC;YACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,sEAAsE;YACtE,WAAW;YACX,IAAI,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzC,OAAO,EAAE,EAAE,CAAC;YAChB,CAAC;YACD,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO,QAAQ,EAAE,CAAC;YACtB,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;YACzB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,eAAe,EAAQ,EAAE;gBACzD,IAAI,GAAG,EAAE,CAAC;oBACN,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACnB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;gBACD,IAAI,CAAC,eAAe,EAAE,CAAC;oBACnB,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,GAAG,EAAE,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;gBACzF,CAAC;qBAAM,IAAI,eAAe,CAAC,GAAG,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBACvD,CAAC;gBACD,OAAO,QAAQ,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,OAAO,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IAEO,YAAY,CAAC,SAAmB,EAAE,EAA2B;QACjE,IAAI,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,WAAW,EAAQ,EAAE;gBACnE,IAAI,GAAG,EAAE,CAAC;oBACN,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;gBACnB,CAAC;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;gBAClE,CAAC;gBACD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACvB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,SAAuB,EAAE,CAAC,CAAC;oBACtE,CAAC;oBACD,iEAAiE;oBACjE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC5B,CAAC;gBACD,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;oBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,OAAO,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;oBAClF,CAAC;oBACD,iEAAiE;oBACjE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC5B,CAAC;gBACD,sEAAsE;gBACtE,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;gBACnB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;gBACzB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,EAAE,CAAC,CAAC,CAAC,CAAC;QACV,CAAC;IACL,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YACzD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAEO,OAAO,CAAC,EAA2B;QACvC,iEAAiE;QACjE,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,OAAO,CAAC,GAAQ,EAAE,EAAyB;QAC/C,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC;YACD,EAAE,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,EAAE,CAAC,CAAC,CAAC,CAAC;QACV,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAuB;QACnD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,8DAA8D;QAC9D,OAAO,CAAC,GAA6B,EAAE,GAAG,IAAW,EAAQ,EAAE;YAC3D,IAAI,GAAG,EAAE,CAAC;gBACN,IAAI,WAAW,EAAE,CAAC;oBACd,MAAM,GAAG,CAAC;gBACd,CAAC;gBACD,WAAW,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO;YACX,CAAC;YACD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAClB,CAAC,CAAC;IACN,CAAC;CACJ;AArOD,0CAqOC"}