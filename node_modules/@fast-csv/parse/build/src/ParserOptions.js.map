{"version": 3, "file": "ParserOptions.js", "sourceRoot": "", "sources": ["../../src/ParserOptions.ts"], "names": [], "mappings": ";;;;;;AAAA,8EAA+C;AAC/C,gEAAiC;AAuBjC,MAAa,aAAa;IACN,gBAAgB,CAAS;IAEzB,UAAU,GAAY,IAAI,CAAC;IAE3B,SAAS,GAAW,GAAG,CAAC;IAExB,WAAW,GAAY,KAAK,CAAC;IAE7B,KAAK,GAAkB,GAAG,CAAC;IAE3B,MAAM,GAAkB,IAAI,CAAC;IAE7B,UAAU,GAAkB,IAAI,CAAC,KAAK,CAAC;IAEvC,OAAO,GAAkB,IAAI,CAAC;IAE9B,gBAAgB,GAAY,KAAK,CAAC;IAElC,KAAK,GAAY,KAAK,CAAC;IAEvB,KAAK,GAAY,KAAK,CAAC;IAEvB,IAAI,GAAY,KAAK,CAAC;IAEtB,OAAO,GAA2D,IAAI,CAAC;IAEvE,aAAa,GAAY,KAAK,CAAC;IAE/B,oBAAoB,GAAY,KAAK,CAAC;IAEtC,sBAAsB,GAAY,KAAK,CAAC;IAExC,cAAc,GAAW,IAAI,CAAC;IAE9B,iBAAiB,CAAS;IAE1B,QAAQ,GAAmB,MAAM,CAAC;IAElC,SAAS,GAAY,KAAK,CAAC;IAE3B,OAAO,GAAW,CAAC,CAAC;IAEpB,SAAS,GAAW,CAAC,CAAC;IAEtB,QAAQ,GAAW,CAAC,CAAC;IAErC,YAAmB,IAAwB;QACvC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACnE,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAA,6BAAY,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAA,sBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,0BAA0B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAExF,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;IACL,CAAC;CACJ;AA7DD,sCA6DC"}