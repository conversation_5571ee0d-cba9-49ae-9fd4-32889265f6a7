{"name": "@fast-csv/format", "version": "5.0.5", "description": "fast-csv formatting module", "keywords": ["csv", "format", "write"], "author": "doug-martin <<EMAIL>>", "homepage": "https://c2fo.github.io/fast-csv/docs/formatting/getting-started/", "license": "MIT", "main": "build/src/index.js", "types": "build/src/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["build/src/**"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/C2FO/fast-csv.git", "directory": "packages/format"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run clean && npm run compile", "clean": "rm -rf ./build && rm -rf tsconfig.tsbuildinfo", "compile": "tsc"}, "bugs": {"url": "https://github.com/C2FO/fast-csv/issues"}, "dependencies": {"lodash.escaperegexp": "^4.1.2", "lodash.isboolean": "^3.0.3", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0"}, "devDependencies": {"@types/lodash.escaperegexp": "4.1.9", "@types/lodash.isboolean": "3.0.9", "@types/lodash.isfunction": "3.0.9", "@types/lodash.isnil": "4.0.9", "@types/node": "^22.7.8"}, "gitHead": "50092405c9fcc75ac39755e5d574267a974731eb"}