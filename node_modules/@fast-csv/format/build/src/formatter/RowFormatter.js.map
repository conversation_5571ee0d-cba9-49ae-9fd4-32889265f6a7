{"version": 3, "file": "RowFormatter.js", "sourceRoot": "", "sources": ["../../../src/formatter/RowFormatter.ts"], "names": [], "mappings": ";;;;;;AAAA,0EAA2C;AAE3C,qDAAkD;AAClD,oCAAoH;AAMpH,MAAa,YAAY;IACb,MAAM,CAAC,cAAc,CAAC,GAAQ;QAClC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,GAAQ;QAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC;IAED,8BAA8B;IACtB,MAAM,CAAC,aAAa,CAAC,GAAQ;QACjC,IAAI,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,oEAAoE;YACpE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAU,EAAE;gBAC1B,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;QACP,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,CAAC;QACf,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED,wDAAwD;IAChD,MAAM,CAAC,eAAe,CAC1B,iBAA6C;QAE7C,IAAI,IAAA,uBAAe,EAAC,iBAAiB,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;gBACjD,IAAI,cAAc,GAAG,IAAI,CAAC;gBAC1B,IAAI,CAAC;oBACD,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;gBAC5C,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC;gBACD,OAAO,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACpC,CAAC,CAAC;QACN,CAAC;QACD,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;YACjD,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC;IACN,CAAC;IAEgB,gBAAgB,CAAyB;IAEzC,cAAc,CAAuB;IAErC,kBAAkB,CAAU;IAErC,aAAa,CAA+B;IAE5C,OAAO,CAAkB;IAEzB,iBAAiB,CAAU;IAE3B,QAAQ,GAAG,CAAC,CAAC;IAErB,YAAmB,gBAAwC;QACvD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;QAC9D,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/C,CAAC;QACD,IAAI,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,SAAS,CAAC;QACnD,CAAC;IACL,CAAC;IAED,IAAW,YAAY,CAAC,iBAA6C;QACjE,IAAI,CAAC,IAAA,2BAAU,EAAC,iBAAiB,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,GAAM,EAAE,EAAwB;QAC1C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,cAAoB,EAAQ,EAAE;YAC1D,IAAI,GAAG,EAAE,CAAC;gBACN,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACP,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;YACD,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC3E,IAAI,IAAI,CAAC,kBAAkB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAChE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAClC,CAAC;gBACD,IAAI,mBAAmB,EAAE,CAAC;oBACtB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;oBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;YACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,EAAwB;QAClC,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,6DAA6D;QAC7D,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC,CAAC;YAC1G,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,6EAA6E;IAC7E,gFAAgF;IACxE,YAAY,CAAC,GAAQ;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,iFAAiF;YACjF,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QAChE,CAAC;QACD,MAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,mDAAmD;YACnD,4BAA4B;YAC5B,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QACxD,CAAC;QACD,6CAA6C;QAC7C,IACI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,EAAW,EAAE;gBACjC,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,EACJ,CAAC;YACC,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;IAClD,CAAC;IAED,uCAAuC;IAC/B,aAAa,CAAC,GAAQ;QAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAU,EAAE;gBACvC,OAAO,GAAG,CAAC,MAAM,CAAW,CAAC;YACjC,CAAC,CAAC,CAAC;QACP,CAAC;QACD,IAAI,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAU,EAAE;gBAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAsB,CAAC;gBACxC,IAAI,GAAG,EAAE,CAAC;oBACN,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC;QACD,+DAA+D;QAC/D,2BAA2B;QAC3B,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3D,OAAO,GAAG,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAU,EAAE;YAC1C,+DAA+D;YAC/D,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe,CAAC,GAAM,EAAE,EAA2B;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,EAAE,CAAC,IAAI,EAAE,GAAmB,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAEO,aAAa,CAAC,OAAiB,EAAE,YAAqB;QAC1D,MAAM,aAAa,GAAG,OAAO;aACxB,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAU,EAAE;YACtB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAC9D,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;CACJ;AAtMD,oCAsMC"}