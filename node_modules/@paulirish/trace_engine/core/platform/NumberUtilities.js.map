{"version": 3, "file": "NumberUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/NumberUtilities.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,yEAAyE;AACzE,6BAA6B;AAE7B,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,GAAW,EAAU,EAAE;IACrE,IAAI,aAAa,GAAG,GAAG,CAAC;IACxB,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;QACd,aAAa,GAAG,GAAG,CAAC;IACtB,CAAC;SAAM,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;QACrB,aAAa,GAAG,GAAG,CAAC;IACtB,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,CAAS,EAAU,EAAE;IAClD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,KAAa,EAAU,EAAE;IACzD,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC1C,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,KAAa,EAAE,SAAS,GAAG,CAAC,EAAU,EAAE;IAC5D,mDAAmD;IACnD,iDAAiD;IACjD,uCAAuC;IACvC,yCAAyC;IACzC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;QACnC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC;IACnD,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACzC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,CAAS,EAAE,CAAS,EAAU,EAAE;IACpE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACf,MAAM,CAAC,GAAG,CAAC,CAAC;QACZ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACV,CAAC,GAAG,CAAC,CAAC;IACR,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IAC3B,CAAC,KAAK,EAAE,OAAO,CAAC;CACjB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,MAAc,EAAU,EAAE;IACnE,MAAM,OAAO,GAAG,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrD,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;QAClB,KAAK,IAAI,OAAO,CAAC;QACjB,MAAM,IAAI,OAAO,CAAC;IACpB,CAAC;IACD,MAAM,MAAM,GAAG,GAAG,KAAK,IAAI,MAAM,EAAE,CAAC;IACpC,OAAO,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;AAC5C,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAS,GAAW;IACxD,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IACtB,MAAM,EAAE,GAAG,cAAc,CAAC;IAC1B,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;QACrB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACpC,CAAC,CAAE,+BAA+B;IAClC,OAAO,GAAG,CAAC;AACb,CAAC,CAAC", "sourcesContent": ["// Copyright (c) 2020 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const clamp = (num: number, min: number, max: number): number => {\n  let clampedNumber = num;\n  if (num < min) {\n    clampedNumber = min;\n  } else if (num > max) {\n    clampedNumber = max;\n  }\n  return clampedNumber;\n};\n\nexport const mod = (m: number, n: number): number => {\n  return ((m % n) + n) % n;\n};\n\nexport const toFixedIfFloating = (value: string): string => {\n  if (!value || Number.isNaN(Number(value))) {\n    return value;\n  }\n  const number = Number(value);\n  return number % 1 ? number.toFixed(3) : String(number);\n};\n\n/**\n * Rounds a number (including float) down.\n */\nexport const floor = (value: number, precision = 0): number => {\n  // Allows for rounding to the nearest whole number.\n  // Ex: 1 / 10 -> round down to nearest 10th place\n  // Ex: 1 / 5 -> round down to nearest 5\n  // Ex: 1 / 50 -> round down to nearest 50\n  if (precision > 0 && precision < 1) {\n    precision = 1 / precision;\n    return Math.floor(value / precision) * precision;\n  }\n\n  const mult = Math.pow(10, precision);\n  return Math.floor(value * mult) / mult;\n};\n\n/**\n * Computes the great common divisor for two numbers.\n * If the numbers are floats, they will be rounded to an integer.\n */\nexport const greatestCommonDivisor = (a: number, b: number): number => {\n  a = Math.round(a);\n  b = Math.round(b);\n  while (b !== 0) {\n    const t = b;\n    b = a % b;\n    a = t;\n  }\n  return a;\n};\n\nconst commonRatios = new Map([\n  ['8∶5', '16∶10'],\n]);\n\nexport const aspectRatio = (width: number, height: number): string => {\n  const divisor = greatestCommonDivisor(width, height);\n  if (divisor !== 0) {\n    width /= divisor;\n    height /= divisor;\n  }\n  const result = `${width}∶${height}`;\n  return commonRatios.get(result) || result;\n};\n\nexport const withThousandsSeparator = function(num: number): string {\n  let str = String(num);\n  const re = /(\\d+)(\\d{3})/;\n  while (str.match(re)) {\n    str = str.replace(re, '$1\\xA0$2');\n  }  // \\xa0 is a non-breaking space\n  return str;\n};\n"]}