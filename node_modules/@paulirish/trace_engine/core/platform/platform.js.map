{"version": 3, "file": "platform.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/platform.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AAEH,OAAO,KAAK,cAAc,MAAM,qBAAqB,CAAC;AACtD,OAAO,KAAK,KAAK,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAC;AAChD,OAAO,KAAK,aAAa,MAAM,oBAAoB,CAAC;AACpD,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,iBAAiB,MAAM,wBAAwB,CAAC;AAC5D,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,KAAK,eAAe,MAAM,sBAAsB,CAAC;AACxD,OAAO,KAAK,eAAe,MAAM,sBAAsB,CAAC;AACxD,OAAO,KAAK,MAAM,MAAM,aAAa,CAAC;AACtC,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAC;AAChE,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAC;AAChE,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,KAAK,gBAAgB,MAAM,uBAAuB,CAAC;AAE1D;;;;GAIG;AACH,OAAO,EAAC,WAAW,EAAE,wBAAwB,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAChG,OAAO,EACL,cAAc,EACd,KAAK,EACL,WAAW,EACX,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,eAAe,EACf,eAAe,EACf,MAAM,EACN,mBAAmB,EACnB,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,GACjB,CAAC", "sourcesContent": ["/*\n * Copyright (C) 2019 Google Inc. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *     * Redistributions of source code must retain the above copyright\n * notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above\n * copyright notice, this list of conditions and the following disclaimer\n * in the documentation and/or other materials provided with the\n * distribution.\n *     * Neither the name of Google Inc. nor the names of its\n * contributors may be used to endorse or promote products derived from\n * this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, E<PERSON><PERSON><PERSON>AR<PERSON>, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport * as ArrayUtilities from './ArrayUtilities.js';\nimport * as Brand from './Brand.js';\nimport * as Constructor from './Constructor.js';\nimport * as DateUtilities from './DateUtilities.js';\nimport * as DevToolsPath from './DevToolsPath.js';\nimport * as DOMUtilities from './DOMUtilities.js';\nimport * as KeyboardUtilities from './KeyboardUtilities.js';\nimport * as MapUtilities from './MapUtilities.js';\nimport * as MimeType from './MimeType.js';\nimport * as NumberUtilities from './NumberUtilities.js';\nimport * as StringUtilities from './StringUtilities.js';\nimport * as Timing from './Timing.js';\nimport * as TypedArrayUtilities from './TypedArrayUtilities.js';\nimport * as TypeScriptUtilities from './TypescriptUtilities.js';\nimport * as UIString from './UIString.js';\nimport * as UserVisibleError from './UserVisibleError.js';\n\n/* `assertNotNullOrUndefined` also need to be exposed, as TypeScript does\n * not allow `asserts` functions to be used with qualified access (e.g.\n * `Platform.TypeScriptUtilities.assertNotNullOrUndefined` causes a compile\n * error).\n */\nexport {assertNever, assertNotNullOrUndefined, assertUnhandled} from './TypescriptUtilities.js';\nexport {\n  ArrayUtilities,\n  Brand,\n  Constructor,\n  DateUtilities,\n  DevToolsPath,\n  DOMUtilities,\n  KeyboardUtilities,\n  MapUtilities,\n  MimeType,\n  NumberUtilities,\n  StringUtilities,\n  Timing,\n  TypedArrayUtilities,\n  TypeScriptUtilities,\n  UIString,\n  UserVisibleError,\n};\n"]}