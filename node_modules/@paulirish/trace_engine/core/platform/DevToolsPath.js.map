{"version": 3, "file": "DevToolsPath.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/DevToolsPath.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAU7B,MAAM,CAAC,MAAM,cAAc,GAAG,EAAe,CAAC;AAE9C;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,OAA0B,EAAE,GAAG,MAAa,EAAa,EAAE,CACjF,MAAM,CAAC,GAAG,CAAC,EAAC,GAAG,EAAE,OAAO,EAAC,EAAE,GAAG,MAAM,CAAc,CAAC;AASvD,MAAM,CAAC,MAAM,kBAAkB,GAAG,EAAmB,CAAC;AAQtD,MAAM,CAAC,MAAM,sBAAsB,GAAG,EAAuB,CAAC", "sourcesContent": ["// Copyright 2021 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport type {Brand} from './Brand.js';\n\n/**\n * URLs are in DevTools are repsented as encoded URL strings.\n *\n * @example 'file:///Hello%20World/file/js'\n */\nexport type UrlString = Brand<string, 'UrlString'>;\nexport const EmptyUrlString = '' as UrlString;\n\n/**\n * Tagged template helper to construct `UrlString`s in a more readable form,\n * without having to sprinkle casts throughout the codebase. Primarily useful\n * for writing unit tests.\n *\n * Usage:\n * ```js\n * const url1 = urlString`https://www.example.com/404.html`;\n * const url2 = urlString`http://${host}/path/to/file.js`;\n * ```\n *\n * This is implemented as a wrapper around `String.raw` for convenience. This\n * function doesn't perform any kind of validation that the returned string is\n * really a valid `UrlString`.\n *\n * @param strings the string parts of the template.\n * @param values the dynamic values of the template.\n * @return the string constructed from `strings` and `values` casted to an\n *         `UrlString`.\n */\nexport const urlString = (strings: ArrayLike<string>, ...values: any[]): UrlString =>\n    String.raw({raw: strings}, ...values) as UrlString;\n\n/**\n * File paths in DevTools that are represented as unencoded absolute\n * or relative paths.\n *\n * @example '/Hello World/file.js'\n */\nexport type RawPathString = Brand<string, 'RawPathString'>;\nexport const EmptyRawPathString = '' as RawPathString;\n\n/**\n * File paths in DevTools that are represented as encoded paths.\n *\n * @example '/Hello%20World/file.js'\n */\nexport type EncodedPathString = Brand<string, 'EncodedPathString'>;\nexport const EmptyEncodedPathString = '' as EncodedPathString;\n"]}