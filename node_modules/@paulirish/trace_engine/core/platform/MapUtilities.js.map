{"version": 3, "file": "MapUtilities.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/MapUtilities.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,yEAAyE;AACzE,6BAA6B;AAE7B,MAAM,CAAC,MAAM,OAAO,GAAG,UAAe,GAAc;IAClD,MAAM,MAAM,GAAG,IAAI,QAAQ,EAAQ,CAAC;IACpC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QACzC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,OAAO,QAAQ;IACX,GAAG,GAAG,IAAI,GAAG,EAAa,CAAC;IAEnC,GAAG,CAAC,GAAM,EAAE,KAAQ;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACzB,CAAC;QACD,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAED,GAAG,CAAC,GAAM;QACR,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IACxC,CAAC;IAED,GAAG,CAAC,GAAM;QACR,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,GAAM,EAAE,KAAQ;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,GAAM,EAAE,KAAQ;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,CAAC,GAAM;QACd,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,SAAS;QACP,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,WAAW;QACT,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/B,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;CACF;AAED;;GAEG;AACH,mEAAmE;AACnE,MAAM,UAAU,cAAc,CAC1B,GAA4B,EAAE,GAAM,EAAE,mBAAmC;IAC3E,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACjC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["// Copyright (c) 2020 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nexport const inverse = function<K, V>(map: Map<K, V>): Multimap<V, K> {\n  const result = new Multimap<V, K>();\n  for (const [key, value] of map.entries()) {\n    result.set(value, key);\n  }\n  return result;\n};\n\nexport class Multimap<K, V> {\n  private map = new Map<K, Set<V>>();\n\n  set(key: K, value: V): void {\n    let set = this.map.get(key);\n    if (!set) {\n      set = new Set();\n      this.map.set(key, set);\n    }\n    set.add(value);\n  }\n\n  get(key: K): Set<V> {\n    return this.map.get(key) || new Set();\n  }\n\n  has(key: K): boolean {\n    return this.map.has(key);\n  }\n\n  hasValue(key: K, value: V): boolean {\n    const set = this.map.get(key);\n    if (!set) {\n      return false;\n    }\n    return set.has(value);\n  }\n\n  get size(): number {\n    return this.map.size;\n  }\n\n  delete(key: K, value: V): boolean {\n    const values = this.get(key);\n    if (!values) {\n      return false;\n    }\n    const result = values.delete(value);\n    if (!values.size) {\n      this.map.delete(key);\n    }\n    return result;\n  }\n\n  deleteAll(key: K): void {\n    this.map.delete(key);\n  }\n\n  keysArray(): K[] {\n    return [...this.map.keys()];\n  }\n\n  keys(): IterableIterator<K> {\n    return this.map.keys();\n  }\n\n  valuesArray(): V[] {\n    const result = [];\n    for (const set of this.map.values()) {\n      result.push(...set.values());\n    }\n    return result;\n  }\n\n  clear(): void {\n    this.map.clear();\n  }\n}\n\n/**\n * Gets value for key, assigning a default if value is falsy.\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nexport function getWithDefault<K extends {}, V>(\n    map: WeakMap<K, V>|Map<K, V>, key: K, defaultValueFactory: (key?: K) => V): V {\n  let value = map.get(key);\n  if (value === undefined || value === null) {\n    value = defaultValueFactory(key);\n    map.set(key, value);\n  }\n\n  return value;\n}\n"]}