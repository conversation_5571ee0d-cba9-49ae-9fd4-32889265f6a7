{"compilerOptions": {"allowJs": true, "checkJs": true, "composite": true, "declaration": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "inlineSources": true, "lib": ["esnext", "dom", "dom.iterable"], "module": "esnext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "outDir": ".", "rootDir": "../../../../../../front_end/core/platform", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "esnext", "tsBuildInfoFile": "devtools_entrypoint-bundle-typescript-tsconfig.json.tsbuildinfo", "typeRoots": [], "useUnknownInCatchVariables": false}, "files": ["../../../../../../front_end/core/platform/platform.ts", "../../../../../../front_end/legacy/legacy-defs.d.ts", "../../../../../../front_end/global_typings/global_defs.d.ts", "../../../../../../node_modules/@types/filesystem/index.d.ts"], "references": [{"path": "./platform-tsconfig.json"}]}