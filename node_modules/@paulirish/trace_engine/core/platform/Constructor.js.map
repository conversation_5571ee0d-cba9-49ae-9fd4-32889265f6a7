{"version": 3, "file": "Constructor.js", "sourceRoot": "", "sources": ["../../../../../../front_end/core/platform/Constructor.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B", "sourcesContent": ["// Copyright 2025 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type Constructor<T, Args extends any[] = any[]> = new (...args: Args) => T;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type AbstractConstructor<T, Args extends any[] = any[]> = (abstract new (...args: Args) => T);\n\nexport type ConstructorOrAbstract<T> = Constructor<T>|AbstractConstructor<T>;\n"]}