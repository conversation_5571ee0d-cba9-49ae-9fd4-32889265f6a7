{"version": 3, "file": "DOMStatsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/DOMStatsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAC/D,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAM3C,MAAM,iBAAiB,GAAsC,IAAI,GAAG,EAAE,CAAC;AAEvE,MAAM,UAAU,KAAK;IACnB,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IACD,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/G,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;AAC9B,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,EAAC,iBAAiB,EAAC,CAAC;AAC7B,CAAC", "sourcesContent": ["// Copyright 2025 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../../core/platform/platform.js';\nimport * as Types from '../types/types.js';\n\nexport interface DOMStatsData {\n  domStatsByFrameId: Map<string, Types.Events.DOMStats[]>;\n}\n\nconst domStatsByFrameId: DOMStatsData['domStatsByFrameId'] = new Map();\n\nexport function reset(): void {\n  domStatsByFrameId.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (!Types.Events.isDOMStats(event)) {\n    return;\n  }\n  const domStatEvents = Platform.MapUtilities.getWithDefault(domStatsByFrameId, event.args.data.frame, () => []);\n  domStatEvents.push(event);\n}\n\nexport async function finalize(): Promise<void> {\n}\n\nexport function data(): DOMStatsData {\n  return {domStatsByFrameId};\n}\n"]}