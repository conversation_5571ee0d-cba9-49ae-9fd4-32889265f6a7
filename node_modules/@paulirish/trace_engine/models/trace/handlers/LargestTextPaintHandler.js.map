{"version": 3, "file": "LargestTextPaintHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/LargestTextPaintHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAG7B,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAC3C;;;;IAII;AACJ,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAsE,CAAC;AAE3G,MAAM,UAAU,KAAK;IACnB,oBAAoB,CAAC,KAAK,EAAE,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,EAAE,CAAC;QACrD,OAAO;IACT,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO;IACT,CAAC;IAED,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;AAC9B,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,oBAAoB,CAAC;AAC9B,CAAC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport type * as Protocol from '../../../generated/protocol.js';\nimport * as Types from '../types/types.js';\n/**\n * A trace file will contain all the text paints that were candidates for the\n * LargestTextPaint. If an LCP event is text, it will point to one of these\n * candidates, so we store them by their DOM Node ID.\n **/\nconst textPaintByDOMNodeId = new Map<Protocol.DOM.BackendNodeId, Types.Events.LargestTextPaintCandidate>();\n\nexport function reset(): void {\n  textPaintByDOMNodeId.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (!Types.Events.isLargestTextPaintCandidate(event)) {\n    return;\n  }\n\n  if (!event.args.data) {\n    return;\n  }\n\n  textPaintByDOMNodeId.set(event.args.data.DOMNodeId, event);\n}\n\nexport async function finalize(): Promise<void> {\n}\n\nexport function data(): Map<Protocol.DOM.BackendNodeId, Types.Events.LargestTextPaintCandidate> {\n  return textPaintByDOMNodeId;\n}\n"]}