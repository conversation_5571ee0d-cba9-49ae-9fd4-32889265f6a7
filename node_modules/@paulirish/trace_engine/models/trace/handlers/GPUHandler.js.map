{"version": 3, "file": "GPUHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/GPUHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,eAAe,EAAC,MAAM,kBAAkB,CAAC;AAGzD,qFAAqF;AACrF,qEAAqE;AACrE,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAA8E,CAAC;AAEpH,IAAI,kBAAkB,GAA2B,EAAE,CAAC;AAEpD,MAAM,UAAU,KAAK;IACnB,qBAAqB,CAAC,KAAK,EAAE,CAAC;IAC9B,kBAAkB,GAAG,EAAE,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO;IACT,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AACtE,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,MAAM,EAAC,YAAY,EAAE,WAAW,EAAC,GAAG,eAAe,EAAE,CAAC;IACtD,MAAM,oBAAoB,GAAG,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACrE,IAAI,oBAAoB,IAAI,WAAW,EAAE,CAAC;QACxC,kBAAkB,GAAG,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACnE,CAAC;AACH,CAAC;AAMD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,kBAAkB;KACnB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {data as metaHandlerData} from './MetaHandler.js';\nimport type {HandlerName} from './types.js';\n\n// Each thread contains events. Events indicate the thread and process IDs, which are\n// used to store the event in the correct process thread entry below.\nconst eventsInProcessThread = new Map<Types.Events.ProcessID, Map<Types.Events.ThreadID, Types.Events.GPUTask[]>>();\n\nlet mainGPUThreadTasks: Types.Events.GPUTask[] = [];\n\nexport function reset(): void {\n  eventsInProcessThread.clear();\n  mainGPUThreadTasks = [];\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (!Types.Events.isGPUTask(event)) {\n    return;\n  }\n\n  Helpers.Trace.addEventToProcessThread(event, eventsInProcessThread);\n}\n\nexport async function finalize(): Promise<void> {\n  const {gpuProcessId, gpuThreadId} = metaHandlerData();\n  const gpuThreadsForProcess = eventsInProcessThread.get(gpuProcessId);\n  if (gpuThreadsForProcess && gpuThreadId) {\n    mainGPUThreadTasks = gpuThreadsForProcess.get(gpuThreadId) || [];\n  }\n}\n\nexport interface GPUHandlerReturnData {\n  mainGPUThreadTasks: readonly Types.Events.GPUTask[];\n}\n\nexport function data(): GPUHandlerReturnData {\n  return {\n    mainGPUThreadTasks,\n  };\n}\n\nexport function deps(): HandlerName[] {\n  return ['Meta'];\n}\n"]}