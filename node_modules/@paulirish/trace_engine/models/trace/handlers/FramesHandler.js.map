{"version": 3, "file": "FramesHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/FramesHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAC/D,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAA2B,IAAI,IAAI,mBAAmB,EAAC,MAAM,6BAA6B,CAAC;AAClG,OAAO,EAAC,IAAI,IAAI,oBAAoB,EAAqB,MAAM,uBAAuB,CAAC;AACvF,OAAO,EAAC,IAAI,IAAI,eAAe,EAAuB,MAAM,kBAAkB,CAAC;AAC/E,OAAO,EAAC,IAAI,IAAI,mBAAmB,EAA2B,MAAM,sBAAsB,CAAC;AAC3F,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AAGxC;;;;;;;;;GASG;AAEH,MAAM,SAAS,GAAyB,EAAE,CAAC;AAC3C,IAAI,KAAK,GAA4B,IAAI,CAAC;AAE1C,MAAM,UAAU,KAAK;IACnB,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,yEAAyE;IACzE,6BAA6B;IAC7B,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAEhD,MAAM,aAAa,GAAG,IAAI,kBAAkB,CACxC,SAAS,EACT,mBAAmB,EAAE,EACrB,mBAAmB,EAAE,EACrB,eAAe,EAAE,EACjB,oBAAoB,EAAE,CACzB,CAAC;IACF,KAAK,GAAG,aAAa,CAAC;AACxB,CAAC;AAOD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,EAAC,GAAG,KAAK,CAAC,UAAU,EAAE,EAAC,CAAC,CAAC,CAAC,EAAE;KACjD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC9D,CAAC;AAMD,SAAS,YAAY,CAAC,KAAyB;IAC7C,OAAO,CACH,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;QAC1G,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC;QAC1F,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC;QAC5C,4EAA4E;QAC5E,oEAAoE;QACpE,2EAA2E;QAC3E,4CAA4C;QAC5C,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;QACrE,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAClF,CAAC;AAED,SAAS,eAAe,CAAC,KAAyB;IAChD,MAAM,wBAAwB,GAAG,uCAAuC,CAAC;IACzE,OAAO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;AACnG,CAAC;AAED,MAAM,OAAO,kBAAkB;IAC7B,OAAO,GAAoB,EAAE,CAAC;IAC9B,UAAU,GAAkC,EAAE,CAAC;IAC/C,gBAAgB,GAAiC,IAAI,4BAA4B,EAAE,CAAC;IACpF,UAAU,GAAuB,IAAI,CAAC;IACtC,mBAAmB,GAAG,KAAK,CAAC;IAC5B,mBAAmB,GAAG,KAAK,CAAC;IAC5B,cAAc,GAA+C,IAAI,CAAC;IAClE,uBAAuB,GAAsB,IAAI,CAAC;IAClD,mBAAmB,GAAsB,IAAI,CAAC;IAC9C,eAAe,GAAgB,IAAI,CAAC;IACpC,oBAAoB,GAAgB,IAAI,CAAC;IACzC,kBAAkB,GAA4B,IAAI,CAAC;IACnD,YAAY,GAAgB,IAAI,CAAC;IACjC,gBAAgB,GAAgC,IAAI,CAAC;IACrD,eAAe,GAA+B,IAAI,CAAC;IACnD,cAAc,CAAgB;IAE9B,YACI,SAAwC,EAAE,YAAiC,EAC3E,mBAAwC,EAAE,QAAyB,EAAE,aAA4B;QACnG,qEAAqE;QACrE,yEAAyE;QACzE,YAAY;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC/F,OAAO,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,UAAU,CAAC,WAAW,IAAI,MAAM,CAAC,oBAAoB,CAAC;QACvF,CAAC,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO;gBACL,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;aAChC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;IACpE,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,iBAAiB,CAAC,SAA6B,EAAE,KAAa;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QAEjC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAED,mBAAmB,CAAC,SAA6B,EAAE,KAAa,EAAE,SAAkB;QAClF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,qEAAqE;QACrE,sEAAsE;QACtE,8BAA8B;QAC9B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7E,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,gBAAgB,CAAC,SAA6B,EAAE,KAAa;QAC3D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QAED,0CAA0C;QAC1C,8FAA8F;QAC9F,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;oBAC1C,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvG,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC5C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC9B,CAAC;gBACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACnC,CAAC;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC;YAE5F,gEAAgE;YAChE,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE,CAAC;gBACtC,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBAE7C,mEAAmE;gBACnE,4DAA4D;gBAC5D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACzC,IAAI,eAAe,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBACpD,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,CAAC;gBACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;oBACpB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;gBACjC,CAAC;gBACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;oBACpB,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,6BAA6B;QAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACxD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,wBAAwB,CAAC,SAAgD;QACvE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAClC,CAAC;IAED,uBAAuB,CAAC,SAA6B,EAAE,eAAwB;QAC7E,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACxC,CAAC;IACH,CAAC;IAED,WAAW,CAAC,SAA6B,EAAE,KAAa;QACtD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,UAAU;YACX,IAAI,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7G,CAAC;IAED,WAAW,CAAC,KAAoB,EAAE,OAA2B;QAC3D,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5C,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS;YAChC,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,OAAO,IAAI,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/E,OAAO,CAAC,MAAM,CACV,KAAK,EAAE,qCAAqC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;QACjH,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QACpC,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC1C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC;QACvE,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,eAAe,CACX,MAAqC,EAAE,UAIrC,EACF,WAAmB;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC;QACtE,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC;QACvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAChF,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC3C,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC5C,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,cAAc,CAAC,KAAyB,EAAE,WAAmB;QAC3D,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YAC9E,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QAClD,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;YACrG,IAAI,CAAC,wBAAwB,CAAC;gBAC5B,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,EAAE;aACX,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;YACD,yEAAyE;YACzE,8DAA8D;YAC9D,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC9E,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED,wBAAwB,CAAC,KAAiB;QACxC,IAAI,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;YACpD,OAAO;QACT,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC7D,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClC,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACvC,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,mEAAmE;YACnE,wDAAwD;YACxD,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/G,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAED,wBAAwB,CAAC,KAAyB;QAChD,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAyB,CAAC,EAAE,CAAC;YACzF,IAAI,CAAC,mBAAmB,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;QACnF,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1E,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QACjE,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClE,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QACD,uEAAuE;QACvE,sCAAsC;QACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACvE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;CACF;AAED,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAoB;IACpD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B;IAC9C,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;IACnC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB;IACzC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;CAC/B,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,aAAa;IACjB,8DAA8D;IAC9D,2EAA2E;IAC3E,6DAA6D;IAC7D,sBAAsB;IACtB,GAAG,GAAG,uBAAuB,CAAC;IAC9B,IAAI,GAAG,OAAO,CAAC;IACf,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;IACjC,EAAE,CAAqB;IACvB,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhC,KAAK,GAAG,CAAC,CAAC,CAAC;IACX,SAAS,CAAqB;IAC9B,eAAe,CAAqB;IACpC,OAAO,CAAqB;IAC5B,QAAQ,CAAqB;IAC7B,IAAI,CAAU;IACd,OAAO,CAAU;IACjB,SAAS,CAAU;IACnB,SAAS,CAA6C;IACtD,MAAM,CAAoB;IAC1B,WAAW,CAAmB;IACrB,KAAK,CAAS;IAEvB,YAAY,KAAa,EAAE,SAA6B,EAAE,eAAmC;QAC3F,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;IAC/B,CAAC;IAED,QAAQ,CAAC,CAAS;QAChB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,CAAC;IAED,UAAU,CAAC,OAA2B;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IACpE,CAAC;IAED,YAAY,CAAC,SAAqD;QAChE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IACjB,MAAM,CAAqB;IACpC,SAAS,CAAuC;IAEhD,YAAY,KAAyB,EAAE,QAA8C;QACnF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IACvC,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,OAAO;QACL,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACvD,OAAO,IAAI,IAAI,WAAW,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;CACF;AAED,MAAM,OAAO,YAAY;IACvB,MAAM,CAAoB;IAC1B,WAAW,CAAmB;IAC9B,WAAW,CAAS;IACpB,YAAY,WAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;CACF;AAED,6CAA6C;AAC7C,MAAM,cAAc;IAClB,KAAK,CAAS;IACd,SAAS,CAAqB;IAC9B,SAAS,CAAU;IACnB,SAAS,CAAU;IACnB,YAAY,KAAa,EAAE,SAA6B,EAAE,SAAkB,EAAE,SAAkB;QAC9F,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;CACF;AAED,gDAAgD;AAChD,wEAAwE;AACxE,gFAAgF;AAChF,kEAAkE;AAClE,MAAM,OAAO,4BAA4B;IAC/B,WAAW,GAAa,EAAE,CAAC;IAEnC,qCAAqC;IAC7B,SAAS,GAAmC,EAAE,CAAC;IAEvD,8DAA8D;IAC9D,mBAAmB,CAAC,KAAa,EAAE,SAA6B,EAAE,SAAkB,EAAE,SAAkB;QACtG,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACnF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,wCAAwC;IACxC,UAAU,CAAC,KAAa,EAAE,SAAkB;QAC1C,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,UAAU,CAAC,KAAa,EAAE,SAAkB;QAC1C,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,KAAa;QAChD,MAAM,iBAAiB,GAAqB,EAAE,CAAC;QAE/C,2EAA2E;QAC3E,4CAA4C;QAC5C,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5B,yEAAyE;YACzE,iCAAiC;YACjC,gEAAgE;YAChE,6DAA6D;YAC7D,yEAAyE;YACzE,4DAA4D;YAC5D,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;gBACrC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,CAAC;oBAC3C,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;gBACvD,CAAC;gBAED,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,CAAC;YAED,4DAA4D;YAC5D,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;QACD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF;AAED,MAAM,UAAU,kBAAkB,CAC9B,MAAmD,EAAE,SAA6B,EAClF,OAA2B;IAC7B,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IACrH,MAAM,SAAS,GACX,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,IAAI,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IAC7G,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC7C,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../../core/platform/platform.js';\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {type AuctionWorkletsData, data as auctionWorkletsData} from './AuctionWorkletsHandler.js';\nimport {data as layerTreeHandlerData, type LayerTreeData} from './LayerTreeHandler.js';\nimport {data as metaHandlerData, type MetaHandlerData} from './MetaHandler.js';\nimport {data as rendererHandlerData, type RendererHandlerData} from './RendererHandler.js';\nimport * as Threads from './Threads.js';\nimport type {HandlerName} from './types.js';\n\n/**\n * IMPORTANT: this handler is slightly different to the rest. This is because\n * it is an adaptation of the TimelineFrameModel that has been used in DevTools\n * for many years. Rather than re-implement all the logic from scratch, instead\n * this handler gathers up the events and instantitates the class in the\n * finalize() method. Once the class has parsed all events, it is used to then\n * return the array of frames.\n *\n * In time we expect to migrate this code to a more \"typical\" handler.\n */\n\nconst allEvents: Types.Events.Event[] = [];\nlet model: TimelineFrameModel|null = null;\n\nexport function reset(): void {\n  allEvents.length = 0;\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  allEvents.push(event);\n}\n\nexport async function finalize(): Promise<void> {\n  // Snapshot events can be emitted out of order, so we need to sort before\n  // building the frames model.\n  Helpers.Trace.sortTraceEventsInPlace(allEvents);\n\n  const modelForTrace = new TimelineFrameModel(\n      allEvents,\n      rendererHandlerData(),\n      auctionWorkletsData(),\n      metaHandlerData(),\n      layerTreeHandlerData(),\n  );\n  model = modelForTrace;\n}\n\nexport interface FramesData {\n  frames: readonly Types.Events.LegacyTimelineFrame[];\n  framesById: Readonly<Record<number, Types.Events.LegacyTimelineFrame|undefined>>;\n}\n\nexport function data(): FramesData {\n  return {\n    frames: model ? Array.from(model.frames()) : [],\n    framesById: model ? {...model.framesById()} : {},\n  };\n}\n\nexport function deps(): HandlerName[] {\n  return ['Meta', 'Renderer', 'AuctionWorklets', 'LayerTree'];\n}\n\ntype FrameEvent = Types.Events.BeginFrame|Types.Events.DroppedFrame|Types.Events.RequestMainThreadFrame|\n                  Types.Events.BeginMainThreadFrame|Types.Events.Commit|Types.Events.CompositeLayers|\n                  Types.Events.ActivateLayerTree|Types.Events.NeedsBeginFrameChanged|Types.Events.DrawFrame;\n\nfunction isFrameEvent(event: Types.Events.Event): event is FrameEvent {\n  return (\n      Types.Events.isSetLayerId(event) || Types.Events.isBeginFrame(event) || Types.Events.isDroppedFrame(event) ||\n      Types.Events.isRequestMainThreadFrame(event) || Types.Events.isBeginMainThreadFrame(event) ||\n      Types.Events.isNeedsBeginFrameChanged(event) ||\n      // Note that \"Commit\" is the replacement for \"CompositeLayers\" so in a trace\n      // we wouldn't expect to see a combination of these. All \"new\" trace\n      // recordings use \"Commit\", but we can easily support \"CompositeLayers\" too\n      // to not break older traces being imported.\n      Types.Events.isCommit(event) || Types.Events.isCompositeLayers(event) ||\n      Types.Events.isActivateLayerTree(event) || Types.Events.isDrawFrame(event));\n}\n\nfunction entryIsTopLevel(entry: Types.Events.Event): boolean {\n  const devtoolsTimelineCategory = 'disabled-by-default-devtools.timeline';\n  return entry.name === Types.Events.Name.RUN_TASK && entry.cat.includes(devtoolsTimelineCategory);\n}\n\nexport class TimelineFrameModel {\n  #frames: TimelineFrame[] = [];\n  #frameById: Record<number, TimelineFrame> = {};\n  #beginFrameQueue: TimelineFrameBeginFrameQueue = new TimelineFrameBeginFrameQueue();\n  #lastFrame: TimelineFrame|null = null;\n  #mainFrameCommitted = false;\n  #mainFrameRequested = false;\n  #lastLayerTree: Types.Events.LegacyFrameLayerTreeData|null = null;\n  #framePendingActivation: PendingFrame|null = null;\n  #framePendingCommit: PendingFrame|null = null;\n  #lastBeginFrame: number|null = null;\n  #lastNeedsBeginFrame: number|null = null;\n  #lastTaskBeginTime: Types.Timing.Micro|null = null;\n  #layerTreeId: number|null = null;\n  #activeProcessId: Types.Events.ProcessID|null = null;\n  #activeThreadId: Types.Events.ThreadID|null = null;\n  #layerTreeData: LayerTreeData;\n\n  constructor(\n      allEvents: readonly Types.Events.Event[], rendererData: RendererHandlerData,\n      auctionWorkletsData: AuctionWorkletsData, metaData: MetaHandlerData, layerTreeData: LayerTreeData) {\n    // We only care about getting threads from the Renderer, not Samples,\n    // because Frames don't exist in a CPU Profile (which won't have Renderer\n    // threads.)\n    const mainThreads = Threads.threadsInRenderer(rendererData, auctionWorkletsData).filter(thread => {\n      return thread.type === Threads.ThreadType.MAIN_THREAD && thread.processIsOnMainFrame;\n    });\n    const threadData = mainThreads.map(thread => {\n      return {\n        tid: thread.tid,\n        pid: thread.pid,\n        startTime: thread.entries[0].ts,\n      };\n    });\n\n    this.#layerTreeData = layerTreeData;\n    this.#addTraceEvents(allEvents, threadData, metaData.mainFrameId);\n  }\n\n  framesById(): Readonly<Record<number, TimelineFrame|undefined>> {\n    return this.#frameById;\n  }\n\n  frames(): TimelineFrame[] {\n    return this.#frames;\n  }\n\n  #handleBeginFrame(startTime: Types.Timing.Micro, seqId: number): void {\n    if (!this.#lastFrame) {\n      this.#startFrame(startTime, seqId);\n    }\n    this.#lastBeginFrame = startTime;\n\n    this.#beginFrameQueue.addFrameIfNotExists(seqId, startTime, false, false);\n  }\n\n  #handleDroppedFrame(startTime: Types.Timing.Micro, seqId: number, isPartial: boolean): void {\n    if (!this.#lastFrame) {\n      this.#startFrame(startTime, seqId);\n    }\n\n    // This line handles the case where no BeginFrame event is issued for\n    // the dropped frame. In this situation, add a BeginFrame to the queue\n    // as if it actually occurred.\n    this.#beginFrameQueue.addFrameIfNotExists(seqId, startTime, true, isPartial);\n    this.#beginFrameQueue.setDropped(seqId, true);\n    this.#beginFrameQueue.setPartial(seqId, isPartial);\n  }\n\n  #handleDrawFrame(startTime: Types.Timing.Micro, seqId: number): void {\n    if (!this.#lastFrame) {\n      this.#startFrame(startTime, seqId);\n      return;\n    }\n\n    // - if it wasn't drawn, it didn't happen!\n    // - only show frames that either did not wait for the main thread frame or had one committed.\n    if (this.#mainFrameCommitted || !this.#mainFrameRequested) {\n      if (this.#lastNeedsBeginFrame) {\n        const idleTimeEnd = this.#framePendingActivation ? this.#framePendingActivation.triggerTime :\n                                                           (this.#lastBeginFrame || this.#lastNeedsBeginFrame);\n        if (idleTimeEnd > this.#lastFrame.startTime) {\n          this.#lastFrame.idle = true;\n          this.#lastBeginFrame = null;\n        }\n        this.#lastNeedsBeginFrame = null;\n      }\n\n      const framesToVisualize = this.#beginFrameQueue.processPendingBeginFramesOnDrawFrame(seqId);\n\n      // Visualize the current frame and all pending frames before it.\n      for (const frame of framesToVisualize) {\n        const isLastFrameIdle = this.#lastFrame.idle;\n\n        // If |frame| is the first frame after an idle period, the CPU time\n        // will be logged (\"committed\") under |frame| if applicable.\n        this.#startFrame(frame.startTime, seqId);\n        if (isLastFrameIdle && this.#framePendingActivation) {\n          this.#commitPendingFrame();\n        }\n        if (frame.isDropped) {\n          this.#lastFrame.dropped = true;\n        }\n        if (frame.isPartial) {\n          this.#lastFrame.isPartial = true;\n        }\n      }\n    }\n    this.#mainFrameCommitted = false;\n  }\n\n  #handleActivateLayerTree(): void {\n    if (!this.#lastFrame) {\n      return;\n    }\n    if (this.#framePendingActivation && !this.#lastNeedsBeginFrame) {\n      this.#commitPendingFrame();\n    }\n  }\n\n  #handleRequestMainThreadFrame(): void {\n    if (!this.#lastFrame) {\n      return;\n    }\n    this.#mainFrameRequested = true;\n  }\n\n  #handleCommit(): void {\n    if (!this.#framePendingCommit) {\n      return;\n    }\n    this.#framePendingActivation = this.#framePendingCommit;\n    this.#framePendingCommit = null;\n    this.#mainFrameRequested = false;\n    this.#mainFrameCommitted = true;\n  }\n\n  #handleLayerTreeSnapshot(layerTree: Types.Events.LegacyFrameLayerTreeData): void {\n    this.#lastLayerTree = layerTree;\n  }\n\n  #handleNeedFrameChanged(startTime: Types.Timing.Micro, needsBeginFrame: boolean): void {\n    if (needsBeginFrame) {\n      this.#lastNeedsBeginFrame = startTime;\n    }\n  }\n\n  #startFrame(startTime: Types.Timing.Micro, seqId: number): void {\n    if (this.#lastFrame) {\n      this.#flushFrame(this.#lastFrame, startTime);\n    }\n    this.#lastFrame =\n        new TimelineFrame(seqId, startTime, Types.Timing.Micro(startTime - metaHandlerData().traceBounds.min));\n  }\n\n  #flushFrame(frame: TimelineFrame, endTime: Types.Timing.Micro): void {\n    frame.setLayerTree(this.#lastLayerTree);\n    frame.setEndTime(endTime);\n    if (this.#lastLayerTree) {\n      this.#lastLayerTree.paints = frame.paints;\n    }\n    const lastFrame = this.#frames[this.#frames.length - 1];\n    if (this.#frames.length && lastFrame &&\n        (frame.startTime !== lastFrame.endTime || frame.startTime > frame.endTime)) {\n      console.assert(\n          false, `Inconsistent frame time for frame ${this.#frames.length} (${frame.startTime} - ${frame.endTime})`);\n    }\n    const newFramesLength = this.#frames.push(frame);\n    frame.setIndex(newFramesLength - 1);\n    if (typeof frame.mainFrameId === 'number') {\n      this.#frameById[frame.mainFrameId] = frame;\n    }\n  }\n\n  #commitPendingFrame(): void {\n    if (!this.#framePendingActivation || !this.#lastFrame) {\n      return;\n    }\n\n    this.#lastFrame.paints = this.#framePendingActivation.paints;\n    this.#lastFrame.mainFrameId = this.#framePendingActivation.mainFrameId;\n    this.#framePendingActivation = null;\n  }\n\n  #addTraceEvents(\n      events: readonly Types.Events.Event[], threadData: Array<{\n        pid: Types.Events.ProcessID,\n        tid: Types.Events.ThreadID,\n        startTime: Types.Timing.Micro,\n      }>,\n      mainFrameId: string): void {\n    let j = 0;\n    this.#activeThreadId = threadData.length && threadData[0].tid || null;\n    this.#activeProcessId = threadData.length && threadData[0].pid || null;\n    for (let i = 0; i < events.length; ++i) {\n      while (j + 1 < threadData.length && threadData[j + 1].startTime <= events[i].ts) {\n        this.#activeThreadId = threadData[++j].tid;\n        this.#activeProcessId = threadData[j].pid;\n      }\n      this.#addTraceEvent(events[i], mainFrameId);\n    }\n    this.#activeThreadId = null;\n    this.#activeProcessId = null;\n  }\n\n  #addTraceEvent(event: Types.Events.Event, mainFrameId: string): void {\n    if (Types.Events.isSetLayerId(event) && event.args.data.frame === mainFrameId) {\n      this.#layerTreeId = event.args.data.layerTreeId;\n    } else if (Types.Events.isLayerTreeHostImplSnapshot(event) && Number(event.id) === this.#layerTreeId) {\n      this.#handleLayerTreeSnapshot({\n        entry: event,\n        paints: [],\n      });\n    } else {\n      if (isFrameEvent(event)) {\n        this.#processCompositorEvents(event);\n      }\n      // Make sure we only use events from the main thread: we check the PID as\n      // well in case two processes have a thread with the same TID.\n      if (event.tid === this.#activeThreadId && event.pid === this.#activeProcessId) {\n        this.#addMainThreadTraceEvent(event);\n      }\n    }\n  }\n\n  #processCompositorEvents(entry: FrameEvent): void {\n    if (entry.args['layerTreeId'] !== this.#layerTreeId) {\n      return;\n    }\n    if (Types.Events.isBeginFrame(entry)) {\n      this.#handleBeginFrame(entry.ts, entry.args['frameSeqId']);\n    } else if (Types.Events.isDrawFrame(entry)) {\n      this.#handleDrawFrame(entry.ts, entry.args['frameSeqId']);\n    } else if (Types.Events.isActivateLayerTree(entry)) {\n      this.#handleActivateLayerTree();\n    } else if (Types.Events.isRequestMainThreadFrame(entry)) {\n      this.#handleRequestMainThreadFrame();\n    } else if (Types.Events.isNeedsBeginFrameChanged(entry)) {\n      // needsBeginFrame property will either be 0 or 1, which represents\n      // true/false in this case, hence the Boolean() wrapper.\n      this.#handleNeedFrameChanged(entry.ts, entry.args['data'] && Boolean(entry.args['data']['needsBeginFrame']));\n    } else if (Types.Events.isDroppedFrame(entry)) {\n      this.#handleDroppedFrame(entry.ts, entry.args['frameSeqId'], Boolean(entry.args['hasPartialUpdate']));\n    }\n  }\n\n  #addMainThreadTraceEvent(entry: Types.Events.Event): void {\n    if (entryIsTopLevel(entry)) {\n      this.#lastTaskBeginTime = entry.ts;\n    }\n    if (!this.#framePendingCommit && MAIN_FRAME_MARKERS.has(entry.name as Types.Events.Name)) {\n      this.#framePendingCommit = new PendingFrame(this.#lastTaskBeginTime || entry.ts);\n    }\n    if (!this.#framePendingCommit) {\n      return;\n    }\n\n    if (Types.Events.isBeginMainThreadFrame(entry) && entry.args.data.frameId) {\n      this.#framePendingCommit.mainFrameId = entry.args.data.frameId;\n    }\n    if (Types.Events.isPaint(entry)) {\n      const snapshot = this.#layerTreeData.paintsToSnapshots.get(entry);\n      if (snapshot) {\n        this.#framePendingCommit.paints.push(new LayerPaintEvent(entry, snapshot));\n      }\n    }\n    // Commit will be replacing CompositeLayers but CompositeLayers is kept\n    // around for backwards compatibility.\n    if ((Types.Events.isCompositeLayers(entry) || Types.Events.isCommit(entry)) &&\n        entry.args['layerTreeId'] === this.#layerTreeId) {\n      this.#handleCommit();\n    }\n  }\n}\n\nconst MAIN_FRAME_MARKERS = new Set<Types.Events.Name>([\n  Types.Events.Name.SCHEDULE_STYLE_RECALCULATION,\n  Types.Events.Name.INVALIDATE_LAYOUT,\n  Types.Events.Name.BEGIN_MAIN_THREAD_FRAME,\n  Types.Events.Name.SCROLL_LAYER,\n]);\n\n/**\n * Legacy class that represents TimelineFrames that was ported from the old SDK.\n * This class is purposefully not exported as it breaks the abstraction that\n * every event shown on the timeline is a trace event. Instead, we use the Type\n * LegacyTimelineFrame to represent frames in the codebase. These do implement\n * the right interface to be treated just like they were a trace event.\n */\nclass TimelineFrame implements Types.Events.LegacyTimelineFrame {\n  // These fields exist to satisfy the base Event type which all\n  // \"trace events\" must implement. They aren't used, but doing this means we\n  // can pass `TimelineFrame` instances into places that expect\n  // Types.Events.Event.\n  cat = 'devtools.legacy_frame';\n  name = 'frame';\n  ph = Types.Events.Phase.COMPLETE;\n  ts: Types.Timing.Micro;\n  pid = Types.Events.ProcessID(-1);\n  tid = Types.Events.ThreadID(-1);\n\n  index = -1;\n  startTime: Types.Timing.Micro;\n  startTimeOffset: Types.Timing.Micro;\n  endTime: Types.Timing.Micro;\n  duration: Types.Timing.Micro;\n  idle: boolean;\n  dropped: boolean;\n  isPartial: boolean;\n  layerTree: Types.Events.LegacyFrameLayerTreeData|null;\n  paints: LayerPaintEvent[];\n  mainFrameId: number|undefined;\n  readonly seqId: number;\n\n  constructor(seqId: number, startTime: Types.Timing.Micro, startTimeOffset: Types.Timing.Micro) {\n    this.seqId = seqId;\n    this.startTime = startTime;\n    this.ts = startTime;\n    this.startTimeOffset = startTimeOffset;\n    this.endTime = this.startTime;\n    this.duration = Types.Timing.Micro(0);\n    this.idle = false;\n    this.dropped = false;\n    this.isPartial = false;\n    this.layerTree = null;\n    this.paints = [];\n    this.mainFrameId = undefined;\n  }\n\n  setIndex(i: number): void {\n    this.index = i;\n  }\n\n  setEndTime(endTime: Types.Timing.Micro): void {\n    this.endTime = endTime;\n    this.duration = Types.Timing.Micro(this.endTime - this.startTime);\n  }\n\n  setLayerTree(layerTree: Types.Events.LegacyFrameLayerTreeData|null): void {\n    this.layerTree = layerTree;\n  }\n\n  /**\n   * Fake the `dur` field to meet the expected value given that we pretend\n   * these TimelineFrame classes are trace events across the codebase.\n   */\n  get dur(): Types.Timing.Micro {\n    return this.duration;\n  }\n}\n\nexport class LayerPaintEvent implements Types.Events.LegacyLayerPaintEvent {\n  readonly #event: Types.Events.Paint;\n  #snapshot: Types.Events.DisplayItemListSnapshot;\n\n  constructor(event: Types.Events.Paint, snapshot: Types.Events.DisplayItemListSnapshot) {\n    this.#event = event;\n    this.#snapshot = snapshot;\n  }\n\n  layerId(): number {\n    return this.#event.args.data.layerId;\n  }\n\n  event(): Types.Events.Paint {\n    return this.#event;\n  }\n\n  picture(): Types.Events.LegacyLayerPaintEventPicture|null {\n    const rect = this.#snapshot.args.snapshot.params?.layer_rect;\n    const pictureData = this.#snapshot.args.snapshot.skp64;\n    return rect && pictureData ? {rect, serializedPicture: pictureData} : null;\n  }\n}\n\nexport class PendingFrame {\n  paints: LayerPaintEvent[];\n  mainFrameId: number|undefined;\n  triggerTime: number;\n  constructor(triggerTime: number) {\n    this.paints = [];\n    this.mainFrameId = undefined;\n    this.triggerTime = triggerTime;\n  }\n}\n\n// The parameters of an impl-side BeginFrame.\nclass BeginFrameInfo {\n  seqId: number;\n  startTime: Types.Timing.Micro;\n  isDropped: boolean;\n  isPartial: boolean;\n  constructor(seqId: number, startTime: Types.Timing.Micro, isDropped: boolean, isPartial: boolean) {\n    this.seqId = seqId;\n    this.startTime = startTime;\n    this.isDropped = isDropped;\n    this.isPartial = isPartial;\n  }\n}\n\n// A queue of BeginFrames pending visualization.\n// BeginFrames are added into this queue as they occur; later when their\n// corresponding DrawFrames occur (or lack thereof), the BeginFrames are removed\n// from the queue and their timestamps are used for visualization.\nexport class TimelineFrameBeginFrameQueue {\n  private queueFrames: number[] = [];\n\n  // Maps frameSeqId to BeginFrameInfo.\n  private mapFrames: Record<number, BeginFrameInfo> = {};\n\n  // Add a BeginFrame to the queue, if it does not already exit.\n  addFrameIfNotExists(seqId: number, startTime: Types.Timing.Micro, isDropped: boolean, isPartial: boolean): void {\n    if (!(seqId in this.mapFrames)) {\n      this.mapFrames[seqId] = new BeginFrameInfo(seqId, startTime, isDropped, isPartial);\n      this.queueFrames.push(seqId);\n    }\n  }\n\n  // Set a BeginFrame in queue as dropped.\n  setDropped(seqId: number, isDropped: boolean): void {\n    if (seqId in this.mapFrames) {\n      this.mapFrames[seqId].isDropped = isDropped;\n    }\n  }\n\n  setPartial(seqId: number, isPartial: boolean): void {\n    if (seqId in this.mapFrames) {\n      this.mapFrames[seqId].isPartial = isPartial;\n    }\n  }\n\n  processPendingBeginFramesOnDrawFrame(seqId: number): BeginFrameInfo[] {\n    const framesToVisualize: BeginFrameInfo[] = [];\n\n    // Do not visualize this frame in the rare case where the current DrawFrame\n    // does not have a corresponding BeginFrame.\n    if (seqId in this.mapFrames) {\n      // Pop all BeginFrames before the current frame, and add only the dropped\n      // ones in |frames_to_visualize|.\n      // Non-dropped frames popped here are BeginFrames that are never\n      // drawn (but not considered dropped either for some reason).\n      // Those frames do not require an proactive visualization effort and will\n      // be naturally presented as continuationss of other frames.\n      while (this.queueFrames[0] !== seqId) {\n        const currentSeqId = this.queueFrames[0];\n        if (this.mapFrames[currentSeqId].isDropped) {\n          framesToVisualize.push(this.mapFrames[currentSeqId]);\n        }\n\n        delete this.mapFrames[currentSeqId];\n        this.queueFrames.shift();\n      }\n\n      // Pop the BeginFrame associated with the current DrawFrame.\n      framesToVisualize.push(this.mapFrames[seqId]);\n      delete this.mapFrames[seqId];\n      this.queueFrames.shift();\n    }\n    return framesToVisualize;\n  }\n}\n\nexport function framesWithinWindow(\n    frames: readonly Types.Events.LegacyTimelineFrame[], startTime: Types.Timing.Micro,\n    endTime: Types.Timing.Micro): Types.Events.LegacyTimelineFrame[] {\n  const firstFrame = Platform.ArrayUtilities.lowerBound(frames, startTime || 0, (time, frame) => time - frame.endTime);\n  const lastFrame =\n      Platform.ArrayUtilities.lowerBound(frames, endTime || Infinity, (time, frame) => time - frame.startTime);\n  return frames.slice(firstFrame, lastFrame);\n}\n"]}