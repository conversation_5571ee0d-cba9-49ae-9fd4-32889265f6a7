{"version": 3, "file": "LargestImagePaintHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/LargestImagePaintHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAE/D,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,QAAQ,EAAC,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAC,IAAI,IAAI,mBAAmB,EAAC,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAC,IAAI,IAAI,mBAAmB,EAAE,UAAU,EAAC,MAAM,6BAA6B,CAAC;AAGpF;;;;;;;;;;;;;;;;;;IAkBI;AACJ,MAAM,6BAA6B,GAC/B,IAAI,GAAG,EAAoG,CAAC;AAChH,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAgD,CAAC;AAEzF,MAAM,UAAU,KAAK;IACnB,6BAA6B,CAAC,KAAK,EAAE,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1E,OAAO;IACT,CAAC;IAED,MAAM,mBAAmB,GACrB,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,6BAA6B,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IACpG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC,MAAM,CAAC;IAC9C,MAAM,EAAC,WAAW,EAAE,yBAAyB,EAAC,GAAG,QAAQ,EAAE,CAAC;IAC5D,MAAM,qBAAqB,GAAG,mBAAmB,EAAE,CAAC,qBAAqB,CAAC;IAE1E,KAAK,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,IAAI,yBAAyB,EAAE,CAAC;QACnE,MAAM,SAAS,GAAG,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3G,MAAM,QAAQ,GAAG,SAAS,EAAE,KAAK,CAAC;QAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3E,SAAS;QACX,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,SAAS;QACX,CAAC;QAED,MAAM,kBAAkB,GAAG,6BAA6B,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QACxF,MAAM,MAAM,GAAG,kBAAkB,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACvD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,SAAS;QACX,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,EAAE,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;QACpD,MAAM,OAAO,GAAG,kBAAkB,CAAC,EAAE,CAAC;QAEtC,IAAI,UAAU,CAAC;QACf,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;gBAC3B,SAAS;YACX,CAAC;YACD,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,EAAE,CAAC;gBAC1B,MAAM;YACR,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;gBAChG,UAAU,GAAG,OAAO,CAAC;gBACrB,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,wBAAwB,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;AACH,CAAC;AAMD,MAAM,UAAU,IAAI;IAClB,OAAO,EAAC,wBAAwB,EAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AACxD,CAAC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../../core/platform/platform.js';\nimport type * as Protocol from '../../../generated/protocol.js';\nimport * as Types from '../types/types.js';\n\nimport {data as metaData} from './MetaHandler.js';\nimport {data as networkRequestsData} from './NetworkRequestsHandler.js';\nimport {data as pageLoadMetricsData, MetricName} from './PageLoadMetricsHandler.js';\nimport type {HandlerName} from './types.js';\n\n/**\n * If the LCP resource was an image, and that image was fetched over the\n * network, we want to be able to find the network request in order to construct\n * the critical path for an LCP image.\n * Within the trace file there are `LargestImagePaint::Candidate` events.\n * Within their data object, they contain a `DOMNodeId` property, which maps to\n * the DOM Node ID for that image.\n *\n * This id maps exactly to the `data.nodeId` property that a\n * `LargestContentfulPaint::Candidate` will have. So, when we find an image\n * paint candidate, we can store it, keying it on the node ID.\n * Then, when it comes to finding the network request for an LCP image, we can\n * use the nodeId from the LCP candidate to find the image candidate. That image\n * candidate also contains a `imageUrl` property, which will have the full URL\n * to the image.\n *\n * `BackendNodeId`s are only unique within a given renderer process, so this is\n * also keyed on `ProcessId`.\n **/\nconst imagePaintsByNodeIdAndProcess =\n    new Map<Types.Events.ProcessID, Map<Protocol.DOM.BackendNodeId, Types.Events.LargestImagePaintCandidate>>();\nconst lcpRequestByNavigationId = new Map<string, Types.Events.SyntheticNetworkRequest>();\n\nexport function reset(): void {\n  imagePaintsByNodeIdAndProcess.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (!Types.Events.isLargestImagePaintCandidate(event) || !event.args.data) {\n    return;\n  }\n\n  const imagePaintsByNodeId =\n      Platform.MapUtilities.getWithDefault(imagePaintsByNodeIdAndProcess, event.pid, () => new Map());\n  imagePaintsByNodeId.set(event.args.data.DOMNodeId, event);\n}\n\nexport async function finalize(): Promise<void> {\n  const requests = networkRequestsData().byTime;\n  const {traceBounds, navigationsByNavigationId} = metaData();\n  const metricScoresByFrameId = pageLoadMetricsData().metricScoresByFrameId;\n\n  for (const [navigationId, navigation] of navigationsByNavigationId) {\n    const lcpMetric = metricScoresByFrameId.get(navigation.args.frame)?.get(navigationId)?.get(MetricName.LCP);\n    const lcpEvent = lcpMetric?.event;\n    if (!lcpEvent || !Types.Events.isLargestContentfulPaintCandidate(lcpEvent)) {\n      continue;\n    }\n\n    const nodeId = lcpEvent.args.data?.nodeId;\n    if (!nodeId) {\n      continue;\n    }\n\n    const lcpImagePaintEvent = imagePaintsByNodeIdAndProcess.get(lcpEvent.pid)?.get(nodeId);\n    const lcpUrl = lcpImagePaintEvent?.args.data?.imageUrl;\n    if (!lcpUrl) {\n      continue;\n    }\n\n    const startTime = navigation?.ts ?? traceBounds.min;\n    const endTime = lcpImagePaintEvent.ts;\n\n    let lcpRequest;\n    for (const request of requests) {\n      if (request.ts < startTime) {\n        continue;\n      }\n      if (request.ts >= endTime) {\n        break;\n      }\n\n      if (request.args.data.url === lcpUrl || request.args.data.redirects.some(r => r.url === lcpUrl)) {\n        lcpRequest = request;\n        break;\n      }\n    }\n\n    if (lcpRequest) {\n      lcpRequestByNavigationId.set(navigationId, lcpRequest);\n    }\n  }\n}\n\nexport interface LargestImagePaintData {\n  lcpRequestByNavigationId: Map<string, Types.Events.SyntheticNetworkRequest>;\n}\n\nexport function data(): LargestImagePaintData {\n  return {lcpRequestByNavigationId};\n}\n\nexport function deps(): HandlerName[] {\n  return ['Meta', 'NetworkRequests', 'PageLoadMetrics'];\n}\n"]}