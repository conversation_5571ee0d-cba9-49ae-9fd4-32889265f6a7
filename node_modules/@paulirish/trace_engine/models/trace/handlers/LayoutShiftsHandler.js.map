{"version": 3, "file": "LayoutShiftsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/LayoutShiftsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAE/D,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,eAAe,EAAC,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAC,mBAAmB,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,IAAI,IAAI,sBAAsB,EAAC,MAAM,yBAAyB,CAAC;AAyDvE,4EAA4E;AAC5E,YAAY;AACZ,MAAM,CAAC,MAAM,oBAAoB,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAE1F,8EAA8E;AAC9E,0DAA0D;AAC1D,MAAM,CAAC,MAAM,oBAAoB,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAE1F,8EAA8E;AAC9E,+EAA+E;AAC/E,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,+EAA+E;AAC/E,UAAU;AACV,MAAM,iBAAiB,GAA+B,EAAE,CAAC;AAEzD,+EAA+E;AAC/E,qDAAqD;AACrD,MAAM,wBAAwB,GAA8C,EAAE,CAAC;AAC/E,MAAM,+BAA+B,GAAqD,EAAE,CAAC;AAC7F,MAAM,6BAA6B,GAAmD,EAAE,CAAC;AACzF,MAAM,qCAAqC,GAAmD,EAAE,CAAC;AACjG,MAAM,gBAAgB,GAA8B,EAAE,CAAC;AACvD,MAAM,wBAAwB,GAAsC,EAAE,CAAC;AACvE,MAAM,WAAW,GAAiB,EAAE,CAAC;AAErC,MAAM,cAAc,GAAG,IAAI,GAAG,EAA8B,CAAC;AAE7D,2EAA2E;AAC3E,mFAAmF;AACnF,wEAAwE;AACxE,sBAAsB;AACtB,MAAM,cAAc,GAA4B,EAAE,CAAC;AAEnD,MAAM,gBAAgB,GAA8B,EAAE,CAAC;AAEvD,IAAI,eAAe,GAAG,CAAC,CAAC;AAExB,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;AAErB,MAAM,QAAQ,GAA+C,EAAE,CAAC;AAChE,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAyE,CAAC;AAShH,wDAAwD;AACxD,gDAAgD;AAChD,MAAM,YAAY,GAAkB,EAAE,CAAC;AAEvC,MAAM,UAAU,KAAK;IACnB,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7B,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,+BAA+B,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,6BAA6B,CAAC,MAAM,GAAG,CAAC,CAAC;IACzC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,qCAAqC,CAAC,MAAM,GAAG,CAAC,CAAC;IACjD,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,cAAc,CAAC,KAAK,EAAE,CAAC;IACvB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,eAAe,GAAG,CAAC,CAAC;IACpB,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,WAAW,GAAG,CAAC,CAAC,CAAC;IACjB,sBAAsB,CAAC,KAAK,EAAE,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC;QAC5E,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO;IACT,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,CAAC;QACrD,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,OAAO;IACT,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,mCAAmC,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1D,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,OAAO;IACT,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1D,qCAAqC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACrC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7C,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9C,WAAW,CAAC,IAAI,CAAC;YACf,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;YAC3B,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;YACnB,wBAAwB,EAAE,KAAK;SAChC,CAAC,CAAC;IACL,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3C,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,UAAU,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACtC,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACrC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAwB;IACnD,OAAO;QACL,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;KAC7B,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,WAA0C,EAAE,MAA0B;IAClG,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC;IACzB,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC5E,CAAC;AAED,SAAS,eAAe,CAAC,SAA6B;IACpD,MAAM,IAAI,GAAG,sBAAsB,EAAE,CAAC;IACtC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC3F,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACrF,OAAO,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC;IACzB,CAAC;IACD,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,IAAI,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QAC1G,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACnH,OAAO,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC;IACzB,CAAC;IACD,iBAAiB;IACjB,OAAO,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;AACrC,CAAC;AAED,SAAS,iBAAiB;IACxB,MAAM,EAAC,WAAW,EAAC,GAAG,eAAe,EAAE,CAAC;IACxC,YAAY,CAAC,IAAI,CAAC,EAAC,EAAE,EAAE,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC;IAEnD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,YAAY,CAAC,IAAI,CAAC,EAAC,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAC,CAAC,CAAC;QAC9G,CAAC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACrB,SAAS;YACX,CAAC;YACD,YAAY,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;YACrD,YAAY,CAAC,IAAI,CAAC,EAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAC,CAAC,CAAC;QACzD,CAAC;QACD,YAAY,CAAC,IAAI,CAAC,EAAC,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY;IACnB,cAAc,CAAC,KAAK,EAAE,CAAC;IAEvB,8CAA8C;IAC9C,KAAK,MAAM,WAAW,IAAI,iBAAiB,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC;YAC3C,SAAS;QACX,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxD,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,yFAAyF;IACzF,KAAK,MAAM,kBAAkB,IAAI,wBAAwB,EAAE,CAAC;QAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YAC1C,SAAS;QACX,CAAC;QACD,cAAc,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD,KAAK,MAAM,yBAAyB,IAAI,+BAA+B,EAAE,CAAC;QACxE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YACjD,SAAS;QACX,CAAC;QACD,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,mDAAmD;IACnD,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9C,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,qCAAqC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;IAC1F,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAE7C,+EAA+E;IAC/E,gBAAgB;IAChB,MAAM,yBAAyB,EAAE,CAAC;IAClC,iBAAiB,EAAE,CAAC;IACpB,YAAY,EAAE,CAAC;AACjB,CAAC;AAED,KAAK,UAAU,yBAAyB;IACtC,MAAM,EAAC,oBAAoB,EAAE,WAAW,EAAE,WAAW,EAAC,GAAG,eAAe,EAAE,CAAC;IAC3E,MAAM,WAAW,GAAG,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAChE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO;IACT,CAAC;IACD,IAAI,cAAc,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C,IAAI,aAAa,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,6CAA6C;IAC7C,sFAAsF;IACtF,uFAAuF;IACvF,uFAAuF;IACvF,oFAAoF;IACpF,2DAA2D;IAC3D,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE,CAAC;QACtC,4EAA4E;QAC5E,oCAAoC;QACpC,MAAM,uBAAuB,GAAG,KAAK,CAAC,EAAE,GAAG,cAAc,GAAG,oBAAoB,CAAC;QACjF,MAAM,kCAAkC,GAAG,KAAK,CAAC,EAAE,GAAG,aAAa,GAAG,oBAAoB,CAAC;QAE3F,yFAAyF;QACzF,WAAW;QACX,MAAM,sBAAsB,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QAClH,MAAM,YAAY,GAAG,mBAAmB,KAAK,sBAAsB,IAAI,sBAAsB,KAAK,IAAI,CAAC;QAEvG,qFAAqF;QACrF,mBAAmB;QACnB,IAAI,uBAAuB,IAAI,kCAAkC,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACtG,oFAAoF;YACpF,MAAM,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC;YAElC,6EAA6E;YAC7E,8EAA8E;YAC9E,6BAA6B;YAC7B,MAAM,2BAA2B,GAAG,uBAAuB,CAAC,CAAC,CAAC,cAAc,GAAG,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC;YAE/G,wEAAwE;YACxE,+EAA+E;YAC/E,MAAM,oBAAoB,GAAG,kCAAkC,CAAC,CAAC,CAAC,aAAa,GAAG,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC;YAElH,yEAAyE;YACzE,qBAAqB;YACrB,MAAM,mBAAmB,GAAG,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;YAE7F,wFAAwF;YACxF,MAAM,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,2BAA2B,EAAE,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;YAEhH,2DAA2D;YAC3D,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrD,oBAAoB,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACjG,CAAC;YAED,uEAAuE;YACvE,gEAAgE;YAChE,cAAc;YACd,MAAM,YAAY,GAAG,sBAAsB,KAAK,IAAI,CAAC,CAAC;gBAClD,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAC5B,WAAW,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;YAChE,0EAA0E;YAC1E,0EAA0E;YAC1E,0EAA0E;YAC1E,8CAA8C;YAE9C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,6BAA6B;gBACnC,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,mBAAmB,CAAC,gBAAgB,CAAC;gBACpD,sBAAsB,EAAE,CAAC;gBACzB,YAAY,EAAE;oBACZ,IAAI,EAAE,mBAAmB,CAAC,gBAAgB,CAAC;iBAC5C;gBACD,YAAY;gBACZ,sFAAsF;gBACtF,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;gBAC/B,GAAG,EAAE,EAAE;gBACP,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAG,uCAAuC;aACtE,CAAC,CAAC;YAEH,cAAc,GAAG,gBAAgB,CAAC;QACpC,CAAC;QAED,uEAAuE;QACvE,iFAAiF;QACjF,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,MAAM,kBAAkB,GAAG,sBAAsB,KAAK,IAAI,CAAC,CAAC;YACxD,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,SAAS,CAAC;QAEd,cAAc,CAAC,sBAAsB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,SAAS;QACX,CAAC;QACD,MAAM,KAAK,GACP,OAAO,CAAC,eAAe,CAAC,sBAAsB,CAAC,sBAAsB,CAAoC;YACvG,cAAc,EAAE,KAAK;YACrB,GAAG,KAAK;YACR,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;YAC9C,IAAI,EAAE;gBACJ,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;gBACvB,IAAI,EAAE;oBACJ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI;oBAClB,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,cAAc,CAAC,YAAY,IAAI,SAAS;iBACvD;aACF;YACD,UAAU,EAAE;gBACV,kBAAkB;gBAClB,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,+BAA+B,EAAE,cAAc,CAAC,sBAAsB;gBACtE,+DAA+D;gBAC/D,iEAAiE;gBACjE,6DAA6D;gBAC7D,yDAAyD;gBACzD,iBAAiB,EAAE,EAAC,qBAAqB,EAAE,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAC;aACnE;SACF,CAAC,CAAC;QACP,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,oBAAoB,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAE7D,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC;QACzB,mBAAmB,GAAG,sBAAsB,CAAC;IAC/C,CAAC;IAED,wEAAwE;IACxE,wEAAwE;IACxE,sEAAsE;IACtE,6BAA6B;IAC7B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAClB,oFAAoF;QACpF,mFAAmF;QACnF,kEAAkE;QAClE,IAAI,OAAO,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;YAC9C,MAAM,uBAAuB,GAAG,oBAAoB,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC;YACjF,MAAM,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,oBAAoB,CAAC;YAC5E,MAAM,mBAAmB,GACrB,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC9G,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,CAAC,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;YAChG,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,WAAW,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;YAC9G,oBAAoB,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,eAAe,GAA4B,IAAI,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnC,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACjD,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;YACpB,mEAAmE;YACnE,cAAc;YACd,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,OAAO,CAAC,sBAAsB,CAAC;YAC1F,IAAI,aAAa,GAAG,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;gBAC5D,0BAA0B;gBAC1B,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACtD,CAAC;iBAAM,IACH,aAAa,IAAI,qBAAqB,CAAC,iBAAiB,IAAI,aAAa,GAAG,qBAAqB,CAAC,GAAG,EAAE,CAAC;gBAC1G,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;oBAC3C,gEAAgE;oBAChE,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC5E,OAAO,CAAC,YAAY,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC;gBAClE,CAAC;gBAED,uCAAuC;gBACvC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;YAClE,CAAC;iBAAM,IAAI,aAAa,IAAI,qBAAqB,CAAC,GAAG,EAAE,CAAC;gBACtD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;oBAC9B,yEAAyE;oBACzE,IAAI,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;wBAC1C,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,CAAC;yBAAM,CAAC;wBACN,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9E,CAAC;oBAED,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC3D,CAAC;gBAED,yBAAyB;gBACzB,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACrD,CAAC;YAED,6EAA6E;YAC7E,0EAA0E;YAC1E,0EAA0E;YAC1E,yEAAyE;YACzE,eAAe;YACf,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;gBAC7B,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC5E,CAAC;iBAAM,IAAI,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;gBACjD,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACzF,CAAC;iBAAM,CAAC;gBACN,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC7E,CAAC;YAED,8CAA8C;YAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,oBAAoB,CAAC;YACpD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,YAAY,EAAE,CAAC;gBAChD,YAAY,GAAG,KAAK,CAAC;gBACrB,eAAe,GAAG,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,2CAA2C;QAC3C,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC5C,CAAC;QAED,sDAAsD;QACtD,yCAAyC;QACzC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5G,4GAA4G;QAC5G,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAE3G,IAAI,aAAa,GAAG,eAAe,EAAE,CAAC;YACpC,WAAW,GAAG,QAAQ,CAAC;YACvB,eAAe,GAAG,aAAa,CAAC;QAClC,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,sBAAsB,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC5G,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,QAAQ;QACR,eAAe;QACf,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,+BAA+B;QAC/B,6BAA6B,EAAE,EAAE;QACjC,qCAAqC;QACrC,gBAAgB;QAChB,wBAAwB;QACxB,WAAW;QACX,YAAY;QACZ,4DAA4D;QAC5D,cAAc,EAAE,CAAC,GAAG,cAAc,CAAC;QACnC,sBAAsB,EAAE,IAAI,GAAG,CAAC,sBAAsB,CAAC;QACvD,gBAAgB;KACjB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,iCAAiC,CAAC,KAAa;IAC7D,IAAI,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC;IACrC,IAAI,KAAK,IAAI,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;QACrD,KAAK,GAAG,mBAAmB,CAAC,EAAE,CAAC;IACjC,CAAC;IAED,IAAI,KAAK,IAAI,qBAAqB,CAAC,GAAG,EAAE,CAAC;QACvC,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC;IAClC,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,gCAAgC;AAChC,MAAM,CAAN,IAAY,qBAIX;AAJD,WAAY,qBAAqB;IAC/B,iEAAQ,CAAA;IACR,6FAAuB,CAAA;IACvB,kEAAU,CAAA;AACZ,CAAC,EAJW,qBAAqB,KAArB,qBAAqB,QAIhC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../../core/platform/platform.js';\nimport type * as Protocol from '../../../generated/protocol.js';\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {data as metaHandlerData} from './MetaHandler.js';\nimport {ScoreClassification} from './PageLoadMetricsHandler.js';\nimport {data as screenshotsHandlerData} from './ScreenshotsHandler.js';\nimport type {HandlerName} from './types.js';\n\n// We start with a score of zero and step through all Layout Shift records from\n// all renderers. Each record not only tells us which renderer it is, but also\n// the unweighted and weighted scores. The unweighted score is the score we would\n// get if the renderer were the only one in the viewport. The weighted score, on\n// the other hand, accounts for how much of the viewport that particular render\n// takes up when the shift happened. An ad frame in the corner of the viewport\n// that shifts is considered less disruptive, therefore, than if it were taking\n// up the whole viewport.\n//\n// Next, we step through all the records from all renderers and add the weighted\n// score to a running total across all of the renderers. We create a new \"cluster\"\n// and reset the running total when:\n//\n// 1. We observe a outermost frame navigation, or\n// 2. When there's a gap between records of > 1s, or\n// 3. When there's more than 5 seconds of continuous layout shifting.\n//\n// Note that for it to be Cumulative Layout Shift in the sense described in the\n// documentation we would need to guarantee that we are tracking from navigation\n// to unload. However, we don't make any such guarantees here (since a developer\n// can record and stop when they please), so we support the cluster approach,\n// and we can give them a score, but it is effectively a \"session\" score, a\n// score for the given recording, and almost certainly not the\n// navigation-to-unload CLS score.\n\ninterface LayoutShifts {\n  clusters: readonly Types.Events.SyntheticLayoutShiftCluster[];\n  clustersByNavigationId: Map<Types.Events.NavigationId, Types.Events.SyntheticLayoutShiftCluster[]>;\n  sessionMaxScore: number;\n  // The session window which contains the SessionMaxScore\n  clsWindowID: number;\n  // We use these to calculate root causes for a given LayoutShift\n  // TODO(crbug/41484172): should be readonly\n  prePaintEvents: Types.Events.PrePaint[];\n  paintImageEvents: Types.Events.PaintImage[];\n  layoutInvalidationEvents: readonly Types.Events.LayoutInvalidationTracking[];\n  scheduleStyleInvalidationEvents: readonly Types.Events.ScheduleStyleInvalidationTracking[];\n  styleRecalcInvalidationEvents: readonly Types.Events.StyleRecalcInvalidationTracking[];\n  renderFrameImplCreateChildFrameEvents: readonly Types.Events.RenderFrameImplCreateChildFrame[];\n  domLoadingEvents: readonly Types.Events.DomLoading[];\n  layoutImageUnsizedEvents: readonly Types.Events.LayoutImageUnsized[];\n  remoteFonts: readonly RemoteFont[];\n  scoreRecords: readonly ScoreRecord[];\n  // TODO(crbug/41484172): should be readonly\n  backendNodeIds: Protocol.DOM.BackendNodeId[];\n}\n\ninterface RemoteFont {\n  display: string;\n  url?: string;\n  name?: string;\n  beginRemoteFontLoadEvent: Types.Events.BeginRemoteFontLoad;\n}\n\n// This represents the maximum #time we will allow a cluster to go before we\n// reset it.\nexport const MAX_CLUSTER_DURATION = Helpers.Timing.milliToMicro(Types.Timing.Milli(5000));\n\n// This represents the maximum #time we will allow between layout shift events\n// before considering it to be the start of a new cluster.\nexport const MAX_SHIFT_TIME_DELTA = Helpers.Timing.milliToMicro(Types.Timing.Milli(1000));\n\n// Layout shifts are reported globally to the developer, irrespective of which\n// frame they originated in. However, each process does have its own individual\n// CLS score, so we need to segment by process. This means Layout Shifts from\n// sites with one process (no subframes, or subframes from the same origin)\n// will be reported together. In the case of multiple renderers (frames across\n// different origins), we offer the developer the ability to switch renderer in\n// the UI.\nconst layoutShiftEvents: Types.Events.LayoutShift[] = [];\n\n// These events denote potential node resizings. We store them to link captured\n// layout shifts to the resizing of unsized elements.\nconst layoutInvalidationEvents: Types.Events.LayoutInvalidationTracking[] = [];\nconst scheduleStyleInvalidationEvents: Types.Events.ScheduleStyleInvalidationTracking[] = [];\nconst styleRecalcInvalidationEvents: Types.Events.StyleRecalcInvalidationTracking[] = [];\nconst renderFrameImplCreateChildFrameEvents: Types.Events.RenderFrameImplCreateChildFrame[] = [];\nconst domLoadingEvents: Types.Events.DomLoading[] = [];\nconst layoutImageUnsizedEvents: Types.Events.LayoutImageUnsized[] = [];\nconst remoteFonts: RemoteFont[] = [];\n\nconst backendNodeIds = new Set<Protocol.DOM.BackendNodeId>();\n\n// Layout shifts happen during PrePaint as part of the rendering lifecycle.\n// We determine if a LayoutInvalidation event is a potential root cause of a layout\n// shift if the next PrePaint after the LayoutInvalidation is the parent\n// node of such shift.\nconst prePaintEvents: Types.Events.PrePaint[] = [];\n\nconst paintImageEvents: Types.Events.PaintImage[] = [];\n\nlet sessionMaxScore = 0;\n\nlet clsWindowID = -1;\n\nconst clusters: Types.Events.SyntheticLayoutShiftCluster[] = [];\nconst clustersByNavigationId = new Map<Types.Events.NavigationId, Types.Events.SyntheticLayoutShiftCluster[]>();\n\n// Represents a point in time in which a  LS score change\n// was recorded.\ninterface ScoreRecord {\n  ts: number;\n  score: number;\n}\n\n// The complete timeline of LS score changes in a trace.\n// Includes drops to 0 when session windows end.\nconst scoreRecords: ScoreRecord[] = [];\n\nexport function reset(): void {\n  layoutShiftEvents.length = 0;\n  layoutInvalidationEvents.length = 0;\n  scheduleStyleInvalidationEvents.length = 0;\n  styleRecalcInvalidationEvents.length = 0;\n  prePaintEvents.length = 0;\n  paintImageEvents.length = 0;\n  renderFrameImplCreateChildFrameEvents.length = 0;\n  layoutImageUnsizedEvents.length = 0;\n  domLoadingEvents.length = 0;\n  remoteFonts.length = 0;\n  backendNodeIds.clear();\n  clusters.length = 0;\n  sessionMaxScore = 0;\n  scoreRecords.length = 0;\n  clsWindowID = -1;\n  clustersByNavigationId.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isLayoutShift(event) && !event.args.data?.had_recent_input) {\n    layoutShiftEvents.push(event);\n    return;\n  }\n  if (Types.Events.isLayoutInvalidationTracking(event)) {\n    layoutInvalidationEvents.push(event);\n    return;\n  }\n  if (Types.Events.isScheduleStyleInvalidationTracking(event)) {\n    scheduleStyleInvalidationEvents.push(event);\n  }\n  if (Types.Events.isStyleRecalcInvalidationTracking(event)) {\n    styleRecalcInvalidationEvents.push(event);\n  }\n  if (Types.Events.isPrePaint(event)) {\n    prePaintEvents.push(event);\n    return;\n  }\n  if (Types.Events.isRenderFrameImplCreateChildFrame(event)) {\n    renderFrameImplCreateChildFrameEvents.push(event);\n  }\n  if (Types.Events.isDomLoading(event)) {\n    domLoadingEvents.push(event);\n  }\n  if (Types.Events.isLayoutImageUnsized(event)) {\n    layoutImageUnsizedEvents.push(event);\n  }\n  if (Types.Events.isBeginRemoteFontLoad(event)) {\n    remoteFonts.push({\n      display: event.args.display,\n      url: event.args.url,\n      beginRemoteFontLoadEvent: event,\n    });\n  }\n  if (Types.Events.isRemoteFontLoaded(event)) {\n    for (const remoteFont of remoteFonts) {\n      if (remoteFont.url === event.args.url) {\n        remoteFont.name = event.args.name;\n      }\n    }\n  }\n  if (Types.Events.isPaintImage(event)) {\n    paintImageEvents.push(event);\n  }\n}\n\nfunction traceWindowFromTime(time: Types.Timing.Micro): Types.Timing.TraceWindowMicro {\n  return {\n    min: time,\n    max: time,\n    range: Types.Timing.Micro(0),\n  };\n}\n\nfunction updateTraceWindowMax(traceWindow: Types.Timing.TraceWindowMicro, newMax: Types.Timing.Micro): void {\n  traceWindow.max = newMax;\n  traceWindow.range = Types.Timing.Micro(traceWindow.max - traceWindow.min);\n}\n\nfunction findScreenshots(timestamp: Types.Timing.Micro): Types.Events.LayoutShiftParsedData['screenshots'] {\n  const data = screenshotsHandlerData();\n  if (data.screenshots) {\n    const before = Helpers.Trace.findPreviousEventBeforeTimestamp(data.screenshots, timestamp);\n    const after = before ? data.screenshots[data.screenshots.indexOf(before) + 1] : null;\n    return {before, after};\n  }\n  if (data.legacySyntheticScreenshots) {\n    const before = Helpers.Trace.findPreviousEventBeforeTimestamp(data.legacySyntheticScreenshots, timestamp);\n    const after = before ? data.legacySyntheticScreenshots[data.legacySyntheticScreenshots.indexOf(before) + 1] : null;\n    return {before, after};\n  }\n  // No screenshots\n  return {before: null, after: null};\n}\n\nfunction buildScoreRecords(): void {\n  const {traceBounds} = metaHandlerData();\n  scoreRecords.push({ts: traceBounds.min, score: 0});\n\n  for (const cluster of clusters) {\n    let clusterScore = 0;\n    if (cluster.events[0].args.data) {\n      scoreRecords.push({ts: cluster.clusterWindow.min, score: cluster.events[0].args.data.weighted_score_delta});\n    }\n    for (let i = 0; i < cluster.events.length; i++) {\n      const event = cluster.events[i];\n      if (!event.args.data) {\n        continue;\n      }\n      clusterScore += event.args.data.weighted_score_delta;\n      scoreRecords.push({ts: event.ts, score: clusterScore});\n    }\n    scoreRecords.push({ts: cluster.clusterWindow.max, score: 0});\n  }\n}\n\n/**\n * Collects backend node ids coming from LayoutShift and LayoutInvalidation\n * events.\n */\nfunction collectNodes(): void {\n  backendNodeIds.clear();\n\n  // Collect the node ids present in the shifts.\n  for (const layoutShift of layoutShiftEvents) {\n    if (!layoutShift.args.data?.impacted_nodes) {\n      continue;\n    }\n    for (const node of layoutShift.args.data.impacted_nodes) {\n      backendNodeIds.add(node.node_id);\n    }\n  }\n\n  // Collect the node ids present in LayoutInvalidation & scheduleStyleInvalidation events.\n  for (const layoutInvalidation of layoutInvalidationEvents) {\n    if (!layoutInvalidation.args.data?.nodeId) {\n      continue;\n    }\n    backendNodeIds.add(layoutInvalidation.args.data.nodeId);\n  }\n  for (const scheduleStyleInvalidation of scheduleStyleInvalidationEvents) {\n    if (!scheduleStyleInvalidation.args.data?.nodeId) {\n      continue;\n    }\n    backendNodeIds.add(scheduleStyleInvalidation.args.data.nodeId);\n  }\n}\n\nexport async function finalize(): Promise<void> {\n  // Ensure the events are sorted by #time ascending.\n  layoutShiftEvents.sort((a, b) => a.ts - b.ts);\n  prePaintEvents.sort((a, b) => a.ts - b.ts);\n  layoutInvalidationEvents.sort((a, b) => a.ts - b.ts);\n  renderFrameImplCreateChildFrameEvents.sort((a, b) => a.ts - b.ts);\n  domLoadingEvents.sort((a, b) => a.ts - b.ts);\n  layoutImageUnsizedEvents.sort((a, b) => a.ts - b.ts);\n  remoteFonts.sort((a, b) => a.beginRemoteFontLoadEvent.ts - b.beginRemoteFontLoadEvent.ts);\n  paintImageEvents.sort((a, b) => a.ts - b.ts);\n\n  // Each function transforms the data used by the next, as such the invoke order\n  // is important.\n  await buildLayoutShiftsClusters();\n  buildScoreRecords();\n  collectNodes();\n}\n\nasync function buildLayoutShiftsClusters(): Promise<void> {\n  const {navigationsByFrameId, mainFrameId, traceBounds} = metaHandlerData();\n  const navigations = navigationsByFrameId.get(mainFrameId) || [];\n  if (layoutShiftEvents.length === 0) {\n    return;\n  }\n  let firstShiftTime = layoutShiftEvents[0].ts;\n  let lastShiftTime = layoutShiftEvents[0].ts;\n  let lastShiftNavigation = null;\n  // Now step through each and create clusters.\n  // A cluster is equivalent to a session window (see https://web.dev/cls/#what-is-cls).\n  // To make the line chart clear, we explicitly demark the limits of each session window\n  // by starting the cumulative score of the window at the time of the first layout shift\n  // and ending it (dropping the line back to 0) when the window ends according to the\n  // thresholds (MAX_CLUSTER_DURATION, MAX_SHIFT_TIME_DELTA).\n  for (const event of layoutShiftEvents) {\n    // First detect if either the cluster duration or the #time between this and\n    // the last shift has been exceeded.\n    const clusterDurationExceeded = event.ts - firstShiftTime > MAX_CLUSTER_DURATION;\n    const maxTimeDeltaSinceLastShiftExceeded = event.ts - lastShiftTime > MAX_SHIFT_TIME_DELTA;\n\n    // Next take a look at navigations. If between this and the last shift we have navigated,\n    // note it.\n    const currentShiftNavigation = Platform.ArrayUtilities.nearestIndexFromEnd(navigations, nav => nav.ts < event.ts);\n    const hasNavigated = lastShiftNavigation !== currentShiftNavigation && currentShiftNavigation !== null;\n\n    // If any of the above criteria are met or if we don't have any cluster yet we should\n    // start a new one.\n    if (clusterDurationExceeded || maxTimeDeltaSinceLastShiftExceeded || hasNavigated || !clusters.length) {\n      // The cluster starts #time should be the timestamp of the first layout shift in it.\n      const clusterStartTime = event.ts;\n\n      // If the last session window ended because the max delta time between shifts\n      // was exceeded set the endtime to MAX_SHIFT_TIME_DELTA microseconds after the\n      // last shift in the session.\n      const endTimeByMaxSessionDuration = clusterDurationExceeded ? firstShiftTime + MAX_CLUSTER_DURATION : Infinity;\n\n      // If the last session window ended because the max session duration was\n      // surpassed, set the endtime so that the window length = MAX_CLUSTER_DURATION;\n      const endTimeByMaxShiftGap = maxTimeDeltaSinceLastShiftExceeded ? lastShiftTime + MAX_SHIFT_TIME_DELTA : Infinity;\n\n      // If there was a navigation during the last window, close it at the time\n      // of the navigation.\n      const endTimeByNavigation = hasNavigated ? navigations[currentShiftNavigation].ts : Infinity;\n\n      // End the previous cluster at the time of the first of the criteria above that was met.\n      const previousClusterEndTime = Math.min(endTimeByMaxSessionDuration, endTimeByMaxShiftGap, endTimeByNavigation);\n\n      // If there is an existing cluster update its closing time.\n      if (clusters.length > 0) {\n        const currentCluster = clusters[clusters.length - 1];\n        updateTraceWindowMax(currentCluster.clusterWindow, Types.Timing.Micro(previousClusterEndTime));\n      }\n\n      // If this cluster happened after a navigation, set the navigationId to\n      // the current navigation. This lets us easily group clusters by\n      // navigation.\n      const navigationId = currentShiftNavigation === null ?\n          Types.Events.NO_NAVIGATION :\n          navigations[currentShiftNavigation].args.data?.navigationId;\n      // TODO: `navigationId` is `string | undefined`, but the undefined portion\n      // comes from `data.navigationId`. I don't think that is possible for this\n      // event type. Can we make this typing stronger? In the meantime, we allow\n      // `navigationId` to include undefined values.\n\n      clusters.push({\n        name: 'SyntheticLayoutShiftCluster',\n        events: [],\n        clusterWindow: traceWindowFromTime(clusterStartTime),\n        clusterCumulativeScore: 0,\n        scoreWindows: {\n          good: traceWindowFromTime(clusterStartTime),\n        },\n        navigationId,\n        // Set default Event so that this event is treated accordingly for the track appender.\n        ts: event.ts,\n        pid: event.pid,\n        tid: event.tid,\n        ph: Types.Events.Phase.COMPLETE,\n        cat: '',\n        dur: Types.Timing.Micro(-1),  // This `cluster.dur` is updated below.\n      });\n\n      firstShiftTime = clusterStartTime;\n    }\n\n    // Given the above we should have a cluster available, so pick the most\n    // recent one and append the shift, bump its score and window values accordingly.\n    const currentCluster = clusters[clusters.length - 1];\n    const timeFromNavigation = currentShiftNavigation !== null ?\n        Types.Timing.Micro(event.ts - navigations[currentShiftNavigation].ts) :\n        undefined;\n\n    currentCluster.clusterCumulativeScore += event.args.data ? event.args.data.weighted_score_delta : 0;\n    if (!event.args.data) {\n      continue;\n    }\n    const shift =\n        Helpers.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent<Types.Events.SyntheticLayoutShift>({\n          rawSourceEvent: event,\n          ...event,\n          name: Types.Events.Name.SYNTHETIC_LAYOUT_SHIFT,\n          args: {\n            frame: event.args.frame,\n            data: {\n              ...event.args.data,\n              rawEvent: event,\n              navigationId: currentCluster.navigationId ?? undefined,\n            },\n          },\n          parsedData: {\n            timeFromNavigation,\n            screenshots: findScreenshots(event.ts),\n            cumulativeWeightedScoreInWindow: currentCluster.clusterCumulativeScore,\n            // The score of the session window is temporarily set to 0 just\n            // to initialize it. Since we need to get the score of all shifts\n            // in the session window to determine its value, its definite\n            // value is set when stepping through the built clusters.\n            sessionWindowData: {cumulativeWindowScore: 0, id: clusters.length},\n          },\n        });\n    currentCluster.events.push(shift);\n    updateTraceWindowMax(currentCluster.clusterWindow, event.ts);\n\n    lastShiftTime = event.ts;\n    lastShiftNavigation = currentShiftNavigation;\n  }\n\n  // Now step through each cluster and set up the times at which the value\n  // goes from Good, to needs improvement, to Bad. Note that if there is a\n  // large jump we may go from Good to Bad without ever creating a Needs\n  // Improvement window at all.\n  for (const cluster of clusters) {\n    let weightedScore = 0;\n    let windowID = -1;\n    // If this is the last cluster update its window. The cluster duration is determined\n    // by the minimum between: time to next navigation, trace end time, time to maximum\n    // cluster duration and time to maximum gap between layout shifts.\n    if (cluster === clusters[clusters.length - 1]) {\n      const clusterEndByMaxDuration = MAX_CLUSTER_DURATION + cluster.clusterWindow.min;\n      const clusterEndByMaxGap = cluster.clusterWindow.max + MAX_SHIFT_TIME_DELTA;\n      const nextNavigationIndex =\n          Platform.ArrayUtilities.nearestIndexFromBeginning(navigations, nav => nav.ts > cluster.clusterWindow.max);\n      const nextNavigationTime = nextNavigationIndex ? navigations[nextNavigationIndex].ts : Infinity;\n      const clusterEnd = Math.min(clusterEndByMaxDuration, clusterEndByMaxGap, traceBounds.max, nextNavigationTime);\n      updateTraceWindowMax(cluster.clusterWindow, Types.Timing.Micro(clusterEnd));\n    }\n\n    let largestScore = 0;\n    let worstShiftEvent: Types.Events.Event|null = null;\n\n    for (const shift of cluster.events) {\n      weightedScore += shift.args.data ? shift.args.data.weighted_score_delta : 0;\n      windowID = shift.parsedData.sessionWindowData.id;\n      const ts = shift.ts;\n      // Update the the CLS score of this shift's session window now that\n      // we have it.\n      shift.parsedData.sessionWindowData.cumulativeWindowScore = cluster.clusterCumulativeScore;\n      if (weightedScore < LayoutShiftsThreshold.NEEDS_IMPROVEMENT) {\n        // Expand the Good window.\n        updateTraceWindowMax(cluster.scoreWindows.good, ts);\n      } else if (\n          weightedScore >= LayoutShiftsThreshold.NEEDS_IMPROVEMENT && weightedScore < LayoutShiftsThreshold.BAD) {\n        if (!cluster.scoreWindows.needsImprovement) {\n          // Close the Good window, and open the needs improvement window.\n          updateTraceWindowMax(cluster.scoreWindows.good, Types.Timing.Micro(ts - 1));\n          cluster.scoreWindows.needsImprovement = traceWindowFromTime(ts);\n        }\n\n        // Expand the needs improvement window.\n        updateTraceWindowMax(cluster.scoreWindows.needsImprovement, ts);\n      } else if (weightedScore >= LayoutShiftsThreshold.BAD) {\n        if (!cluster.scoreWindows.bad) {\n          // We may jump from Good to Bad here, so update whichever window is open.\n          if (cluster.scoreWindows.needsImprovement) {\n            updateTraceWindowMax(cluster.scoreWindows.needsImprovement, Types.Timing.Micro(ts - 1));\n          } else {\n            updateTraceWindowMax(cluster.scoreWindows.good, Types.Timing.Micro(ts - 1));\n          }\n\n          cluster.scoreWindows.bad = traceWindowFromTime(shift.ts);\n        }\n\n        // Expand the Bad window.\n        updateTraceWindowMax(cluster.scoreWindows.bad, ts);\n      }\n\n      // At this point the windows are set by the timestamps of the events, but the\n      // next cluster begins at the timestamp of its first event. As such we now\n      // need to expand the score window to the end of the cluster, and we do so\n      // by using the Bad widow if it's there, or the NI window, or finally the\n      // Good window.\n      if (cluster.scoreWindows.bad) {\n        updateTraceWindowMax(cluster.scoreWindows.bad, cluster.clusterWindow.max);\n      } else if (cluster.scoreWindows.needsImprovement) {\n        updateTraceWindowMax(cluster.scoreWindows.needsImprovement, cluster.clusterWindow.max);\n      } else {\n        updateTraceWindowMax(cluster.scoreWindows.good, cluster.clusterWindow.max);\n      }\n\n      // Find the worst layout shift of the cluster.\n      const score = shift.args.data?.weighted_score_delta;\n      if (score !== undefined && score > largestScore) {\n        largestScore = score;\n        worstShiftEvent = shift;\n      }\n    }\n    // Update the cluster's worst layout shift.\n    if (worstShiftEvent) {\n      cluster.worstShiftEvent = worstShiftEvent;\n    }\n\n    // layout shifts are already sorted by time ascending.\n    // Capture the time range of the cluster.\n    cluster.ts = cluster.events[0].ts;\n    const lastShiftTimings = Helpers.Timing.eventTimingsMicroSeconds(cluster.events[cluster.events.length - 1]);\n    // Add MAX_SHIFT_TIME_DELTA, the section gap after the last layout shift. This marks the end of the cluster.\n    cluster.dur = Types.Timing.Micro((lastShiftTimings.endTime - cluster.events[0].ts) + MAX_SHIFT_TIME_DELTA);\n\n    if (weightedScore > sessionMaxScore) {\n      clsWindowID = windowID;\n      sessionMaxScore = weightedScore;\n    }\n\n    if (cluster.navigationId) {\n      const clustersForId = Platform.MapUtilities.getWithDefault(clustersByNavigationId, cluster.navigationId, () => {\n        return [];\n      });\n      clustersForId.push(cluster);\n    }\n  }\n}\n\nexport function data(): LayoutShifts {\n  return {\n    clusters,\n    sessionMaxScore,\n    clsWindowID,\n    prePaintEvents,\n    layoutInvalidationEvents,\n    scheduleStyleInvalidationEvents,\n    styleRecalcInvalidationEvents: [],\n    renderFrameImplCreateChildFrameEvents,\n    domLoadingEvents,\n    layoutImageUnsizedEvents,\n    remoteFonts,\n    scoreRecords,\n    // TODO(crbug/41484172): change the type so no need to clone\n    backendNodeIds: [...backendNodeIds],\n    clustersByNavigationId: new Map(clustersByNavigationId),\n    paintImageEvents,\n  };\n}\n\nexport function deps(): HandlerName[] {\n  return ['Screenshots', 'Meta'];\n}\n\nexport function scoreClassificationForLayoutShift(score: number): ScoreClassification {\n  let state = ScoreClassification.GOOD;\n  if (score >= LayoutShiftsThreshold.NEEDS_IMPROVEMENT) {\n    state = ScoreClassification.OK;\n  }\n\n  if (score >= LayoutShiftsThreshold.BAD) {\n    state = ScoreClassification.BAD;\n  }\n\n  return state;\n}\n\n// Based on https://web.dev/cls/\nexport enum LayoutShiftsThreshold {\n  GOOD = 0,\n  NEEDS_IMPROVEMENT = 0.1,\n  BAD = 0.25,\n}\n"]}