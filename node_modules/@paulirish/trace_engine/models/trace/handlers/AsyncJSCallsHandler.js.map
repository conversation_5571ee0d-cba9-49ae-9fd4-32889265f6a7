{"version": 3, "file": "AsyncJSCallsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/AsyncJSCallsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAC7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAE/D,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAC3D,OAAO,EAAC,IAAI,IAAI,mBAAmB,EAAC,MAAM,sBAAsB,CAAC;AAEjE,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAA4C,CAAC;AAEtF,MAAM,2BAA2B,GAC7B,IAAI,GAAG,EAA8E,CAAC;AAC1F,MAAM,oBAAoB,GACtB,IAAI,GAAG,EAAwF,CAAC;AAEpG,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAyE,CAAC;AAElH,MAAM,UAAU,KAAK;IACnB,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAClC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,2BAA2B,CAAC,KAAK,EAAE,CAAC;IACpC,wBAAwB,CAAC,KAAK,EAAE,CAAC;AACnC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,CAAqB;AACjD,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,MAAM,EAAC,KAAK,EAAC,GAAG,gBAAgB,EAAE,CAAC;IACnC,MAAM,EAAC,WAAW,EAAC,GAAG,mBAAmB,EAAE,CAAC;IAC5C,2BAA2B;IAC3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,uBAAuB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7B,SAAS;QACX,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,EAAE,CAAC;YACjE,+DAA+D;YAC/D,0DAA0D;YAC1D,eAAe;YACf,gCAAgC;YAChC,EAAE;YACF,6EAA6E;YAC7E,oDAAoD;YACpD,wDAAwD;YACxD,EAAE;YACF,sBAAsB;YACtB,EAAE;YACF,sEAAsE;YACtE,EAAE;YACF,qEAAqE;YACrE,mEAAmE;YACnE,kEAAkE;YAClE,2DAA2D;YAC3D,EAAE;YACF,iEAAiE;YACjE,oEAAoE;YACpE,kDAAkD;YAClD,uBAAuB,GAAG,2BAA2B,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrF,CAAC;QACD,IAAI,CAAC,uBAAuB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,EAAE,CAAC;YACpG,SAAS;QACX,CAAC;QACD,MAAM,QAAQ,GAAG,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE,CAAC;YACxE,iCAAiC;YACjC,SAAS;QACX,CAAC;QACD,8DAA8D;QAC9D,2BAA2B,CAAC,GAAG,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC;QAEvE,sCAAsC;QACtC,MAAM,WAAW,GAAG,qBAAqB,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;QAEhF,6EAA6E;QAC7E,MAAM,eAAe,GAAG,oCAAoC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAExF,iFAAiF;QACjF,2EAA2E;QAC3E,wBAAwB,CAAC,GAAG,CACxB,eAAe,IAAI,YAAY,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,IAAI,uBAAuB,EAAC,CAAC,CAAC;QACpG,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,EAAE,CAAC;YACrC,kDAAkD;YAClD,SAAS;QACX,CAAC;QACD,sCAAsC;QACtC,wCAAwC;QACxC,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,yBAAyB,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3G,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAElC,sCAAsC;QACtC,kEAAkE;QAClE,gCAAgC;QAChC,MAAM,qBAAqB,GAAG,+BAA+B,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACzF,KAAK,MAAM,IAAI,IAAI,qBAAqB,EAAE,CAAC;YACzC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;AACH,CAAC;AACD;;;;GAIG;AACH,SAAS,qBAAqB,CAC1B,kBAA2D,EAC3D,WAAwE;IAC1E,IAAI,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAC;IACvD,OAAO,IAAI,EAAE,CAAC;QACZ,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACvF,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AACD;;;;;GAKG;AACH,SAAS,4BAA4B,CAAC,KAAyB;IAC7D,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnE,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACvF,OAAO,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACpG,CAAC;AAED;;;GAGG;AACH,SAAS,oCAAoC,CACzC,YAA+C,EAC/C,WAAwE;IAC1E,+DAA+D;IAC/D,mEAAmE;IACnE,OAAO,0BAA0B,CACtB,YAAY,EAAE,WAAW,EAAE,4BAA4B,EAAE,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC;SACnG,EAAE,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAS,+BAA+B,CACpC,YAA+C,EAC/C,WAAwE;IAC1E,+DAA+D;IAC/D,mEAAmE;IACnE,OAAO,0BAA0B,CAC7B,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAClG,CAAC;AAED;;;;GAIG;AACH,SAAS,0BAA0B,CAC/B,IAAwB,EAAE,WAAwE,EAClG,eAA0D,EAC1D,eAAuD;IACzD,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5C,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,MAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;iBAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,IAAI;IAwBlB,OAAO;QACL,yBAAyB;QACzB,oBAAoB;QACpB,wBAAwB;KACzB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright 2024 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\nimport * as Platform from '../../../core/platform/platform.js';\nimport type * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {data as flowsHandlerData} from './FlowsHandler.js';\nimport {data as rendererHandlerData} from './RendererHandler.js';\n\nconst schedulerToRunEntryPoints = new Map<Types.Events.Event, Types.Events.Event[]>();\n\nconst taskScheduleForTaskRunEvent =\n    new Map<Types.Events.DebuggerAsyncTaskRun, Types.Events.DebuggerAsyncTaskScheduled>();\nconst asyncCallToScheduler =\n    new Map<Types.Events.SyntheticProfileCall, {taskName: string, scheduler: Types.Events.Event}>();\n\nconst runEntryPointToScheduler = new Map<Types.Events.Event, {taskName: string, scheduler: Types.Events.Event}>();\n\nexport function reset(): void {\n  schedulerToRunEntryPoints.clear();\n  asyncCallToScheduler.clear();\n  taskScheduleForTaskRunEvent.clear();\n  runEntryPointToScheduler.clear();\n}\n\nexport function handleEvent(_: Types.Events.Event): void {\n}\n\nexport async function finalize(): Promise<void> {\n  const {flows} = flowsHandlerData();\n  const {entryToNode} = rendererHandlerData();\n  // Process async task flows\n  for (const flow of flows) {\n    let maybeAsyncTaskScheduled = flow.at(0);\n    if (!maybeAsyncTaskScheduled) {\n      continue;\n    }\n    if (Types.Events.isDebuggerAsyncTaskRun(maybeAsyncTaskScheduled)) {\n      // Sometimes a AsyncTaskRun event run can incorrectly appear as\n      // initiated by another AsyncTaskRun from Perfetto's flows\n      // perspective.\n      // For example, in this snippet:\n      //\n      // const myTask = console.createTask('hola'); // creates an AsyncTaskSchedule\n      // myTask.run(something); // creates an AsyncTaskRun\n      // myTask.run(somethingElse); // creates an AsyncTaskRun\n      //\n      // or also in this one\n      //\n      // setInterval(something); // creates multiple connected AsyncTaskRun.\n      //\n      // Because the flow id is created based on the task's memory address,\n      // the three events will end up belonging to the same flow (even if\n      // in the frontend we receive it as pairs), and elements in a flow\n      // are connected to their immediately consecutive neighbor.\n      //\n      // To ensure we use the right Schedule event, if the \"initiating\"\n      // portion of the flow is a Run event, we look for any corresponding\n      // Schedule event that we might have found before.\n      maybeAsyncTaskScheduled = taskScheduleForTaskRunEvent.get(maybeAsyncTaskScheduled);\n    }\n    if (!maybeAsyncTaskScheduled || !Types.Events.isDebuggerAsyncTaskScheduled(maybeAsyncTaskScheduled)) {\n      continue;\n    }\n    const taskName = maybeAsyncTaskScheduled.args.taskName;\n    const asyncTaskRun = flow.at(1);\n    if (!asyncTaskRun || !Types.Events.isDebuggerAsyncTaskRun(asyncTaskRun)) {\n      // Unexpected flow shape, ignore.\n      continue;\n    }\n    // Cache the Schedule event for this Run for future reference.\n    taskScheduleForTaskRunEvent.set(asyncTaskRun, maybeAsyncTaskScheduled);\n\n    // Get the JS call scheduled the task.\n    const asyncCaller = findNearestJSAncestor(maybeAsyncTaskScheduled, entryToNode);\n\n    // Get the trace entrypoint for the scheduled task (e.g. FunctionCall, etc.).\n    const asyncEntryPoint = findFirstJsInvocationForAsyncTaskRun(asyncTaskRun, entryToNode);\n\n    // Store the async relationship between traces to be shown with initiator arrows.\n    // Default to the AsyncTask events in case the JS entrypoints aren't found.\n    runEntryPointToScheduler.set(\n        asyncEntryPoint || asyncTaskRun, {taskName, scheduler: asyncCaller || maybeAsyncTaskScheduled});\n    if (!asyncCaller || !asyncEntryPoint) {\n      // Unexpected async call trace data shape, ignore.\n      continue;\n    }\n    // Set scheduler -> scheduled mapping.\n    // The scheduled being the JS entrypoint\n    const entryPoints = Platform.MapUtilities.getWithDefault(schedulerToRunEntryPoints, asyncCaller, () => []);\n    entryPoints.push(asyncEntryPoint);\n\n    // Set scheduled -> scheduler mapping.\n    // The scheduled being the JS calls (instead of the entrypoints as\n    // above, for usage ergonomics).\n    const scheduledProfileCalls = findFirstJSCallsForAsyncTaskRun(asyncTaskRun, entryToNode);\n    for (const call of scheduledProfileCalls) {\n      asyncCallToScheduler.set(call, {taskName, scheduler: asyncCaller});\n    }\n  }\n}\n/**\n * Given a DebuggerAsyncTaskScheduled event, returns its closest\n * ProfileCall or JS invocation ancestor, which represents the JS call\n * that scheduled the async task.\n */\nfunction findNearestJSAncestor(\n    asyncTaskScheduled: Types.Events.DebuggerAsyncTaskScheduled,\n    entryToNode: Map<Types.Events.Event, Helpers.TreeHelpers.TraceEntryNode>): Types.Events.Event|null {\n  let node = entryToNode.get(asyncTaskScheduled)?.parent;\n  while (node) {\n    if (Types.Events.isProfileCall(node.entry) || acceptJSInvocationsPredicate(node.entry)) {\n      return node.entry;\n    }\n    node = node.parent;\n  }\n  return null;\n}\n/**\n * Entrypoints to JS execution in the timeline. We ignore those starting\n * with 'v8' because they aren't shown in the timeline, and ultimately\n * this function's output results in \"initiated\" events, so ideally this\n * returns events that end up in the flame chart.\n */\nfunction acceptJSInvocationsPredicate(event: Types.Events.Event): event is Types.Events.Event {\n  const eventIsConsoleRunTask = Types.Events.isConsoleRunTask(event);\n  const eventIsV8EntryPoint = event.name.startsWith('v8') || event.name.startsWith('V8');\n  return Types.Events.isJSInvocationEvent(event) && (eventIsConsoleRunTask || !eventIsV8EntryPoint);\n}\n\n/**\n * Given a DebuggerAsyncTaskRun event, returns its closest JS entry\n * point descendant, which contains the task being scheduled.\n */\nfunction findFirstJsInvocationForAsyncTaskRun(\n    asyncTaskRun: Types.Events.DebuggerAsyncTaskRun,\n    entryToNode: Map<Types.Events.Event, Helpers.TreeHelpers.TraceEntryNode>): Types.Events.Event|undefined {\n  // Ignore descendants of other DebuggerAsyncTaskRuns since they\n  // are part of another async task and have to be handled separately\n  return findFirstDescendantsOfType(\n             asyncTaskRun, entryToNode, acceptJSInvocationsPredicate, Types.Events.isDebuggerAsyncTaskRun)\n      .at(0);\n}\n\n/**\n * Given an async task run event, returns the top level call frames\n * (profile calls) directly called by the async task. This implies that\n * any profile calls under another async task run event are ignored.\n * These profile calls represent the JS task being scheduled, AKA\n * the other part of the async stack.\n *\n * For example, here the profile calls \"js 1\", \"js 2\" and \"js 4\" would\n * be returned:\n *\n * |------------------Async Task Run------------------|\n * |--FunctionCall--|    |--FunctionCall--|\n * |-js 1-||-js 2-|        |-js 4-|\n * |-js 3-|\n *\n * But here, only \"js 1\" and \"js 2\" would be returned:\n *\n * |------------------Async Task Run------------------|\n * |--FunctionCall--|    |------------------------|\n * |-js 1-||-js 2-|       |---Async Task Run--|\n * |-js 3-|                |--FunctionCall--|\n *                          |-js 4-|\n */\nfunction findFirstJSCallsForAsyncTaskRun(\n    asyncTaskRun: Types.Events.DebuggerAsyncTaskRun,\n    entryToNode: Map<Types.Events.Event, Helpers.TreeHelpers.TraceEntryNode>): Types.Events.SyntheticProfileCall[] {\n  // Ignore descendants of other DebuggerAsyncTaskRuns since they\n  // are part of another async task and have to be handled separately\n  return findFirstDescendantsOfType(\n      asyncTaskRun, entryToNode, Types.Events.isProfileCall, Types.Events.isDebuggerAsyncTaskRun);\n}\n\n/**\n * Given a root event returns all the first descendants that meet a\n * predicate condition (predicateAccept) while ignoring subtrees whose\n * top event meets an ignore condition (predicateIgnore).\n */\nfunction findFirstDescendantsOfType<T extends Types.Events.Event>(\n    root: Types.Events.Event, entryToNode: Map<Types.Events.Event, Helpers.TreeHelpers.TraceEntryNode>,\n    predicateAccept: (event: Types.Events.Event) => event is T,\n    predicateIgnore: (event: Types.Events.Event) => boolean): T[] {\n  const node = entryToNode.get(root);\n  if (!node) {\n    return [];\n  }\n  const childrenGroups = [[...node.children]];\n  const firstDescendants = [];\n  for (let i = 0; i < childrenGroups.length; i++) {\n    const siblings = childrenGroups[i];\n    for (let j = 0; j < siblings.length; j++) {\n      const node = siblings[j];\n      if (predicateAccept(node.entry)) {\n        firstDescendants.push(node.entry);\n      } else if (!predicateIgnore(node.entry)) {\n        childrenGroups.push([...node.children]);\n      }\n    }\n  }\n  return firstDescendants;\n}\n\nexport function data(): {\n  // Given a profile call, returns the JS entrypoint it scheduled (if any).\n  // For example, given a setTimeout call, returns the JS entry point\n  // trace event for the timeout callback run event (usually a\n  // FunctionCall event).\n  schedulerToRunEntryPoints: typeof schedulerToRunEntryPoints,\n  // Given a profile call, returns the profile call that scheduled it.\n  // For example given a timeout callback run event, returns its\n  // setTimeout call event.\n  asyncCallToScheduler: typeof asyncCallToScheduler,\n  // Given a trace event, returns its corresponding async parent trace\n  // event caused by an async js call. This can be used as a fallback\n  // for cases where a corresponding JS call is not found at either\n  // end of the async task scheduling pair (e.g. due to sampling data\n  // incompleteness).\n  // In the StackTraceForEvent helper, as we move up the call tree,\n  // this is used to jump to an async parent stack from a\n  // non-profile call trace event in cases where a profile call wasn't\n  // found before. In theory we should make the jump from the scheduled\n  // profile  call using `asyncCallToScheduler`, but its possible that\n  // the the call information isn't available to us as a consequence of\n  // missing samples.\n  runEntryPointToScheduler: typeof runEntryPointToScheduler,\n} {\n  return {\n    schedulerToRunEntryPoints,\n    asyncCallToScheduler,\n    runEntryPointToScheduler,\n  };\n}\n\nexport function deps(): ['Renderer', 'Flows'] {\n  return ['Renderer', 'Flows'];\n}\n"]}