{"version": 3, "file": "AnimationFramesHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/AnimationFramesHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAC7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAS3C,SAAS,SAAS,CAAC,IAAwB;IACzC,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;AACnC,CAAC;AACD,2EAA2E;AAC3E,8CAA8C;AAC9C,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAmD,CAAC;AACxF,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAiD,CAAC;AACpF,8EAA8E;AAC9E,2EAA2E;AAC3E,MAAM,2BAA2B,GAAG,IAAI,GAAG,EAAmD,CAAC;AAE/F,qDAAqD;AACrD,MAAM,eAAe,GAA+C,EAAE,CAAC;AAEvE,MAAM,oBAAoB,GACtB,IAAI,GAAG,EAAqF,CAAC;AAEjG,MAAM,UAAU,KAAK;IACnB,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAC3B,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,2BAA2B,CAAC,KAAK,EAAE,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK,CAAC,EAAE,CAAC;QACnD,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7B,MAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACrD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC1C,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC;QACxD,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7B,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACnD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;QAC9E,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,4EAA4E;IAC5E,6EAA6E;IAC7E,0EAA0E;IAC1E,aAAa;IACb,0EAA0E;IAC1E,iBAAiB;IACjB,2EAA2E;IAC3E,yEAAyE;IACzE,2EAA2E;IAC3E,oBAAoB;IACpB,KAAK,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,EAAE,CAAC;QAChE,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS;QACX,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAClD,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,0DAA0D;gBAC1D,MAAM;YACR,CAAC;YACD,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAElC,MAAM,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC,sBAAsB;iBACzC,sBAAsB,CAA2C;gBAChE,cAAc,EAAE,UAAU;gBAC1B,GAAG,UAAU;gBACb,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;gBACpD,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,UAAU,EAAE,UAAU;wBACtB,QAAQ;qBACT;iBACF;aACF,CAAC,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAErC,0EAA0E;YAC1E,oDAAoD;YACpD,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/B,IAAI,EAAE,EAAE,CAAC;gBACP,MAAM,iBAAiB,GAAG,2BAA2B,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC9D,IAAI,iBAAiB,EAAE,CAAC;oBACtB,oBAAoB,CAAC,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,eAAe;QACf,oBAAoB;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright 2024 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport type {HandlerName} from './types.js';\n\nexport interface Data {\n  animationFrames: Types.Events.SyntheticAnimationFramePair[];\n  presentationForFrame: Map<Types.Events.SyntheticAnimationFramePair, Types.Events.AnimationFramePresentation>;\n}\n\nfunction threadKey(data: Types.Events.Event): string {\n  return `${data.pid}-${data.tid}`;\n}\n// Track all the start + end events. We key them by the PID+TID so we don't\n// accidentally pair across different threads.\nconst animationFrameStarts = new Map<string, Types.Events.AnimationFrameAsyncStart[]>();\nconst animationFrameEnds = new Map<string, Types.Events.AnimationFrameAsyncEnd[]>();\n// Store all the AnimationFrame::Presentation events. Key them by their ID for\n// easy look-up later on when we associate one to the AnimationFrame event.\nconst animationFramePresentations = new Map<string, Types.Events.AnimationFramePresentation>();\n\n// The final list of animation frames that we return.\nconst animationFrames: Types.Events.SyntheticAnimationFramePair[] = [];\n\nconst presentationForFrame =\n    new Map<Types.Events.SyntheticAnimationFramePair, Types.Events.AnimationFramePresentation>();\n\nexport function reset(): void {\n  animationFrameStarts.clear();\n  animationFrameEnds.clear();\n  animationFrames.length = 0;\n  presentationForFrame.clear();\n  animationFramePresentations.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isAnimationFrameAsyncStart(event)) {\n    const key = threadKey(event);\n    const existing = animationFrameStarts.get(key) ?? [];\n    existing.push(event);\n    animationFrameStarts.set(key, existing);\n  } else if (Types.Events.isAnimationFrameAsyncEnd(event)) {\n    const key = threadKey(event);\n    const existing = animationFrameEnds.get(key) ?? [];\n    existing.push(event);\n    animationFrameEnds.set(key, existing);\n  } else if (Types.Events.isAnimationFramePresentation(event) && event.args?.id) {\n    animationFramePresentations.set(event.args.id, event);\n  }\n}\n\nexport async function finalize(): Promise<void> {\n  // AnimationFrames are represented with begin & end events on a stack; so we\n  // can pair them by walking through the list of start events and pairing with\n  // the same index in the list of end events, once both lists are sorted by\n  // timestamp.\n  // We walk through the set of begin/end events we gathered per pid+tid and\n  // pair those up.\n  // Unfortunately we cannot use the pairing helpers in Helpers.Trace because\n  // only the begin event has an ID; the end event does not. But because we\n  // know that AnimationFrames are sequential and do not overlap, we can pair\n  // up events easily.\n  for (const [key, startEvents] of animationFrameStarts.entries()) {\n    const endEvents = animationFrameEnds.get(key);\n    if (!endEvents) {\n      continue;\n    }\n\n    Helpers.Trace.sortTraceEventsInPlace(startEvents);\n    Helpers.Trace.sortTraceEventsInPlace(endEvents);\n\n    for (let i = 0; i < startEvents.length; i++) {\n      const endEvent = endEvents.at(i);\n      if (!endEvent) {\n        // Invalid data: break. We can't pair any other events up.\n        break;\n      }\n      const startEvent = startEvents[i];\n\n      const syntheticEvent = Helpers.SyntheticEvents.SyntheticEventsManager\n                                 .registerSyntheticEvent<Types.Events.SyntheticAnimationFramePair>({\n                                   rawSourceEvent: startEvent,\n                                   ...startEvent,\n                                   dur: Types.Timing.Micro(endEvent.ts - startEvent.ts),\n                                   args: {\n                                     data: {\n                                       beginEvent: startEvent,\n                                       endEvent,\n                                     },\n                                   },\n                                 });\n      animationFrames.push(syntheticEvent);\n\n      // AnimationFrame begin events + AnimationFrame::Presentation events share\n      // an args.id, so we can pair them up based on that.\n      const id = startEvent.args?.id;\n      if (id) {\n        const presentationEvent = animationFramePresentations.get(id);\n        if (presentationEvent) {\n          presentationForFrame.set(syntheticEvent, presentationEvent);\n        }\n      }\n    }\n  }\n}\n\nexport function data(): Data {\n  return {\n    animationFrames,\n    presentationForFrame,\n  };\n}\n\nexport function deps(): HandlerName[] {\n  return ['Meta'];\n}\n"]}