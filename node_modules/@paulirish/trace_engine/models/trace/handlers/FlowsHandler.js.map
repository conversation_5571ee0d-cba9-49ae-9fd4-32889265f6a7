{"version": 3, "file": "FlowsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/FlowsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAC7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAC/D,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,qEAAqE;AACrE,wEAAwE;AAExE,sEAAsE;AACtE,sEAAsE;AACtE,uEAAuE;AACvE,kEAAkE;AAClE,iEAAiE;AAEjE,uEAAuE;AACvE,8DAA8D;AAE9D,wEAAwE;AACxE,mEAAmE;AACnE,aAAa;AAEb,wEAAwE;AACxE,uEAAuE;AACvE,sEAAsE;AACtE,yDAAyD;AACzD,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAkB,CAAC;AASvD,mEAAmE;AACnE,uEAAuE;AACvE,qEAAqE;AACrE,qBAAqB;AACrB,qEAAqE;AACrE,uEAAuE;AACvE,uEAAuE;AACvE,oEAAoE;AACpE,6DAA6D;AAC7D,eAAe;AACf,MAAM,aAAa,GAAqB,IAAI,GAAG,EAAE,CAAC;AAElD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAuD,CAAC;AACjF,MAAM,UAAU,GAA6B,EAAE,CAAC;AAChD,MAAM,aAAa,GAAyB,EAAE,CAAC;AAC/C,IAAI,KAAK,GAA2B,EAAE,CAAC;AACvC,MAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,MAAM,UAAU,KAAK;IACnB,KAAK,GAAG,EAAE,CAAC;IACX,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACtB,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACzB,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,aAAa,CAAC,KAAK,EAAE,CAAC;IACtB,SAAS,CAAC,KAAK,EAAE,CAAC;AACpB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;QACzC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO;IACT,CAAC;IACD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAyB;IACpD,MAAM,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,OAAO;IACT,CAAC;IACD,MAAM,EAAC,KAAK,EAAE,aAAa,EAAC,GAAG,gBAAgB,CAAC;IAChD,IAAI,aAAa,EAAE,CAAC;QAClB,mEAAmE;QACnE,OAAO;IACT,CAAC;IACD,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAC7C,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAA0C,CAAC,CAAC;QAChF,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IACD,gBAAgB,CAAC,aAAa,GAAG,IAAI,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,SAAS,gBAAgB,CAAC,cAAsC;IAC9D,MAAM,SAAS,GAAG,+BAA+B,CAAC,cAAc,CAAC,CAAC;IAClE,QAAQ,cAAc,CAAC,EAAE,EAAE,CAAC;QAC1B,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,EAAC,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC;YACnG,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;YACvD,uBAAuB,CAAC,cAAc,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QACD,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,+DAA+D;gBAC/D,qDAAqD;gBACrD,OAAO;YACT,CAAC;YACD,uBAAuB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QACD,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,+DAA+D;gBAC/D,qDAAqD;gBACrD,OAAO;YACT,CAAC;YACD,uBAAuB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAChD,iEAAiE;YACjE,qBAAqB;YACrB,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;AACH,CAAC;AAGD;;;;GAIG;AACH,SAAS,uBAAuB,CAAC,KAAyB,EAAE,MAAc;IACxE,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CACnD,aAAa,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CACnD,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IAC5C,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CACnD,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IAC5C,MAAM,QAAQ,GACV,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAC,KAAK,EAAE,IAAI,GAAG,EAAE,EAAE,aAAa,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;IAClH,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,+BAA+B,CAAC,KAA6B;IACpE,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,sBAAsB,GAAG,KAAK,CAAC,IAAI,GAAG,sBAAsB,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC;AAClG,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,4DAA4D;IAC5D,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACrC,aAAa,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAC3C,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;SAClB,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;SAC7C,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;SACtD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,KAAK;KACN,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright 2024 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\nimport * as Platform from '../../../core/platform/platform.js';\nimport * as Types from '../types/types.js';\n\n// A flow is a logic connection between trace events. We display this\n// connection as arrows between trace events belonging to the same flow.\n\n// In the trace event format, flows are represented with pairing \"flow\n// phase\" events. Each flow phase event corresponds to one trace event\n// and indicates the role a trace event plays in a flow (start, step or\n// end). For each flow, one `start` and one `end` phase events are\n// included, while the amount of `step` phase events can be >= 0.\n\n// A flow phase event is assigned to a trace event when their cat, tid,\n// pid and ts are equal (see @flowPhaseBindingTokenForEvent ).\n\n// It's possible for a single event to belong to multiple flows. In that\n// case, it will have multiple corresponding flow phase events (one\n// per flow).\n\n// To parse flows, we first handle flow phase events, by creating unique\n// flows with the timestamps of each phase. Then, we place trace events\n// in the flows where their corresponding phase events were placed (if\n// there are any corresponding flow phase events at all).\nconst flowDataByGroupToken = new Map<string, number>();\n\ninterface EventFlowData {\n  flows: Set<number>;\n  bindingParsed: boolean;\n}\ntype FlowBindingTuple =\n    Map<Types.Timing.Micro, Map<Types.Events.ProcessID, Map<Types.Events.ThreadID, Map<string, EventFlowData>>>>;\n\n// Given a trace event's flow binding tuple (timestamp, process id,\n// thread id and category) we determine if there is any flow data bound\n// to it by using this map's content. It's built when processing flow\n// events in a trace.\n// An alternative to having a map of four levels is having single map\n// from a string token built from concatenating the binding data to the\n// corresponding flow data. However, this token would be calculated for\n// every event in a trace, resulting in a lot of memory overhead and\n// major GC triggering. So we are trading off readability for\n// performance.\nconst boundFlowData: FlowBindingTuple = new Map();\n\nconst flowsById = new Map<number, Map<Types.Timing.Micro, Types.Events.Event>>();\nconst flowEvents: Types.Events.FlowEvent[] = [];\nconst nonFlowEvents: Types.Events.Event[] = [];\nlet flows: Types.Events.Event[][] = [];\nconst ID_COMPONENT_SEPARATOR = '-$-';\nexport function reset(): void {\n  flows = [];\n  flowEvents.length = 0;\n  nonFlowEvents.length = 0;\n  flowDataByGroupToken.clear();\n  boundFlowData.clear();\n  flowsById.clear();\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isFlowPhaseEvent(event)) {\n    flowEvents.push(event);\n    return;\n  }\n  nonFlowEvents.push(event);\n}\n\nfunction processNonFlowEvent(event: Types.Events.Event): void {\n  const flowDataForEvent = boundFlowData.get(event.ts)?.get(event.pid)?.get(event.tid)?.get(event.cat);\n  if (!flowDataForEvent) {\n    return;\n  }\n  const {flows, bindingParsed} = flowDataForEvent;\n  if (bindingParsed) {\n    // We only consider the first event for a given flow binding tuple.\n    return;\n  }\n  for (const flowId of flows) {\n    const flow = Platform.MapUtilities.getWithDefault(\n        flowsById, flowId, () => new Map<Types.Timing.Micro, Types.Events.Event>());\n    flow.set(event.ts, event);\n  }\n  flowDataForEvent.bindingParsed = true;\n}\n\n/**\n * Creates unique flows by tracking flow phase events. A new created\n * flow whenever a flow start phase event is detected.\n * Subsequent flow phase events with the same group token are added to\n * this flow until a flow end phase is detected.\n */\nfunction processFlowEvent(flowPhaseEvent: Types.Events.FlowEvent): void {\n  const flowGroup = flowGroupTokenForFlowPhaseEvent(flowPhaseEvent);\n  switch (flowPhaseEvent.ph) {\n    case (Types.Events.Phase.FLOW_START): {\n      const flowMetadata = {flowId: flowPhaseEvent.id, times: new Map([[flowPhaseEvent.ts, undefined]])};\n      flowDataByGroupToken.set(flowGroup, flowPhaseEvent.id);\n      addFlowIdToEventBinding(flowPhaseEvent, flowMetadata.flowId);\n      return;\n    }\n    case (Types.Events.Phase.FLOW_STEP): {\n      const flowId = flowDataByGroupToken.get(flowGroup);\n      if (flowId === undefined) {\n        // Found non-start flow event with no corresponding start flow,\n        // start event. Quietly ignore the problematic event.\n        return;\n      }\n      addFlowIdToEventBinding(flowPhaseEvent, flowId);\n      return;\n    }\n    case (Types.Events.Phase.FLOW_END): {\n      const flowId = flowDataByGroupToken.get(flowGroup);\n      if (flowId === undefined) {\n        // Found non-start flow event with no corresponding start flow,\n        // start event. Quietly ignore the problematic event.\n        return;\n      }\n      addFlowIdToEventBinding(flowPhaseEvent, flowId);\n      // We don't need this data anymore as the flow has been finished,\n      // so we can drop it.\n      flowDataByGroupToken.delete(flowGroup);\n    }\n  }\n}\n\ntype MapValueType<T extends Map<unknown, unknown>> = NonNullable<ReturnType<T['get']>>;\n/**\n * A single trace event can belong to multiple flows. This method\n * tracks which flows (flowId) an event belongs to given its flow\n * binding tuple (made of its ts, pid, tid and cat).\n */\nfunction addFlowIdToEventBinding(event: Types.Events.Event, flowId: number): void {\n  const flowsByPid = Platform.MapUtilities.getWithDefault<Types.Timing.Micro, MapValueType<typeof boundFlowData>>(\n      boundFlowData, event.ts, () => new Map());\n  const flowsByTid = Platform.MapUtilities.getWithDefault<Types.Events.ProcessID, MapValueType<typeof flowsByPid>>(\n      flowsByPid, event.pid, () => new Map());\n  const flowsByCat = Platform.MapUtilities.getWithDefault<Types.Events.ThreadID, MapValueType<typeof flowsByTid>>(\n      flowsByTid, event.tid, () => new Map());\n  const flowData =\n      Platform.MapUtilities.getWithDefault(flowsByCat, event.cat, () => ({flows: new Set(), bindingParsed: false}));\n  flowData.flows.add(flowId);\n}\n\n/**\n * Returns a token to group flow phase events (start, step and end)\n * belonging to the same flow. Flow phase events belonging to the same\n * flow share category, thread id, process id and name.\n *\n * Note that other phase events of other flows can share these\n * attributes too. For this reason, we group flow phase events in\n * cycles. A cycle starts on a flow start phase event and finishes on a\n * flow end phase event. For this reason, flow phase events need to be\n * handled in timestamp order.\n */\nfunction flowGroupTokenForFlowPhaseEvent(event: Types.Events.FlowEvent): string {\n  return `${event.cat}${ID_COMPONENT_SEPARATOR}${event.name}${ID_COMPONENT_SEPARATOR}${event.id}`;\n}\n\nexport async function finalize(): Promise<void> {\n  // Order is important: flow events need to be handled first.\n  flowEvents.forEach(processFlowEvent);\n  nonFlowEvents.forEach(processNonFlowEvent);\n  flows = [...flowsById.values()]\n              .map(flowMapping => [...flowMapping.values()])\n              .map(flow => flow.filter(event => event !== undefined))\n              .filter(flow => flow.length > 1);\n}\n\nexport function data(): {flows: Types.Events.Event[][]} {\n  return {\n    flows,\n  };\n}\n"]}