{"version": 3, "file": "ImagePaintingHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/ImagePaintingHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,QAAQ,MAAM,oCAAoC,CAAC;AAC/D,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,eAAe,EAAC,MAAM,kBAAkB,CAAC;AAEzD;;;;;;;;;;;;;;GAcG;AAEH,yCAAyC;AACzC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAiF,CAAC;AAClH,MAAM,wBAAwB,GAC1B,IAAI,GAAG,EAAyF,CAAC;AAErG,iEAAiE;AACjE,8EAA8E;AAC9E,8EAA8E;AAC9E,0BAA0B;AAC1B,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAmC,CAAC;AAE5E,qGAAqG;AACrG,+EAA+E;AAC/E,8EAA8E;AAC9E,wDAAwD;AACxD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAA+C,CAAC;AAEjF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAqC,CAAC;AAErE,MAAM,gCAAgC,GAAG,IAAI,GAAG,EAA4D,CAAC;AAE7G,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAEjC,MAAM,UAAU,KAAK;IACnB,gBAAgB,CAAC,KAAK,EAAE,CAAC;IACzB,wBAAwB,CAAC,KAAK,EAAE,CAAC;IACjC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IACjC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IAC1B,eAAe,CAAC,KAAK,EAAE,CAAC;IACxB,gCAAgC,CAAC,KAAK,EAAE,CAAC;IACzC,oBAAoB,GAAG,KAAK,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,UAAU,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAAoD,CAAC;QAClH,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAClD,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACrC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAE5C,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YAC1G,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY,KAAK,WAAW,EAAE,CAAC;QAChG,sEAAsE;QACtE,MAAM,UAAU,GACZ,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAA4D,CAAC;QACnH,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAClD,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACrC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAED,0EAA0E;IAC1E,4EAA4E;IAC5E,iCAAiC;IACjC,yEAAyE;IACzE,wEAAwE;IACxE,iBAAiB;IACjB,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY,KAAK,WAAW,EAAE,CAAC;QAC9F,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QACD,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACtE,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACtC,qEAAqE;QACrE,gCAAgC;QAChC,EAAE;QACF,sEAAsE;QACtE,uCAAuC;QACvC,EAAE;QACF,2EAA2E;QAC3E,qEAAqE;QACrE,wEAAwE;QACxE,kBAAkB;QAClB,EAAE;QACF,6FAA6F;QAC7F,MAAM,2BAA2B,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,2BAA2B,EAAE,CAAC;YAChC,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,2BAA2B,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,sGAAsG;QACtG,MAAM,sBAAsB,GAAG,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/F,IAAI,OAAO,sBAAsB,EAAE,IAAI,EAAE,YAAY,KAAK,WAAW,EAAE,CAAC;YACtE,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,wBAAwB,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1F,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;QACT,CAAC;QACD,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ,CAAC,OAAyC;IACtE,mFAAmF;IACnF,mFAAmF;IACnF,WAAW;IACX,wDAAwD;IAExD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QAC/B,OAAO;IACT,CAAC;IAED,kFAAkF;IAClF,gBAAgB;IAChB,MAAM,EAAC,gBAAgB,EAAE,WAAW,EAAC,GAAG,eAAe,EAAE,CAAC;IAC1D,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO;IACT,CAAC;IAED,KAAK,MAAM,QAAQ,IAAI,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;QACjD,KAAK,MAAM,WAAW,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC7E,MAAM,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC/E,MAAM,KAAK,GAAG,cAAc,GAAG,WAAW,CAAC;gBAC3C,MAAM,MAAM,GAAG,eAAe,GAAG,WAAW,CAAC;gBAC7C,gCAAgC,CAAC,GAAG,CAAC,UAAU,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAED,oBAAoB,GAAG,IAAI,CAAC;AAC9B,CAAC;AAWD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,4BAA4B,EAAE,wBAAwB;QACtD,kBAAkB,EAAE,iBAAiB;QACrC,qBAAqB,EAAE,eAAe;QACtC,gCAAgC;QAChC,oBAAoB;KACrB,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright 2024 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Platform from '../../../core/platform/platform.js';\nimport * as Types from '../types/types.js';\n\nimport {data as metaHandlerData} from './MetaHandler.js';\n\n/**\n * This handler is responsible for the relationships between:\n * DecodeImage/ResizeImage, PaintImage and DrawLazyPixelRef events.\n *\n * When we get a DecodeImage event, we want to associate it to a PaintImage\n * event, primarily so we can determine the NodeID of the image that was\n * decoded.\n * We can do this in two ways:\n *\n * 1. If there is a PaintImage event on the same thread, use that\n *    (if there are multiple, use the latest one).\n *\n * 2. If not, we can find the DecodeLazyPixelRef event on the same thread, and\n *    use the PaintImage event associated with it via the `LazyPixelRef` key.\n */\n\n// Track paintImageEvents across threads.\nconst paintImageEvents = new Map<Types.Events.ProcessID, Map<Types.Events.ThreadID, Types.Events.PaintImage[]>>();\nconst decodeLazyPixelRefEvents =\n    new Map<Types.Events.ProcessID, Map<Types.Events.ThreadID, Types.Events.DecodeLazyPixelRef[]>>();\n\n// A DrawLazyPixelRef event will contain a numerical reference in\n// args.LazyPixelRef. As we parse each DrawLazyPixelRef, we can assign it to a\n// paint event. Later we want to look up paint events by this reference, so we\n// store them in this map.\nconst paintImageByLazyPixelRef = new Map<number, Types.Events.PaintImage>();\n\n// When we find events that we want to tie to a particular PaintImage event, we add them to this map.\n// These are currently only DecodeImage and ResizeImage events, but the type is\n// deliberately generic as in the future we might want to add more events that\n// have a relationship to a individual PaintImage event.\nconst eventToPaintImage = new Map<Types.Events.Event, Types.Events.PaintImage>();\n\nconst urlToPaintImage = new Map<string, Types.Events.PaintImage[]>();\n\nconst paintEventToCorrectedDisplaySize = new Map<Types.Events.PaintImage, {width: number, height: number}>();\n\nlet didCorrectForHostDpr = false;\n\nexport function reset(): void {\n  paintImageEvents.clear();\n  decodeLazyPixelRefEvents.clear();\n  paintImageByLazyPixelRef.clear();\n  eventToPaintImage.clear();\n  urlToPaintImage.clear();\n  paintEventToCorrectedDisplaySize.clear();\n  didCorrectForHostDpr = false;\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isPaintImage(event)) {\n    const forProcess = paintImageEvents.get(event.pid) || new Map<Types.Events.ThreadID, Types.Events.PaintImage[]>();\n    const forThread = forProcess.get(event.tid) || [];\n    forThread.push(event);\n    forProcess.set(event.tid, forThread);\n    paintImageEvents.set(event.pid, forProcess);\n\n    if (event.args.data.url) {\n      const paintsForUrl = Platform.MapUtilities.getWithDefault(urlToPaintImage, event.args.data.url, () => []);\n      paintsForUrl.push(event);\n    }\n\n    return;\n  }\n\n  if (Types.Events.isDecodeLazyPixelRef(event) && typeof event.args?.LazyPixelRef !== 'undefined') {\n    // Store these because we use them to tie DecodeImage to a PaintEvent.\n    const forProcess =\n        decodeLazyPixelRefEvents.get(event.pid) || new Map<Types.Events.ThreadID, Types.Events.DecodeLazyPixelRef[]>();\n    const forThread = forProcess.get(event.tid) || [];\n    forThread.push(event);\n    forProcess.set(event.tid, forThread);\n    decodeLazyPixelRefEvents.set(event.pid, forProcess);\n  }\n\n  // If we see a DrawLazyPixelRef event, we need to find the last PaintImage\n  // event on the thread and associate it to the LazyPixelRef that is supplied\n  // in the DrawLazyPixelRef event.\n  // This means that later on if we see a DecodeLazyPixelRef event with the\n  // same LazyPixelRef key, we can find its associated PaintImage event by\n  // looking it up.\n  if (Types.Events.isDrawLazyPixelRef(event) && typeof event.args?.LazyPixelRef !== 'undefined') {\n    const lastPaintEvent = paintImageEvents.get(event.pid)?.get(event.tid)?.at(-1);\n    if (!lastPaintEvent) {\n      return;\n    }\n    paintImageByLazyPixelRef.set(event.args.LazyPixelRef, lastPaintEvent);\n    return;\n  }\n\n  if (Types.Events.isDecodeImage(event)) {\n    // When we see a DecodeImage, we want to associate it to a PaintImage\n    // event. We try two approaches:\n    //\n    // 1. If the thread of the DecodeImage event has a previous PaintImage\n    // event, that is the associated event.\n    //\n    // 2. If that is false, we then look on the thread for a DecodeLazyPixelRef\n    // event. If we find that, we then look for its associated PaintImage\n    // event, which we associate via DrawLazyPixelRef events (the code block\n    // above this one)\n    //\n    // 1. Find a PaintImage event on the same thread. If we find it, that's our association done.\n    const lastPaintImageEventOnThread = paintImageEvents.get(event.pid)?.get(event.tid)?.at(-1);\n    if (lastPaintImageEventOnThread) {\n      eventToPaintImage.set(event, lastPaintImageEventOnThread);\n      return;\n    }\n\n    // 2. Find the last DecodeLazyPixelRef event and, if we find it, find its associated PaintImage event.\n    const lastDecodeLazyPixelRef = decodeLazyPixelRefEvents.get(event.pid)?.get(event.tid)?.at(-1);\n    if (typeof lastDecodeLazyPixelRef?.args?.LazyPixelRef === 'undefined') {\n      return;\n    }\n\n    const paintEvent = paintImageByLazyPixelRef.get(lastDecodeLazyPixelRef.args.LazyPixelRef);\n    if (!paintEvent) {\n      return;\n    }\n    eventToPaintImage.set(event, paintEvent);\n  }\n}\n\nexport async function finalize(options: Types.Configuration.ParseOptions): Promise<void> {\n  // Painting in Chrome never uses the emulated DPR, but instead used the host's DPR.\n  // We need to correct for that for our responsive image checks in the ImageDelivery\n  // insight.\n  // See: crbug.com/427552461 crbug.com/416580500#comment5\n\n  if (!options.metadata?.hostDPR) {\n    return;\n  }\n\n  // Note: this isn't necessarily emulated (for desktop+no DPR emulation, it's equal\n  // to host DPR).\n  const {devicePixelRatio: emulatedDpr} = metaHandlerData();\n  if (!emulatedDpr) {\n    return;\n  }\n\n  for (const byThread of paintImageEvents.values()) {\n    for (const paintEvents of byThread.values()) {\n      for (const paintEvent of paintEvents) {\n        const cssPixelsWidth = paintEvent.args.data.width / options.metadata.hostDPR;\n        const cssPixelsHeight = paintEvent.args.data.height / options.metadata.hostDPR;\n        const width = cssPixelsWidth * emulatedDpr;\n        const height = cssPixelsHeight * emulatedDpr;\n        paintEventToCorrectedDisplaySize.set(paintEvent, {width, height});\n      }\n    }\n  }\n\n  didCorrectForHostDpr = true;\n}\n\nexport interface ImagePaintData {\n  paintImageByDrawLazyPixelRef: Map<number, Types.Events.PaintImage>;\n  paintImageForEvent: Map<Types.Events.Event, Types.Events.PaintImage>;\n  paintImageEventForUrl: Map<string, Types.Events.PaintImage[]>;\n  paintEventToCorrectedDisplaySize: Map<Types.Events.PaintImage, {width: number, height: number}>;\n  /** Go read the comment in finalize(). */\n  didCorrectForHostDpr: boolean;\n}\n\nexport function data(): ImagePaintData {\n  return {\n    paintImageByDrawLazyPixelRef: paintImageByLazyPixelRef,\n    paintImageForEvent: eventToPaintImage,\n    paintImageEventForUrl: urlToPaintImage,\n    paintEventToCorrectedDisplaySize,\n    didCorrectForHostDpr,\n  };\n}\n"]}