{"version": 3, "file": "InvalidationsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/InvalidationsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAgE,CAAC;AACtG,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAA8B,CAAC;AAExE,IAAI,oBAAoB,GAAuC,IAAI,CAAC;AAEpE,sEAAsE;AACtE,IAAI,UAAU,GAAG,KAAK,CAAC;AAEvB,MAAM,6BAA6B,GAA6C,EAAE,CAAC;AAEnF,MAAM,UAAU,KAAK;IACnB,qBAAqB,CAAC,KAAK,EAAE,CAAC;IAC9B,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAClC,oBAAoB,GAAG,IAAI,CAAC;IAC5B,6BAA6B,CAAC,MAAM,GAAG,CAAC,CAAC;IACzC,UAAU,GAAG,KAAK,CAAC;IACnB,wBAAwB,GAAG,IAAI,CAAC;AAClC,CAAC;AAED,IAAI,wBAAwB,GAAgB,IAAI,CAAC;AACjD,MAAM,UAAU,gBAAgB,CAAC,UAA6C;IAC5E,wBAAwB,GAAG,UAAU,CAAC,6BAA6B,CAAC;AACtE,CAAC;AAED,SAAS,sBAAsB,CAAC,KAAyB,EAAE,YAAoD;IAC7G,MAAM,qBAAqB,GAAG,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACrE,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAEzC,IAAI,wBAAwB,KAAK,IAAI,IAAI,qBAAqB,CAAC,MAAM,GAAG,wBAAwB,EAAE,CAAC;QACjG,qBAAqB,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IACD,qBAAqB,CAAC,GAAG,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;IAExD,MAAM,KAAK,GAAG,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxD,yBAAyB,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,2EAA2E;IAC3E,6EAA6E;IAC7E,8BAA8B;IAC9B,IAAI,wBAAwB,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3C,oBAAoB,GAAG,KAAK,CAAC;QAE7B,4DAA4D;QAC5D,KAAK,MAAM,YAAY,IAAI,6BAA6B,EAAE,CAAC;YACzD,IAAI,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC5D,mEAAmE;gBACnE,SAAS;gBACT,SAAS;YACX,CAAC;YAED,MAAM,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;YAEjE,IAAI,aAAa,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;gBACpE,sBAAsB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/C,IAAI,UAAU,EAAE,CAAC;YACf,qEAAqE;YACrE,4DAA4D;YAC5D,6BAA6B,CAAC,MAAM,GAAG,CAAC,CAAC;YACzC,oBAAoB,GAAG,IAAI,CAAC;YAC5B,UAAU,GAAG,KAAK,CAAC;QACrB,CAAC;QAED,wLAAwL;QACxL,6DAA6D;QAC7D,wEAAwE;QACxE,oEAAoE;QACpE,4CAA4C;QAC5C,IAAI,oBAAoB;YACpB,CAAC,KAAK,CAAC,MAAM,CAAC,mCAAmC,CAAC,KAAK,CAAC;gBACvD,KAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,KAAK,CAAC;gBACrD,KAAK,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjE,MAAM,aAAa,GAAG,oBAAoB,CAAC,EAAE,GAAG,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAChF,IAAI,KAAK,CAAC,EAAE,IAAI,oBAAoB,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,aAAa;gBAChE,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACzE,sBAAsB,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,oEAAoE;QACpE,UAAU,GAAG,IAAI,CAAC;QAClB,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAC/C,KAAK,MAAM,YAAY,IAAI,6BAA6B,EAAE,CAAC;YACzD,wEAAwE;YACxE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7D,SAAS;YACX,CAAC;YAED,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gBACjD,sBAAsB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;AAC9B,CAAC;AAOD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,qBAAqB;QACrB,yBAAyB;KAC1B,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Types from '../types/types.js';\n\nconst invalidationsForEvent = new Map<Types.Events.Event, Types.Events.InvalidationTrackingEvent[]>();\nconst invalidationCountForEvent = new Map<Types.Events.Event, number>();\n\nlet lastRecalcStyleEvent: Types.Events.UpdateLayoutTree|null = null;\n\n// Used to track paints so we track invalidations correctly per paint.\nlet hasPainted = false;\n\nconst allInvalidationTrackingEvents: Types.Events.InvalidationTrackingEvent[] = [];\n\nexport function reset(): void {\n  invalidationsForEvent.clear();\n  invalidationCountForEvent.clear();\n  lastRecalcStyleEvent = null;\n  allInvalidationTrackingEvents.length = 0;\n  hasPainted = false;\n  maxInvalidationsPerEvent = null;\n}\n\nlet maxInvalidationsPerEvent: number|null = null;\nexport function handleUserConfig(userConfig: Types.Configuration.Configuration): void {\n  maxInvalidationsPerEvent = userConfig.maxInvalidationEventsPerEvent;\n}\n\nfunction addInvalidationToEvent(event: Types.Events.Event, invalidation: Types.Events.InvalidationTrackingEvent): void {\n  const existingInvalidations = invalidationsForEvent.get(event) || [];\n  existingInvalidations.push(invalidation);\n\n  if (maxInvalidationsPerEvent !== null && existingInvalidations.length > maxInvalidationsPerEvent) {\n    existingInvalidations.shift();\n  }\n  invalidationsForEvent.set(event, existingInvalidations);\n\n  const count = invalidationCountForEvent.get(event) ?? 0;\n  invalidationCountForEvent.set(event, count + 1);\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  // Special case: if we have been configured to not store any invalidations,\n  // we take that as a sign that we don't even want to gather any invalidations\n  // data at all and early exit.\n  if (maxInvalidationsPerEvent === 0) {\n    return;\n  }\n\n  if (Types.Events.isUpdateLayoutTree(event)) {\n    lastRecalcStyleEvent = event;\n\n    // Associate any prior invalidations with this recalc event.\n    for (const invalidation of allInvalidationTrackingEvents) {\n      if (Types.Events.isLayoutInvalidationTracking(invalidation)) {\n        // LayoutInvalidation events cannot be associated with a LayoutTree\n        // event.\n        continue;\n      }\n\n      const recalcFrameId = lastRecalcStyleEvent.args.beginData?.frame;\n\n      if (recalcFrameId && invalidation.args.data.frame === recalcFrameId) {\n        addInvalidationToEvent(event, invalidation);\n      }\n    }\n    return;\n  }\n\n  if (Types.Events.isInvalidationTracking(event)) {\n    if (hasPainted) {\n      // If we have painted, then we can clear out the list of all existing\n      // invalidations, as we cannot associate them across frames.\n      allInvalidationTrackingEvents.length = 0;\n      lastRecalcStyleEvent = null;\n      hasPainted = false;\n    }\n\n    // Style invalidation events can occur before and during recalc styles. When we get a recalc style event (aka UpdateLayoutTree), we check and associate any prior invalidations with it.\n    // But any invalidations that occur during a UpdateLayoutTree\n    // event would be reported in trace events after. So each time we get an\n    // invalidation that might be due to a style recalc, we check if the\n    // timings overlap and if so associate them.\n    if (lastRecalcStyleEvent &&\n        (Types.Events.isScheduleStyleInvalidationTracking(event) ||\n         Types.Events.isStyleRecalcInvalidationTracking(event) ||\n         Types.Events.isStyleInvalidatorInvalidationTracking(event))) {\n      const recalcEndTime = lastRecalcStyleEvent.ts + (lastRecalcStyleEvent.dur || 0);\n      if (event.ts >= lastRecalcStyleEvent.ts && event.ts <= recalcEndTime &&\n          lastRecalcStyleEvent.args.beginData?.frame === event.args.data.frame) {\n        addInvalidationToEvent(lastRecalcStyleEvent, event);\n      }\n    }\n\n    allInvalidationTrackingEvents.push(event);\n    return;\n  }\n\n  if (Types.Events.isPaint(event)) {\n    // Used to ensure that we do not create relationships across frames.\n    hasPainted = true;\n    return;\n  }\n\n  if (Types.Events.isLayout(event)) {\n    const layoutFrame = event.args.beginData.frame;\n    for (const invalidation of allInvalidationTrackingEvents) {\n      // The only invalidations that cause a Layout are LayoutInvalidations :)\n      if (!Types.Events.isLayoutInvalidationTracking(invalidation)) {\n        continue;\n      }\n\n      if (invalidation.args.data.frame === layoutFrame) {\n        addInvalidationToEvent(event, invalidation);\n      }\n    }\n  }\n}\n\nexport async function finalize(): Promise<void> {\n}\n\ninterface InvalidationsData {\n  invalidationsForEvent: Map<Types.Events.Event, Types.Events.InvalidationTrackingEvent[]>;\n  invalidationCountForEvent: Map<Types.Events.Event, number>;\n}\n\nexport function data(): InvalidationsData {\n  return {\n    invalidationsForEvent,\n    invalidationCountForEvent,\n  };\n}\n"]}