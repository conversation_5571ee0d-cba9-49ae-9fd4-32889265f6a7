{"version": 3, "file": "LayerTreeHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/LayerTreeHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,eAAe,EAAC,MAAM,kBAAkB,CAAC;AAGzD,MAAM,WAAW,GAAyB,EAAE,CAAC;AAC7C,MAAM,cAAc,GAA2C,EAAE,CAAC;AAClE,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAA4D,CAAC;AAE/F,IAAI,mBAAmB,GAAuC,EAAE,CAAC;AAEjE,IAAI,2BAA2B,GAAgB,IAAI,CAAC;AACpD,MAAM,iBAAiB,GAA+B,EAAE,CAAC;AAKzD,MAAM,cAAc,GAA6B,EAAE,CAAC;AACpD,MAAM,UAAU,KAAK;IACnB,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IACvB,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAE3B,mBAAmB,GAAG,EAAE,CAAC;IACzB,2BAA2B,GAAG,IAAI,CAAC;IACnC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7B,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,6EAA6E;IAC7E,2EAA2E;IAC3E,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,CAAC;QAChF,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1E,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;IACnC,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;IAErD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;QACnC,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACnD,iEAAiE;gBACjE,SAAS;YACX,CAAC;YACD,2BAA2B,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QAC5D,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7C,wEAAwE;YACxE,mEAAmE;YACnE,2EAA2E;YAC3E,YAAY;YACZ,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7B,qFAAqF;gBACrF,wEAAwE;gBACxE,qDAAqD;gBACrD,SAAS;YACX,CAAC;YACD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;YACrD,SAAS;QACX,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7D,wEAAwE;YACxE,mEAAmE;YACnE,yEAAyE;YACzE,OAAO;YACP,IAAI,6BAA6B,GAAkC,IAAI,CAAC;YACxE,KAAK,IAAI,CAAC,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvD,MAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,WAAW,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;oBACnE,6BAA6B,GAAG,WAAW,CAAC;oBAC5C,MAAM;gBACR,CAAC;YACH,CAAC;YACD,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACnC,qDAAqD;gBACrD,SAAS;YACX,CAAC;YACD,IAAI,6BAA6B,CAAC,IAAI,CAAC,WAAW,KAAK,2BAA2B,EAAE,CAAC;gBACnF,sEAAsE;gBACtE,SAAS;YACX,CAAC;YACD,MAAM,UAAU,GAAG,mBAAmB,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnF,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,6CAA6C;gBAC7C,SAAS;YACX,CAAC;YACD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE3B,6DAA6D;YAC7D,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;AACH,CAAC;AAQD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,cAAc;QACzB,iBAAiB,EAAE,kBAAkB;KACtC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {data as metaHandlerData} from './MetaHandler.js';\nimport type {HandlerName} from './types.js';\n\nconst paintEvents: Types.Events.Paint[] = [];\nconst snapshotEvents: Types.Events.DisplayItemListSnapshot[] = [];\nconst paintToSnapshotMap = new Map<Types.Events.Paint, Types.Events.DisplayItemListSnapshot>();\n\nlet lastPaintForLayerId: Record<number, Types.Events.Paint> = {};\n\nlet currentMainFrameLayerTreeId: number|null = null;\nconst updateLayerEvents: Types.Events.UpdateLayer[] = [];\n\ntype RelevantLayerTreeEvent =\n    Types.Events.Paint|Types.Events.DisplayItemListSnapshot|Types.Events.UpdateLayer|Types.Events.SetLayerTreeId;\n\nconst relevantEvents: RelevantLayerTreeEvent[] = [];\nexport function reset(): void {\n  paintEvents.length = 0;\n  snapshotEvents.length = 0;\n  paintToSnapshotMap.clear();\n\n  lastPaintForLayerId = {};\n  currentMainFrameLayerTreeId = null;\n  updateLayerEvents.length = 0;\n  relevantEvents.length = 0;\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  // We gather up the events here but do all the processing in finalize(). This\n  // is because we need to have all the events before we process them, and we\n  // need the Meta handler to be finalized() so we can use its data as we need\n  // the mainFrameId to know which Layer(s) to care about.\n  if (Types.Events.isPaint(event) || Types.Events.isDisplayListItemListSnapshot(event) ||\n      Types.Events.isUpdateLayer(event) || Types.Events.isSetLayerId(event)) {\n    relevantEvents.push(event);\n  }\n}\n\nexport async function finalize(): Promise<void> {\n  const metaData = metaHandlerData();\n  Helpers.Trace.sortTraceEventsInPlace(relevantEvents);\n\n  for (const event of relevantEvents) {\n    if (Types.Events.isSetLayerId(event)) {\n      if (metaData.mainFrameId !== event.args.data.frame) {\n        // We only care about LayerId changes that affect the main frame.\n        continue;\n      }\n      currentMainFrameLayerTreeId = event.args.data.layerTreeId;\n    } else if (Types.Events.isUpdateLayer(event)) {\n      // We don't do anything with this event, but we need to store it because\n      // the information in it determines if we need to care about future\n      // snapshot events - we need to know what the active layer is when we see a\n      // snapshot.\n      updateLayerEvents.push(event);\n    } else if (Types.Events.isPaint(event)) {\n      if (!event.args.data.layerId) {\n        // Note that this check purposefully includes excluding an event with a layerId of 0.\n        // 0 indicates that this paint was for a subframe - we do not want these\n        // as we only care about paints for top level frames.\n        continue;\n      }\n      paintEvents.push(event);\n      lastPaintForLayerId[event.args.data.layerId] = event;\n      continue;\n    } else if (Types.Events.isDisplayListItemListSnapshot(event)) {\n      // First we figure out which layer is active for this event's thread. To\n      // do this we work backwards through the list of UpdateLayerEvents,\n      // finding the first one (i.e. the most recent one) with the same pid and\n      // tid.\n      let lastUpdateLayerEventForThread: Types.Events.UpdateLayer|null = null;\n      for (let i = updateLayerEvents.length - 1; i > -1; i--) {\n        const updateEvent = updateLayerEvents[i];\n        if (updateEvent.pid === event.pid && updateEvent.tid === event.tid) {\n          lastUpdateLayerEventForThread = updateEvent;\n          break;\n        }\n      }\n      if (!lastUpdateLayerEventForThread) {\n        // No active layer, so this snapshot is not relevant.\n        continue;\n      }\n      if (lastUpdateLayerEventForThread.args.layerTreeId !== currentMainFrameLayerTreeId) {\n        // Snapshot applies to a layer that is not the main frame, so discard.\n        continue;\n      }\n      const paintEvent = lastPaintForLayerId[lastUpdateLayerEventForThread.args.layerId];\n      if (!paintEvent) {\n        // No paint event for this layer, so discard.\n        continue;\n      }\n      snapshotEvents.push(event);\n\n      // Store the relationship between the paint and the snapshot.\n      paintToSnapshotMap.set(paintEvent, event);\n    }\n  }\n}\n\nexport interface LayerTreeData {\n  paints: Types.Events.Paint[];\n  snapshots: Types.Events.DisplayItemListSnapshot[];\n  paintsToSnapshots: Map<Types.Events.Paint, Types.Events.DisplayItemListSnapshot>;\n}\n\nexport function data(): LayerTreeData {\n  return {\n    paints: paintEvents,\n    snapshots: snapshotEvents,\n    paintsToSnapshots: paintToSnapshotMap,\n  };\n}\n\nexport function deps(): HandlerName[] {\n  return ['Meta'];\n}\n"]}