{"version": 3, "file": "InitiatorsHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/InitiatorsHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,IAAI,IAAI,uBAAuB,EAAC,MAAM,0BAA0B,CAAC;AACzE,OAAO,EAAC,IAAI,IAAI,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAE3D,MAAM,8BAA8B,GAAG,IAAI,GAAG,EAAmD,CAAC;AAElG,+EAA+E;AAC/E,qBAAqB;AACrB,8EAA8E;AAC9E,4EAA4E;AAC5E,eAAe;AACf,MAAM,6BAA6B,GAAG,IAAI,GAAG,EAA8B,CAAC;AAE5E,yEAAyE;AACzE,2EAA2E;AAC3E,6EAA6E;AAC7E,aAAa;AACb,MAAM,2BAA2B,GAAG,IAAI,GAAG,EAAyC,CAAC;AAErF,kEAAkE;AAClE,2FAA2F;AAC3F,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAA0C,CAAC;AAC9E,6EAA6E;AAC7E,iEAAiE;AACjE,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAA4C,CAAC;AAEjF,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAAwC,CAAC;AAClF,MAAM,kCAAkC,GAAG,IAAI,GAAG,EAAiD,CAAC;AAEpG,MAAM,UAAU,KAAK;IACnB,8BAA8B,CAAC,KAAK,EAAE,CAAC;IACvC,6BAA6B,CAAC,KAAK,EAAE,CAAC;IACtC,2BAA2B,CAAC,KAAK,EAAE,CAAC;IACpC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IAC5B,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAClC,kCAAkC,CAAC,KAAK,EAAE,CAAC;AAC7C,CAAC;AAED,SAAS,cAAc,CAAC,IAAgE;IACtF,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACpD,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IAC1E,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,CAAC;QACrD,8BAA8B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAClD,uEAAuE;QACvE,4EAA4E;QAC5E,oCAAoC;QACpC,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACzB,gEAAgE;YAChE,wDAAwD;YACxD,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAEnE,yEAAyE;YACzE,uDAAuD;YACvD,MAAM,sBAAsB,GAAG,8BAA8B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9F,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,cAAc,CAAC;oBACb,KAAK;oBACL,SAAS,EAAE,sBAAsB;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAClD,mGAAmG;QACnG,IAAI,qBAAqB,GAAuB,KAAK,CAAC;QAEtD,yEAAyE;QACzE,yEAAyE;QACzE,qCAAqC;QACrC,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9D,6DAA6D;YAC7D,wDAAwD;YACxD,+EAA+E;YAC/E,6DAA6D;YAC7D,oGAAoG;YACpG,MAAM,4BAA4B,GAAG,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5F,IAAI,4BAA4B,EAAE,CAAC;gBACjC,MAAM,EAAC,OAAO,EAAC,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,4BAA4B,CAAC,CAAC;gBACxF,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBAEtF,IAAI,uBAAuB,IAAI,OAAO,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC;oBAC7D,qBAAqB,GAAG,uBAAuB,CAAC;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;QACD,6BAA6B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;IAClF,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACxC,kEAAkE;QAClE,MAAM,gBAAgB,GAAG,6BAA6B,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvF,IAAI,gBAAgB,EAAE,CAAC;YACrB,cAAc,CAAC;gBACb,KAAK;gBACL,SAAS,EAAE,gBAAgB;aAC5B,CAAC,CAAC;QACL,CAAC;QACD,gKAAgK;QAChK,6BAA6B,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QACjD,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1F,MAAM,mBAAmB,GAAG,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtF,IAAI,mBAAmB,EAAE,CAAC;YACxB,cAAc,CAAC;gBACb,KAAK;gBACL,SAAS,EAAE,mBAAmB;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1D,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACxE,CAAC;SAAM,IAAI,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,CAAC;QACpG,MAAM,gBAAgB,GAAG,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,gBAAgB,EAAE,CAAC;YACrB,cAAc,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,4BAA4B;IACnC,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC,KAAK,CAAC;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,cAAc,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,mCAAmC;IAC1C,MAAM,gBAAgB,GAAG,uBAAuB,EAAE,CAAC,yBAAyB,CAAC,OAAO,EAAE,CAAC;IACvF,KAAK,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,gBAAgB,EAAE,CAAC;QAC3D,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,cAAc,CAAC,EAAC,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,4BAA4B,EAAE,CAAC;IAC/B,mCAAmC,EAAE,CAAC;AACxC,CAAC;AAOD,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,gBAAgB,EAAE,mBAAmB;QACrC,iBAAiB,EAAE,oBAAoB;KACxC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AACnC,CAAC", "sourcesContent": ["// Copyright 2023 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nimport {data as AsyncJSCallsHandlerData} from './AsyncJSCallsHandler.js';\nimport {data as flowsHandlerData} from './FlowsHandler.js';\n\nconst lastScheduleStyleRecalcByFrame = new Map<string, Types.Events.ScheduleStyleRecalculation>();\n\n// This tracks the last event that is considered to have invalidated the layout\n// for a given frame.\n// Note that although there is an InvalidateLayout event, there are also other\n// events (ScheduleStyleRecalculation) that could be the reason a layout was\n// invalidated.\nconst lastInvalidationEventForFrame = new Map<string, Types.Events.Event>();\n\n// Important: although the event is called UpdateLayoutTree, in the UI we\n// present these to the user as \"Recalculate Style\". So don't get confused!\n// These are the same - just UpdateLayoutTree is what the event from Chromium\n// is called.\nconst lastUpdateLayoutTreeByFrame = new Map<string, Types.Events.UpdateLayoutTree>();\n\n// These two maps store the same data but in different directions.\n// For a given event, tell me what its initiator was. An event can only have one initiator.\nconst eventToInitiatorMap = new Map<Types.Events.Event, Types.Events.Event>();\n// For a given event, tell me what events it initiated. An event can initiate\n// multiple events, hence why the value for this map is an array.\nconst initiatorToEventsMap = new Map<Types.Events.Event, Types.Events.Event[]>();\n\nconst webSocketCreateEventsById = new Map<number, Types.Events.WebSocketCreate>();\nconst schedulePostTaskCallbackEventsById = new Map<number, Types.Events.SchedulePostTaskCallback>();\n\nexport function reset(): void {\n  lastScheduleStyleRecalcByFrame.clear();\n  lastInvalidationEventForFrame.clear();\n  lastUpdateLayoutTreeByFrame.clear();\n  eventToInitiatorMap.clear();\n  initiatorToEventsMap.clear();\n  webSocketCreateEventsById.clear();\n  schedulePostTaskCallbackEventsById.clear();\n}\n\nfunction storeInitiator(data: {initiator: Types.Events.Event, event: Types.Events.Event}): void {\n  eventToInitiatorMap.set(data.event, data.initiator);\n  const eventsForInitiator = initiatorToEventsMap.get(data.initiator) || [];\n  eventsForInitiator.push(data.event);\n  initiatorToEventsMap.set(data.initiator, eventsForInitiator);\n}\n\n/**\n * IMPORTANT: Before adding support for new initiator relationships in\n * trace events consider using Perfetto's flow API on the events in\n * question, so that they get automatically computed.\n * @see {@link flowsHandlerData}\n *\n * The events manually computed here were added before we had support\n * for flow events. As such they should be migrated to use the flow\n * API so that no manual parsing is needed.\n */\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isScheduleStyleRecalculation(event)) {\n    lastScheduleStyleRecalcByFrame.set(event.args.data.frame, event);\n  } else if (Types.Events.isUpdateLayoutTree(event)) {\n    // IMPORTANT: although the trace event is called UpdateLayoutTree, this\n    // represents a Styles Recalculation. This event in the timeline is shown to\n    // the user as \"Recalculate Styles.\"\n    if (event.args.beginData) {\n      // Store the last UpdateLayout event: we use this when we see an\n      // InvalidateLayout and try to figure out its initiator.\n      lastUpdateLayoutTreeByFrame.set(event.args.beginData.frame, event);\n\n      // If this frame has seen a ScheduleStyleRecalc event, then that event is\n      // considered to be the initiator of this StylesRecalc.\n      const scheduledStyleForFrame = lastScheduleStyleRecalcByFrame.get(event.args.beginData.frame);\n      if (scheduledStyleForFrame) {\n        storeInitiator({\n          event,\n          initiator: scheduledStyleForFrame,\n        });\n      }\n    }\n  } else if (Types.Events.isInvalidateLayout(event)) {\n    // By default, the InvalidateLayout event is what triggered the layout invalidation for this frame.\n    let invalidationInitiator: Types.Events.Event = event;\n\n    // However, if we have not had any prior invalidations for this frame, we\n    // want to consider StyleRecalculation events as they might be the actual\n    // cause of this layout invalidation.\n    if (!lastInvalidationEventForFrame.has(event.args.data.frame)) {\n      // 1. If we have not had an invalidation event for this frame\n      // 2. AND we have had an UpdateLayoutTree for this frame\n      // 3. AND the UpdateLayoutTree event ended AFTER the InvalidateLayout startTime\n      // 4. AND we have an initiator for the UpdateLayoutTree event\n      // 5. Then we set the last invalidation event for this frame to be the UpdateLayoutTree's initiator.\n      const lastUpdateLayoutTreeForFrame = lastUpdateLayoutTreeByFrame.get(event.args.data.frame);\n      if (lastUpdateLayoutTreeForFrame) {\n        const {endTime} = Helpers.Timing.eventTimingsMicroSeconds(lastUpdateLayoutTreeForFrame);\n        const initiatorOfUpdateLayout = eventToInitiatorMap.get(lastUpdateLayoutTreeForFrame);\n\n        if (initiatorOfUpdateLayout && endTime && endTime > event.ts) {\n          invalidationInitiator = initiatorOfUpdateLayout;\n        }\n      }\n    }\n    lastInvalidationEventForFrame.set(event.args.data.frame, invalidationInitiator);\n  } else if (Types.Events.isLayout(event)) {\n    // The initiator of a Layout event is the last Invalidation event.\n    const lastInvalidation = lastInvalidationEventForFrame.get(event.args.beginData.frame);\n    if (lastInvalidation) {\n      storeInitiator({\n        event,\n        initiator: lastInvalidation,\n      });\n    }\n    // Now clear the last invalidation for the frame: the last invalidation has been linked to a Layout event, so it cannot be the initiator for any future layouts.\n    lastInvalidationEventForFrame.delete(event.args.beginData.frame);\n  } else if (Types.Events.isWebSocketCreate(event)) {\n    webSocketCreateEventsById.set(event.args.data.identifier, event);\n  } else if (Types.Events.isWebSocketInfo(event) || Types.Events.isWebSocketTransfer(event)) {\n    const matchingCreateEvent = webSocketCreateEventsById.get(event.args.data.identifier);\n    if (matchingCreateEvent) {\n      storeInitiator({\n        event,\n        initiator: matchingCreateEvent,\n      });\n    }\n  } else if (Types.Events.isSchedulePostTaskCallback(event)) {\n    schedulePostTaskCallbackEventsById.set(event.args.data.taskId, event);\n  } else if (Types.Events.isRunPostTaskCallback(event) || Types.Events.isAbortPostTaskCallback(event)) {\n    const matchingSchedule = schedulePostTaskCallbackEventsById.get(event.args.data.taskId);\n    if (matchingSchedule) {\n      storeInitiator({event, initiator: matchingSchedule});\n    }\n  }\n}\n\nfunction createRelationshipsFromFlows(): void {\n  const flows = flowsHandlerData().flows;\n  for (let i = 0; i < flows.length; i++) {\n    const flow = flows[i];\n    for (let j = 0; j < flow.length - 1; j++) {\n      storeInitiator({event: flow[j + 1], initiator: flow[j]});\n    }\n  }\n}\n\nfunction createRelationshipsFromAsyncJSCalls(): void {\n  const asyncCallEntries = AsyncJSCallsHandlerData().schedulerToRunEntryPoints.entries();\n  for (const [asyncCaller, asyncCallees] of asyncCallEntries) {\n    for (const asyncCallee of asyncCallees) {\n      storeInitiator({event: asyncCallee, initiator: asyncCaller});\n    }\n  }\n}\n\nexport async function finalize(): Promise<void> {\n  createRelationshipsFromFlows();\n  createRelationshipsFromAsyncJSCalls();\n}\n\nexport interface InitiatorsData {\n  eventToInitiator: Map<Types.Events.Event, Types.Events.Event>;\n  initiatorToEvents: Map<Types.Events.Event, Types.Events.Event[]>;\n}\n\nexport function data(): InitiatorsData {\n  return {\n    eventToInitiator: eventToInitiatorMap,\n    initiatorToEvents: initiatorToEventsMap,\n  };\n}\n\nexport function deps(): ['Flows', 'AsyncJSCalls'] {\n  return ['Flows', 'AsyncJSCalls'];\n}\n"]}