{"version": 3, "file": "AnimationHandler.js", "sourceRoot": "", "sources": ["../../../../../../../front_end/models/trace/handlers/AnimationHandler.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,KAAK,OAAO,MAAM,uBAAuB,CAAC;AACjD,OAAO,KAAK,KAAK,MAAM,mBAAmB,CAAC;AAE3C,MAAM,UAAU,GAA6B,EAAE,CAAC;AAChD,MAAM,yBAAyB,GAA0C,EAAE,CAAC;AAM5E,MAAM,UAAU,KAAK;IACnB,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACtB,yBAAyB,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAyB;IACnD,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QACpC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO;IACT,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,QAAQ;IAC5B,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,UAAU,CAAC,CAAC;IACrF,yBAAyB,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,OAAO;QACL,UAAU,EAAE,yBAAyB;KACtC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport * as Helpers from '../helpers/helpers.js';\nimport * as Types from '../types/types.js';\n\nconst animations: Types.Events.Animation[] = [];\nconst animationsSyntheticEvents: Types.Events.SyntheticAnimationPair[] = [];\n\nexport interface AnimationData {\n  animations: readonly Types.Events.SyntheticAnimationPair[];\n}\n\nexport function reset(): void {\n  animations.length = 0;\n  animationsSyntheticEvents.length = 0;\n}\n\nexport function handleEvent(event: Types.Events.Event): void {\n  if (Types.Events.isAnimation(event)) {\n    animations.push(event);\n    return;\n  }\n}\n\nexport async function finalize(): Promise<void> {\n  const syntheticEvents = Helpers.Trace.createMatchedSortedSyntheticEvents(animations);\n  animationsSyntheticEvents.push(...syntheticEvents);\n}\n\nexport function data(): AnimationData {\n  return {\n    animations: animationsSyntheticEvents,\n  };\n}\n"]}