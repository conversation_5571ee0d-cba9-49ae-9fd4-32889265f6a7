{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "<PERSON>ý tự đại diện (*) sẽ không bao gồm việc uỷ quyền khi xử lýAccess-Control-Allow-Headers CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Nền sử dụng thuộc tính disableRemotePlaybacknhằm tắt chế độ tích hợp <PERSON> mặc định thay vì sử dụng bộ chọn -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "<PERSON><PERSON><PERSON> trị của giao diện CSS slider-vertical không được chuẩn hoá và sẽ bị xoá."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "<PERSON><PERSON><PERSON> yêu cầu tài nguyên có URL chứa cả ký tự \\(n|r|t) khoảng trắng đã bị xoá và ký tự nhỏ hơn (<) sẽ bị chặn. Vui lòng xoá các dòng mới và mã hoá ký tự nhỏ hơn khỏi các vị trí như giá trị thuộc tính phần tử để tải những tài nguyên như vậy."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, hãy sử dụng API đã chuẩn hoá: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, hãy sử dụng API đã chuẩn hoá: <PERSON><PERSON>."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() không được dùng nữa; thay vào đ<PERSON>, h<PERSON>y sử dụng API đã chuẩn hoá: nextHopProtocol trong Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "<PERSON><PERSON> có chứa ký tự \\(0|r|n) sẽ bị từ chối thay vì cắt bớt."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "<PERSON><PERSON> thống không còn thiết lập document.domain để nới lỏng chính sách cùng nguồn gốc nữa và tính năng này sẽ bị tắt theo mặc định. Cảnh bảo về việc không dùng nữa này là dành cho quyền truy cập nhiều nguồn gốc đã được bật bằng cách thiết lập document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "<PERSON><PERSON><PERSON> năng kích hoạt window.alert qua các iframe trên nhiều nguồn gốc không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "<PERSON><PERSON><PERSON> năng kích hoạt window.confirm qua các iframe trên nhiều nguồn gốc không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Hỗ trợ về dữ liệu: Các URL trong SVGUseElement không được dùng nữa và sẽ bị loại bỏ trong tương lai."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() và watchPosition() không còn hoạt động trên các nguồn gốc không an toàn. Đ<PERSON> dùng t<PERSON>h năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() và watchPosition() không được dùng nữa trên các nguồn gốc không an toàn. Đ<PERSON> dùng tính năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() không còn hoạt động trên các nguồn gốc không an toàn. Đ<PERSON> dùng t<PERSON>h năng nà<PERSON>, bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<PERSON><PERSON> tìm thấy thẻ <h1> trong <article>, <aside>, <nav> hoặc <section> không có cỡ chữ được chỉ định. Kích cỡ của văn bản tiêu đề này sẽ thay đổi trong trình duyệt này trong thời gian sắp tới. Hãy truy cập vào https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 để biết thêm thông tin."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate không còn hoạt động. Thay vào đó, hãy sử dụng RTCPeerConnectionIceErrorEvent.address hoặc RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Định dạng này dùng cho yêu cầu cung cấp thông tin đăng nhập kỹ thuật số navigator.credentials.get() không còn được dùng nữa. <PERSON><PERSON> lòng cập nhật lệnh gọi để sử dụng định dạng mới."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "<PERSON><PERSON><PERSON>n gốc của bên bán và dữ liệu bất kỳ trong sự kiện canmakepayment của trình chạy dịch vụ không được dùng nữa và sẽ bị loại bỏ: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Trang web này đã yêu cầu một tài nguyên phụ qua một mạng mà trang web này chỉ truy cập được do có vị thế mạng đặc quyền của người dùng. Những yêu cầu như vậy làm lộ các thiết bị và máy chủ không công khai trên Internet, làm tăng nguy cơ bị tấn công giả mạo yêu cầu trên nhiều trang web (CSRF) và/hoặc rò rỉ thông tin. Để giảm thiểu những rủi ro này, Chrome sẽ ngừng yêu cầu các nguồn phụ không công khai khi được khởi tạo từ các ngữ cảnh không an toàn và sẽ bắt đầu chặn những yêu cầu đó."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Trường dailyUpdateUrl của InterestGroups đư<PERSON>c truyền đến joinAdInterestGroup() đã được đổi tên thành updateUrl để phản ánh chính xác hơn hành vi của trường này."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator không đư<PERSON>c dùng nữa. <PERSON>hay v<PERSON><PERSON> đ<PERSON>, h<PERSON><PERSON> sử dụng Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "<PERSON>h<PERSON>ng tải được CSS qua các URL file: trừ phi chúng kết thúc bằng đuôi tệp .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "<PERSON><PERSON> thống không dùng SourceBuffer.abort() để huỷ việc xoá khoảng không đồng bộ của remove() nữa do thay đổi về quy cách. <PERSON>u này chế độ hỗ trợ cũng sẽ bị xoá. Thay vào đó, bạn nên nghe sự kiện updateend. <PERSON>ục đích của abort() chỉ là huỷ lệnh thêm nội dung nghe nhìn không đồng bộ (asynchronous media append) hoặc đặt lại trạng thái của trình phân tích cú pháp."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Do thay đổi về quy cách, hệ thống không còn dùng chế độ đặt giá trị dưới dấu thời gian trình bày cao nhất cho MediaSource.duration nữa đối với mọi khung đã mã hoá và lưu vào vùng đệm. Chế độ hỗ trợ yêu cầu xoá tường minh cho nội dung phương tiện lưu trong bộ đệm bị cắt bớt sau này cũng sẽ bị xoá. Bạn nên triển khai lệnh remove(newDuration, oldDuration) tường minh trên mọi sourceBuffers, khi newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI sẽ yêu cầu cấp quyền sử dụng ngay cả khi MIDIOptions không chỉ rõ sysex."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Có thể Notification API không được dùng nữa qua các nguồn gốc không an toàn. Bạn nên cân nhắc việc chuyển ứng dụng sang một nguồn gốc an toàn, chẳng hạn như HTTPS. Xem https://goo.gle/chrome-insecure-origins để biết thêm thông tin."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "<PERSON><PERSON> thể quyền cho Notification API không còn được yêu cầu qua một iframe nhiều nguồn gốc nữa. Thay vào đó, bạn nên cân nhắc việc yêu cầu quyền qua một khung cấp cao hoặc mở một cửa sổ mới."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "<PERSON><PERSON> chọn imageOrientation: 'none' trong createImageBitmap không được dùng nữa. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng createImageBitmap bằng tuỳ chọn '{imageOrientation: 'from-image'}'."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> tác của bạn đang đàm phán một phiên bản (D)TLS đã lỗi thời. Vui lòng trao đổi với đối tác của bạn để khắc phục."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Việc chỉ định overflow: visible trên thẻ img, video và canvas có thể khiến các thẻ này tạo ra nội dung hình ảnh bên ngoài ranh giới thành phần. Xem tại https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments không được dùng nữa. <PERSON><PERSON> lòng sử dụng chế độ cài đặt đúng thời điểm cho trình xử lý thanh toán."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Lệnh gọi PaymentRequest của bạn đã bỏ qua chỉ thị connect-src của <PERSON><PERSON> sách bảo mật nội dung (CSP). Chế độ bỏ qua này không được dùng nữa. <PERSON><PERSON> lòng thêm giá trị nhận dạng phương thức thanh toán từ API PaymentRequest (trong trường supportedMethods) vào chỉ thị connect-src của CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent không còn hoạt động. <PERSON>hay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng navigator.storage đã chuẩn hoá."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> c<PERSON> phần tử mẹ <picture> là không hợp lệ nên sẽ bị bỏ qua. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame là phương thức có tiền tố nhà cung cấp. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng cancelAnimationFrame tiêu chuẩn."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame là phương thức có tiền tố nhà cung cấp. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng requestAnimationFrame tiêu chu<PERSON>n."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen không được dùng nữa. Thay vào đó, vui lòng sử dụng Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() không được dùng nữa. Thay v<PERSON><PERSON> đ<PERSON>, vui lòng sử dụng Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen không còn được dùng nữa. Thay vào đó, vui lòng sử dụng Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "<PERSON>úng tôi sẽ không dùng API chrome.privacy.websites.privacySandboxEnabled nữa. <PERSON><PERSON>, API này sẽ vẫn hoạt động nhằm đảm bảo khả năng tương thích ngược cho đến khi phát hành phiên bản M113. T<PERSON> vào đ<PERSON>, vui lòng sử dụng chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled và chrome.privacy.websites.adMeasurementEnabled. Truy cập vào https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "<PERSON>iều kiện hạn chế DtlsSrtpKeyAgreement đã bị xoá. Bạn đã chỉ định một giá trị false cho điều kiện hạn chế này, đ<PERSON><PERSON> được coi là nỗ lực sử dụng phương thức SDES key negotiation đã bị xoá. Chức năng này đã bị xoá; thay vào đó, hãy sử dụng một dịch vụ có hỗ trợ DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "<PERSON>i<PERSON>u kiện hạn chế DtlsSrtpKeyAgreement đã bị xoá. Bạn đã chỉ định một giá trị true cho điều kiện hạn chế này, việc này không có tác dụng gì nhưng bạn có thể xoá điều kiện hạn chế này cho gọn gàng."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Phương thức getStats() dựa trên lệnh gọi lại không được dùng nữa và sẽ bị loại bỏ. <PERSON><PERSON> v<PERSON><PERSON> đ<PERSON>, hãy sử dụng getStats() tuân thủ quy cách."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() không được dùng nữa. Thay vào đ<PERSON>, vui lòng sử dụng Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON> cầu tài nguyên phụ có URL chứa thông tin xác thực đượ<PERSON> nhún<PERSON> (ví dụ: **********************/) sẽ bị chặn."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Tuỳ chọn rtcpMuxPolicy không được dùng nữa và sẽ bị xoá."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer sẽ đòi hỏi việc tách biệt nhiều nguồn gốc. Xem https://developer.chrome.com/blog/enabling-shared-array-buffer/ để biết thêm thông tin."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "<PERSON><PERSON> không có hoạt động của người dùng, speechSynthesis.speak() không được dùng nữa và sẽ được loại bỏ."}, "generated/Deprecation.ts | UnloadHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> nghe sự kiện huỷ tải không dùng được nữa và sẽ bị loại bỏ."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON><PERSON><PERSON> tiện ích phải chọn sử dụng chế độ tách biệt nhiều nguồn gốc đẻ có thể tiếp tục sử dụng SharedArrayBuffer. Xem https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "<PERSON><PERSON><PERSON><PERSON> tính GPUAdapter isFallbackAdapter đã ngừng hoạt động. <PERSON><PERSON> v<PERSON><PERSON> đ<PERSON>, hãy sử dụng thuộc tính GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON phản hồi không hỗ trợ UTF-16 trong XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "T<PERSON>h năng XMLHttpRequest đồng bộ trên luồng chính không được dùng nữa do có tác động không tốt đối với trải nghiệm của người dùng cuối. <PERSON><PERSON> xem thêm thông tin trợ giúp, h<PERSON><PERSON> tham kh<PERSON>o https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Ảnh động"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "<PERSON><PERSON> cục thay đổi khi phần tử di chuyển mà không có sự tương tác của người dùng. [Hãy tìm hiểu nguyên nhân bố cục thay đổi](https://web.dev/articles/optimize-cls), chẳng hạn như phần tử được thêm, bị xoá hoặc phông chữ của phần tử thay đổi khi trang tải."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u phông chữ"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> thay đổi bố cục @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON> phát hiện ra nguyên nhân thay đổi bố cục"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON><PERSON><PERSON> có sự thay đổi bố cục"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> nhân làm thay đổi bố cục"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON> nhân làm thay đổi bố cục thường gặp nhất"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "<PERSON><PERSON><PERSON><PERSON> phần hình <PERSON>nh chưa xác định kích thước"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON> t<PERSON> nhất"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> thay đổi bố cục tệ nhất"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "<PERSON>h<PERSON><PERSON> gian tồn tạ<PERSON> (TTL) của bộ nhớ đệm"}, "models/trace/insights/Cache.ts | description": {"message": "Bộ nhớ đệm có thời gian hữu dụng dài có thể giúp tăng tốc số lượt truy cập lặp lại vào trang của bạn. [Tìm hiểu thêm](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "<PERSON>h<PERSON>ng yêu cầu nào có ch<PERSON>h sách bộ nhớ đệm không hiệu quả"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} mục kh<PERSON>c"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "<PERSON><PERSON> dụng thời gian hữu dụng của bộ nhớ đệm hiệu quả"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM lớn có thể làm các phép tính về kiểu và quy trình trình bày lại bố cục dài hơn, ảnh hưởng đến tốc độ phản hồi của trang. DOM lớn cũng sẽ làm tăng mức sử dụng bộ nhớ. [Tìm hiểu cách tránh kích thước DOM quá lớn](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "<PERSON><PERSON><PERSON> tử"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "<PERSON><PERSON><PERSON> hết trẻ em"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Chiều sâu DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "<PERSON><PERSON><PERSON><PERSON> kê"}, "models/trace/insights/DOMSize.ts | title": {"message": "<PERSON><PERSON><PERSON>u hoá kích thước DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON>ng số phần tử"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON> trị"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "<PERSON><PERSON><PERSON> cầu mạng đầu tiên là quan trọng nhất.  <PERSON><PERSON><PERSON><PERSON> độ trễ bằng cách tránh dùng đường liên kết chuyển hướng, đ<PERSON><PERSON> bả<PERSON> máy chủ phản hồi nhanh và bật tính năng nén văn bản."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON><PERSON> chuyển hướng ({PH1} chuyển hướng, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON><PERSON> chủ phản hồi chậm (theo quan sát là {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Không áp dụng tính năng nén"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON><PERSON> chủ phản hồ<PERSON> n<PERSON> (theo quan sát là {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "<PERSON><PERSON> dụng t<PERSON>h năng nén văn bản"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi của máy chủ"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "<PERSON><PERSON> tr<PERSON> khi yêu cầu tài liệu"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "<PERSON><PERSON><PERSON> x<PERSON>ng bản không n<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Số byte trùng lặp"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Xoá các mô-đun JavaScript trùng lặp lớn khỏi gói để giảm số byte mà hoạt động mạng tiêu thụ không cần thiết."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript trùng lặp"}, "models/trace/insights/FontDisplay.ts | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc đặt [font-display](https://developer.chrome.com/blog/font-display) thành swap hoặc optional để đảm bảo văn bản hiển thị nhất quán. <PERSON><PERSON> thể tối ưu hoá swap hơn nữa để giảm thiểu thay đổi bố cục bằng thuộc tính [ghi đè chỉ số phông chữ](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Phông chữ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "<PERSON><PERSON><PERSON> thị phông chữ"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON>h<PERSON><PERSON> gian bị lãng phí"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(<PERSON><PERSON> danh)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, thư<PERSON><PERSON> đọc hình học bố cục, b<PERSON><PERSON><PERSON> công cụ kết xuất phải tạm dừng thực thi tập lệnh để tính toán kiểu và bố cục. Tìm hiểu thêm về [chế độ buộc chỉnh lại luồng](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) và cách giảm thiểu tác động của chế độ này."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> ng<PERSON>n xếp"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Buộc chỉnh lại luồng"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "<PERSON><PERSON><PERSON> gọi hàm hàng đầu"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "T<PERSON>ng thời gian chỉnh lại luồng"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phân bổ]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Vi<PERSON><PERSON> giảm thời gian tải hình ảnh xuống có thể cải thiện thời gian tải trang và LCP được cảm nhận. [Tìm hiểu thêm về cách tối ưu hoá kích thước hình <PERSON>nh](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Ước tính là {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON><PERSON><PERSON><PERSON> có hình ảnh nào có thể tối ưu hoá"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON><PERSON>u ho<PERSON> kích thư<PERSON><PERSON> tệp"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} mục kh<PERSON>c"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "<PERSON><PERSON><PERSON> thiện vi<PERSON><PERSON> phân phối hình <PERSON>nh"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "<PERSON><PERSON><PERSON><PERSON> tăng hệ số nén của hình ảnh có thể cải thiện kích thước tải xuống của hình ảnh này."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Việc sử dụng định dạng hình ảnh hiện đại (WebP, AVIF) hoặc tăng độ nén của hình ảnh có thể cải thiện kích thước tải xuống của hình ảnh này."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Tệ<PERSON> hình <PERSON>nh này lớn hơn mức cần thiết ({PH1}) so với kích thước hiển thị ({PH2}). H<PERSON>y sử dụng hình ảnh thích ứng để giảm kích thước tải xuống của hình <PERSON>nh."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Việc sử dụng định dạng video thay vì GIF có thể cải thiện kích thước tải xuống của nội dung động."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "B<PERSON>t đầu kiểm tra giai đoạn dài nhất. [<PERSON><PERSON> thể giảm thiểu sự chậm trễ](https://web.dev/articles/optimize-inp#optimize_interactions). <PERSON><PERSON> giảm thời gian xử lý, [hãy tối ưu hoá hiệu suất của luồng chín<PERSON>](https://web.dev/articles/optimize-long-tasks), thường là JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi nhập thông tin"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON><PERSON><PERSON><PERSON> phát hiện ra hoạt động tương tác nào"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Pha"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi trình bày"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> gian xử lý"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP theo pha"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Tối ưu hoá LCP (Nội dung lớn nhất hiển thị) bằng cách giúp trình duyệt [có thể tìm thấy](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) hình ảnh LCP dựa vào HTML ngay lập tức và [tránh tải từng phần](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "<PERSON><PERSON> áp dụng giá trị \"high\" củ<PERSON> thu<PERSON><PERSON> t<PERSON>h fetchpriority"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Bạn nên áp dụng fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ch<PERSON><PERSON> áp dụng phư<PERSON>ng thức tải từng phần"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "<PERSON><PERSON><PERSON>nh LCP được tải sau {PH1} kể từ thời điểm bắt đầu sớm nhất."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hiện ra LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Không phát hiện ra tài nguyên LCP vì LCP không phải là hình <PERSON>nh"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "<PERSON><PERSON> thể tìm thấy yêu cầu trong tài liệu ban đầu"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u <PERSON>"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Mỗi [giai đoạn có một chiến lược cải thiện cụ thể](https://web.dev/articles/optimize-lcp#lcp-breakdown). Tốt nhất là thời gian hiển thị LCP (Nội dung lớn nhất hiển thị) nên được dùng để tải các tài nguyên thay vì bị chậm trễ."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON>ộ tr<PERSON> khi hiển thị phần tử"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Trường p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hiện ra LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Pha"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON> tr<PERSON> khi tải tài nguyên"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON>h<PERSON><PERSON> lượng tải tài nguyên"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Th<PERSON><PERSON> gian cho byte đầu tiên"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP theo pha"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Số byte bị lãng phí"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Mã Polyfill và Transform cho phép các trình duyệt cũ sử dụng những tính năng mới của JavaScript. <PERSON><PERSON>, c<PERSON> nhiều tính năng không cần thiết cho trình duyệt hiện đại. Hãy cân nhắc sửa đổi quy trình xây dựng JavaScript để không chuyển đổi mã nguồn các tính năng [Đường cơ sở](https://web.dev/articles/baseline-and-polyfills), trừ phi bạn biết rằng mình phải hỗ trợ các trình duyệt cũ. [Tìm hiểu lý do hầu hết các trang web có thể triển khai mã ES6+ mà không cần chuyển đổi mã nguồn](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript cũ"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 và HTTP/3 mang lại nhiều lợi ích hơn HTTP/1.1, chẳng hạn như ghép kênh. [Tìm hiểu thêm về cách sử dụng HTTP hiện đại](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu nào sử dụng HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "<PERSON><PERSON><PERSON> th<PERSON>"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP hiện đại"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>c"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON><PERSON> gian"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> kiệm LCP ước t<PERSON>h"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i trướ<PERSON> chưa sử dụng. <PERSON><PERSON><PERSON> kiểm tra để đảm b<PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h crossorigin được sử dụng đúng cách."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Tr<PERSON><PERSON> tạo chuỗi các yêu cầu quan trọng](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) bằng cách giảm độ dài chuỗi, gi<PERSON>m kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Thêm gợi ý [kết nối trước](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) cho các nguồn gốc quan trọng nhất, nh<PERSON><PERSON> hãy cố gắng sử dụng không quá 4 gợi ý."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "<PERSON><PERSON> xuất kết nối trước"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON> trễ tối đa của đường dẫn quan trọng:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "<PERSON>h<PERSON>ng có tác vụ kết xuất nào chịu ảnh hưởng của phần phụ thuộc mạng"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "<PERSON><PERSON><PERSON><PERSON> có điểm gốc nào khác phù hợp để kết nối trước"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "kh<PERSON><PERSON> có điểm gốc nào đư<PERSON><PERSON> kết nối trước"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Gợi ý [kết nối trước](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) giúp trình duyệt thiết lập kết nối sớm hơn trong quá trình tải trang, nhờ đó tiết kiệm thời gian khi gửi yêu cầu đầu tiên đối với điểm gốc đó. Sau đây là những điểm gốc mà trang đã kết nối trước."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> gốc đ<PERSON><PERSON><PERSON> kết nối trước"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n phụ thuộc mạng"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON> phát hiện ra hơn 4 đường kết nối preconnect. Bạn nên dùng những đường kết nối này một cách hợp lý và chỉ dùng cho các điểm gốc quan trọng nhất."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "<PERSON><PERSON><PERSON> nối trước chưa sử dụng. Chỉ sử dụng preconnect cho những điểm gốc mà trang có khả năng yêu cầu."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "<PERSON>r<PERSON><PERSON> tạo chuỗi các yêu cầu quan trọng bằng cách giảm độ dài chuỗi, gi<PERSON><PERSON> kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "<PERSON><PERSON><PERSON> yêu cầu đang chặn quá trình kết xuất ban đầu của trang, điều này có thể làm trễ LCP. [Việc hoãn hoặc dùng cùng dòng](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) có thể di chuyển các yêu cầu mạng này ra khỏi đường dẫn quan trọng."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu chặn hiển thị cho thao tác điều hướng này"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "<PERSON><PERSON><PERSON> cầu chặn quá trình hiển thị"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Nếu thấy chi phí của T<PERSON>h toán lại kiểu vẫn cao, bạn có thể tối ưu hoá bộ chọn để giảm chi phí. [Tối ưu hoá bộ chọn](https://developer.chrome.com/docs/devtools/performance/selector-stats) có tỷ lệ phần trăm đường dẫn chậm và thời gian trôi qua đều cao. Bộ chọn đơn giản hơn, số lượng bộ chọn ít hơn, DOM nhỏ hơn và DOM nông hơn sẽ giúp giảm chi phí khớp."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Thời gian đã trôi qua"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu về bộ chọn CSS. Bạn cần bật số liệu thống kê về bộ chọn CSS trong phần cài đặt bảng điều khiển hiệu suất."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Số lần kh<PERSON>p"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Số lượng trùng khớp"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> c<PERSON><PERSON> chọn CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "<PERSON><PERSON> chọn hàng đầu"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Tổng"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON>h<PERSON>i gian thực thi trên chuỗi chính"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Mã của bên thứ ba có thể tác động đáng kể đến hiệu suất tải. [Giảm và trì hoãn việc tải mã của bên thứ ba](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) để ưu tiên nội dung trên trang của bạn."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bên thứ ba nào"}, "models/trace/insights/ThirdParties.ts | title": {"message": "bên thứ ba"}, "models/trace/insights/Viewport.ts | description": {"message": "<PERSON><PERSON>t động tương tác nhấn có thể [bị chậm tới 300 mili giây](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) nếu khung nhìn chưa được tối ưu hoá cho thiết bị di động."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "<PERSON><PERSON> trễ khi nhấn trên thiết bị di động"}, "models/trace/insights/Viewport.ts | title": {"message": "<PERSON><PERSON><PERSON>u hoá khung nhìn cho thiết bị di động"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Chỉ những trang được tải qua yêu cầu GET mới đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Chỉ những trang có mã trạng thái là 2XX mới có thể lưu vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome đã phát hiện thấy một lần thử thực thi JavaScript khi đang ở bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON>hững trang đã yêu cầu AppBanner hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do cờ. H<PERSON>y truy cập chrome://flags/#back-forward-cache để bật tính năng đó trên thiết bị này."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do dòng lệnh."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do thiếu bộ nhớ."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON> được uỷ quyền không hỗ trợ bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt đối với trình kết xuất trước."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON>h<PERSON>ng thể lưu trang này vào bộ nhớ đệm vì trang có một thực thể BroadcastChannel chứa các trình nghe đã đăng ký."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có tiêu đề cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Bộ nhớ đệm này đã bị xoá một cách có chủ đích."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm để cho phép lưu một trang khác vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "<PERSON>h<PERSON>ng trang có chứa trình bổ trợ hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Những trang sử dụng API FileChooser không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Những trang sử dụng API Truy cập hệ thống tệp không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Những trang sử dụng Trình điều phối thiết bị truyền thông không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON><PERSON>t trình phát đa phương tiện đang phát khi rời khỏi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Những trang sử dụng API MediaSession và đặt một trạng thái phát không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Những trang sử dụng API MediaSession và đặt các trình xử lý hành động hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do trình đọc màn hình."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Những trang sử dụng SecurityHandler không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Những trang sử dụng API nối tiếp không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Những trang sử dụng API WebAuthetication không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Những trang sử dụng API WebBluetooth không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Những trang sử dụng API WebUSB không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các cookie bị tắt trên một trang sử dụng Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Những trang sử dụng một trình xử lý hoặc worklet riêng hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "<PERSON><PERSON><PERSON> liệu này chưa tải xong trước khi rời khỏi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "App Banner đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON> quản lý mật khẩu của Chrome đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Quy trình lọc DOM đang diễn ra trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer đ<PERSON> xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các tiện ích sử dụng API nhắn tin."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "<PERSON><PERSON><PERSON> tiện ích có kết nối liên tục phải ngắt kết nối trước khi chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "<PERSON><PERSON><PERSON> tiện ích có kết nối liên tục đã cố gắng gửi thông báo đến các khung trong bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do các tiện ích."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "<PERSON>ộ<PERSON> thoại phư<PERSON> thức (chẳng hạn như gửi lại biểu mẫu) hoặc hộp thoại mật khẩu http đang xuất hiện trên trang trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Trang ngoại tuyến đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Thanh Out-Of-Memory Intervention đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "<PERSON><PERSON> yêu cầu câ<PERSON>p quyền trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON>r<PERSON><PERSON> chặn cửa sổ bật lên đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Thông tin chi tiết về công cụ Duyệt web an toàn đang xuất hiện trong khi rời đi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON>ông cụ Duyệt web an toàn coi trang này là một cửa sổ bật lên sai mục đích và bị chặn."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON>ột trình chạy dịch vụ đã đư<PERSON><PERSON> kích hoạt khi trang này đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "<PERSON><PERSON> tắt bộ nhớ đệm cho thao tác tiến/lùi do lỗi tài liệu"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON> lưu trữ được trang dùng FencedFrames vào bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm để cho phép lưu một trang khác vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON>ng trang đã cấp quyền truy cập luồng đa phương tiện hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có một số loại nội dung đư<PERSON><PERSON> nhúng nhất định (ví dụ: PDF) hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON>hững trang sử dụng IdleManager hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Những trang có kết nối IndexedDB mở hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do sự kiện IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "<PERSON><PERSON><PERSON> <PERSON> được sử dụng không đủ điều kiện."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Những trang có JavaScript được chèn vào bằng tiện ích hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Những trang có StyleSheet được chèn vào bằng tiện ích hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Lỗi nội bộ."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt do một số yêu cầu mạng JavaScript nhận được tài nguyên có tiêu đề Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt do có yêu cầu duy trì hoạt động."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Những trang sử dụng tính năng <PERSON>a bàn phím hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON>rang này chưa tải xong trước khi rời khỏi."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên ch<PERSON>h chứa cache-control:no-cache không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên ch<PERSON>h chứa cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Ho<PERSON>t động điều hướng đã bị huỷ trước khi có thể khôi phục trang từ bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm vì một kết nối mạng đang hoạt động đã nhận quá nhiều dữ liệu. Chrome giới hạn lượng dữ liệu mà một trang có thể nhận trong khi được lưu vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON>h<PERSON>ng trang có tính năng tìm nạp() hoặc XHR đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm cho thao tác tiến/lùi vì một yêu cầu mạng đang hoạt động đã dẫn đến lệnh chuyển hướng."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm vì một kết nối mạng ở trạng thái mở quá lâu. Chrome giới hạn khoảng thời gian mà một trang có thể nhận dữ liệu trong khi được lưu vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON><PERSON><PERSON> trang không có tiêu đề phản hồi hợp lệ không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON>t động điều hướng đã diễn ra trong một khung không phải khung ch<PERSON>h."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Trang có các giao dịch đang diễn ra trên cơ sở dữ liệu đã lập chỉ mục hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "<PERSON>h<PERSON>ng trang có một yêu cầu mạng đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON><PERSON> trang có một yêu cầu mạng tìm nạp đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "<PERSON>h<PERSON>ng trang có một yêu cầu mạng đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "<PERSON>hững trang có một yêu cầu mạng XHR đang tiến hành hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Những trang sử dụng PaymentManager hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Những trang sử dụng tính năng Hình trong hình hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "<PERSON>hững trang hiển thị Giao diện người dùng cho thao tác in hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Trang này đã được mở bằng \"window.open()\" và có một thẻ khác tham chiếu đến thành phần đó, hoặc trang này đã mở một cửa sổ."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "<PERSON><PERSON> trình kết xuất đồ hoạ cho trang trong bộ nhớ đệm cho thao tác tiến/lùi đã gặp sự cố."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "<PERSON><PERSON> trình kết xuất đồ hoạ cho trang trong bộ nhớ đệm cho thao tác tiến/lùi đã bị tắt."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON>hững trang đã yêu cầu quyền ghi âm hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền truy cập cảm biến hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Những trang đã yêu cầu quyền tìm nạp hoặc đồng bộ hoá dưới nền hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền MIDI hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON>hững trang đã yêu cầu quyền thông báo hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền truy cập bộ nhớ hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON>h<PERSON>ng trang đã yêu cầu quyền quay video hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Chỉ những trang có lược đồ URL là HTTP/HTTPS mới có thể lưu vào bộ nhớ đệm."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Trang này đã được một trình chạy dịch vụ xác nhận khi đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Một trình chạy dịch vụ đã cố gửi một MessageEvent cho trang trong bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker đã bị huỷ đăng ký khi một trang đang ở bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Trang này đã bị loại khỏi bộ nhớ đệm cho thao tác tiến/lùi do việc kích hoạt một trình chạy dịch vụ."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome đã khởi động lại và xoá các mục trong bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Những trang sử dụng SharedWorker hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Những trang sử dụng SpeechRecognizer hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Những trang sử dụng SpeechSynthesis hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Một iframe trên trang này đã bắt đầu một hoạt động điều hướng chưa hoàn tất."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên phụ chứa cache-control:no-cache không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON>h<PERSON>ng trang có tài nguyên phụ chứa cache-control:no-store không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Trang này đã vượt quá thời gian tối đa trong bộ nhớ đệm cho thao tác tiến/lùi và đã hết hạn."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Trang này đã hết thời gian chờ chuyển sang bộ nhớ đệm cho thao tác tiến/lùi (kh<PERSON> năng là do các trình xử lý ẩn trang chạy trong thời gian dài)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Trang này có một trình xử lý huỷ tải trong khung ch<PERSON>h."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Trang này có một trình xử lý huỷ tải trong khung phụ."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Tr<PERSON><PERSON> duyệt đã thay đổi tiêu đề ghi đè tác nhân người dùng."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON>hững trang đã cấp quyền quay video hoặc ghi âm hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Những trang sử dụng WebDatabase hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Những trang sử dụng WebHID hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Những trang sử dụng WebLocks hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Những trang sử dụng WebNfc hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "<PERSON>h<PERSON>ng trang sử dụng WebOTPService hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Những trang có WebRTC không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebRTC đã được dùng."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Những trang sử dụng WebShare hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Những trang có WebSocket không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebSocket đã đư<PERSON><PERSON> dùng."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Những trang có WebTransport không thể chuyển sang bộ nhớ đệm cho thao tác tiến/lùi."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Bộ nhớ đệm cho thao tác tiến/lùi bị tắt vì WebTransport đã đư<PERSON>c dùng."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Những trang sử dụng WebXR hiện không đủ điều kiện dùng bộ nhớ đệm cho thao tác tiến/lùi."}}