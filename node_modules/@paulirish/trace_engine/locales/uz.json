{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers q<PERSON><PERSON> is<PERSON><PERSON><PERSON><PERSON> (*) belgili hisob tekshiru<PERSON> ishl<PERSON>."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Birlamchi translatsiya integratsiyasini faolsizlantirishda -internal-media-controls-overlay-cast-button tanlagichi oʻrniga disableRemotePlayback atributi ishlatilishi lozim."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS koʻ<PERSON>sh qiymati slider-vertical standartlashtirilmagan va olib tashlanadi."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Boʻshliq va \\(n|r|t) belgilaridan iborat URL manzilli resurs soʻrovlari olib tashlandi va <dan kam belgidan iboratlari bloklandi. Bu resurslar yuklanishi uchun yangi qatorlarni olib tashlang va element atributi qiymatlaridan kam belgilarni shifrlang."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() eskirdi, uning oʻrniga standartlashtirilgan Navigation Timing 2 API ishlating."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() eskirdi, uning oʻrniga standartlashtirilgan Paint Timing API ishlating."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() eskirdi, uning oʻrniga standartlashtirilgan nextHopProtocol Navigation Timing 2 API ishlating."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Tarkibida \\(0|r|n) belgisi bor <PERSON>ie soʻrovlari qisqartirilma<PERSON>dan rad qilinadi."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain sozlamasi orqali bir xil manba siyosatining yumshatilishi eskirdi va standart holatda oʻchiq boʻladi. Ushbu eskirgan begona manbaga ruxsat ogohlantiruvi document.domain sozlamasi orqali yoqildi."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "<PERSON><PERSON>a manba ichki freymlaridan window.alert ch<PERSON><PERSON><PERSON><PERSON><PERSON> baja<PERSON>h eski<PERSON> va keyingi vers<PERSON><PERSON>an olib ta<PERSON>."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "<PERSON><PERSON>a manba ichki freymlaridan window.confirm ch<PERSON><PERSON><PERSON><PERSON><PERSON> baja<PERSON>h eskirdi va keyingi vers<PERSON><PERSON>an olib ta<PERSON>."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Axborotlar taʼminoti: SVG SVGUseElement eskirgan va kelasi versiyada olib tashlanadi."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "<PERSON>i him<PERSON>gan manabalarda getCurrentPosition() va watchPosition() ishlamaydi. Bu funksiyadan foydalanish uchun ilovangizni HTTPS kabi himoyalangan manbalarga oʻtkazing. Batafsil: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Himoyasiz manbalarda getCurrentPosition() va watchPosition() eskirdi. Bu funksiyadan foydalanish uchun ilovangizni HTTPS kabi himoyalangan manbalarga oʻtkazing. Batafsil: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "Endi himoyalan<PERSON>gan manbalarda getUserMedia() ishlamaydi. Bu funksiyadan foydalanish uchun ilovangizni HTTPS kabi himoyalangan manbalarga oʻtkazing. Batafsil: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> yoki <section> elementlari tarkibida shrift oʻlchami bel<PERSON> <h1> tegi aniqlandi. Kelgusida ushbu brauzerda mazkur sarlavha oʻlchami oʻzgaradi. Batafsil: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate tugatilgan. Oʻrniga RTCPeerConnectionIceErrorEvent.address yoki RTCPeerConnectionIceErrorEvent.port ishlating."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON> hisob ma<PERSON><PERSON><PERSON> olish uchun ishlati<PERSON>igan joriy “navigator.credentials.get()” soʻrov formati eskirgan. Soʻrov parametrlarida yangi formatdan foydalaning."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment service worker hodisasida savdo manbasi va ixti<PERSON><PERSON>y axborotlar ishlatilishi eskirgan va olib tashlanadi: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "<PERSON><PERSON> foydalanuvchilari imtiyoz asosida tartiblanadigan tarmoqdan quyi resurs soʻradi. Bunday soʻrovlar yopiq qurilma va serverlarni internetga oshkor qilishi va tinimsiz saytl<PERSON>ro soʻrovli (CSRF) hujumlar va/yoki axborot sizib chiqishi kabi xavfli oqibatlarga olib kelishi mumkin. Xavfni kamaytirish maqsadida Chrome himoyasiz kontekstlardan yopiq quyi manbalarga yuborilgan soʻrovlarni bekor qiladi va bloklaydi."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup() ga oʻtkazilgan InterestGroups ning dailyUpdateUrl maydoni updateUrl deb nomlandi, shunda uning funksiyalari aniqroq ifodalanadi."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator tugatilgan. Oʻrniga Intl.Segmenter ishlating."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "file: URL manzillardan .css kengaytmasiz CSS fayllar yuklanmaydi."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove()ning asinxron oraligʻini SourceBuffer.abort() orqali olib tashlash tavsif oʻzgarishiga koʻra eskirdi. Uning ishlatilishi keyingi versiyalardan olib tashlanadi. Oʻrniga updateend hodisasini kuzating. abort() faqat asinxron media kiritilishini yoki tahlil qilish vositasi holatini bekor qilish uchun ishlatiladi."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Tavsifdagi oʻzgarishga koʻra MediaSource.duration qiymatini buferdagi har qaysi shifrlangan freymning eng yuqori vaqt orasidan kam belgilash eskirdi. Keyingi versiyalarda buferdagi qisqartirilgan media fayllarning nofaol olib tashlanishi ishlamay qoladi. Oʻrniga newDuration < oldDuration holatlarda barcha sourceBuffers uchun faol remove(newDuration, oldDuration) ishlating."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Hatto MIDIOptions parametrida sysex qiymati belgilanmasa ham Web MIDI ishlatilishi uchun ruxsat soʻraladi."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Endi himoyasiz manbalarning Notification API soʻrovlari ishlamaydi. Ilovangizni HTTPS kabi himoyalangan manbalarga oʻtkazing. Batafsil: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Notification API ishlatishga ruxsat soʻrovi begona ichki freymdan kelmagan boʻlishi mumkin. Ruxsat soʻrovini yuqori darajali freym yoki yangi ochilgan oyna orqali yuboring."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap ichidagi imageOrientation: 'none' parametri eskirgan. Oʻrniga {imageOrientation: 'from-image'} parametrli createImageBitmap ishlating."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Hamkoringiz es<PERSON> (D)TLS versiyasi yuzasidan muzokara olib boryapti. Buni tuzatish uchun hamkoringizga murojaat qiling."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Rasm, video va kanvas teglarida overflow: visible bel<PERSON><PERSON><PERSON>, ular vizual kontentni element tashqarisida yaratishi mumkin. Batafsil: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments tugatilgan. Toʻlovlar bilan ishlash vositasi uchun soʻralganda oʻrnatish parametridan foydalaning."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest talabingiz Kontent xavfizligi siyosatinining (CSP) connect-src shartini chetlab oʻtdi. Bu chetlash usuli eskirgan. PaymentRequest API (supportedMethods qatori) toʻlov usuli identifikatoriga oʻz CSP connect-src shartingizni kiriting."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent tugatilgan. Oʻrniga standartlashtirilgan navigator.storage ishlating."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> tuzilmali <source src> yaroqsiz va inkor etiladi. Oʻrniga <source srcset> ishlating."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame usuli maʼlum bir provayderga bogʻlangan. Oʻrniga standart cancelAnimationFrame ishlating."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame usuli maʼlum bir provayderga bogʻlangan. Oʻrniga standart requestAnimationFrame ishlating."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen eskirgan. Oʻrniga Document.fullscreenElement ishlating."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() eskirgan. Oʻrniga Element.requestFullscreen() ishlating."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() eskirgan. Oʻrniga Element.requestFullscreen() ishlating."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() eskirgan. Oʻrniga Document.exitFullscreen() ishlating."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() eskirgan. Oʻrniga Document.exitFullscreen() ishlating."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen eskirgan. Oʻrniga Document.fullscreenEnabled ishlating."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled tug<PERSON><PERSON><PERSON><PERSON><PERSON>, lekin u M113 chiqarilgunga qadar eski versiyalarga moslik uchun faol qoladi. Oʻrniga chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled va chrome.privacy.websites.adMeasurementEnabled ishlating. Batafsil: https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement uslubi olib tashlandi. Sizda bu uslub uchun false qiymati belgilangan va olib tashlangan SDES key negotiation uslubidan foydalanishga urinish sifatida qabul qilinadi. Bu funksiyalar olib tashlang<PERSON>, ularning oʻrniga DTLS key negotiation bilan mos xizmatdan foydalaning."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement uslubi olib tashlandi. Sizda bu uslub uchun taʼsir qilmaydigan true qiymati bel<PERSON>, lekin aniqlik uchun bu uslubni olib tashlash mumkin."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() callback-funksiyasi es<PERSON> va olib tashlanadi. Buning oʻrniga spetsifikatsiyaga mos getStats() funksiyasidan foydalaning."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() eskirgan. Oʻrniga Selection.modify() ishlating."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON> joylangan hisob ma<PERSON> (**********************/ kabi) mavjud quyi resurs soʻ<PERSON>lari blokla<PERSON>."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy parametri eskirdi va olib tash<PERSON>."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer begona manba i<PERSON>lyatsiyasini talab qiladi. Batafsil: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Foydalanuvchi amalisiz speechSynthesis.speak() eskirdi va olib tashlanadi."}, "generated/Deprecation.ts | UnloadHandler": {"message": "<PERSON><PERSON><PERSON> an<PERSON>i yuk<PERSON>i ye<PERSON>sh eski<PERSON> va olib tash<PERSON>."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Kengaytmalar SharedArrayBuffer ishlatish uchun begona manba izolyatsiyasiga yozilishi kerak. Batafsil: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter atributi es<PERSON>, oʻrniga GPUAdapterInfo isFallbackAdapter atributidan foydalaning."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest json javobida UTF-16 kodlash ishlamaydi"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Asos<PERSON>y zanjirdagi sinxronlangan XMLHttpRequest eskirgan va foydalanuvchi tajribasiga salbiy taʼsir qilishi mumkin. Batafsil: https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animatsiya"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Maket oʻzgarishi sahifa elementlari foydalanuvchi ishtirokisiz harakatlanganda yuz beradi. Bunga sahifa yuklanishidagi elementlar qoʻshilishi yoki olinishi hamda shriftlarning oʻzgarishi sabab boʻlishi mumkin. [Maket oʻzgarishi sabablarini aniqlang](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Shrift soʻrovi"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON> o<PERSON><PERSON><PERSON> klasteri @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "<PERSON><PERSON><PERSON> o<PERSON>zgartirish culprits aniq<PERSON><PERSON>i"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON><PERSON> oʻ<PERSON> yoʻq"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sa<PERSON>"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Eng yomon klaster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Eng yomon maket oʻ<PERSON><PERSON><PERSON> klasteri"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Keshning amal qilish vaqti (TTL)"}, "models/trace/insights/Cache.ts | description": {"message": "Uzoq saqlanadigan kesh kelgusi tashriflarda sahifaning tezroq yuklanishiga yordam beradi. [Batafsil](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "<PERSON><PERSON><PERSON><PERSON> kesh q<PERSON>ri mavjud soʻ<PERSON> topilmadi"}, "models/trace/insights/Cache.ts | others": {"message": "yana {PH1} ta"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "<PERSON><PERSON><PERSON> samarali saq<PERSON> davrini tanlang"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM hajmi kattaligi sababli uslublarni hisoblash va maketni tuzish koʻproq vaqt olishi mumkin. Bu sahifa tezligiga taʼsir qiladi. Katta DOM xotira sarfini oshiradi. [Haddan ortiq DOM hajmiga yoʻl qoʻymaslik haqida batafsil](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> quyi <PERSON>"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM chuqurligi"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM hajmini optimallash"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Dastl<PERSON><PERSON> tarmoq soʻrovingiz — juda muhim.  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mat<PERSON><PERSON> siqish va server jav<PERSON><PERSON> tezlatish orqali kechik<PERSON><PERSON><PERSON>i kama<PERSON>."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Yoʻ<PERSON><PERSON><PERSON><PERSON><PERSON> ({PH1} yoʻ<PERSON><PERSON><PERSON><PERSON>, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Server sekin javob <PERSON> ({PH1} kuzatuvida)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Ar<PERSON>vlash tatbiq etiladi"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Server tez javob <PERSON> ({PH1} kuzatuvida)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "<PERSON><PERSON><PERSON> arxivlash tatbiq etiladi"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Server jav<PERSON><PERSON> vaqti"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>i kechikishi"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Siqilmagan faylni yuklab olish"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Manba"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Tarmoq faolligida sarflangan keraksiz baytlar sonini kamaytirish uchun paketlardan yirik va takrorlanuvchi JavaScript modullarini olib tashlang."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Takroriy JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Matn bir xil chiqishi uchun [font-display](https://developer.chrome.com/blog/font-display) uchun swap yoki optional belgilash tavsiya etiladi. [Shrift koʻrsatkichlarini qayta belgilash](https://developer.chrome.com/blog/font-fallbacks) orqali maket oʻzgarishlarini kamaytirish uchun qoʻshimcha tarzda swap <PERSON><PERSON><PERSON> mumkin."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Shrift"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Ishlatilgan shriftlar"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Yoʻqotilgan vaqt"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonim)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Aksariyat API tizimlar avval maket geometriyasini oʻqiydi va uslub va maketni hisoblash uchun renderlash tizimini skript ishga tushirishni toʻxtatishga undaydi. [<PERSON><PERSON><PERSON><PERSON> tuzish](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) va uning oldini olish haqida batafsil."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Majburiy reflow"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Eng koʻp vaqt oladigan chaqiruv"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "<PERSON><PERSON> maket tuzish vaqti"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[atribut<PERSON>z]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Rasm<PERSON><PERSON> yuklab olish vaqtini kamaytirish sahifaning qabul qilingan yuklash vaqtini va LCPni yaxshilashi mumkin. [Rasm oʻlchamini optimallash haqida batafsil](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Taxminan {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Optimallanadigan rasmlar yoʻq"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON> haj<PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "yana {PH1} ta"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Rasm <PERSON><PERSON><PERSON><PERSON> yaxshilash"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Ra<PERSON><PERSON> siqish koeffitsiyentini oshi<PERSON>h ushbu rasmni yuklab olish hajmini yaxshi<PERSON>i mumkin."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "<PERSON>amon<PERSON>y rasm formatidan (WebP, AVIF) foydalanish yoki tasvirni siqishni oshirish ushbu tasvirni yuklab olish hajmini yaxshi<PERSON>i mumkin."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON>u rasmli fayl koʻ<PERSON><PERSON>gan oʻlcha<PERSON> ({PH2}) uchun keragidan ({PH1}) kattaroq. Rasmlarni yuklab olish hajmini kamaytirish uchun mos<PERSON>u<PERSON>chan rasmlardan foydalaning."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFlar oʻrniga video formatlaridan foydalanish animatsiyali kontentni yuklab olish hajmini yaxshi<PERSON>i mumkin."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Tahlilni eng uzoq bosqichdan boshlang. [Kechikishl<PERSON><PERSON> kamaytirish mumkin](https://web.dev/articles/optimize-inp#optimize_interactions). Qayta ishlash davomiyligini qisqartirish uchun [asos<PERSON><PERSON> oqim xaraja<PERSON>ari<PERSON> optimallang](https://web.dev/articles/optimize-long-tasks). Odatda ular JS kodiga taalluqli."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "<PERSON><PERSON><PERSON> kechikishi"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON><PERSON> qanday muloqot an<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Bosqich"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP bosqichlari"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Bevosita HTML koddagi eng yirik tas<PERSON> [ko<PERSON><PERSON><PERSON><PERSON>](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) qilish orqali LCP metrikasni optimallang va [zarurat asosida yuklash ishlatmang](https://web.dev/articles/lcp-lazy-loading)."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high tatbiq qilindi"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high tatbiq qilish zarur"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "z<PERSON><PERSON> as<PERSON>ida yuklash tatbiq qilin<PERSON>i"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP tasviri eng erta bosh<PERSON>ich nuqtadan {PH1} keyin yuk<PERSON>."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP aniqlanmadi"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP rasm emasligi sababli hech qanday LCP resursi aniq<PERSON>i"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Soʻrovni ilk hujjatdan topish mumkin"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP va aloqador soʻrovlar axboroti"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON>r bosqichda [yax<PERSON><PERSON> strategiyalari mavjud](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideal holatda LCP elementlarni chizish uchun kerakli vaqtning katta qismi kechikishga emas, resurslarni yuklashga sarflanishi kerak."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75-<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP aniqlanmadi"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Bosqich"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON> kechikish"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON>"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Ilk bayt vaqti"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP bosqichlari"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polifill va transform parametrlari eskirgan brauzerlarga yangi JavaScript funksiyalaridan foydalanish imkonini beradi. Ammo, ular zamonaviy brauzerlarda talab etilmaydi. Eskirgan brauzerlarni dastaklash kerak bo<PERSON><PERSON>sa, JavaScript ishlab chiqish jarayonida [Baseline](https://web.dev/articles/baseline-and-polyfills) funksiyalarini transpil qilmaslikni hisobga oling. [Aksariyat saytlar nima uchun ES6+ kodni transpil qilmasdan ishlatishi mumkinligi haqida batafsil](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Eskirgan JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 va HTTP/3 HTTP/1.1 protokollariga nisbatan bir qancha afzalliklarga ega, masalan, multipleksing. [Zamonaviy HTTP protokolidan foydalanish haqida bat<PERSON>l](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 ishlatilgan soʻrovlar topilmadi"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Zamonaviy HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Manbasi"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Manba"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Vaqt"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Taxminiy LCP tejamlar"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Ishlatilmagan oldindan ulanish. crossorigin atributi bexato ishl<PERSON><PERSON> tekshiring."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> soʻrovlar zanjirini yaratmaslikka urining](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains). Sahifa tezroq yuklanishi uchun soʻrovlar sonini qisqartiring, yuklanadigan resurslar hajmini kamaytiring yoki keraksiz resurslar yuk<PERSON><PERSON><PERSON> keyinga qoldiring."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Eng muhim man<PERSON> [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) bild<PERSON><PERSON><PERSON> kiritish mumkin, ammo 4 martadan ortiq ishlati<PERSON>sin"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "<PERSON><PERSON><PERSON> ulanishi mumkin"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Ziddiyatli manzil maksimal kechikishi:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "<PERSON><PERSON> qaysi renderlash vazifasiga tarmoq quyi soʻrovlari taʼsir qilmagan"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "<PERSON><PERSON><PERSON> ulniash uchun qoʻshimcha manbalar mavjud emas"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "hech qaysi manba old<PERSON>n ul<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) bildirgilari brauzerga sahifani yuklashda oldindan ulanish va manba ilk soʻrovini yuborganda vaqtni tejashga yordam beradi. Quyidagi manbalarda sahifa oldindan ulangan."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON> ulangan manbalar"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Tarmoq<PERSON>gi quyi soʻ<PERSON> da<PERSON>ti"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Kamida 4 preconnect ul<PERSON><PERSON> an<PERSON>i. Ulardan tejamkor va faqat eng muhim manbalar uchun foydalanish kerak."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Ishlatilmagan oldindan ulanish. preconnect faqat so<PERSON><PERSON> yub<PERSON>h eh<PERSON> baland sahifalarning manbalarida ishlatilsin."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Zid<PERSON>yatli soʻrovlar zanjirini yaratmaslikka urining. <PERSON><PERSON><PERSON> te<PERSON><PERSON> yuklanishi uchun soʻrovlar sonini qisqartiring, yuklanadigan resurslar hajmini kamaytiring yoki keraksiz resurslar yuk<PERSON><PERSON><PERSON> keyinga qoldiring."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Soʻ<PERSON><PERSON> sahifaning das<PERSON><PERSON><PERSON> renderlanishini bloklamoqda va LCP kechikishiga olib kelishi mumkin. [](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) alohida yoki bir qatorda joylan<PERSON>, ma<PERSON>kur tarmoq soʻrovlari ziddiyatli yoʻ<PERSON>dan olinishi mumkin."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Bu navigatsiya uchun renderni bloklash soʻrovlari yoʻq"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Renderni bloklagan soʻrovlar"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Uslublarni qayta hisoblash xarajatlari yuqori qolsa, selektorni optimallash ularni pasaytirishi mumkin. Katta vaqt davomida yuqori foizli slow-path bilan [selektorlarni optimallang](https://developer.chrome.com/docs/devtools/performance/selector-stats). Sodda va kamroq selektorlar hamda ixchamroq tuzilmali DOM yordamida muvofiq xarajatlarni kamaytirish mumkin."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Oʻtgan vaqt"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "<PERSON>ch qanday CSS selektor maʼlumotlari topilmadi. CSS selektor statistikasi samaradorlik paneli sozlamalarida yoqilishi zarur."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Moslik topishga urinishlar"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Mosliklar"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS selektor xarajatlari"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON> xizmat"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON> hajmi"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Tashqi xizmat kodi yuklash unumdorligiga sezilarli taʼsir qilishi mumkin. Sahifadagi kontent namoyishini tezlatish uchun [tashqi kodni qisqartiring va yuklanishini kechiktiring](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON><PERSON> kontent topil<PERSON>i"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "<PERSON>gar koʻrinadigan qismi mobil qurilmalar uchun optimallashtiril<PERSON>gan boʻlsa, teginishlar [300 ms gacha kechikishi](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) mumkin."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "<PERSON><PERSON> teginish kechikishi"}, "models/trace/insights/Viewport.ts | title": {"message": "<PERSON><PERSON> uchun koʻrinadigan qismini optimallash"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Faqatgina GET soʻrovi orqali yuk<PERSON>an sahi<PERSON>lar keshdan ortga qaytarilishi mumkin."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Faqatgina 2XX holat kodiga ega sahifalar keshlanishi mumkin."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome kesh ichidan JavaScript ishga tushirish urinishini aniqladi."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "App<PERSON><PERSON><PERSON> so<PERSON><PERSON><PERSON> yuboradigan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh bayroqcha tomonidan faolsizlantirildi. Uni bu qurilmada chrome://flags/#back-forward-cache sahi<PERSON><PERSON> or<PERSON>li mustaqil yoqish mumkin."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Ortga qay<PERSON>igan kesh buryuqlar qatoridan faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Xotira kamligi sababli ortga qaytadigan kesh faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh vakil taʼminoti bilan ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh <PERSON><PERSON>n <PERSON> uchun faolsiz<PERSON>ldi."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Tarkibida qayd q<PERSON>an kutish vositalariga ega BroadcastChannel namunasi borligi uchun sahifani keshlash imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Sarlavhasida cache-control:no-store parametri bor sahi<PERSON><PERSON> keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> kesh<PERSON> maq<PERSON> keshdan o<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Tarkibida plaginlari bor sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Fayllar tizimiga ruxsat API taʼminotidan foydalanuvchi sahifalarda hozircha ortga qaytadigan kesh is<PERSON>amaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media qurilmalar dispetcheridan foydalanadigan sahifalarda ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Boshqa sahifaga oʻtish vaqtida media pleyer ijrosi faol edi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API ishlatadigan va ijro holati belgilangan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API ishlatadigan va amal bajarish vositalari belgilangan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "<PERSON><PERSON><PERSON> kesh skrin rider <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler ishl<PERSON><PERSON><PERSON> sa<PERSON>a ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store ishlatuvchi sahifalarda cookielar yoniq emasligi uchun orqaga qaytadigan kesh faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Alohida worker yoki worklet interfeysi bor sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "<PERSON><PERSON><PERSON><PERSON> boshqa joyga oʻ<PERSON>dan oldin oxirigacha yuk<PERSON>i."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Boshqa sahifaga oʻtish vaqtida ilova banneri chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Boshqa sahifaga oʻtish vaqtida Chrome Parollar menejeri faol edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Boshqa sahifaga oʻtish vaqtida DOM distillash jarayoni faol edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Boshqa sahifaga oʻtish vaqtida DOM Distillanishini koʻrish vositasi chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh kengaytmalar xabarlashuv APIsidan foydalangani uchun faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Uzoq aloqada turuvchi kengaytmalar ortga qaytadigan keshga kirishdan avval aloqasini uzishi lozim."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Uzoq aloqada turuvchi kengaytmalar ortga qaytadigan keshdan freymlarga xabar yuborishga urindi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh kengaytmalar sababli faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Boshqa sahifaga oʻtish vaqtida http parol oynasi yoki shakl kiritish kabi modal oyna chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Boshqa sahifaga oʻtish vaqtida oflayn sahifa chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Boshqa sahifaga oʻtish vaqtida xotirada joy qolmaganda aralashuvchi panel chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Boshqa sahifaga oʻtish vaqtida ruxsat soʻrovlari faol edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Boshqa sahifaga oʻtish vaqtida pop-ap blokeri faol edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Boshqa sahifaga oʻtish vaqtida Saytlarni xavfsiz kezish tafsiloti chiqqan edi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Saytlarni xav<PERSON>iz kezish tizimi bu sahifani zararli hisobladi va undagi pop-ap oynani blokladi."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON>ga q<PERSON>igan keshdaligida service worker a<PERSON><PERSON><PERSON><PERSON> qili<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON> saba<PERSON>li ortga qaytadigan kesh faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames ishlatadigan sahifalar bfcache ichida <PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> kesh<PERSON> maq<PERSON> keshdan o<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Media oqimga ruxsat olgan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>amaydi."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "PDF fayllar kabi muayyan ichki joylangan kontentga ega sahifalar hozirda orqaga qaytiladigan keshdan foydalana o<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Ochiq Indekslangan axborot ombori ulanishlariga ega sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Ortga q<PERSON> kesh Indekslangan axborot ombori hodisasi sababli faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Ruxsat etilmagan API taʼminotlar ishlatildi."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Tarkibiga kengaytma tomonidan JavaScript qiymatlari kiritiladigan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Tarkibiga kengaytma tomonidan StyleSheet qiymatlari kiritiladigan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Ayrim <PERSON> tarmoq soʻrovi Cache-Control: no-store sarlavhali resursni qabul qilingani uchun Or<PERSON>ga qaytadigan kesh faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "<PERSON><PERSON><PERSON> q<PERSON> kesh keepalive soʻrovi tufayli faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Klaviatura qulfidan foydalanigan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON><PERSON><PERSON> boshqa joyga oʻ<PERSON>dan oldin oxirigacha yuk<PERSON>i."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON> resursida cache-control:no-cache parametri bor sahi<PERSON><PERSON> keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON> resursida cache-control:no-store parametri bor sahi<PERSON>lar<PERSON> keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON> ortga qaytadigan keshdan qayta tiklanguncha navigatsiya bekor qilindi."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Faol tarmoq ulanishi ortiqcha axborot qabul qilgani sababli sahifa keshdan oʻ<PERSON>. Chrome sahifalarning kesh<PERSON>sh vaqtida qabul qilishi mumkin axborot hajmini cheklaydi."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Harakatdagi fetch() yoki XHR tarmoq soʻrovlari bor sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Faol tarmoq soʻrovi qayta yoʻ<PERSON><PERSON><PERSON>ishi sababli sahifa ortga qaytadigan keshdan oʻchirildi."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Tarmoq ulanishi juda uzoq ochiq turgani sababli sahifa keshdan oʻ<PERSON>. Chrome sahifalarning keshlanish jarayonida qabul qilishi mumkin axborot vaqtini cheklaydi."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON><PERSON><PERSON> javob <PERSON> bo<PERSON><PERSON><PERSON> sahi<PERSON>lar ortga qaytadigan keshga kiritil<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON> frey<PERSON>dan tashqari freymda oʻ<PERSON>h amali yuz berdi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Indekslangan axborot ombori tranzaksiyalari bor sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Harakatdagi tarmoq soʻrovlari bor sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Harakatdagi yuklash tarmoq soʻrovlari bor sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Harakatdagi tarmoq soʻrovlari bor sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Harakatdagi XHR tarmoq soʻrovlari bor sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON><PERSON> ustida tasvir rejimidagi sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Bosma interfeysni chiqaradigan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Sahifa window.open() yo<PERSON><PERSON><PERSON> ochildi va unga boshqa varaq yoʻnaltirilgan yoki sahifa oynani ochdi."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "<PERSON><PERSON><PERSON> q<PERSON> keshdagi sahifani renderlash jarayoni ishdan ch<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "<PERSON><PERSON><PERSON> q<PERSON> keshdagi sahifani renderlash jarayoni tugatildi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Audio yozib olishga ruxsat olmo<PERSON><PERSON> boʻlgan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Sensorlarga ruxsat olmo<PERSON><PERSON> bo<PERSON><PERSON>gan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Fonda sinxronlash yoki yuklashga ruxsat olmo<PERSON><PERSON> boʻ<PERSON>gan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI signallarga ruxsat olmo<PERSON><PERSON> boʻlgan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Bildirishnomalarga ruxsat olmo<PERSON><PERSON> boʻlgan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON>otira omboriga ruxsat olmo<PERSON><PERSON> boʻlgan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Video yozib olishga ruxsat olmo<PERSON><PERSON> boʻlgan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Faqat HTTP / HTTPS URL andozali sahifalar keshlanadi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON><PERSON> q<PERSON> keshdaligida service worker tomonidan talab qilindi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service worker or<PERSON><PERSON> q<PERSON> keshdagi sahifaga MessageEvent yuborishga urindi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "<PERSON><PERSON><PERSON> or<PERSON>ga qay<PERSON>igan kesh ichidaligida ServiceWorker roʻyxatdan olib tashlandi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Sahifa service worker a<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> ortga qay<PERSON> k<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome qaytadan ishga tushdi va ortga qaytadigan kesh qaydlari tozalandi."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> if<PERSON>e oʻ<PERSON>h amali<PERSON> b<PERSON>, lekin bu navigat<PERSON>ya yaku<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Quyi resursida cache-control:no-cache parametri bor sahi<PERSON><PERSON><PERSON> keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Quyi resursida cache-control:no-store parametri bor sahi<PERSON><PERSON><PERSON> keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON><PERSON><PERSON> ortga qaytadigan keshda boʻlishi uchun ajratilgan maksimal vaqti oʻtib ketdi va uning muddati tugadi."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Sahi<PERSON><PERSON> ortga qaytadigan keshga kirishga ulgurmayapti (sa<PERSON>fani berkitadigan vositalar uzoq ishlayotgani uchun boʻlishi mumkin)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "<PERSON><PERSON><PERSON> as<PERSON>y f<PERSON>da yuklanishni yechish vositasiga ega."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "<PERSON><PERSON><PERSON> quyi freymda yuklanishni yechish vositasiga ega."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON>r sarl<PERSON><PERSON><PERSON> foydalanuvchi agenti qiymati oʻzgartirildi."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Video yoki audio yozib olishga ruxsat olgan sahifalarda hozircha ortga qaytadigan kesh is<PERSON>i."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService ishlatadigan sahifalarda hozircha ortga qaytadigan kesh (bfcache) ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC sahifalarini keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Ortga qaytadigan kesh WebRTC ishlatilgani uchun faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare ishlatadigan sahifalarda hozircha ortga qaytadigan kesh ishlamaydi."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket protokolidagi sahifalarini keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Ortga qay<PERSON>igan kesh WebSocket ishlatilgani uchun faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport protokolidagi sahifalarini keshdan ortga qaytarish imkonsiz."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Ortga qaytadigan kesh WebTrasport ishlatilgani uchun faolsizlantirildi."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR ishlatadigan sahifalarida hozircha ortga qaytadigan kesh ishl<PERSON>ydi."}}