{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "A CORS Access-Control-Allow-Headers kezelésében a hitelesítést nem fogja fedni a helyettesítő karakter (*)."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Az -internal-media-controls-overlay-cast-button v<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>ett a disableRemotePlayback attribútumot kell használni az alapértelmezett Cast-integráció letiltásához."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "A(z) slider-vertical CSS-megjelenítési érték nem szabványosított, ez<PERSON><PERSON> el fogjuk távolítani."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Le vannak tiltva azok az erőforráskérések, amelyek esetében az URL-ek eltávolított \\(n|r|t) térközkaraktereket és kisebb mint karaktereket (<) is tartalmaznak. Az ilyen erőforrások betöltése érdekében távolítsa el az „új sor” jelöléseket, és kódolja a kisebb mint karaktereket az olyan helyeken, mint az elemek attribútumértékei."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Az chrome.loadTimes() függvényt megszüntettük. Használja helyette a szabványosított Navigation Timing 2 API-t."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "A chrome.loadTimes() függvényt megszüntettük, haszná<PERSON><PERSON> helyette a következő szabványosított API-t: <PERSON><PERSON> Tim<PERSON>."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "A chrome.loadTimes() függvényt megszüntettük, haszná<PERSON>ja helyette a következőt a szabványosított Navigation Timing 2 API keretein belül: nextHopProtocol."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "A \\(0|r|n) karaktert tartalmazó cookie-kat a böngésző csonkolás helyett el fogja utasítani."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Az ugyanarra az eredetre vonatkozó házirendnek a document.domain beállításával történő enyhítésére szolgáló lehetőséget megszüntettük, és alapértelmezés szerint le lesz tiltva. Ez a megszüntetési figyelmeztetés azokra az eredeteken átívelő hozzáférésekre érvényes, amelyek a document.domain beállításával lettek engedélyezve."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "A window.alert eredeteken átívelő iframe-ekből való aktiválásának lehetőségét megszüntettük, és a jövőben el fogjuk távolítani."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "A window.confirm eredeteken átívelő iframe-ekből való aktiválásának lehetőségét megszüntettük, és a jövőben el fogjuk távolítani."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Adatok támogatása: az SVGUseElement elemben lévő URL-ek elavultak, és a jövőben el fogjuk távolítani őket."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "A getCurrentPosition() és watchPosition() függvények többé nem működnek nem biztonságos eredetek esetében. A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "A következő függvényeket megszüntettük a nem biztonságos eredetek esetében: getCurrentPosition() és watchPosition(). A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "A getUserMedia() függvény többé nem működik nem biztonságos eredetek esetében. A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Tal<PERSON>lt<PERSON> egy <h1> cí<PERSON><PERSON><PERSON><PERSON> egy <article>, <aside>, <nav> vagy <section> c<PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON><PERSON> nincs megad<PERSON> a betűméret. A címsor szövegének mérete nemsokára megváltozik ebben a böngészőben. További információ: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "A(z) RTCPeerConnectionIceErrorEvent.hostCandidate elemet megszüntettük. Használja helyette a következők valamelyikét: RTCPeerConnectionIceErrorEvent.address vagy RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "A digitális hitelesítési adatokra vonatkozó navigator.credentials.get()-ké<PERSON>s ezen formátuma elavult. Módosítsa a hívást az új formátum használatához."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "A kereskedői eredetet és a(z) canmakepayment service worker-eseményből származó tetszőleges adatokat megszüntettük, és eltávolítjuk őket: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "A webhely olyan hálózatból igényelt alerőforrást, amelyhez csak a felhasználói kiemelt hálózati pozíciója miatt tudott hozzáférni. Az ilyen kérelmek láthatóvá teszik a nem nyilvános eszközöket és szervereket az interneten, és így növelik a webhelyközi kérések hamisításával (CSRF) elkövetett támadások és az információszivárgás kockázatát. A kockázatok csökkentése érdekében a Chrome megszünteti a nem nyilvános részerőforrásokhoz irányuló olyan kérelmeket, amelyeket nem biztonságos környezetből indítanak, és elkezdi blokkolni őket."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "A(z) InterestGroups joinAdInterestGroup() struktúrának átadott dailyUpdateUrl mezőjét átnevezte a rendszer a következőre, hogy pontosabban kifejezze a viselkedését: updateUrl."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "A(z) Intl.v8BreakIterator paramétert megszüntettük. Használja helyette a következőt: Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "file: URL-ekből csak akkor lehet CSS-t betölteni, ha az URL-ek .css fájlkiterjesztésre végződnek."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Megszüntettük a SourceBuffer.abort() használatának lehetőségét a remove() függvény aszinkron tartományeltávolításának megszakítására a specifikációk változása miatt. A funkció támogatását is megszüntetjük a jövőben. Ebben az esetben érdemes meghallgatnia a következő eseményt:updateend. Az abort() csak arra szol<PERSON>l, hogy megszakítson egy aszinkron médiatartalom-hozzáfűzést, vagy hogy visszaállítsa az elemző állapotát."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "A specifikációk változása miatt megszüntettük annak lehetőségét, hogy a MediaSource.duration értékét a pufferelt és kódolt keretek legmagasabb prezentációs időbélyege alá lehessen beállítani. A csonkolt és pufferelt médiatartalmak implicit eltávolításának támogatását meg fogjuk szüntetni a jövőben. Helyette explicit remove(newDuration, oldDuration) műveletet hajtson végre az összes olyan sourceBuffers esetében, amelynél: newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "A webes MIDI még akkor is engedélyt kér a használatra, ha a SysEx nincs megadva itt: MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "A Notification API többé nem használható nem biztonságos helyek esetében. Fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "A Notification API-r<PERSON> <PERSON><PERSON><PERSON> engedélyt többé nem lehet eredeteken átívelő iframe-b<PERSON>l kérelmezni. <PERSON><PERSON><PERSON> inkább egy legfelső szintű kerettől kérjen enged<PERSON>, vagy nyisson meg egy új ablakot."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "A createImageBitmap imageOrientation: 'none' lehetősége megszűnt. A createImageBitmap metódust ehelyett az '{imageOrientation: 'from-image'}' lehetőséggel használja."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partnere egy elavult (D)TLS-verzióról tárgyal. <PERSON><PERSON><PERSON><PERSON>, hogy javítsa ki ezt a problémát."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Ha a(z) overflow: visible értéket adja meg az img, video és canvas címkéken, el<PERSON>fordulhat, hogy az elem határain kívül hoznak létre vizuális tartalmat. Lásd: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "Megszüntettük a következőt: paymentManager.instruments. Használja helyette az igény szerinti telepítést a fizetéskezelőkhöz."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "A(z) PaymentRequest hívás megkerülte a Tartalombiztonsági irányelvek (CSP) connect-src utasítását. Ez a megkerülés elavult. Adja hozzá a(z) PaymentRequest API-ban szereplő fizetési mód azonosítóját (a[z] supportedMethods mezőben) a(z) connect-src CSP-utasításhoz."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "A(z) StorageType.persistent elemet megszüntettük. <PERSON>z<PERSON><PERSON><PERSON> he<PERSON><PERSON> a szabványosított navigator.storage paramétert."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "A <picture> fölérendelt elemmel rendelkező <source src> elem <PERSON>, ez<PERSON>rt a rendszer figyelmen kívül hagyja. Használja helyette a következőt: <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "A webkitCancelAnimationFrame szolgáltatóspecifikus. A szabványos cancelAnimationFrame elemet használja he<PERSON>."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "A webkitRequestAnimationFrame szolgáltatóspecifikus. A szabványos requestAnimationFrame elemet használja he<PERSON>."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "A HTMLVideoElement.webkitDisplayingFullscreen elavult. Használja helyette a következőt: Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "A HTMLVideoElement.webkitEnterFullScreen() elavult. Az Element.requestFullscreen() elemet haszná<PERSON> he<PERSON>."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "A HTMLVideoElement.webkitEnterFullscreen() elavult. Az Element.requestFullscreen() elemet haszná<PERSON> he<PERSON>."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "A HTMLVideoElement.webkitExitFullScreen() elavult. A Document.exitFullscreen() elemet haszná<PERSON> he<PERSON>."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "A HTMLVideoElement.webkitExitFullscreen() elavult. A Document.exitFullscreen() elemet haszná<PERSON> he<PERSON>."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "A HTMLVideoElement.webkitSupportsFullscreen elavult. A Document.fullscreenEnabled elemet hasz<PERSON><PERSON><PERSON> he<PERSON>ette."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Megszüntetjük a(z) chrome.privacy.websites.privacySandboxEnabled API-t, de a visszamenőleges kompatibilitás érdekében az M113-as kiadásig aktív marad. Használja helyette a következők valamelyikét: chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled vagy chrome.privacy.websites.adMeasurementEnabled. Lásd: https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "A DtlsSrtpKeyAgreement korlátozást eltávolítottuk. Olyan false értéket adott meg ennél a korl<PERSON><PERSON>zásnál, amelyet a rendszer úgy értelmez, hogy az eltávolított SDES key negotiation metódust szeretné használni. Ezt a funkciót eltávolítottuk. Használjon helyette egy olyan s<PERSON>, amely támogat<PERSON> a DTLS key negotiation műveletet."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "A DtlsSrtpKeyAgreement korlátozást eltávolítottuk. Olyan true értéket adott meg ennél a korl<PERSON><PERSON><PERSON><PERSON>l, amely nem járt semmily<PERSON> hat<PERSON>, de a kód rendben tartása érdekében eltávolíthatja."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "A visszahíváson alapuló getStats() függvényt megszüntettük, és el fogjuk távolítani. Helyette a specifikációknak megfelelő getStats() függvényt használja."}, "generated/Deprecation.ts | RangeExpand": {"message": "A Range.expand() elavult. A Select.modify() elemet hasz<PERSON><PERSON> he<PERSON>."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Le vannak tiltva azok az alerőforrás-k<PERSON><PERSON>sek, amelyek U<PERSON>-jei beágyazott hitelesítési adatokat tartalmaznak (pl. **********************/)."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Az rtcpMuxPolicy lehetőséget megszüntettük, és el fogjuk távolítani."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "A SharedArrayBuffer objektum megköveteli az eltérő eredetek elkülönítését. További részletek: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "A felhasználói aktiválási nélküli speechSynthesis.speak() függvényt megszüntettük, és el fogjuk távolítani."}, "generated/Deprecation.ts | UnloadHandler": {"message": "A kiürítési eseményfigyelők elavultak, és el lesznek távolítva."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "A SharedArrayBuffer használatának folytatásához a bővítményeknél engedélyezni kell az eltérő eredetek elkülönítését. További információ: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "A GPUAdapter isFallbackAdapter attrib<PERSON><PERSON><PERSON> el<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>ette a GPUAdapterInfo isFallbackAdapter attribútumát."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Az XMLHttpRequest esetében az UTF-16 kódolást nem támogatja a válasz-JSON."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "A fő szálon futó szinkron XMLHttpRequest függvényt megszüntettük, mert hátr<PERSON>yo<PERSON> hatással van a végfelhasználói élményre. További információ: https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Az elrendezésmozgások akkor fordulnak elő, amikor az elemek bárminemű felhasználói interakció nélkül mozognak. [Vizsgálja meg a elrendezésmozgás okait](https://web.dev/articles/optimize-cls), például az elemeket hozzáadását, eltávolítását vagy az elrendezés betűtípusának változását az oldal betöltődésekor."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Betűtípus-kérelem"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON>e került <PERSON>"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Elrendezésmozgás klasztere ekkor: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "<PERSON><PERSON> észlelni az elrendezésmozgás okozóit"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Az elrendezésmozgás okozói"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Az elrendezésmozgás legfontosabb okai"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Legrosszabb klaszter"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Legrosszabb elrendezésmozgás klasztere"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Gyorsítótár-TTL"}, "models/trace/insights/Cache.ts | description": {"message": "Ha a gyorsítótárak <PERSON>lett<PERSON>ama ho<PERSON>zú, gyorsabb<PERSON> válnak az oldal későbbi ismételt megnyitásai. [További információ](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nincsenek nem hatékony gyorsítótár-házirenddel rendelkező kérelmek"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> {PH1} felhasznál<PERSON>"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Hatékony gyorsítótár-élettart<PERSON>"}, "models/trace/insights/DOMSize.ts | description": {"message": "A nagy méretű DOM megnövelheti a stílusszámítások és az elrendezés-újraszámítások időtartamát, ami befolyásolja az oldal reagálóképességét. A nagy méretű DOM megnöveli a memóriahasználatot is. [További információ a túl nagy méretű DOM elkerüléséről](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "A legtöbb alárendelt elem"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-mélység"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Jellemző"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM-m<PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Az első hálózati kérelem a legfontosabb.  Csökkentse a várakozási idejét az átirányítások elkerülésével, gyors szerverválasz biztosításával és a szövegtömörítés engedélyezésével."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Voltak átirányítások ({PH1} átirányítás, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "A szerver lassan vá<PERSON>lt ({PH1} észlelve)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "<PERSON><PERSON><PERSON> tömörí<PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Elkerüli az átirányításokat"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "A szerver gyorsan válaszol ({PH1} észlelve)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Szövegtömörítés alkalmazva"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Átirányítások"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Szerver válaszideje"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Dokumentumkérés várakozási ideje"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Ismétlődő bájtok"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Távolítsa el a csomagjaiból a nagy méretű, ismétlődően előforduló JavaScript-modulokat, hogy kevesebb hálózati forgalmat okozzanak."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Ismétlődő JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "A szöveg következetes láthatóságának biztosítása érdekében fontolja meg a [font-display](https://developer.chrome.com/blog/font-display) beállítását a következők egyikére: swap vagy optional. A swap tovább optimalizálható az elrendezésmozgások mérséklése érdekében a [betűméretmutató-felülbírálásokkal](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Betűtípus"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Betűtípus me<PERSON>"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Elvesztegetett idő"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(névtelen)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Sok API – jellemzően az elrendezés geometriáját olvasó API – a szkriptvégrehajtás szüneteltetésére kényszeríti a renderelőmotort a stílus és az elrendezés kiszámítása érdekében. További információ az [újraszámítás kényszerítéséről](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) és a lehetséges megoldásokról."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Veremkivonat"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Kényszerített újraszámítás"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Legidőigényesebb függvényhívás"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Teljes <PERSON>ámítási idő"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[nem <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "A képek letöltési idejének csökkentésével javíthatja az oldal és az LCP észlelt betöltési idejét. [További információ a képméret optimalizálásáról](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (be<PERSON><PERSON><PERSON> – {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON><PERSON><PERSON> k<PERSON>p"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> {PH1} felhasznál<PERSON>"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Képtovábbí<PERSON><PERSON> javít<PERSON>"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "A kép tömörítési tényezőjének növelésével javíthatja a kép letöltési méretét."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Modern képformátum (WebP, AVIF) használatával vagy képek tömörítésének növelésével javítható a kép letöltési mérete."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ez a képfájl nagyobb, mint amennyire a megjelenített méretéhez ({PH2}) szükség van ({PH1}). A kép letöltési méretének csökkentéséhez használjon reszponzív képeket."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Ha GIF-ek he<PERSON>ett videóformátumokat <PERSON>, azzal javíthatja az animált tartalom letöltési méretét."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "A leghosszabb fázissal kezdje a vizsgálatot. [A késések minimalizálhatók](https://web.dev/articles/optimize-inp#optimize_interactions). A feldolgozási idő csökkentése érdekében [optimalizálja a fő szál költségeit](https://web.dev/articles/optimize-long-tasks), gyakran a JS-t."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Időtar<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Bemeneti k<PERSON>és"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON><PERSON><PERSON>lt interakció"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Szakasz"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Prezentáció <PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Feldolgozás időtartama"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP szakaszonként"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimalizálja az LCP-t úgy, hogy az LCP-képet azonnal [felfedezhetővé](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) teszi a HTML-ben, é<PERSON> [el<PERSON><PERSON><PERSON> a késleltetett betöltést](https://web.dev/articles/lcp-lazy-loading)."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high alkalmazva"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "A fetchpriority=high attribútumot kell alkalmazni"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "késleltetett betöltés nincs alkalmazva"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Az LCP-kép a legkorábbi kezdőpont után {PH1} elteltével töltődött be."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "<PERSON>em <PERSON>tő LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "<PERSON><PERSON> észleltünk LCP-er<PERSON><PERSON><PERSON><PERSON><PERSON>, mert az LCP nem kép"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "A kérelem a kezdeti dokumentumban található"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP-kérelem felfedezése"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Minden [fázisnak megvannak a maga specifikus javítási stratégiái](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideális esetben az LCP-idő nagy részét az erőforrások betöltésére kell ford<PERSON>tani, nem pedig késésekre."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Időtar<PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Mező: 75. percentilis"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "<PERSON>em <PERSON>tő LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Szakasz"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Erőforrás betöltésének késése"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Erőforrás betöltési ideje"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP szakaszonként"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Szkript"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Elvesz<PERSON><PERSON><PERSON> b<PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "A polyfillek és az átalakítások lehetővé teszik, hogy a régi böngészők is használhassák az újabb JavaScript-funkciókat. A modern böngészők esetében azonban sok polyfill és átalakítás szükségtelen. Fontolja meg a JavaScript-buildkészítési folyamat módosítását, hogy az ne alakítsa át a [Baseline](https://web.dev/articles/baseline-and-polyfills) funkciókat, kivéve, ha tudja, hogy támogatnia kell a régi böngészőket. [További információ arról, hogy a legtöbb webhely miért tud átalakítás nélkül ES6+ kódot telepíteni](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Régi JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "A HTTP/2 és a HTTP/3 számos előnyt nyújt a HTTP/1.1-hez képest (ilyen például a multiplexálás). [További információ a modern HTTP használatáról](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Egy kérelem sem használta a HTTP/1.1 protokollt"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokoll"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Modern HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Becsült LCP-megtakarítás"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "<PERSON><PERSON> has<PERSON> elő<PERSON>tes kapcsolat. <PERSON><PERSON><PERSON><PERSON>, hogy megfelelően használja-e a crossorigin attribútumot."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Kerülje a kritikus kérések láncba fűzését](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) a láncok hosszának csökkentésével, a letöltött források méretének csökkentésével, vagy a felesleges források letöltésének késleltetésével, hogy javuljon az oldalbetöltés."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Adjon meg [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) tippeket a legfontosabb erede<PERSON><PERSON><PERSON><PERSON><PERSON>, de próbáljon meg négynél többet has<PERSON>i."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Előzetes kapcsolódás jelöltjei"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Kritikus elérési út maximális várakozási ideje:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "<PERSON><PERSON><PERSON> olyan me<PERSON> fela<PERSON>, amely<PERSON> befolyásolnak a hálózati függőségek"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> jó jel<PERSON>ltek lennének az előzetes kapcsolódásra"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ninc<PERSON><PERSON> elő<PERSON> ka<PERSON><PERSON> er<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Az [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) tippek segítenek a böngészőnek abban, hogy az oldal betöltésének korábbi szak<PERSON>z<PERSON><PERSON> létesí<PERSON> ka<PERSON>, így id<PERSON>t ta<PERSON>that meg, amikor elküldi az első kérést az adott eredethelynek. A következők azok az eredethelyek, amely<PERSON><PERSON> az oldal előre kapcsolódott."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Előzetesen ka<PERSON>olódott er<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Hálózati függőségi fa"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Több mint négy preconnect kapcsolat található. Ezeket érdemes csak ritkán has<PERSON>, a legfontosabb eredethelyekhez."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Nem használt előzetes kapcsolat. Csak az oldal által valószínűleg kérelmezett forrásokhoz használja a preconnect lehetőséget."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Kerülje a kritikus kérések láncba fűzését a láncok hosszának csökkentésével, a letöltött források méretének csökkentésével, vagy a felesleges források letöltésének késleltetésével, hogy javuljon az oldalbetöltés."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Kérelmek blokkolják az oldal kezdeti megjelenítését, ami késleltetheti az LCP-t. [Elhalasztás vagy a beágyazás](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) segítségével ezeket a hálózati kérelmeket el lehet távolítani a kritikus útvonalról."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Időtar<PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nincsenek rendereléstiltási kérelmek ehhez a navigációhoz"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Renderelést blokkoló kérelmek megjelenítése"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ha a stílus-újraszámítási költségek magasak maradnak, a választó optimalizálása csökkentheti őket. [Optimalizálja azokat a választókat](https://developer.chrome.com/docs/devtools/performance/selector-stats), amelyek magas eltelt idővel és magas lassú útvonali százalékkal rendelkeznek. Az egyszerűbb választók, a kevesebb választó, a kisebb DOM és a sekélyebb DOM használata egyaránt csökkenti az egyezési költségeket."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Eltelt idő"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nem tal<PERSON>lható CSS-választóhoz tartozó adat. A CSS-választó statisztikáit engedélyezni kell a teljesítménypanel beállításaiban."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Egyeztetési kísérletek"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Egyezések s<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS-választó költségei"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Legnépszerűbb választók"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Összesen"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Főszálon töltött idő"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON>dik fél"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Átvitel<PERSON> mé<PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "A harmadik felektől származó kódok jelentős hatással lehetnek a betöltés teljesítményére. [Csökkentse és késleltesse a harmadik féltől származó kód betöltését](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) az oldal tartalmának előrébb sorolása érdekében."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON> harmadi<PERSON> fél"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Harmadik felek"}, "models/trace/insights/Viewport.ts | description": {"message": "Ha a megjelenítési terület nincs optimalizálva mobilra, akkor a koppintási műveleteknél [akár 300 ms késés is jelentkez<PERSON>t](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Mobilos koppintás késleltetése"}, "models/trace/insights/Viewport.ts | title": {"message": "A megjelenítési terület mobilra való optimalizálása"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Kizárólag a GET metódussal betöltött oldalak alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Kizárólag a 2XX állapotkódú oldalak gyorsítótárazhatók."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "A Chrome JavaScript végrehajtására tett kísérletet észlelt, amíg az oldal a gyorsítótárban volt."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Az AppBannert kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "<PERSON>z előre-vissza gyorsítótárazást jelzők tiltják le. Keresse fel a chrome://flags/#back-forward-cache oldalt, ahol helyileg tudja erre az eszk<PERSON><PERSON><PERSON> von<PERSON> engedélyezni."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "<PERSON>z előre-vissza gyorsítótárazás le van tiltva a parancssorban."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "A rendszer letiltotta az előre-vissza gyorsítótáraz<PERSON>t, mert nem áll rendelkezésre elegendő memória."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "<PERSON>z előre-vissza gyorsítótárazás meghatalmazó <PERSON> nem támogatott."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Az előzetes megjelenítő számára le van tilt<PERSON> az előre-vissza gyorsítótárazás."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Az oldal gyorsítótárazása nem lehetséges, mert regisztrált figyelőkkel rendelkező BroadcastChannel-példánya van."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Azok az oldalak, am<PERSON><PERSON> fej<PERSON> tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "A gyorsítótár kiürítése szándékosan tö<PERSON>ént."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, hogy lehetővé tegye egy másik oldal gyorsítótárazását."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "A beépülő modult tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON><PERSON> megadva"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "A FileChooser API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "A File System Access API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "A Media Device Dispatchert haszná<PERSON>ó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Az oldal elhagyása közben médialejátszó j<PERSON> le tartalmat."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "A MediaSession API-t használó és lejátszási állapotot beállító oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "A MediaSession API-t használó és műveletkezelőket beállító oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Az előre-vissza gyorsítótárazás le van tiltva a képernyőolvasó miatt."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "A SecurityHandler kezelőt használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "A Serial API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "A WebAuthentication API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "A WebBluetooth API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "A WebUSB API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Az előre-vissza gyorsítótárazás le van tiltva, mert a cookie-k le vannak tiltva egy Cache-Control: no-store paramétert használó oldalon."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "A dedikált feldolgozót vagy workletet használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Nem fej<PERSON> be a dokumentum betöltése az oldal elhagyása előtt."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Alkalmazásszalag volt jelen az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Jelen volt a Chrome Jelszókezelő az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A DOM Distiller működésben volt az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "A DOM Distiller Viewer jelen volt az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Az előre-vissza gyorsítótárazás le van tiltva üzenetkezelési API-t használó bővítmények miatt."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "A hosszú élettartamú kapcsolattal rendelkező bővítményeknek be kell zárniuk a kapcsolatot az előre-vissza gyorsítótárazás megkezdése előtt."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Hosszú <PERSON> ka<PERSON>olattal rendelkező bővítményeknek próbáltak üzeneteket küldeni az előre-vissza gyorsítótárban található kere<PERSON>ez."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "<PERSON>z előre-vissza gyorsítótárazás le van tiltva a bővítmények miatt."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> (pl. űrlap újraküldésével vagy http-jels<PERSON><PERSON>val kapcsola<PERSON> párbeszédpanel) volt látható az oldalhoz az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Az offline oldal volt látható az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Az elfogyó memória miatti beavatkozás sávja jelen volt az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Engedélykérések jelentek meg az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Jelen volt előugró ablakokat blokkoló szoftver az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "A Biztonságos Böngészés részletei láthatók voltak az oldal elhagyásakor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "A Biztonságos Böngészés visszaélésszerűnek ítélte ezt az oldalt, és letiltotta az előugró ablakokat."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Egy service worker f<PERSON><PERSON><PERSON>, am<PERSON>g az oldal az előre-vissza gyorsítótárban volt."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Az előre-vissza gyorsítótárazás le van tilt<PERSON> dokumentumhiba miatt."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "A FencedFrame elemeket használó oldalak nem tárolhatók előre-vissza gyorsítótárazásban."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, hogy lehetővé tegye egy másik oldal gyorsítótárazását."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "A médiastreamhez hozzáférést biztosító oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "A bizonyos típusú beágyazott tartalmakat (például PDF-eket) tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Az IdleManager jelenleg nem alkalmas az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "A nyílt IndexedDB-kapcsolattal rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Az előre-vissza gyorsítótárazás le van tiltva IndexedDB-esemény miatt."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Nem megfelelő API-k használata."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Azok az <PERSON>, amelyekbe JavaScript van bővít<PERSON><PERSON><PERSON>, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Azok az <PERSON>, amelyekbe StyleSheet van bővít<PERSON><PERSON><PERSON>, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Belső hiba."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Az előre-vissza gyorsítótárazás le van tiltva, mert egyes JavaScript-hálózati kérelmek Cache-Control: no-store fejléccel rendelkező erőforrást eredményeztek."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "<PERSON>z előre-vissza gyorsítótárazás keepalive kérelem mi<PERSON> le <PERSON> tilt<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "A Billenntyűzár funkciót használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON>em fej<PERSON> be az oldal betöltése az oldal elhagyása előtt."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Azok az oldalak, amelyek fő erőforrása tartalmazza a cache-control:no-cache para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Azok az oldalak, amelyek fő erőforrása tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "A navigációt visszavonták, mielőtt az oldal visszaállítható lett volna az előre-vissza gyorsítótárazásból."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, mivel egy aktív hálózati kapcsolat túl sok adatot fogadott. A Chrome korlátozza az egyes oldalak által gyorsítótárazásuk közben fogadható adatok mennyiségét."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "A menet közbeni fetch() lekéréssel vagy XHR-rel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Az oldalt a rendszer kizárta az előre-vissza gyorsítótárból, mert egy aktív hálózati kérés átirányítást tartalmazott."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, mert túl sokáig volt nyitva egy hálózati kapcsolat. A Chrome korlátozza azt az időt, ameddig az egyes oldalak gyorsítótárazásuk közben adatokat fogadhat."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Azok az oldalak, amelyek nem rendelkeznek érvényes válasszal, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigáció tö<PERSON>ént a fő kerettől eltérő keretben."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "A folyamatban lévő indexelt DB-tranzakciókat tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "A menet közbeni hálózati kéréssel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "A menet közbeni lekérési hálózati kérést tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "A menet közbeni hálózati kéréssel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "A menet közbeni XHR-hálózati kérést tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "A PaymentManagert használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "A Kép a képben funkciót használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "A Nyomtatás felhasználói felületet megjelenítő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Az oldal megnyitása a „window.open()” <PERSON><PERSON><PERSON><PERSON>, és egy másik lap rá mutató hivatkozást tartalmaz, vagy az oldal ablakot nyitott meg."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Összeomlott az előre-vissza gyorsítótárban lévő oldal megjelenítési folyamata."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Az előre-vissza gyorsítótárban lévő oldallal kapcsolatos megjelenítési folyamatot leállította a rendszer."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "A hangrögzítésre engedélyt kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Azok az old<PERSON>k, <PERSON><PERSON><PERSON>khoz való hozzáférési jogosultságot k<PERSON>rtek, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "A háttérszinkronizálást kérő vagy lekérési jogosultságot kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Azok az <PERSON>k, amelyek <PERSON>-eszközökhöz való hozzáférési jogosultsá<PERSON> k<PERSON>rte<PERSON>, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Az értesítési engedélyt kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "A tárhelyhozzáférést kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "A videórögzítési engedélyt kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Csak azok az oldalak gyorsítótárazhatók, amelyek URL-sémája HTTP vagy HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON>z oldalt service worker <PERSON><PERSON><PERSON><PERSON><PERSON>, amíg az előre-vissza gyorsítótárazása folyamatban volt."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service worker fájl kísérelte meg elküldeni az előre-vissza gyorsítótárban lévő oldal számára a következőt: MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "A rendszer törölte a ServiceWorker regisztrációj<PERSON>t, amíg az oldal az előre-vissza gyorsítótárban volt."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON>z oldalt egy service worker fájl aktiválása miatt zárta ki a rendszer az előre-vissza gyorsítótárazásból."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "A <PERSON>rome <PERSON>, és kiürítette az előre-vissza gyorsítótárazott bejegyzéseket."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "A SharedWorkert használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "A SpeechRecognizer API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "A SpeechSynthesist használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Az oldalon lévő iframe navigációt indított, amely nem fejeződött be."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Azok az oldalak, am<PERSON><PERSON>forrása tartalmazza a cache-control:no-cache para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Azok az oldalak, am<PERSON><PERSON> alerőforrása tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Az oldal túllépte a előre-vissza gyorsítótárban eltölthető időt, és lejá<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Az oldal időtúllépést okozott az előre-vissza gyorsítótárazásakor (valószínűleg a sokáig futó pagehide kezelők miatt)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Az oldal fő keretében kiürítéskezelő található."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Az oldal valamelyik alkeretében kiürítéskezelő található."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "A böngésző megváltoztatta a felhasználói ügynök felülbírálási fejlécét."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Azok az <PERSON>k, am<PERSON><PERSON>férést adtak a videó vagy a hang rögzítéséhez, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "A WebDatabase-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "A WebHID-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "A WebLocksot használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "A WebNfc-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "A WebOTPService API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "A WebRTC-vel készített oldalak nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Az előre-vissza gyorsítótárazás le van tiltva a WebRTC használata miatt."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "A WebShare-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "A WebSocket használatával készített oldalak nem kerülhetnek az előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Az előre-vissza gyorsítótárazás le van tiltva a WebSocket használata miatt."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "A WebTransport API-t tartalmazó oldalak nem kerülhetnek előre-vissza gyorsítótárba."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Az előre-vissza gyorsítótárazás le van tiltva a WebTransport használata miatt."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "A WebXR-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}}