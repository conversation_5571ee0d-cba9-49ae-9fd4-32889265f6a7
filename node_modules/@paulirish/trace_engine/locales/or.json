{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headersର ପରିଚାଳନାରେ, ୱାଇଲ୍ଡକାର୍ଡ ଚିହ୍ନ (*) ଦ୍ୱାରା ଅଥୋରାଇଜେସନକୁ କଭର କରାଯିବ ନାହିଁ।"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ଡିଫଲ୍ଟ କାଷ୍ଟ ଇଣ୍ଟିଗ୍ରେସନକୁ ଅକ୍ଷମ କରିବା ପାଇଁ -internal-media-controls-overlay-cast-button ଚୟନକାରୀକୁ ବ୍ୟବହାର କରିବା ପରିବର୍ତ୍ତେ disableRemotePlayback ଆଟ୍ରିବ୍ୟୁଟକୁ ବ୍ୟବହାର କରାଯିବା ଉଚିତ।"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS ଏପିଏରେନ୍ସ ମୂଲ୍ୟ slider-vertical ଷ୍ଟାଣ୍ଡାର୍ଡାଇଜ୍ଡ ହୋଇନାହିଁ ଏବଂ ଏହାକୁ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ଯେଉଁ ରିସୋର୍ସ ଅନୁରୋଧଗୁଡ଼ିକର URLଗୁଡ଼ିକରେ ଉଭୟ କାଢ଼ି ଦିଆଯାଇଥିବା ହ୍ୱାଇଟସ୍ପେସ \\(n|r|t) କେରେକ୍ଟର ଏବଂ ଲେସ-ଦ୍ୟାନ କେରେକ୍ଟର (<), ଅନ୍ତର୍ଭୁକ୍ତ ଅଛି, ସେଗୁଡ଼ିକୁ ବ୍ଲକ କରାଯାଇଛି। ଏହି ରିସୋର୍ସଗୁଡ଼ିକୁ ଲୋଡ କରିବା ପାଇଁ ଦୟାକରି ନୂଆ ଲାଇନଗୁଡ଼ିକୁ କାଢ଼ି ଦିଅନ୍ତୁ ଏବଂ ଉପାଦାନର ଆଟ୍ରିବ୍ୟୁଟ ମୂଲ୍ୟ ପରି ସ୍ଥାନଗୁଡ଼ିକରୁ ଲେସ-ଦ୍ୟାନ କେରେକ୍ଟରଗୁଡ଼ିକୁ ଏନକୋଡ କରନ୍ତୁ।"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() ଅସମର୍ଥିତ ଅଟେ, ଏହା ପରିବର୍ତ୍ତେ ଷ୍ଟାଣ୍ଡାର୍ଡାଇଜ୍ଡ API: Navigation Timing 2 ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() ଅସମର୍ଥିତ ଅଟେ, ଏହା ପରିବର୍ତ୍ତେ ଷ୍ଟାଣ୍ଡାର୍ଡାଇଜ୍ଡ API: Paint Timing ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() ଅସମର୍ଥିତ ଅଟେ, ଏହା ପରିବର୍ତ୍ତେ Navigation Timing 2ରେ ଷ୍ଟାଣ୍ଡାର୍ଡାଇଜ୍ଡ API: nextHopProtocol ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n)ଟି କେରେକ୍ଟର ଥିବା କୁକୀକୁ ଛୋଟ କରିଦିଆଯିବା ପରିବର୍ତ୍ତେ ଅଗ୍ରାହ୍ୟ କରାଯିବ।"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domainକୁ ସେଟ କରି ସମାନ-ଅରିଜିନ ନୀତିକୁ କୋହଳ କରିବାକୁ ଅସମର୍ଥିତ କରାଯାଇଛି ଏବଂ ଏହାକୁ ଡିଫଲ୍ଟ ରୂପେ ଅକ୍ଷମ କରାଯିବ। ଏହି ଅସମର୍ଥିତ ଚେତାବନୀ, document.domainକୁ ସେଟ କରି ସକ୍ଷମ କରାଯାଇଥିବା ଏକ କ୍ରସ-ଅରିଜିନ ଆକ୍ସେସ ପାଇଁ ଅଟେ।"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "କ୍ରସ ଅରିଜିନ iframesରୁ window.alertକୁ ଟ୍ରିଗର କରିବାକୁ ଅସମର୍ଥିତ କରାଯାଇଛି ଏବଂ ଏହାକୁ ଭବିଷ୍ୟତରେ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "କ୍ରସ ଅରିଜିନ iframesରୁ window.confirmକୁ ଟ୍ରିଗର କରିବାକୁ ଅସମର୍ଥିତ କରାଯାଇଛି ଏବଂ ଏହାକୁ ଭବିଷ୍ୟତରେ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ଡାଟା ପାଇଁ ସମର୍ଥନ: SVGUseElementରେ ଥିବା URLଗୁଡ଼ିକ ଅସମର୍ଥିତ ଅଟେ ଏବଂ ଏଗୁଡ଼ିକୁ ଭବିଷ୍ୟତରେ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() ଏବଂ watchPosition() ଆଉ ଅସୁରକ୍ଷିତ ଅରିଜିନରେ କାମ କରେ ନାହିଁ। ଏହି ଫିଚର ବ୍ୟବହାର କରିବାକୁ, ଆପଣ ଆପଣଙ୍କ ଆପ୍ଲିକେସନକୁ HTTPS ପରି ଏକ ସୁରକ୍ଷିତ ଅରିଜିନକୁ ସ୍ୱିଚ କରିବା ପାଇଁ ବିଚାର କରିବା ଉଚିତ। ଅଧିକ ବିବରଣୀ ପାଇଁ https://goo.gle/chrome-insecure-origins ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "ଅସୁରକ୍ଷିତ ଅରିଜିନରେ getCurrentPosition() ଏବଂ watchPosition() ଅସମର୍ଥିତ ଅଟେ। ଏହି ଫିଚର ବ୍ୟବହାର କରିବାକୁ, ଆପଣ ଆପଣଙ୍କ ଆପ୍ଲିକେସନକୁ HTTPS ପରି ଏକ ସୁରକ୍ଷିତ ଅରିଜିନକୁ ସ୍ୱିଚ କରିବା ପାଇଁ ବିଚାର କରିବା ଉଚିତ। ଅଧିକ ବିବରଣୀ ପାଇଁ https://goo.gle/chrome-insecure-origins ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ଆଉ ଅସୁରକ୍ଷିତ ଅରିଜିନରେ କାମ କରେ ନାହିଁ। ଏହି ଫିଚର ବ୍ୟବହାର କରିବାକୁ, ଆପଣ ଆପଣଙ୍କ ଆପ୍ଲିକେସନକୁ HTTPS ପରି ଏକ ସୁରକ୍ଷିତ ଅରିଜିନକୁ ସ୍ୱିଚ କରିବା ପାଇଁ ବିଚାର କରିବା ଉଚିତ। ଅଧିକ ବିବରଣୀ ପାଇଁ https://goo.gle/chrome-insecure-origins ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> କିମ୍ବା <section> ମଧ୍ୟରେ ଏକ <h1> ଟାଗ ମିଳିଛି, ଯାହାର କୌଣସି ନିର୍ଦ୍ଦିଷ୍ଟ ଫଣ୍ଟ-ସାଇଜ ନାହିଁ। ନିକଟ ଭବିଷ୍ୟତରେ ଏହି ବ୍ରାଉଜରରେ ଏହି ହେଡିଂ ଟେକ୍ସଟର ସାଇଜ ପରିବର୍ତ୍ତନ ହେବ। ଅଧିକ ସୂଚନା ପାଇଁ https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ RTCPeerConnectionIceErrorEvent.address କିମ୍ବା RTCPeerConnectionIceErrorEvent.port ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ଡିଜିଟାଲ କ୍ରେଡେନସିଆଲ ପାଇଁ navigator.credentials.get() ଅନୁରୋଧର ଏହି ଫର୍ମାଟକୁ ବନ୍ଦ କରିଦିଆଯାଇଛି, ଦୟାକରି ନୂଆ ଫର୍ମାଟ ବ୍ୟବହାର କରିବା ପାଇଁ ଆପଣଙ୍କ କଲକୁ ଅପଡେଟ କରନ୍ତୁ।"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment ସର୍ଭିସ ୱାର୍କର ଇଭେଣ୍ଟରୁ ମିଳିଥିବା ମାର୍ଚେଣ୍ଟ ଅରିଜିନ ଏବଂ ଆର୍ବିଟ୍ରାରି ଡାଟା ଅସମର୍ଥିତ ଅଟେ ଓ ଏହାକୁ କାଢ଼ି ଦିଆଯିବ: topOrigin, paymentRequestOrigin, methodData, modifiers।"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "ୱେବସାଇଟଟି, ଏହାର ଉପଯୋଗକର୍ତ୍ତାମାନଙ୍କର ବିଶେଷ ଅଧିକାରଯୁକ୍ତ ନେଟୱାର୍କ ସ୍ଥିତି ଯୋଗୁଁ, କେବଳ ଆକ୍ସେସ କରିପାରିବ ବୋଲି କୌଣସି ନେଟୱାର୍କରୁ ଏହା ଏକ ସବରିସୋର୍ସର ଅନୁରୋଧ କରିଛି। ଏହି ଅନୁରୋଧଗୁଡ଼ିକ ଅଣ-ସାର୍ବଜନୀନ ଡିଭାଇସ ଏବଂ ସର୍ଭରଗୁଡ଼ିକୁ ଇଣ୍ଟରନେଟରେ ପ୍ରକାଶ କରିଥାଏ, ଯାହା ଫଳରେ କ୍ରସ-ସାଇଟ ଅନୁରୋଧର ଜାଲିଆତି (CSRF) ଆକ୍ରମଣ ଏବଂ/କିମ୍ବା ସୂଚନାର ଲିକେଜର ରିସ୍କ ଅଧିକ ହୋଇଥାଏ। ଏହି ରିସ୍କଗୁଡ଼ିକୁ ହ୍ରାସ କରିବା ପାଇଁ, ଅଣ-ସୁରକ୍ଷିତ ପ୍ରସଙ୍ଗରୁ ଆରମ୍ଭ ହେବା ବେଳେ, ଅଣ-ସାର୍ବଜନୀନ ସବରିସୋର୍ସଗୁଡ଼ିକୁ କରାଯାଇଥିବା ଅନୁରୋଧଗୁଡ଼ିକୁ Chrome ଅସମର୍ଥିତ କରେ ଏବଂ ସେଗୁଡ଼ିକୁ ବ୍ଲକ କରିବା ଆରମ୍ଭ କରିବ।"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "ଏହାର ଆଚରଣକୁ ଅଧିକ ସଠିକ ଭାବେ ପ୍ରତିଫଳିତ କରିବା ପାଇଁ joinAdInterestGroup()କୁ ପାସ କରାଯାଇଥିବା InterestGroupsର dailyUpdateUrl ଫିଲ୍ଡକୁ updateUrlରେ ରିନେମ କରାଯାଇଛି।"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Intl.Segmenter ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS, .css ଫାଇଲ ଏକ୍ସଟେନସନ ସହ ସମାପ୍ତ ନହେବା ପର୍ଯ୍ୟନ୍ତ file: URLଗୁଡ଼ିକରୁ ଏହାକୁ ଲୋଡ କରାଯାଇପାରିବ ନାହିଁ।"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "ସ୍ପେସିଫିକେସନରେ ପରିବର୍ତ୍ତନ ଯୋଗୁଁ, SourceBuffer.abort()କୁ ବ୍ୟବହାର କରି remove()ର ଅସମକାଳୀନ ରେଞ୍ଜ ରିମୁଭାଲକୁ ରଦ୍ଦ କରିବାକୁ ଅସମର୍ଥିତ କରାଯାଇଛି। ଭବିଷ୍ୟତରେ ସମର୍ଥନକୁ କାଢ଼ି ଦିଆଯିବ। ଆପଣ ଏହା ପରିବର୍ତ୍ତେ updateend ଇଭେଣ୍ଟ ଶୁଣିବା ଉଚିତ। abort()ର ଉଦ୍ଦେଶ୍ୟ ହେଉଛି କେବଳ ଏକ ଅସମକାଳୀନ ମିଡିଆର ଯୋଗକୁ ରଦ୍ଦ କରିବା କିମ୍ବା ପାର୍ସର ସ୍ଥିତିକୁ ରିସେଟ କରିବା।"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "ସ୍ପେସିଫିକେସନରେ ପରିବର୍ତ୍ତନ ଯୋଗୁଁ, MediaSource.durationକୁ ଯେ କୌଣସି ବଫର ଥିବା କୋଡଯୁକ୍ତ ଫ୍ରେମର ସର୍ବାଧିକ ପ୍ରେଜେଣ୍ଟେସନ ଟାଇମଷ୍ଟାମ୍ପର ତଳେ ସେଟ କରିବା ଅସମର୍ଥିତ ଅଟେ। ଛୋଟ କରାଯାଇଥିବା ବଫର ଥିବା ମିଡିଆର ଅସ୍ପଷ୍ଟ ରିମୁଭାଲ ପାଇଁ ସମର୍ଥନକୁ ଭବିଷ୍ୟତରେ କାଢ଼ି ଦିଆଯିବ। ଏହା ପରିବର୍ତ୍ତେ, ଯେଉଁଠି newDuration < oldDuration, ସେଠାରେ ଆପଣ ସମସ୍ତ sourceBuffersରେ ସ୍ପଷ୍ଟ ଭାବରେ remove(newDuration, oldDuration) କରିବା ଉଚିତ।"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptionsରେ SysExକୁ ନିର୍ଦ୍ଦିଷ୍ଟ କରାଯାଇନଥିଲେ ମଧ୍ୟ ଏହାକୁ ବ୍ୟବହାର କରିବା ପାଇଁ Web MIDI ଅନୁମତି ମାଗିବ।"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification APIକୁ ଆଉ ଅସୁରକ୍ଷିତ ଅରିଜିନରୁ ବ୍ୟବହାର କରାଯିବ ନାହିଁ। ଆପଣ ଆପଣଙ୍କ ଆପ୍ଲିକେସନକୁ HTTPS ପରି ଏକ ସୁରକ୍ଷିତ ଅରିଜିନକୁ ସ୍ୱିଚ କରିବା ପାଇଁ ବିଚାର କରିବା ଉଚିତ। ଅଧିକ ବିବରଣୀ ପାଇଁ https://goo.gle/chrome-insecure-origins ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "ଏକ କ୍ରସ-ଅରିଜିନ iframeରୁ Notification API ପାଇଁ ଆଉ ଅନୁମତିର ଅନୁରୋଧ କରାଯାଇନପାରେ। ଏକ ଶୀର୍ଷ-ଲେଭେଲର ଫ୍ରେମରୁ ଅନୁମତିର ଅନୁରୋଧ କରିବା କିମ୍ବା ଏହା ପରିବର୍ତ୍ତେ ଏକ ନୂଆ ୱିଣ୍ଡୋ ଖୋଲିବା ବିଷୟରେ ଆପଣ ବିଚାର କରିବା ଉଚିତ।"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmapରେ ଥିବା imageOrientation: 'none' ବିକଳ୍ପ ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ '{imageOrientation: 'from-image'}' ବିକଳ୍ପ ସହିତ createImageBitmap ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "ଆପଣଙ୍କ ପାର୍ଟନର ଏକ ଅପ୍ରଚଳିତ (D)TLS ସଂସ୍କରଣ ବିଷୟରେ ଆଲୋଚନା କରୁଛନ୍ତି। ଏହାର ସମାଧାନ କରିବାକୁ ଦୟାକରି ଆପଣଙ୍କ ପାର୍ଟନରଙ୍କ ସହ ଯୋଗାଯୋଗ କରନ୍ତୁ।"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "ଇମେଜ, ଭିଡିଓ ଏବଂ କାନଭାସ ଟାଗଗୁଡ଼ିକରେ overflow: visibleକୁ ନିର୍ଦ୍ଦିଷ୍ଟ କରିବା ଫଳରେ ସେଗୁଡ଼ିକ ଉପାଦାନ ସୀମା ବାହାରେ ଭିଜୁଆଲ ବିଷୟବସ୍ତୁ ତିଆରି କରିବାର କାରଣ ହୋଇପାରେ। https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.mdକୁ ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments ଅସମର୍ଥିତ ଅଟେ। ଏହା ପରିବର୍ତ୍ତେ ପେମେଣ୍ଟ ହେଣ୍ଡଲରଗୁଡ଼ିକ ପାଇଁ ଦୟାକରି \"ଠିକ୍-ସମୟରେ ଇନଷ୍ଟଲ\" ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "ଆପଣଙ୍କ PaymentRequest କଲ ବିଷୟବସ୍ତୁ-ସୁରକ୍ଷା-ନୀତି (CSP) connect-src ଡିରେକ୍ଟିଭକୁ ବାଇପାସ କରିଛି। ଏହି ବାଇପାସ ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି PaymentRequest API (supportedMethods ଫିଲ୍ଡରେ)ରୁ ଆପଣଙ୍କ CSP connect-src ଡିରେକ୍ଟିଭରେ ପେମେଣ୍ଟ ପଦ୍ଧତି ଚିହ୍ନଟକାରୀ ଯୋଗ କରନ୍ତୁ।"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ ଷ୍ଟାଣ୍ଡାର୍ଡାଇଜ୍ଡ navigator.storage ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "ଜଣେ ବାପା କିମ୍ବା ମାଙ୍କ <picture> ଥିବା <source src> ଅବୈଧ ଅଟେ ଏବଂ ସେଥିପାଇଁ ଏହାକୁ ଅଣଦେଖା କରାଯାଇଛି। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ <source srcset> ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame ଭେଣ୍ଡର-ନିର୍ଦ୍ଦିଷ୍ଟ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ ଷ୍ଟାଣ୍ଡାର୍ଡ cancelAnimationFrame ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame ଭେଣ୍ଡର-ନିର୍ଦ୍ଦିଷ୍ଟ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ ଷ୍ଟାଣ୍ଡାର୍ଡ requestAnimationFrame ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Document.fullscreenElement ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Element.requestFullscreen() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Element.requestFullscreen() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Document.exitFullscreen() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Document.exitFullscreen() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Document.fullscreenEnabled ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "ଆମେ chrome.privacy.websites.privacySandboxEnabled APIକୁ ବନ୍ଦ କରିଦେଉଛୁ, ଯଦିଓ ଏହା M113 ରିଲିଜ ପର୍ଯ୍ୟନ୍ତ ବେକୱାର୍ଡ କମ୍ପାଟିବିଲିଟୀ ପାଇଁ ସକ୍ରିୟ ରହିବ। ଏହା ପରିବର୍ତ୍ତେ, ଦୟାକରି chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled ଏବଂ chrome.privacy.websites.adMeasurementEnabled ବ୍ୟବହାର କରନ୍ତୁ। https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "ବାଧା DtlsSrtpKeyAgreementକୁ କାଢ଼ି ଦିଆଯାଇଛି। ଆପଣ ଏହି ବାଧା ପାଇଁ ଏକ false ମୂଲ୍ୟ ନିର୍ଦ୍ଦିଷ୍ଟ କରିଛନ୍ତି, ଯାହାକୁ କାଢ଼ି ଦିଆଯାଇଥିବା SDES key negotiation ପଦ୍ଧତିକୁ ବ୍ୟବହାର କରିବାର ଏକ ପ୍ରଚେଷ୍ଟା ଭାବେ ବ୍ଯାଖ୍ୟା କରାଯାଇଛି। ଏହି କାର୍ଯ୍ଯକ୍ଷମତାକୁ କାଢ଼ି ଦିଆଯାଇଛି; ଏହା ପରିବର୍ତ୍ତେ DTLS key negotiationକୁ ସମର୍ଥନ କରୁଥିବା ଏକ ସେବାକୁ ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "ବାଧା DtlsSrtpKeyAgreementକୁ କାଢ଼ି ଦିଆଯାଇଛି। ଆପଣ ଏହି ବାଧା ପାଇଁ କୌଣସି ପ୍ରଭାବ ନଥିବା ଏକ true ମୂଲ୍ୟ ନିର୍ଦ୍ଦିଷ୍ଟ କରିଛନ୍ତି, କିନ୍ତୁ ବ୍ୟବସ୍ଥିତ ରଖିବା ପାଇଁ ଆପଣ ଏହି ବାଧାକୁ କାଢ଼ି ଦେଇପାରିବେ।"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "କଲବେକ-ଆଧାରିତ getStats() ଅସମର୍ଥିତ ଅଟେ ଏବଂ ଏହାକୁ କାଢ଼ି ଦିଆଯିବ। ଏହା ପରିବର୍ତ୍ତେ ସ୍ପେକ-ଅନୁପାଳନ କରୁଥିବା getStats() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ଅସମର୍ଥିତ ଅଟେ। ଦୟାକରି ଏହା ପରିବର୍ତ୍ତେ Selection.modify() ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ଯେଉଁ ସବରିସୋର୍ସ ଅନୁରୋଧଗୁଡ଼ିକର URLଗୁଡ଼ିକରେ ଏମ୍ବେଡ ହୋଇଥିବା କ୍ରେଡେନ୍ସିଆଲଗୁଡ଼ିକ (ଉ.ଦା. **********************/) ଅଛି, ସେଗୁଡ଼ିକୁ ବ୍ଲକ କରାଯାଇଛି।"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy ବିକଳ୍ପଟି ଅସମର୍ଥିତ ଅଟେ ଏବଂ ଏହାକୁ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer ପାଇଁ କ୍ରସ-ଅରିଜିନ ଆଇସୋଲେସନର ଆବଶ୍ୟକତା ହେବ। ଅଧିକ ବିବରଣୀ ପାଇଁ https://developer.chrome.com/blog/enabling-shared-array-buffer/ ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "ଉପଯୋଗକର୍ତ୍ତାଙ୍କ ସକ୍ରିୟକରଣ ବିନା speechSynthesis.speak() ଅସମର୍ଥିତ ଅଟେ ଏବଂ ଏହାକୁ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | UnloadHandler": {"message": "ଅନଲୋଡ ଇଭେଣ୍ଟ ଲିସନର୍ସକୁ ବନ୍ଦ କରିଦିଆଯାଇଛି ଏବଂ ସେଗୁଡ଼ିକୁ କାଢ଼ି ଦିଆଯିବ।"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBufferକୁ ବ୍ୟବହାର କରିବା ଜାରି ରଖିବା ପାଇଁ, ଏକ୍ସଟେନସନଗୁଡ଼ିକୁ କ୍ରସ-ଅରିଜିନ ଆଇସୋଲେସନରେ ଅପ୍ଟ ଇନ କରିବା ଉଚିତ। https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ ଦେଖନ୍ତୁ।"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ଆଟ୍ରିବ୍ୟୁଟ ଅସମର୍ଥିତ ଅଟେ, ଏହା ପରିବର୍ତ୍ତେ GPUAdapterInfo isFallbackAdapter ଆଟ୍ରିବ୍ୟୁଟକୁ ବ୍ୟବହାର କରନ୍ତୁ।"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequestରେ ଉତ୍ତର JSON ଦ୍ୱାରା UTF-16 ସମର୍ଥିତ ନୁହେଁ"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "ୟୁଜରଙ୍କ ଅନୁଭୂତି ଉପରେ କ୍ଷତିକାରକ ପ୍ରଭାବ ଯୋଗୁଁ ମୁଖ୍ୟ ଥ୍ରେଡରେ ସମକାଳୀନ XMLHttpRequest ଅସମର୍ଥିତ ଅଟେ। ଅଧିକ ସହାୟତା ପାଇଁ, https://xhr.spec.whatwg.org/ ଦେଖନ୍ତୁ।"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ଆନିମେସନ"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "ୟୁଜର ଇଣ୍ଟରାକ୍ସନ ବିନା ଏଲିମେଣ୍ଟର ସ୍ଥିତି ପରିବର୍ତ୍ତନ ହେଲେ ଲେଆଉଟ ସିଫ୍ଟ ହୁଏ। [ଲେଆଉଟ ସିଫ୍ଟର କାରଣ ବିଷୟରେ ତଦନ୍ତ କରନ୍ତୁ](https://web.dev/articles/optimize-cls), ଯେପରି ପୃଷ୍ଠାଗୁଡ଼ିକ ଲୋଡ ହେବା ସମୟରେ ଉପାଦାନଗୁଡ଼ିକୁ ଯୋଗ କରିବା, ସେଗୁଡ଼ିକୁ କାଢ଼ିବା କିମ୍ବା ସେଗୁଡ଼ିକର ଫଣ୍ଟଗୁଡ଼ିକୁ ପରିବର୍ତ୍ତନ କରିବା।"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ଫଣ୍ଟର ଅନୁରୋଧ"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "ଇଞ୍ଜେକ୍ଟ କରାଯାଇଥିବା iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "ଲେଆଉଟ ସିଫ୍ଟ କ୍ଲଷ୍ଟର @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "କୌଣସି ଲେଆଉଟ ସିଫ୍ଟ କଲପ୍ରିଟ ଚିହ୍ନଟ କରାଯାଇପାରିଲା ନାହିଁ"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "କୌଣସି ଲେଆଉଟ ସିଫ୍ଟ ନାହିଁ"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "ଲେଆଉଟ ସିଫ୍ଟ ସମସ୍ୟା"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "ଟପ ଲେଆଉଟ ସିଫ୍ଟ ସମସ୍ୟା ସୃଷ୍ଟିକାରୀ"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "ସବୁଠାରୁ ଖରାପ କ୍ଲଷ୍ଟର"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "ସବୁଠାରୁ ଖରାପ ଲେଆଉଟ ସିଫ୍ଟ କ୍ଲଷ୍ଟର"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "କେଶେ TTL"}, "models/trace/insights/Cache.ts | description": {"message": "ଏକ ଲମ୍ବା କେଶେ ଲାଇଫଟାଇମ ଆପଣଙ୍କ ପୃଷ୍ଠାକୁ ରିପିଟ ଭିଜିଟ ତ୍ୱରାନ୍ୱିତ କରିପାରିବ। [ଅଧିକ ଜାଣନ୍ତୁ](https://web.dev/uses-long-cache-ttl/)।"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "କାର୍ଯ୍ୟକ୍ଷମ ନଥିବା କେଶେ ନୀତିଗୁଡ଼ିକ ସହ କୌଣସି ଅନୁରୋଧ ନାହିଁ"}, "models/trace/insights/Cache.ts | others": {"message": "ଅନ୍ୟ {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "ଅନୁରୋଧ କରନ୍ତୁ"}, "models/trace/insights/Cache.ts | title": {"message": "କାର୍ଯ୍ୟକ୍ଷମ କେଶେ ଲାଇଫଟାଇମ ବ୍ୟବହାର କରନ୍ତୁ"}, "models/trace/insights/DOMSize.ts | description": {"message": "ଏକ ବଡ଼ DOM ପୃଷ୍ଠାର ପ୍ରତିକ୍ରିୟାଶୀଳତାକୁ ପ୍ରଭାବିତ କରି ଷ୍ଟାଇଲ କାଲକୁଲେସନ ଏବଂ ଲେଆଉଟ ରିଫ୍ଲୋର ଅବଧିକୁ ବୃଦ୍ଧି କରିପାରେ। ଏକ ବଡ଼ DOM ମେମୋରୀ ବ୍ୟବହାରକୁ ମଧ୍ୟ ବୃଦ୍ଧି କରିବ। [ଏକ ଅତ୍ୟଧିକ DOM ସାଇଜ କିପରି ଏଡ଼ାଇବେ ତାହା ଜାଣନ୍ତୁ](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)।"}, "models/trace/insights/DOMSize.ts | element": {"message": "ଏଲିମେଣ୍ଟ"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "ଅଧିକାଂଶ ପିଲାଙ୍କ ପାଇଁ"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM ଡେପ୍ଥ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "ପରିସଂଖ୍ୟାନ"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM ସାଇଜକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "ମୋଟ ଏଲିମେଣ୍ଟ"}, "models/trace/insights/DOMSize.ts | value": {"message": "ମୂଲ୍ୟ"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "ଆପଣଙ୍କ ପ୍ରଥମ ନେଟୱାର୍କ ଅନୁରୋଧ ସବୁଠାରୁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅଟେ।  ରିଡାଇରେକ୍ଟକୁ ଏଡ଼ାଇ, ଏକ ଦ୍ରୁତ ସର୍ଭର ଉତ୍ତର ସୁନିଶ୍ଚିତ କରି ଏବଂ ଟେକ୍ସଟ କମ୍ପ୍ରେସନକୁ ସକ୍ଷମ କରି ଏହାର ଲାଟେନ୍ସିକୁ ହ୍ରାସ କରନ୍ତୁ।"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "ରିଡାଇରେକ୍ଟ ଅଛି ({PH1} ରିଡାଇରେକ୍ଟ +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "ସର୍ଭର ଧୀରେ ଉତ୍ତର ଦେଇଛି ({PH1} ନିରୀକ୍ଷଣ କରାଯାଇଛି)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "କୌଣସି କମ୍ପ୍ରେସନ ଲାଗୁ କରାଯାଇନାହିଁ"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "ରିଡାଇରେକ୍ଟ କରିବାକୁ ଏଡ଼ାଏ"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "ସର୍ଭର ଶୀଘ୍ର ଉତ୍ତର ଦେଉଛି ({PH1} ନିରୀକ୍ଷଣ କରାଯାଇଛି)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ଟେକ୍ସଟ କମ୍ପ୍ରେସନ ଲାଗୁ କରେ"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "ରିଡାଇରେକ୍ଟ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "ସର୍ଭର ଉତ୍ତର ଦେବା ସମୟ"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ଡକ୍ୟୁମେଣ୍ଟ ଅନୁରୋଧ ବିଳମ୍ବତା"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "କମ୍ପ୍ରେସ କରାଯାଇନଥିବା ଡାଉନଲୋଡ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ଡୁପ୍ଲିକେଟ କରାଯାଇଥିବା ବାଇଟ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ସୋର୍ସ"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "ନେଟୱାର୍କ କାର୍ଯ୍ୟକଳାପ ଦ୍ୱାରା ବ୍ୟବହୃତ ଅନାବଶ୍ୟକ ବାଇଟଗୁଡ଼ିକୁ ହ୍ରାସ କରିବା ପାଇଁ ବଣ୍ଡଲଗୁଡ଼ିକରୁ ବଡ଼, ଡୁପ୍ଲିକେଟ JavaScript ମଡ୍ୟୁଲଗୁଡ଼ିକୁ କାଢ଼ି ଦିଅନ୍ତୁ।"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ଡୁପ୍ଲିକେଟ କରାଯାଇଥିବା JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "ଟେକ୍ସଟ ବାଧାରହିତ ଭାବେ ଭିଜିବିଲ ହେଉଥିବା ସୁନିଶ୍ଚିତ କରିବାକୁ [font-display](https://developer.chrome.com/blog/font-display)କୁ swap କିମ୍ବା optionalରେ ସେଟ କରିବାକୁ ବିଚାର କରନ୍ତୁ। [ଫଣ୍ଟ ମେଟ୍ରିକ ଓଭରରାଇଡଗୁଡ଼ିକ](https://developer.chrome.com/blog/font-fallbacks) ସହ ଲେଆଉଟ ସିଫ୍ଟଗୁଡ଼ିକୁ କମ କରିବା ପାଇଁ swapକୁ ଆହୁରି ଅପ୍ଟିମାଇଜ କରାଯାଇପାରିବ।"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ଫଣ୍ଟ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ଫଣ୍ଟ ଡିସପ୍ଲେ"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "ନଷ୍ଟ ହୋଇଥିବା ସମୟ"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(ବେନାମୀ)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "ଅନେକ API, ସାଧାରଣତଃ ଲେଆଉଟର ଜ୍ୟାମିତି ପଢ଼ନ୍ତି, ଷ୍ଟାଇଲ ଓ ଲେଆଉଟକୁ ଗଣନା କରିବା ପାଇଁ ସ୍କ୍ରିପ୍ଟ ଏକଜିକ୍ୟୁସନକୁ ବିରତ କରିବାକୁ ରେଣ୍ଡରିଂ ଇଞ୍ଜିନକୁ ବାଧ୍ୟ କରନ୍ତି। [ବାଧ୍ୟତାମୂଳକ ଭାବେ ରିଫ୍ଲୋ](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) ଏବଂ ଏହାର ମିଟିଗେସନଗୁଡ଼ିକ ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ।"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "ଷ୍ଟାକ ଟ୍ରେସ"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "ବାଧ୍ୟତାମୂଳକ ଭାବେ ରିଫ୍ଲୋ କରନ୍ତୁ"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "ଟପ ଫଙ୍କସନ କଲ"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "ମୋଟ ରିଫ୍ଲୋ ସମୟ"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[unattributed]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "ଇମେଜଗୁଡ଼ିକର ଡାଉନଲୋଡ ସମୟକୁ ହ୍ରାସ କରିବା ପୃଷ୍ଠା ଏବଂ LCPର ପରସିଭ୍ଡ ଲୋଡ ସମୟକୁ ଉନ୍ନତ କରିପାରିବ। [ଇମେଜ ସାଇଜକୁ ଅପ୍ଟିମାଇଜ କରିବା ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ଆନୁମାନିକ {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ଅପ୍ଟିମାଇଜ କରିହେଉଥିବା କୌଣସି ଇମେଜ ନାହିଁ"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ଫାଇଲ ସାଇଜକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "ଅନ୍ୟ {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ଇମେଜ ଡେଲିଭରୀକୁ ଉନ୍ନତ କରନ୍ତୁ"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "ଇମେଜ କମ୍ପ୍ରେସନ ଫେକ୍ଟର ବୃଦ୍ଧି କରିବା ଏହି ଇମେଜର ଡାଉନଲୋଡ ସାଇଜରେ ଉନ୍ନତି ଆଣିପାରେ।"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "ଏକ ଆଧୁନିକ ଇମେଜ ଫର୍ମାଟ (WebP, AVIF) ବ୍ୟବହାର କରିବା କିମ୍ବା ଇମେଜ କମ୍ପ୍ରେସନ ବୃଦ୍ଧି କରିବା ଏହି ଇମେଜର ଡାଉନଲୋଡ ସାଇଜରେ ଉନ୍ନତି ଆଣିପାରେ।"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ଏହି ଇମେଜ ଫାଇଲ ଏହାର ଡିସପ୍ଲେ ହୋଇଥିବା ଡାଇମେନସନ୍ସ ({PH2}) ପାଇଁ ({PH1}) ଥିବା ଆବଶ୍ୟକତାଠାରୁ ବଡ଼ ଅଟେ। ଇମେଜ ଡାଉନଲୋଡ ସାଇଜ କମ କରିବା ପାଇଁ ରେସପନସିଭ ଇମେଜଗୁଡ଼ିକୁ ବ୍ୟବହାର କରନ୍ତୁ।"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFଗୁଡ଼ିକ ପରିବର୍ତ୍ତେ ଭିଡିଓ ଫର୍ମାଟଗୁଡ଼ିକୁ ବ୍ୟବହାର କରିବା ଆନିମେଟେଡ ବିଷୟବସ୍ତୁର ଡାଉନଲୋଡ ସାଇଜରେ ଉନ୍ନତି ଆଣିପାରେ।"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "ସବୁଠାରୁ ଲମ୍ବା ଫେଜ ସହିତ ତଦନ୍ତ କରିବା ଆରମ୍ଭ କରନ୍ତୁ [ବିଳମ୍ବକୁ କମ କରାଯାଇପାରିବ](https://web.dev/articles/optimize-inp#optimize_interactions)। ପ୍ରକ୍ରିୟାକରଣ ଅବଧିକୁ ହ୍ରାସ କରିବା ପାଇଁ [ମୁଖ୍ୟ-ଥ୍ରେଡ ମୂଲ୍ୟକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ](https://web.dev/articles/optimize-long-tasks), ବହୁ ସମୟରେ JS।"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "ଅବଧି"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ଇନପୁଟ ବିଳମ୍ବ"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "କୌଣସି ଇଣ୍ଟରାକ୍ସନ ଚିହ୍ନଟ କରାଯାଇନାହିଁ"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ଫେଜ"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "ପ୍ରେଜେଣ୍ଟେସନରେ ବିଳମ୍ବ"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "ପ୍ରକ୍ରିୟାକରଣ ଅବଧି"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "ଫେଜ ଅନୁସାରେ INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "ତୁରନ୍ତ HTMLରୁ LCP ଇମେଜକୁ [ଖୋଜିପାଇବାଯୋଗ୍ୟ](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) କରି [ଲେଜି-ଲୋଡିଂକୁ ଏଡ଼ାଇ](https://web.dev/articles/lcp-lazy-loading) LCPକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high ଲାଗୁ କରାଯାଇଛି"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=ହାଇ ଲାଗୁ କରାଯିବା ଉଚିତ"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ଲେଜି ଲୋଡ ଲାଗୁ ହୋଇନାହିଁ"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "ପ୍ରାରମ୍ଭିକ ଆରମ୍ଭ ପଏଣ୍ଟର {PH1} ପରେ LCP ଇମେଜ ଲୋଡ ହୋଇଛି।"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "କୌଣସି LCP ଚିହ୍ନଟ କରାଯାଇନାହିଁ"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP ଏକ ଇମେଜ ହୋଇନଥିବା ଯୋଗୁଁ କୌଣସି LCP ରିସୋର୍ସ ଚିହ୍ନଟ କରାଯାଇନାହିଁ"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "ପ୍ରାରମ୍ଭିକ ଡକ୍ୟୁମେଣ୍ଟରେ ଅନୁରୋଧ ଖୋଜିପାଇବାଯୋଗ୍ୟ ଅଟେ"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP ଅନୁରୋଧ ଡିସ୍କଭରି"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ପ୍ରତ୍ୟେକ [ଫେଜରେ ନିର୍ଦ୍ଦିଷ୍ଟ ଉନ୍ନତି ଷ୍ଟ୍ରାଟେଜୀଗୁଡ଼ିକ](https://web.dev/articles/optimize-lcp#lcp-breakdown) ଅଛି। ଉପଯୁକ୍ତ ଭାବେ, ଅଧିକାଂଶ LCP ସମୟକୁ ରିସୋର୍ସଗୁଡ଼ିକୁ ଲୋଡ କରିବାରେ ଅତିବାହିତ କରାଯିବା ଉଚିତ, ବିଳମ୍ବ ମଧ୍ୟରେ ନୁହେଁ।"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "ଅବଧି"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "ଏଲିମେଣ୍ଟ ରେଣ୍ଡର କରିବାରେ ବିଳମ୍ବ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ଫିଲ୍ଡ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "କୌଣସି LCP ଚିହ୍ନଟ କରାଯାଇନାହିଁ"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ଫେଜ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ରିସୋର୍ସ ଲୋଡ କରିବାରେ ବିଳମ୍ବ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "ରିସୋର୍ସ ଲୋଡ ହେବାର ଅବଧି"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "ପ୍ରଥମ ବାଇଟ ପାଇଁ ସମୟ"}, "models/trace/insights/LCPPhases.ts | title": {"message": "ଫେଜ ଅନୁସାରେ LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "ସ୍କ୍ରିପ୍ଟ"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "ନଷ୍ଟ ହୋଇଥିବା ବାଇଟ"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "ନୂଆ JavaScript ଫିଚର ବ୍ୟବହାର କରିବାକୁ ପଲିଫିଲ ଏବଂ ଟ୍ରାନ୍ସଫର୍ମ ପୁରୁଣା ବ୍ରାଉଜରଗୁଡ଼ିକୁ ସକ୍ଷମ କରିଥାଏ। କିନ୍ତୁ, ଆଧୁନିକ ବ୍ରାଉଜର ପାଇଁ ଅନେକ ଆବଶ୍ୟକ ନୁହେଁ। ଆପଣଙ୍କୁ ପୁରୁଣା ବ୍ରାଉଜରଗୁଡ଼ିକୁ ସପୋର୍ଟ କରିବାକୁ ହେବ ବୋଲି ଆପଣ ନଜାଣିବା ପର୍ଯ୍ୟନ୍ତ [ବେସଲାଇନ](https://web.dev/articles/baseline-and-polyfills) ଫିଚରଗୁଡ଼ିକୁ ଟ୍ରାନ୍ସପିଲ ନକରିବା ପାଇଁ ଆପଣଙ୍କର JavaScript ବିଲ୍ଡ ପ୍ରକ୍ରିୟାକୁ ପରିବର୍ତ୍ତନ କରିବା ବିଷୟରେ ବିଚାର କରନ୍ତୁ। [ଅଧିକାଂଶ ସାଇଟ ଟ୍ରାନ୍ସପିଲ ନକରି କାହିଁକି ES6+ କୋଡ କାର୍ଯ୍ୟକାରୀ କରିପାରିବେ ତାହା ଜାଣନ୍ତୁ](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "ଲିଗାସୀ JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/1.1 ତୁଳନାରେ HTTP/2 ଏବଂ HTTP/3 ମଲ୍ଟିପ୍ଲେକ୍ସିଂ ପରି ଅଧିକ ଲାଭ ଅଫର କରନ୍ତି। [ଆଧୁନିକ HTTP ବ୍ୟବହାର ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)।"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "କୌଣସି ଅନୁରୋଧ HTTP/1.1 ବ୍ୟବହାର କରିନାହିଁ"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "ପ୍ରୋଟୋକଲ"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "ଅନୁରୋଧ କରନ୍ତୁ"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "ଆଧୁନିକ HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ଅରିଜିନ"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "ଅନୁରୋଧ"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ସୋର୍ସ"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "ସମୟ"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "ଆନୁମାନିକ LCP ସେଭିଂ"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "ଅବ୍ୟବହୃତ ପ୍ରିକନେକ୍ଟ। crossorigin ଆଟ୍ରିବ୍ୟୁଟ ସଠିକ ଭାବେ ବ୍ୟବହୃତ ହୋଇଛି ବୋଲି ଯାଞ୍ଚ କରନ୍ତୁ।"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "ପୃଷ୍ଠା ଲୋଡରେ ଉନ୍ନତି ଆଣିବା ପାଇଁ ଚେନର ଲମ୍ବ ହ୍ରାସ କରିବା, ରିସୋର୍ସଗୁଡ଼ିକର ଡାଉନଲୋଡ ସାଇଜ ହ୍ରାସ କରିବା କିମ୍ବା ଅନାବଶ୍ୟକ ରିସୋର୍ସଗୁଡ଼ିକର ଡାଉନଲୋଡକୁ ବିଳମ୍ବ କରିବା ପାଇଁ [ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅନୁରୋଧଗୁଡ଼ିକୁ ଚେନ କରିବାରୁ ଦୂରେଇ ରୁହନ୍ତୁ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "ଆପଣଙ୍କର ସବୁଠାରୁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅରିଜିନଗୁଡ଼ିକରେ [ପ୍ରିକନେକ୍ଟ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ହିଣ୍ଟଗୁଡ଼ିକୁ ଯୋଗ କରନ୍ତୁ, କିନ୍ତୁ 4ରୁ ଅଧିକ ବ୍ୟବହାର କରିବାକୁ ଚେଷ୍ଟା କରନ୍ତୁ ନାହିଁ।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ପ୍ରିକନେକ୍ଟ କେଣ୍ଡିଡେଟ"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "ସର୍ବାଧିକ ଜଟିଳ ପାଥ ଲାଟେନ୍ସି:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ନେଟୱାର୍କ ନିର୍ଭରଶୀଳତା ଦ୍ୱାରା କୌଣସି ରେଣ୍ଡରିଂ ଟାସ୍କ ପ୍ରଭାବିତ ହୋଇନାହିଁ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "ପ୍ରିକନେକ୍ଟ କରିବା ପାଇଁ କୌଣସି ଅତିରିକ୍ତ ଅରିଜିନ ଭଲ କେଣ୍ଡିଡେଟ ନୁହେଁ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "କୌଣସି ଅରିଜିନ ପ୍ରିକନେକ୍ଟ କରାଯାଇନାହିଁ"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[ପ୍ରିକନେକ୍ଟ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ହିଣ୍ଟଗୁଡ଼ିକ ବ୍ରାଉଜରକୁ ପୃଷ୍ଠା ଲୋଡ ହେବା ପୂର୍ବରୁ ଏକ କନେକ୍ସନ ସ୍ଥାପନ କରିବାରେ ସାହାଯ୍ୟ କରେ, ଯାହା ସେହି ଅରିଜିନ ପାଇଁ ପ୍ରଥମ ଅନୁରୋଧ କରାଯିବା ବେଳେ ସମୟ ସେଭ କରେ। ପୃଷ୍ଠାଟି ଯେଉଁ ଅରିଜିନଗୁଡ଼ିକ ସହ ପ୍ରିକନେକ୍ଟ ହୋଇଛି ତାହା ନିମ୍ନରେ ଦିଆଯାଇଛି।"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "ପ୍ରିକନେକ୍ଟ ହୋଇଥିବା ଅରିଜିନ"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ନେଟୱାର୍କ ନିର୍ଭରଶୀଳତା ଟ୍ରି"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4ରୁ ଅଧିକ preconnect କନେକ୍ସନ ମିଳିଲା। ଏଗୁଡ଼ିକୁ କମ ଏବଂ କେବଳ ସବୁଠାରୁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅରିଜିନଗୁଡ଼ିକ ପାଇଁ ବ୍ୟବହାର କରାଯିବା ଉଚିତ।"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "ଅବ୍ୟବହୃତ ପ୍ରିକନେକ୍ଟ। କେବଳ ସେହି ଅରିଜିନ ପାଇଁ preconnectକୁ ବ୍ୟବହାର କରନ୍ତୁ ଯାହାକୁ ପୃଷ୍ଠା ଅନୁରୋଧ କରିବାର ସମ୍ଭାବନା ଅଛି।"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "ପୃଷ୍ଠା ଲୋଡରେ ଉନ୍ନତି ଆଣିବା ପାଇଁ ଚେନର ଲମ୍ବ ହ୍ରାସ କରିବା, ରିସୋର୍ସଗୁଡ଼ିକର ଡାଉନଲୋଡ ସାଇଜ ହ୍ରାସ କରିବା କିମ୍ବା ଅନାବଶ୍ୟକ ରିସୋର୍ସଗୁଡ଼ିକର ଡାଉନଲୋଡକୁ ବିଳମ୍ବ କରିବା ପାଇଁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅନୁରୋଧଗୁଡ଼ିକୁ ଚେନ କରିବାରୁ ଦୂରେଇ ରୁହନ୍ତୁ।"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "ଅନୁରୋଧଗୁଡ଼ିକ ପୃଷ୍ଠାର ପ୍ରାରମ୍ଭିକ ରେଣ୍ଡରକୁ ବ୍ଲକ କରୁଛି, ଯାହା ଯୋଗୁଁ LCPକୁ ବିଳମ୍ବ ହୋଇପାରେ। [ବିଳମ୍ବ କରିବା କିମ୍ବା ଇନଲାଇନ କରିବା](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ଫଳରେ ଏହି ନେଟୱାର୍କ ଅନୁରୋଧଗୁଡ଼ିକୁ ଏହି ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ପାଥରୁ ମୁଭ କରାଯାଇପାରିବ।"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "ଅବଧି"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ଏହି ନାଭିଗେସନ ପାଇଁ କୌଣସି ରେଣ୍ଡର ବ୍ଲକିଂ ଅନୁରୋଧ ନାହିଁ"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "ଅନୁରୋଧ"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "ରେଣ୍ଡର ବ୍ଲକିଂ ଅନୁରୋଧ"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "ଯଦି ରିକାଲକୁଲେଟ ଷ୍ଟାଇଲ ମୂଲ୍ୟ ଅଧିକ ରହେ, ତେବେ ଚୟନକାରୀ ଅପ୍ଟିମାଇଜେସନ ସେଗୁଡ଼ିକୁ ହ୍ରାସ ହୋଇପାରେ। ଉଭୟ ଅଧିକ ଅତିବାହିତ ହୋଇଥିବା ସମୟ ଏବଂ ଅଧିକ ସ୍ଲୋ-ପାଥ % ଥିବା [ଚୟନକାରୀଗୁଡ଼ିକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ](https://developer.chrome.com/docs/devtools/performance/selector-stats)। ସମସ୍ତ ସରଳ ଚୟନକାରୀ, କମ ଚୟନକାରୀ, ଏକ ଛୋଟ DOM ଏବଂ ଅଳ୍ପ DOM ମେଚିଂ ମୂଲ୍ୟ ହ୍ରାସ କରିବ।"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "ଅତିବାହିତ ହୋଇଥିବା ସମୟ"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "କୌଣସି CSS ଚୟନକାରୀ ଡାଟା ମିଳିଲା ନାହିଁ। ପରଫରମାନ୍ସ ପେନେଲ ସେଟିଂସରେ CSS ଚୟନକାରୀ ପରିସଂଖ୍ୟାନକୁ ସକ୍ଷମ କରାଯିବା ଆବଶ୍ୟକ।"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ମେଳ କରିବାର ପ୍ରଚେଷ୍ଟା"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "ମେଳର ସଂଖ୍ୟା"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ଚୟନକାରୀ ମୂଲ୍ୟ"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ଶ୍ରେଷ୍ଠ ଚୟନକାରୀ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "ମୋଟ"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "ମୁଖ୍ୟ ଥ୍ରେଡ ସମୟ"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "3ୟ ପକ୍ଷ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ଟ୍ରାନ୍ସଫରର ସାଇଜ"}, "models/trace/insights/ThirdParties.ts | description": {"message": "3ୟ ପକ୍ଷ କୋଡ ଲୋଡ ପରଫରମାନ୍ସକୁ ଉଲ୍ଲେଖନୀୟ ଭାବେ ପ୍ରଭାବିତ କରିପାରିବ। ଆପଣଙ୍କ ପୃଷ୍ଠାର ବିଷୟବସ୍ତୁକୁ ପ୍ରାଥମିକତା ଦେବା ପାଇଁ [3ୟ ପକ୍ଷ କୋଡର ଲୋଡିଂକୁ ହ୍ରାସ କରି ସ୍ଥଗିତ ରଖନ୍ତୁ](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)।"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "କୌଣସି ତୃତୀୟ ପକ୍ଷ ମିଳିଲା ନାହିଁ"}, "models/trace/insights/ThirdParties.ts | title": {"message": "3ୟ ପକ୍ଷ"}, "models/trace/insights/Viewport.ts | description": {"message": "ଯଦି ମୋବାଇଲ ପାଇଁ ଭ୍ୟୁପୋର୍ଟକୁ ଅପ୍ଟିମାଇଜ କରାଯାଇନାହିଁ, ତେବେ ଇଣ୍ଟରାକ୍ସନରେ ଟାପ କରିବା [300 ms ପର୍ଯ୍ୟନ୍ତ ବିଳମ୍ବିତ ହୋଇପାରେ](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)।"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "ମୋବାଇଲ ଟାପ ବିଳମ୍ବ"}, "models/trace/insights/Viewport.ts | title": {"message": "ମୋବାଇଲ ପାଇଁ ଭ୍ୟୁପୋର୍ଟକୁ ଅପ୍ଟିମାଇଜ କରନ୍ତୁ"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "କେବଳ ଏକ GET ଅନୁରୋଧ ମାଧ୍ୟମରେ ଲୋଡ ହୋଇଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ଅଟେ।"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XXର ଏକ ସ୍ଥିତି କୋଡ ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକୁ କେବଳ କ୍ୟାଶ କରାଯାଇପାରିବ।"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "କ୍ୟାଶରେ ଥିବା ସମୟରେ JavaScript କାର୍ଯ୍ୟକାରୀ କରିବା ପାଇଁ ଏକ ପ୍ରୟାସକୁ Chrome ଚିହ୍ନଟ କରିଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "ଏକ AppBanner ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ଫ୍ଲାଗଗୁଡ଼ିକ ଦ୍ୱାରା ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି। ଏହି ଡାଭାଇସରେ ସ୍ଥାନୀୟ ଭାବେ ଏହାକୁ ସକ୍ଷମ କରିବା ପାଇଁ chrome://flags/#back-forward-cache ଭିଜିଟ କରନ୍ତୁ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "କମାଣ୍ଡ ଲାଇନ ଦ୍ୱାରା ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "ପର୍ଯ୍ୟାପ୍ତ ମେମୋରୀ ନଥିବା ଯୋଗୁଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଡେଲିଗେଟ ଦ୍ୱାରା ସମର୍ଥିତ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "ପ୍ରିରେଣ୍ଡରର ପାଇଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "ପୃଷ୍ଠାରେ ପଞ୍ଜିକୃତ ହୋଇଥିବା ଲିସନରଗୁଡ଼ିକର ଏକ BroadcastChannel ଇନଷ୍ଟାନ୍ସ ଥିବା ଯୋଗୁଁ ଏହାକୁ କ୍ୟାଶ କରାଯାଇପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store ହେଡର ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "କ୍ୟାଶକୁ ଉଦ୍ଦେଶ୍ୟମୂଳକ ଭାବେ ଖାଲି କରାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "ଅନ୍ୟ ଏକ ପୃଷ୍ଠାକୁ କ୍ୟାଶ କରିବା ନିମନ୍ତେ ଅନୁମତି ଦେବା ପାଇଁ ଏହି ପୃଷ୍ଠାଟିକୁ କ୍ୟାଶରୁ ବାହାର କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "ପ୍ଲଗଇନ ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "ଅପରିଭାଷିତ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "ମିଡିଆ ଡିଭାଇସ ଡିସପାଚର ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Chrome ବ୍ରାଉଜରରୁ ନାଭିଗେଟ କରି ବାହାରି ଗଲା ପରେ ମଧ୍ୟ ଏକ ମିଡିଆ ପ୍ଲେୟାର ଚାଲୁଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API ବ୍ୟବହାର କରୁଥିବା ଏବଂ ଏକ ପ୍ଲେବ୍ୟାକ ସ୍ଥିତି ସେଟ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API ବ୍ୟବହାର କରୁଥିବା ଏବଂ କାର୍ଯ୍ୟ ହ୍ୟାଣ୍ଡଲର ସେଟ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "ସ୍କ୍ରିନ ରିଡର ଯୋଗୁଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store ବ୍ୟବହାର କରୁଥିବା ଏକ ପୃଷ୍ଠାରେ କୁକୀଗୁଡ଼ିକୁ ଅକ୍ଷମ କରାଯାଇଥିବା ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶେକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "ଏକ ଉତ୍ସର୍ଗୀକୃତ ୱାର୍କର କିମ୍ବା ୱାର୍କଲେଟ ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "ନାଭିଗେଟ କରିବା ପୂର୍ବରୁ ଡକ୍ୟୁମେଣ୍ଟଟିକୁ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ଲୋଡ କରାଯାଇନଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ଆପ ବ୍ୟାନର ଦେଖାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ Chrome ପାସୱାର୍ଡ ମ୍ୟାନେଜର ଦେଖାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ DOM ଡିଷ୍ଟିଲେସନ ପ୍ରକ୍ରିୟା ଚାଲିଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ DOM ଡିଷ୍ଟିଲର ଭ୍ୟୁଅର ଦେଖାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "ଏକ୍ସଟେନସନ, ମେସେଜିଂ API ବ୍ୟବହାର କରିଥିବା ଯୋଗୁଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଏଣ୍ଟର କରିବା ପୂର୍ବରୁ ଦୀର୍ଘ-ସ୍ଥାୟୀ ସଂଯୋଗ ଥିବା ଏକ୍ସଟେନସନଗୁଡ଼ିକ ଦ୍ୱାରା ସଂଯୋଗକୁ ବନ୍ଦ କରାଯିବା ଉଚିତ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "ଦୀର୍ଘ-ସ୍ଥାୟୀ ସଂଯୋଗ ଥିବା ଏକ୍ସଟେନସନଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ଫ୍ରେମଗୁଡ଼ିକୁ ମେସେଜ ପଠାଇବାକୁ ଚେଷ୍ଟା କରିଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "ଏକ୍ସଟେନସନଗୁଡ଼ିକ ଯୋଗୁଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ପୃଷ୍ଠା ପାଇଁ ଫର୍ମ ପୁଣି ଦାଖଲ କରିବା କିମ୍ବା HTTP ପାସୱାର୍ଡ ଡାଏଲଗ ପରି ମୋଡାଲ ଡାଏଲଗ ପ୍ରଦର୍ଶିତ ହୋଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ଅଫଲାଇନ ପୃଷ୍ଠାଟି ପ୍ରଦର୍ଶିତ ହୋଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ଆଉଟ-ଅଫ-ମେମୋରୀ ଇଣ୍ଟରଭେନସନ ବାର ଦେଖାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ଅନୁମତି ଅନୁରୋଧ ପ୍ରକ୍ରିୟା ଚାଲିଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ପପଅପ ବ୍ଲକର ଦେଖାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "ନାଭିଗେଟ କରି ବାହାରି ଯିବା ସମୟରେ ସେଫ ବ୍ରାଉଜିଂର ବିବରଣୀ ପ୍ରଦର୍ଶିତ ହୋଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "ସେଫ ବ୍ରାଉଜିଂ ଏହି ପୃଷ୍ଠାକୁ ଅପମାନଜନକ ବୋଲି ବିଚାର କରି ପପଅପକୁ ବ୍ଲକ କରିଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "ପୃଷ୍ଠାଟି ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ସମୟରେ ଜଣେ ସର୍ଭିସ ୱାର୍କର ସକ୍ରିୟ ଥିଲେ।"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ଏକ ଡକ୍ୟୁମେଣ୍ଟ ତ୍ରୁଟି ଯୋଗୁଁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକୁ bfcacheରେ ଷ୍ଟୋର କରାଯାଇପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "ଅନ୍ୟ ଏକ ପୃଷ୍ଠାକୁ କ୍ୟାଶ କରିବା ନିମନ୍ତେ ଅନୁମତି ଦେବା ପାଇଁ ଏହି ପୃଷ୍ଠାଟିକୁ କ୍ୟାଶରୁ ବାହାର କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "ମିଡିଆ ଷ୍ଟ୍ରିମ ଆକ୍ସେସକୁ ଅନୁମତି ଦେଇଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "ନିର୍ଦ୍ଦିଷ୍ଟ ପ୍ରକାରର ଏମ୍ବେଡ କରାଯାଇଥିବା ବିଷୟବସ୍ତୁ ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ (ଉ.ଦା. PDF) ବର୍ତ୍ତମାନ ବେକ/ଫରୱାର୍ଡ କେଶେ ପାଇଁ ଉପଲବ୍ଧ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "ଏକ ଖୋଲା IndexedDB ସଂଯୋଗ ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "ଏକ IndexedDB ଇଭେଣ୍ଟ ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "ଅଯୋଗ୍ୟ APIଗୁଡ଼ିକୁ ବ୍ୟବହାର କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "ଏକ୍ସଟେନସନ ଦ୍ୱାରା JavaScript ଇଞ୍ଜେକ୍ଟ କରାଯାଇଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବେକ/ଫରୱାର୍ଡ କେଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "ଏକ୍ସଟେନସନ ଦ୍ୱାରା ଏକ StyleSheet ଇଞ୍ଜେକ୍ଟ କରାଯାଇଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବେକ/ଫରୱାର୍ଡ କେଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "ଇଣ୍ଟର୍ନଲ ତ୍ରୁଟି।"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "କିଛି JavaScript ନେଟୱାର୍କ ଅନୁରୋଧ Cache-Control: no-store ହେଡର ଥିବା ରିସୋର୍ସ ପାଇଥିବା ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶେକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "ଏକ କିପଆଲାଇଭ ଅନୁରୋଧ ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "କୀବୋର୍ଡ ଲକ ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "ନାଭିଗେଟ କରିବା ପୂର୍ବରୁ ପୃଷ୍ଠାଟିକୁ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ଲୋଡ କରାଯାଇନଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ମୁଖ୍ୟ ରିସୋର୍ସରେ cache-control:no-cache ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ମୁଖ୍ୟ ରିସୋର୍ସରେ cache-control:no-store ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରୁ ପୃଷ୍ଠାକୁ ରିଷ୍ଟୋର କରାଯିବା ପୂର୍ବରୁ ନାଭିଗେସନକୁ ବାତିଲ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "ଏକ ସକ୍ରିୟ ନେଟୱାର୍କ ସଂଯୋଗ ଅଧିକ ଡାଟା ପ୍ରାପ୍ତ କରିଥିବା ଯୋଗୁଁ ପୃଷ୍ଠାଟିକୁ କ୍ୟାଶରୁ କାଢ଼ି ଦିଆଯାଇଛି। କ୍ୟାଶ ହୋଇଥିବା ସମୟରେ ଏକ ପୃଷ୍ଠା ପାଇପାରିବା ଡାଟାର ପରିମାଣକୁ Chrome ସୀମିତ କରେ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ଇନଫ୍ଲାଇଟ ଫେଚ() କିମ୍ବା XHR ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "ଏକ ସକ୍ରିୟ ନେଟୱାର୍କ ଅନୁରୋଧରେ ଗୋଟିଏ ରିଡାଇରେକ୍ଟ ସାମିଲ ଥିବା ଯୋଗୁଁ ପୃଷ୍ଠାଟିକୁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରୁ କାଢ଼ି ଦିଆଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ଅନେକ ସମୟ ଧରି ଏକ ନେଟୱାର୍କ ସଂଯୋଗ ଖୋଲା ଥିବା ଯୋଗୁଁ ପୃଷ୍ଠାଟିକୁ କ୍ୟାଶରୁ କାଢ଼ି ଦିଆଯାଇଛି। ଗୋଟିଏ ପୃଷ୍ଠା କ୍ୟାଶ ହୋଇଥିବା ସମୟରେ ଯେଉଁ ସମୟ ପାଇଁ ଡାଟା ପାଇପାରେ ସେହି ସମୟକୁ Chrome ସୀମିତ କରିଥାଏ।"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "ଏକ ବୈଧ ପ୍ରତିକ୍ରିୟା ହେଡ ନଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "ମୁଖ୍ୟ ଫ୍ରେମ ବ୍ୟତୀତ ଅନ୍ୟ ଏକ ଫ୍ରେମରେ ନାଭିଗେସନ ହୋଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "ଇଣ୍ଡେକ୍ସ କରାଯାଇଥିବା ଅନଗୋଇଂ DB ଟ୍ରାଞ୍ଜେକସନ ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠା ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "ଏକ ଇନ-ଫ୍ଲାଇଟ ନେଟୱାର୍କ ଅନୁରୋଧ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "ଏକ ଇନ-ଫ୍ଲାଇଟ ଫେଚ ନେଟୱାର୍କ ଅନୁରୋଧ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "ଏକ ଇନ-ଫ୍ଲାଇଟ ନେଟୱାର୍କ ଅନୁରୋଧ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "ଏକ ଇନ-ଫ୍ଲାଇଟ XHR ନେଟୱାର୍କ ଅନୁରୋଧ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "ପିକଚର-ଇନ-ପିକଚର ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "ପ୍ରିଣ୍ଟିଂ UI ଦେଖାଉଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "ପୃଷ୍ଠାଟି 'window.open()' ବ୍ୟବହାର କରି ଖୋଲାଯାଇଛି ଏବଂ ଅନ୍ୟ ଏକ ଟାବରେ ଏହାର ରେଫରେନ୍ସ ଅଛି କିମ୍ବା ସେହି ପୃଷ୍ଠାଟି ଏକ ୱିଣ୍ଡୋ ଖୋଲିଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ପୃଷ୍ଠା ପାଇଁ ରେଣ୍ଡରର ପ୍ରକ୍ରିୟା କ୍ରାସ ହୋଇଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ପୃଷ୍ଠା ପାଇଁ ରେଣ୍ଡରର ପ୍ରକ୍ରିୟା ବାଧାପ୍ରାପ୍ତ ହୋଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ଅଡିଓ କ୍ୟାପଚର ଅନୁମତି ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "ସେନ୍ସର ଅନୁମତି ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "ପୃଷ୍ଠପଟ ସିଙ୍କ କିମ୍ବା ଫେଚ ଅନୁମତି ପାଇଁ ଅନୁରୋଧ କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI ଅନୁମତି ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "ବିଜ୍ଞପ୍ତି ଅନୁମତିଗୁଡ଼ିକ ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "ଷ୍ଟୋରେଜ ଆକ୍ସେସ ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "ଭିଡିଓ କ୍ୟାପଚର ଅନୁମତି ପାଇଁ ଅନୁରୋଧ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "କେବଳ HTTP / HTTPS URL ସ୍କିମ ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକୁ କ୍ୟାଶ କରାଯାଇପାରିବ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "ପୃଷ୍ଠାଟି ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ସମୟରେ ଜଣେ ସର୍ଭିସ ୱାର୍କରଙ୍କ ଦ୍ୱାରା ଦାବି କରାଯାଇଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "ଜଣେ ସର୍ଭିସ ୱାର୍କର ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ଏକ MessageEventକୁ ପୃଷ୍ଠାଟି ପଠାଇବା ପାଇଁ ଚେଷ୍ଟା କରିଛନ୍ତି।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ଏକ ପୃଷ୍ଠା ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ଥିବା ସମୟରେ ServiceWorker ପଞ୍ଜିକୃତ କରାଯାଇନଥିଲା।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "ଏକ ସର୍ଭିସ ୱାର୍କର ସକ୍ରିୟକରଣ ଯୋଗୁଁ ପୃଷ୍ଠାଟିକୁ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରୁ କାଢ଼ି ଦିଆଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chromeକୁ ରିଷ୍ଟାର୍ଟ କରାଯାଇଛି ଏବଂ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଏଣ୍ଟ୍ରିଗୁଡ଼ିକୁ ଖାଲି କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "ପୃଷ୍ଠାରେ ଥିବା ଏକ if<PERSON>e, ଏକ ନାଭିଗେସନ ଆରମ୍ଭ କରିଛି, ଯାହା ସମ୍ପୂର୍ଣ୍ଣ ହୋଇନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "ସବରିସୋର୍ସରେ cache-control:no-cache ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "ସବରିସୋର୍ସରେ cache-control:no-store ଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "ପୃଷ୍ଠାଟି ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶରେ ସର୍ବାଧିକ ସମୟ ଅତିକ୍ରମ କରିଛି ଏବଂ ସମୟ ସୀମା ସମାପ୍ତ ହୋଇଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "ପୃଷ୍ଠାର ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିବାର ସମୟ ସୀମା ସମାପ୍ତ ହୋଇଛି (ପେଜହାଇଡ ହେଣ୍ଡଲର ଅଧିକ ସମୟ-ଚାଲିବା ଯୋଗୁଁ ହୋଇଥାଇପାରେ)।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "ପୃଷ୍ଠାର ମୁଖ୍ୟ ଫ୍ରେମରେ ଏକ ଅନଲୋଡ ହ୍ୟାଣ୍ଡଲର ଅଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "ପୃଷ୍ଠାର ଏକ ସବ ଫ୍ରେମରେ ଏକ ଅନଲୋଡ ହ୍ୟାଣ୍ଡଲର ଅଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ବ୍ରାଉଜର ୟୁଜର ଏଜେଣ୍ଟ ଓଭରରାଇଡ ହେଡରକୁ ପରିବର୍ତ୍ତନ କରିଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "ଭିଡିଓ କିମ୍ବା ଅଡିଓ ରେକର୍ଡ କରିବାକୁ ଆକ୍ସେସ ପ୍ରଦାନ କରିଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC ବ୍ୟବହାର କରାଯାଇଥିବା ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket ବ୍ୟବହାର କରାଯାଇଥିବା ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ଆକ୍ସେସ କରିପାରିବ ନାହିଁ।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport ବ୍ୟବହାର କରାଯାଇଥିବା ଯୋଗୁଁ ବେକ/ଫରୱାର୍ଡ କେଶକୁ ଅକ୍ଷମ କରାଯାଇଛି।"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR ବ୍ୟବହାର କରୁଥିବା ପୃଷ୍ଠାଗୁଡ଼ିକ ବର୍ତ୍ତମାନ ବ୍ୟାକ/ଫରୱାର୍ଡ କ୍ୟାଶ ପାଇଁ ଯୋଗ୍ୟ ନୁହେଁ।"}}