{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers හැසිරවීමේදී ආදේශක සංකේතය (*) මගින් අවසරය ආවරණය නොවනු ඇත."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "-internal-media-controls-overlay-cast-button තෝරකය භාවිත කරනවා වෙනුවට පෙරනිමි විකාශ ඒකාබද්ධ කිරීම අබල කිරීම සඳහා disableRemotePlayback උපලක්ෂණය භාවිත කළ යුතුය."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS දිස් වීම් අගය slider-vertical ප්‍රමිත නොකෙරෙන අතර ඉවත් කරනු ලැබේ."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ඉවත් කරන ලද හිස්ඉඩ \\(n|r|t) අනුලකුණු සහ අනුලකුණුවලට වඩා අඩු (<) යන දෙකම අඩංගු URL ඉල්ලන සම්පත් අවහිර කර ඇත. කරුණාකර මෙම සම්පත් පූරණය කිරීම සඳහා මූලාංග උපලක්ෂණ අගයන් වැනි ස්ථානවලින් නව පේළි ඉවත් කර අනුලකුණුවලට වඩා අඩු ඒවා කේතනය කරන්න."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() අත්හැර ඇත, ඒ වෙනුවට ප්‍රමිතිගත API භාවිත කරන්න: සංචාලන වේලාව 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() අත්හැර ඇත, ඒ වෙනුවට ප්‍රමිතිගත API භාවිත කරන්න: සිතුවම් කාල නියාමනය."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() අත්හැර ඇත, ඒ වෙනුවට ප්‍රමිතිගත API භාවිත කරන්න: සංචාලන වේලාව තුළ nextHopProtocol 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) අනුලකුණක් අඩංගු කුකි කප්පාදු කිරීම වෙනුවට ප්‍රතික්ෂේප කරනු ඇත."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain සැකසීමෙන් එකම මූලාරම්භ ප්‍රතිපත්තිය ලිහිල් කිරීම අත්හරින අතර, පෙරනිමියෙන් අබල කරනු ඇත. මෙම ක්ෂය කිරීමේ අනතුරු ඇඟවීම document.domain සැකසීම මගින් සක්‍රීය කරන ලද හරස් මූලාරම්භ ප්‍රවේශයක් සඳහා වේ."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "හරස් සම්භවයක් ඇති iframes වෙතින් window.alert ප්‍රේරණය කිරීම අත්හරින ලද අතර අනාගතයේ දී එය ඉවත් කරනු ඇත."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "හරස් සම්භවයක් ඇති iframes වෙතින් window.confirm ප්‍රේරණය කිරීම අත්හරින ලද අතර අනාගතයේ දී එය ඉවත් කරනු ඇත."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "දත්ත සඳහා සහාය: SVGUseElement හි URL අවලංගු කර ඇති අතර අනාගතයේ දී එය ඉවත් කරනු ලැබේ."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() සහ watchPosition() අනාරක්ෂිත මූලාරම්භවල තවදුරටත් ක්‍රියා නොකරයි. මෙම විශේෂාංගය භාවිත කිරීමට, ඔබ ඔබගේ යෙදුම HTTPS වැනි ආරක්ෂිත මූලාරම්භයකට මාරු කිරීම සලකා බැලිය යුතුය. වැඩි විස්තර සඳහා https://goo.gle/chrome-insecure-origins බලන්න."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() සහ watchPosition() අනාරක්ෂිත මූලාරම්භවල අත්හැර ඇත. මෙම විශේෂාංගය භාවිත කිරීමට, ඔබ ඔබගේ යෙදුම HTTPS වැනි ආරක්ෂිත මූලාරම්භයකට මාරු කිරීම සලකා බැලිය යුතුය. වැඩි විස්තර සඳහා https://goo.gle/chrome-insecure-origins බලන්න."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() අනාරක්ෂිත මූලාරම්භවල තවදුරටත් ක්‍රියා නොකරයි. මෙම විශේෂාංගය භාවිත කිරීමට, ඔබ ඔබගේ යෙදුම HTTPS වැනි ආරක්ෂිත මූලාරම්භයකට මාරු කිරීම සලකා බැලිය යුතුය. වැඩි විස්තර සඳහා https://goo.gle/chrome-insecure-origins බලන්න."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "නිශ්චිත අකුරු ප්‍රමාණයක් නොමැති <article>, <aside>, <nav>, හෝ <section> තුළ <h1> ටැගයක් හමු විය. මෙම බ්‍රවුසරයේ මෙම ශීර්ෂ පාඨයේ ප්‍රමාණය නුදුරු අනාගතයේ දී වෙනස් වනු ඇත. වැඩිදුර තොරතුරු සඳහා https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 බලන්න."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate අත්හැරේ. ඒ වෙනුවට RTCPeerConnectionIceErrorEvent.address හෝ RTCPeerConnectionIceErrorEvent.port භාවිත කරන්න."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ඩිජිටල් අක්තපත්‍ර සඳහා navigator.credentials.get() ඉල්ලීම සඳහා මෙම ආකෘතිය අතහැර දමා ඇත, නව ආකෘතිය භාවිතා කිරීමට ඔබේ ඇමතුම යාවත්කාලීන කරන්න."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment සේවා සේවක සිදුවීමෙන් ලැබෙන වෙළෙන්දා සම්භවය සහ අන්තනෝමතික දත්ත අත්හරිනු ලබන අතර ඉවත් කරනු ලැබේ: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "වෙබ් අඩවිය එහි පරිශීලකයින්ගේ වරප්‍රසාද ලත් ජාල පිහිටීම නිසා පමණක් ප්‍රවේශ විය හැකි ජාලයකින් උප සම්පතක් ඉල්ලා ඇත. මෙම ඉල්ලීම් අන්තර්ජාලයට පොදු නොවන උපාංග සහ සේවාදායක, හරස් අඩවි ඉල්ලීම් ව්‍යාජ (CSRF) ප්‍රහාරයක් සහ/හෝ තොරතුරු කාන්දු වීමේ අවදානම වැඩි කරමින් නිරාවරණය කරයි. මෙම අවදානම් අවම කිරීම සඳහා, ආරක්ෂිත නොවන සන්දර්භවලින් ආරම්භ කරන විට Chrome විසින් පොදු නොවන උප සම්පත් වෙත ඉල්ලීම් අවලංගු කරන අතර, ඒවා අවහිර කිරීම ආරම්භ කරනු ඇත."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "එහි හැසිරීම වඩාත් නිවැරදිව පිළිබිඹු කිරීම සඳහා, joinAdInterestGroup() වෙත ලබා දුන් InterestGroups හි dailyUpdateUrl ක්ෂේත්‍රය updateUrl වෙත යළි නම් කර ඇත."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator අත්හරිනු ලැබේ. කරුණාකර ඒ වෙනුවට Intl.Segmenter භාවිත කරන්න."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "ඒවා .css ගොනු දිගුවකින් අවසන් වන්නේ නම් මිස file: URL වෙතින් CSS පූරණය කළ නොහැකිය."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "පිරිවිතර වෙනස් වීම හේතුවෙන් remove() හි අසමමුහුර්ත පරාසය ඉවත් කිරීම අවලංගු කිරීමට SourceBuffer.abort() භාවිත කිරීම. අනාගතයේදී සහාය ඉවත් කරනු ඇත. ඔබ ඒ වෙනුවට updateend සිදුවීමට සවන් දිය යුතුය. abort() අසමමුහුර්ත මාධ්‍ය ඇමුණුමක් හෝ විග්‍රහ කිරීමේ තත්ත්වය යළි පිහිටුවීමක් පමණක් නතර කිරීමට අදහස් කෙරේ."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "පිරිවිතර වෙනස් වීම හේතුවෙන් ඕනෑම බෆර කළ කේත රාමුවල ඉහළම ඉදිරිපත් කිරීමේ කාල මුද්‍රාවට පහළින් MediaSource.duration සැකසීම අත්හරිනු ලැබේ. කප්පාදු කරන ලද බෆර මාධ්‍ය ව්‍යංගයෙන් ඉවත් කිරීම සඳහා වන සහාය අනාගතයේදී ඉවත් කෙරෙනු ඇත. ඔබ එ වෙනුවට remove(newDuration, oldDuration) සියලු sourceBuffers හි, newDuration < oldDuration තැන්වල පැහැදිලිව සිදු කළ යුතුය."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "වෙබ් MIDI MIDIOptions තුළ sysex නිශ්චිතව දක්වා නොමැති වුවද භාවිත කිරීමට අවසරය ඉල්ලනු ඇත."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "දැනුම්දීම් API තවදුරටත් අනාරක්ෂිත මූලාරම්භවලින් භාවිත නොකළ හැකිය. ඔබ ඔබගේ යෙදුම HTTPS වැනි, ආරක්ෂිත මූලාරම්භයකට මාරු කිරීම සලකා බැලිය යුතුය. වැඩි විස්තර සඳහා https://goo.gle/chrome-insecure-origins බලන්න."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Notification API සඳහා අවසරය හරස් සම්භවයක් ඇති iframe වෙතින් තවදුරටත් ඉල්ලිය නොහැකිය. ඔබ ඉහළ මට්ටමේ රාමුවකින් අවසර ඉල්ලීම හෝ ඒ වෙනුවට නව කවුළුවක් විවෘත කිරීම සලකා බැලිය යුතුය."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap හි imageOrientation: 'none' විකල්පය අත්හැර ඇත. ඒ වෙනුවට '{imageOrientation: 'from-image'}' විකල්පය සමග createImageBitmap භාවිත කරන්න."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "ඔබගේ සහායක යල් පැන ගිය (D)TLS අනුවාදයක් ගැන සාකච්ඡා කරමින් සිටී. මෙය නිවැරදි කිරීමට කරුණාකර ඔබගේ සහායක සමඟ පරීක්ෂා කරන්න."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, වීඩියෝ සහ කැන්වස් ටැග් මත overflow: visible සඳහන් කිරීම මූලද්‍රව්‍ය සීමාවෙන් පිටත දෘශ්‍ය අන්තර්ගතයක් නිපදවීමට හේතු විය හැක. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md බලන්න."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments අත්හරිනු ලැබේ. ඒ වෙනුවට ගෙවීම් හසුරුවන්නන් සඳහා කාලීන ස්ථාපනය භාවිතා කරන්න."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "ඔබේ PaymentRequest ඇමතුම අන්තර්ගත-ආරක්ෂක-ප්‍රතිපත්ති (CSP) connect-src විධානය මග හරින ලදී. මෙම මගහැරීම අත්හරින ලදි. ඔබේ CSP connect-src විධානයට PaymentRequest API (supportedMethods ක්ෂේත්‍රයේ) වෙතින් ගෙවීම් ක්‍රම හැඳුනුම්කාරකය එක් කරන්න."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent අත්හැරේ. ඒ වෙනුවට සම්මත navigator.storage භාවිත කරන්න."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> මූලය සහිත <source src> වලංගු නොවන අතර එබැවින් නොසලකා හැරේ. කරුණාකර ඒ වෙනුවට <source srcset> භාවිත කරන්න."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame වෙළෙන්දාට විශේෂිත වේ. ඒ වෙනුවට සම්මත cancelAnimationFrame භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame වෙළෙන්දාට විශේෂිත වේ. ඒ වෙනුවට සම්මත requestAnimationFrame භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen අත්හැර ඇත. ඒ වෙනුවට Document.fullscreenElement භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() අත්හරින ලදි. ඒ වෙනුවට Element.requestFullscreen() භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() අත්හරින ලදි. ඒ වෙනුවට Element.requestFullscreen() භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() අත්හරින ලදි. ඒ වෙනුවට Document.exitFulscreen() භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() අත්හරින ලදි. ඒ වෙනුවට Document.exitFulscreen() භාවිතා කරන්න."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen අත්හැර ඇත. ඒ වෙනුවට Document.fullscreenEnabled භාවිතා කරන්න."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "M113 නිකුත් කරන තෙක් එය පසුගාමී ගැළපුම සඳහා සක්‍රියව පවතිනු ඇතත්, අපි API chrome.privacy.websites.privacySandboxEnabled අත්හරිමු. ඒ වෙනුවට, chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled සහ chrome.privacy.websites.adMeasurementEnabled භාවිතා කරන්න. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled බලන්න."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement බාධාව ඉවත් කර ඇත. ඔබ මෙම සීමාව සඳහා false අගයක් සඳහන් කර ඇත, එය ඉවත් කරන ලද SDES key negotiation ක්‍රමය භාවිත කිරීමේ උත්සාහයක් ලෙස අර්ථ දැක්වේ. මෙම ක්‍රියාකාරිත්වය ඉවත් කර ඇත; ඒ වෙනුවට DTLS key negotiation සහාය දක්වන සේවාවක් භාවිත කරන්න."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement බාධාව ඉවත් කර ඇත. ඔබ මෙම සීමාව සඳහා true අගයක් සඳහන් කර ඇත, එහි බලපෑමක් නැත, නමුත් ඔබට මෙම බාධාව පිළිවෙළට සකස් කිරීම සඳහා ඉවත් කළ හැකිය."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "ආපසු ඇමතුම් පදනම් getStats() අත්හැර ඇති අතර ඉවත් කරනු ලැබේ. ඒ වෙනුවට පිරිවිතර-අනුකූල getStats() භාවිතා කරන්න."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() Range.expand() is deprecated. ඒ වෙනුවට Selection.modify() භාවිතා කරන්න."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL හි කාවැද්දූ අක්තපත්‍ර අඩංගු උප සම්පත් ඉල්ලීම් (උදා. **********************/) අවහිර කර ඇත."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy විකල්පය අත්හැර ඇති අතර ඉවත් කරනු ඇත."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer හරස් සම්භවය හුදකලා කිරීම අවශ්‍ය වනු ඇත. වැඩි විස්තර සඳහා https://developer.chrome.com/blog/enabling-shared-array-buffer/ බලන්න."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "පරිශීලක සක්‍රිය කිරීමෙන් තොරව speechSynthesis.speak() අත්හරින අතර ඉවත් කරනු ඇත."}, "generated/Deprecation.ts | UnloadHandler": {"message": "සිදුවීම් අසන්නන් ඉවත් කර ඇති අතර ඉවත් කරනු ලැබේ."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer භාවිත කිරීම දිගටම කරගෙන යාමට දිගු හරස් සම්භවය හුදකලාව තෝරා ගත යුතුය. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ බලන්න."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter උපලක්ෂණය අතහැර දමා ඇත, ඒ වෙනුවට GPUAdapterInfo isFallbackAdapter උපලක්ෂණය භාවිතා කරන්න."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 XMLHttpRequest හි ප්‍රතිචාර json මගින් සහාය නොදක්වයි"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "ප්‍රධාන පොටෙහි සමමුහුර්ත XMLHttpRequest අවසාන පරිශීලකයාගේ අත්දැකීමට ඇති අහිතකර බලපෑම් නිසා අත්හරිනු ලැබේ. තවත් උදවු සඳහා, https://xhr.spec.whatwg.org/ පරීක්ෂා කරන්න."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "සජීවිකරණය"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "කිසියම් පරිශීලක අන්තර්ක්‍රියාවක් නොමැතිව මූලද්‍රව්‍ය චලනය වන විට පිරිසැලසුම් මාරුවීම් සිදුවේ. [මූලද්‍රව්‍ය එකතු කිරීම, ඉවත් කිරීම හෝ පිටුව පූරණය වන විට ඒවායේ අකුරු වෙනස් වීම වැනි පිරිසැලසුම් මාරුවීම්](https://web.dev/articles/optimize-cls), සඳහා හේතු විමර්ශනය කරන්න."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "අකුරු ඉල්ලීම"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "එන්නත් කළ iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "පිරිසැලසුම් මාරු පොකුර @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "පිරිසැලසුම් මාරු කිරීමේ වැරදිකරුවන් කිසිවෙකු හඳුනා ගැනීමට නොහැකි විය"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "පිරිසැලසුම් මාරු කිරීම් නැත"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "පිරිසැලසුම් මාරුව වැරදිකරුවන්"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "ඉහළ පිරිසැලසුම් මාරු වැරදිකරුවන්"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "නරකම පොකුරක්"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "නරකම පිරිසැලසුම් මාරු පොකුර"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "කෑෂයේ TTL"}, "models/trace/insights/Cache.ts | description": {"message": "දිගු කෑෂ් ආයු කාලයක් ඔබේ පිටුවට නැවත නැවතත් පැමිණීම් වේගවත් කළ හැක. [තව දැන ගන්න](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "අකාර්යක්ෂම කෑෂ් ප්‍රතිපත්ති සහිත ඉල්ලීම් නැත"}, "models/trace/insights/Cache.ts | others": {"message": "වෙනත් {PH1}ක්"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "ඉල්ලීම"}, "models/trace/insights/Cache.ts | title": {"message": "කාර්යක්ෂම කෑෂ් ආයු කාලයන් භාවිතා කරන්න"}, "models/trace/insights/DOMSize.ts | description": {"message": "විශාල DOM එකක් මඟින් විලාස ගණනය කිරීම් සහ පිරිසැලසුම් ප්‍රතිප්‍රවාහ කාලසීමාව වැඩි කළ හැකි අතර, එය පිටු ප්‍රතිචාර දැක්වීමට බලපායි. විශාල DOM එකක් මතක භාවිතය වැඩි කරයි. [අධික DOM ප්‍රමාණයක් වළක්වා ගන්නා ආකාරය දැන ගන්න](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "මූලාංගය"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "බොහෝ දරුවන්"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM ගැඹුර"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "සංඛ්‍යානය"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM ප්‍රමාණය ප්‍රශස්ත කරන්න"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "මුළු මූලිකාංග"}, "models/trace/insights/DOMSize.ts | value": {"message": "අගය"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "ඔබේ පළමු ජාල ඉල්ලීම වඩාත් වැදගත් වේ.  යළි-යොමුවීම් වළක්වා ගැනීමෙන්, වේගවත් සේවාදායක ප්‍රතිචාරයක් සහතික කිරීමෙන් සහ පෙළ සම්පීඩනය සබල කිරීමෙන් එහි ප්‍රමාදය අඩු කරන්න."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "යළි-යොමුවීම් තිබුණා (යළි-යොමුවීම් {PH1}, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "සේවාදායකයා සෙමින් ප්‍රතිචාර දැක්වීය (නිරීක්ෂණය කරන ලදි {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "කිසිදු සම්පීඩනයක් යොදවා නැත"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "යළි-යොමුවීම් වළක්වයි"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "සේවාදායකය ඉක්මනින් ප්‍රතිචාර දක්වයි (නිරීක්ෂණය කරන ලදි {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "පෙළ සම්පීඩනය යොදයි"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "යළි-යොමුවීම්"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "සේවාදායක ප්‍රතිචාර කාලය"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ලේඛන ඉල්ලීම් ප්‍රමාදය"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "සම්පීඩනය නොකළ බාගත කිරීම"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "අනුපිටපත් කළ බයිට්"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "මූලාශ්‍රය"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "ජාල ක්‍රියාකාරකම් මඟින් පරිභෝජනය කරන අනවශ්‍ය බයිට් අඩු කිරීම සඳහා පොදි වලින් විශාල, අනුපත් JavaScript මොඩියුල ඉවත් කරන්න."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "අනුපිටපත් කළ ජාවාස්ක්‍රිප්ට්"}, "models/trace/insights/FontDisplay.ts | description": {"message": "පෙළ අඛණ්ඩව දෘශ්‍යමාන බව සහතික කිරීම සඳහා [font-display](https://developer.chrome.com/blog/font-display) සිට swap හෝ optional දක්වා සැකසීම සලකා බලන්න. [අකුරු මාත්‍රික ප්‍රතික්‍ෂේප කිරීම්](https://developer.chrome.com/blog/font-fallbacks) සමග පිරිසැලසුම් මාරු කිරීම් අවම කිරීමට swap තවදුරටත් ප්‍රශස්ත කළ හැක."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ෆොන්ටය"}, "models/trace/insights/FontDisplay.ts | title": {"message": "අකුරු සංදර්ශකය"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "කාලය නාස්ති කළා"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(නිර්නාමික)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "බොහෝ API, සාමාන්‍යයෙන් පිරිසැලසුම් ජ්‍යාමිතිය කියවන අතර, විලාසය සහ පිරිසැලසුම ගණනය කිරීම සඳහා විදැහුම් එන්ජිමට ස්ක්‍රිප්ට් ක්‍රියාත්මක කිරීම විරාම කිරීමට බල කරයි. බලහත්කාරයෙන් [ප්‍රතිප්‍රවාහ කිරීම](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) සහ එහි අවම කිරීම් පිළිබඳව තව දැන ගන්න."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "ඇමතුම් අට්ටි අනුසාරය"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "බලාත්මක කළ ප්‍රතිප්‍රවාහය"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "ඉහළම ක්‍රියාකාරී ඇමතුම"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "මුළු ප්‍රතිප්‍රවාහ කාලය"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[උපලක්ෂණ නොවන]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "රූප බාගත කිරීමේ කාලය අඩු කිරීමෙන් පිටුවේ සහ LCP හි සංජානනීය පැටවීමේ කාලය වැඩි දියුණු කළ හැක. [රූප ප්‍රමාණය ප්‍රශස්ත කිරීම ගැන තව දැන ගන්න](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ඇස්තමේන්තුගත {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ප්‍රශස්ත කළ හැකි රූප නැත"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ගොනු ප්‍රමාණය ප්‍රශස්ත කරන්න"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "වෙනත් {PH1}ක්"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "රූප බෙදා හැරීම වැඩි දියුණු කරන්න"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "රූප සංකෝචන සාධකය වැඩි කිරීමෙන් මෙම රූපයේ බාගත කිරීමේ ප්‍රමාණය වැඩි දියුණු කළ හැක."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "නවීන රූප ආකෘතියක් (WebP, AVIF) භාවිත කිරීම හෝ රූප සංකෝචන වැඩි කිරීම මඟින් මෙම රූපයේ බාගත කිරීමේ ප්‍රමාණය වැඩි දියුණු කළ හැක."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "මෙම රූප ගොනුව එහි ප්‍රදර්ශනය කරන ලද මානයන් ({PH2}) සඳහා ({PH1}) අවශ්‍ය ප්‍රමාණයට වඩා විශාල වේ. රූප බාගත කිරීමේ ප්‍රමාණය අඩු කිරීමට ප්‍රතිචාරාත්මක රූප භාවිත කරන්න."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF වෙනුවට වීඩියෝ ආකෘති භාවිත කිරීමෙන් සජීවීකරණ අන්තර්ගතයේ බාගත කිරීමේ ප්‍රමාණය වැඩි දියුණු කළ හැක."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "දිගම අදියර සමග විමර්ශනය ආරම්භ කරන්න. [ප්‍රමාදය අවම කර ගත හැක](https://web.dev/articles/optimize-inp#optimize_interactions). සැකසුම් කාලය අඩු කිරීම සඳහා, [ප්‍රධාන පොට පිරිවැය ප්‍රශස්ත කරන්න](https://web.dev/articles/optimize-long-tasks), බොහෝ විට JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "කාල පරාසය"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ආදාන ප්‍රමාදය"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "අන්තර්ක්‍රියා අනාවරණය නොවුණි"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "අදියර"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "ඉදිරිපත් කිරීමේ ප්‍රමාදය"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "සැකසුම් කිරීම් කාල සීමාව"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "අදියර අනුව INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP රූපය HTML වෙතින් වහාම [සොයා ගත හැකි බවට](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) පත් කිරීමෙන් සහ [කම්මැලි-පූරණය වළක්වා ගැනීමෙන්](https://web.dev/articles/lcp-lazy-loading) LCP ප්‍රශස්ත කරන්න"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "ලබා ගැනීමේ ප්‍රමුඛත්වය=ඉහළ යෙදී ඇත"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high යෙදිය යුතු ය"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "කම්මැලි බරක් යොදන්නේ නැත"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP රූපය මුල්ම ආරම්භක ස්ථානයෙන් පසුව {PH1} පූරණය කරන ලදි."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP අනාවරණය වී නොමැත"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP යනු රූපයක් නොවන නිසා LCP සම්පතක් අනාවරණය නොවුණි"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "මූලික ලේඛනයේ ඉල්ලීම සොයා ගත හැක"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP ඉල්ලීම සොයා ගැනීම"}, "models/trace/insights/LCPPhases.ts | description": {"message": "එක් එක් [අදියරකටම නිශ්චිත වැඩි දියුණු කිරීමේ උපාය මාර්ග ඇත](https://web.dev/articles/optimize-lcp#lcp-breakdown). ඉතා මැනවින්, LCP කාලයෙන් වැඩි ප්‍රමාණයක් වැය කළ යුත්තේ ප්‍රමාදයන් තුළ නොව සම්පත් පැටවීම සඳහා ය."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "කාල පරාසය"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "මූලද්‍රව්‍ය විදැහුම් ප්‍රමාදය"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ක්ෂේත්‍රය p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP අනාවරණය වී නොමැත"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "අදියර"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "සම්පත් පූරණ කිරීමේ ප්‍රමාදය"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "සම්පත් පූරණ වීමේ කාල සීමාව"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "පළමු බයිට් එකට කාලයයි"}, "models/trace/insights/LCPPhases.ts | title": {"message": "අදියර අනුව LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "ස්ක්‍රිප්ට"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "අපතේ ගිය බයිට්"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "පොලිෆිල් සහ පරිවර්තන මඟින් පැරණි බ්‍රව්සර්වලට නව ජාවාස්ක්‍රිප්ට් විශේෂාංග භාවිතා කිරීමට හැකියාව ලැබේ. කෙසේ වෙතත්, බොහෝ ඒවා නවීන බ්‍රව්සර් සඳහා අවශ්‍ය නොවේ. ඔබ පැරණි බ්‍රව්සර් සඳහා සහාය දැක්විය යුතු බව ඔබ දන්නේ නම් මිස, [මූලික රේඛා](https://web.dev/articles/baseline-and-polyfills) විශේෂාංග විකෘති නොකිරීමට ඔබේ ජාවාස්ක්‍රිප්ට් ගොඩනැගීමේ ක්‍රියාවලිය වෙනස් කිරීම සලකා බලන්න. [බොහෝ අඩවි වලට ES6+ කේතය පරිවර්තනයකින් තොරව යෙදවිය හැක්කේ ඇයි දැයි දැන ගන්න](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "ලෙගසි ජාවාස්ක්‍රිප්ට්"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 සහ HTTP/3, HTTP/1.1 හට වඩා බොහෝ ප්‍රතිලාභ ලබා දෙයි, උදාහරණයක් ලෙස බහුකාර්යකරණය. [නවීන HTTP භාවිතය පිළිබඳ තව දැන ගන්න](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 ඉල්ලීම් භාවිතා කර නැත"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "ප්‍රොටොකෝලය"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "ඉල්ලීම"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "නවීන HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "මූලාරම්භය"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "ඉල්ලීම"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "මූලාශ්‍රය"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "වේලාව"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "ඇස්තමේන්තුගත LCP සුරැකුම්"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "භාවිතයට නොගත් පූර්ව සම්බන්ධතාවය. crossorigin උපලක්ෂණය නිවැරදිව භාවිතා කර තිබේ දැයි පරීක්ෂා කරන්න."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "පිටු පූරණ වීම වැඩි දියුණු කිරීම සඳහා දාමවල දිග අඩු කිරීම, සම්පත් බාගැනීම් ප්‍රමාණය අඩු කිරීම හෝ අනවශ්‍ය සම්පත් බාගැනීම කල් දැමීම මඟින් [තීරණාත්මක ඉල්ලීම් දාමගත කිරීමෙන් වළකින්න](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "ඔබේ වඩාත්ම වැදගත් මූලාරම්භයන්ට [පූර්ව සම්බන්ධතා](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ඉඟි එක් කරන්න, නමුත් 4ට වඩා භාවිතා නොකිරීමට උත්සාහ කරන්න."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "අපේක්ෂකයින් පූර්ව සම්බන්ධ කරන්න"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "උපරිම තීරණාත්මක මාර්ග පමාව:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ජාල පරායත්තතා මඟින් බලපෑමට ලක් වූ විදහීම් කාර්යයන් නොමැත"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "පූර්ව සම්බන්ධතාවය සඳහා අමතර මූලාරම්භයන් හොඳ අපේක්ෂකයින් නොවේ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "කිසිදු මූලාරම්භයක් පූර්ව සම්බන්ධිත නොවීය"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[පූර්ව සම්බන්ධතා](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ඉඟි මඟින් බ්‍රවුසරයට පිටු පූරණයේ දී කලින් සම්බන්ධතාවයක් ස්ථාපිත කිරීමට උදවු වන අතර, එම ආරම්භය සඳහා පළමු ඉල්ලීම කරන විට කාලය ඉතිරි කරයි. පිටුව පූර්ව සම්බන්ධ වූ මූලාරම්භයන් පහත දැක්වේ."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "පූර්ව සම්බන්ධිත මූලාරම්භයන්"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ජාල පරායත්තතා ගස"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "preconnect සම්බන්ධතා 4කට වඩා හමු විය. මේවා අරපිරිමැස්මෙන් භාවිතා කළ යුතු අතර වඩාත්ම වැදගත් මූලාරම්භයන් සඳහා පමණක් භාවිතා කළ යුතු ය."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "භාවිතයට නොගත් පූර්ව සම්බන්ධතාවය. පිටුව ඉල්ලා සිටීමට ඉඩ ඇති මූලාරම්භයන් සඳහා preconnect පමණක් භාවිතා කරන්න."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "පිටු පූරණ වීම වැඩි දියුණු කිරීම සඳහා දාමවල දිග අඩු කිරීම, සම්පත් බාගැනීම් ප්‍රමාණය අඩු කිරීම හෝ අනවශ්‍ය සම්පත් බාගැනීම කල් දැමීම මඟින් තීරණාත්මක ඉල්ලීම් දාමගත කිරීමෙන් වළකින්න."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "ඉල්ලීම් පිටුවේ මූලික විදැහුම්කරණය අවහිර කරයි, එය LCP ප්‍රමාද විය හැක. [කල්දැමීම හෝ ඇතුල් කිරීම](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) මෙම ජාල ඉල්ලීම් තීරණාත්මක මාර්ගයෙන් පිටතට ගෙන යා හැක."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "කාල පරාසය"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "මෙම සංචාලනය සඳහා විදැහුම් අවහිර කිරීමේ ඉල්ලීම් නොමැත"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "ඉල්ලීම"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "අවහිර කිරීමේ ඉල්ලීම් ඉදිරිපත් කරන්න"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Recalculate Style පිරිවැය ඉහළ මට්ටමක පවතී නම්, තේරීම් ප්‍රශස්තකරණයට ඒවා අඩු කළ හැක. ඉහළ ගත වූ කාලය සහ ඉහළ මන්දගාමී මාර්ගය % යන දෙකම සමගින් [තේරීම් ප්‍රශස්ත කරන්න](https://developer.chrome.com/docs/devtools/performance/selector-stats). සරල තේරීම්, අඩු තේරීම්, කුඩා DOM සහ නොගැඹුරු DOM යන සියල්ලම ගැළපෙන පිරිවැය අඩු කරයි."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "ගත වූ කාලය"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS තේරීම් දත්ත හමු නොවුණි. කාර්ය සාධන පැනල සැකසීම් තුළ CSS තේරීම් සංඛ්‍යාලේඛන සබල කිරීමට අවශ්‍ය වේ."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ගැළපුම් ප්‍රයත්න"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "ගැළපුම් ගණන"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS තේරීම් පිරිවැය"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ඉහළ තේරීම් කරන්නන්"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "එකතුව"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "ප්‍රධාන පොට වේලාව"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "3වන පාර්ශ්වය"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "හුවමාරු තරම"}, "models/trace/insights/ThirdParties.ts | description": {"message": "3 වන පාර්ශවීය කේතය පැටවීමේ ක්‍රියාකාරිත්වයට සැලකිය යුතු ලෙස බලපෑ හැක. ඔබේ පිටුවේ අන්තර්ගතයට ප්‍රමුඛත්වය දීම සඳහා [3 වන පාර්ශවීය කේත පූරණය අඩු කර කල් දමන්න](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "තෙවන පාර්ශ්වයන් හමු නොවුණි"}, "models/trace/insights/ThirdParties.ts | title": {"message": "3 වන පාර්ශව"}, "models/trace/insights/Viewport.ts | description": {"message": "viewport ජංගම දුරකථන සඳහා ප්‍රශස්ත කර නොමැති නම්, තට්ටු අන්තර්ක්‍රියා [300 ms දක්වා ප්‍රමාද විය](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) හැක."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "ජංගම තට්ටු ප්‍රමාදය"}, "models/trace/insights/Viewport.ts | title": {"message": "ජංගමය සඳහා බැලීම් පෝටය ප්‍රශස්ත කරන්න"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET ඉල්ලීමක් හරහා පූරණය කරන ලද පිටු පමණක් පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් ලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX තත්ත්ව කේතයක් සහිත පිටු පමණක් හැඹිලිගත කළ හැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome හැඹිලිය තුළ ඇති අතරතුර ජාවාස්ක්‍රිප්ට් ක්‍රියාත්මක කිරීම සඳහා වන උත්සාහයක් අනාවරණය කර ගත්තේය."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner එකක් ඉල්ලූ පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ආපසු/ඉදිරි හැඹිලිය සලකුණු කිරීම් මගින් අබල කර ඇත. එය ස්ථානීයව මෙම උපාංගයේ සබල කිරීමට chrome://flags/#back-forward-cache වෙත පිවිසෙන්න"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "ආපසු/ඉදිරි හැඹිලිය විධාන පේළිය මගින් අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "ප්‍රමාණවත් මතකයක් නොමැති නිසා පසුපස/ඉදිරි හැඹිලිය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "ආපසු/ඉදිරි හැඹිලිය නියෝජිතයා විසින් සහාය නොදක්වයි."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "ආපසු/ඉදිරි හැඹිලිය පූර්ව විදහන්නා මගින් අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "ලියාපදිංචි සවන් දෙන්නන් සමඟ BroadcastChannel නිදර්ශනයක් ඇති බැවින් පිටුව හැඹිලිගත කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store ශීර්ෂකය සහිත පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "හැඹිලිය හිතාමතා හිස් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "වෙනත් පිටුවක් හැඹිලිගත කිරීමට ඉඩ දීම සඳහා පිටුව හැඹිලියෙන් ඉවත් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "ප්ලගින අඩංගු පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "නිර්ණය නොකළ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "ඉවතට සංචාලනය කිරීම මත මාධ්‍ය වාදකයක් වාදනය විය."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API භාවිත කරන සහ පසුධාවන තත්ත්වයක් සකසන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API භාවිත කරන සහ ක්‍රියා හසුරුවන්නන් සකසන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "තිර කියවනය නිසා පසුපසට/ඉදිරියට හැඹිලිය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial AP භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB AP භාවිත කරන පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store භාවිතා කරන පිටුවක කුකීස් අබල කර ඇති නිසා ආපසු/ඉදිරි කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "කැපවූ සේවකයෙකු හෝ වැඩපලක් භාවිතා කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "ලේඛනය ඉවතට සංචාලනය කිරීමට පෙර පූරණය කිරීම අවසන් නොවීය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "ඉවතට සංචාලනය කිරීම මත යෙදුම් බැනරය විද්‍යාමාන විය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "ඉවතට සංචාලනය කිරීම මත Chrome මුරපද කළමනාකරු විද්‍යාමාන විය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "ඉවතට සංචාලනය කිරීම මත DOM පෙරීම සිදු වෙමින් පැවතුණි."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "ඉවතට සංචාලනය කිරීම මත DOM Distiller දක්වනය විද්‍යාමාන විය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "දිගු පණිවිඩ යැවීම් API භාවිත කිරීම නිසා පසුපසට/ඉදිරියට හැඹිලිය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "දිගුකාලීන සම්බන්ධතාවක් සහිත දිගු පසුපසට ඉදිරියට හැඹිලියට ඇතුළු වීමට පෙර සම්බන්ධතාව අවසන් කළ යුතුය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "දිගුකාලීන සම්බන්ධතාවක් සහිත දිගු පසුපසට ඉදිරියට හැඹිලිය තුළ රාමු වෙත පණිවිඩ යැවීමට උත්සාහ කළේය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "දිගු නිසා පසුපසට/ඉදිරියට හැඹිලිය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ඉවතට සංචාලනය මත පිටුව සඳහා පෝරමය නැවත යොමු කිරීම හෝ http මුරපද සංවාදය වැනි ආකෘති සංවාදයක් පෙන්වන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "ඉවතට සංචාලනය මත නොබැඳි පිටුව පෙන්වන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "ඉවතට සංචාලනය කිරීම මත මතකයෙන් බැහැර මැදිහත්වීම් තීරුව විද්‍යාමාන විය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "ඉවතට සංචාලනය කිරීම මත අවසර ඉල්ලීම් තිබුණි."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "ඉවතට සංචාලනය කිරීම මත උත්පතන අවහිරකය විද්‍යාමාන විය."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "ආරක්ෂිත බ්‍රවුස් කිරීම් විස්තර ඉවතට සංචාලනය මත පෙන්වන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "ආරක්ෂිත බ්‍රවුස් කිරීම මෙම පිටුව අපයෝජනාත්මක සහ උත්පතන අවහිර කරන ලද ලෙස සැලකේ."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "පිටුව පසුපස/ඉදිරි හැඹිලියේ තිබියදී සේවා සේවකයෙකු සක්‍රිය කරන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ලේඛන දෝෂය නිසා පසුපසට ඉදිරියට හැඹිලිය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames භාවිත කරන පිටු bfcache තුළ ගබඩා කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "වෙනත් පිටුවක් හැඹිලිගත කිරීමට ඉඩ දීම සඳහා පිටුව හැඹිලියෙන් ඉවත් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "මාධ්‍ය ප්‍රවාහ ප්‍රවේශය ලබා දී ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "ඇතැම් ආකාරවල කාවැද්දූ අන්තර්ගත ඇති පිටු (උදා. PDF) දැනට පසුපස/ඉදිරි කෑෂය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "විවෘත IndexedDB සම්බන්ධතාවක් ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB සිදුවීමක් හේතුවෙන් පසුපසට/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Ineligible API භාවිත කරන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "JavaScript දිගු මගින් එන්නත් කරන ලද පිටු දැනට පසුපස/ඉදිරි කෑෂය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "StyleSheet දිගු මගින් එන්නත් කරන ලද පිටු දැනට පසුපස/ඉදිරි කෑෂය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "අභ්‍යන්තර දෝෂය."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "සමහර ජාවාස්ක්‍රිප්ට් ජාල ඉල්ලීම් Cache-Control: no-store ශීර්ෂකය සමග සම්පත් ලැබුණු නිසා ආපසු/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Keepalive ඉල්ලීමක් හේතුවෙන් පසුපසට/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "යතුරු පුවරු අගුල භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "පිටුව ඉවතට සංචාලනය කිරීමට පෙර පූරණය කිරීම අවසන් නොවීය."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ප්‍රධාන සම්පත සතුව cache-control:no-cache ඇති පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ප්‍රධාන සම්පත සතුව cache-control:no-store ඇති පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "පිටුව ආපසු/ඉදිරි හැඹිලියෙන් ප්‍රතිසාධනය කිරීමට හැකි වීමට පෙර සංචාලනය අවලංගු කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "සක්‍රිය ජාල සබැඳුමකට දත්ත ඉතා වැඩියෙන් ලැබුණු නිසා පිටුව හැඹිලිය වෙතින් ඉවත් කරන ලදි. Chrome හැඹිලිගත කිරීම අතරතුර පිටුවකට ලැබිය හැකි දත්ත ප්‍රමාණය සීමා කරයි."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ඉන්ෆ්ලයිට් ෆෙච්() හෝ XHR ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "සක්‍රිය ජාල ඉල්ලීමක් ප්‍රතියොමු කිරීමක් හා සම්බන්ධ වූ නිසා පිටුව පසුපස/ඉදිරි හැඹිලියෙන් ඉවත් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ජාල සම්බන්ධතාවක් දිගු කාලයක් විවෘතව තිබූ නිසා පිටුව හැඹිලියෙන් ඉවත් කරන ලදී. Chrome හැඹිලිගත කිරීම අතරතුර පිටුවකට ලැබිය හැකි වේලාවෙහි ප්‍රමාණය සීමා කරයි."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "වලංගු ප්‍රතිචාර ශීර්ෂයක් නොමැති පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "ප්‍රධාන රාමුව හැර වෙනත් රාමුවක සංචාලනය සිදු විය."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "දැනට කෙරෙමින් පවතින සුචිගත DB ගනුදෙනු සහිත පිටු පසුපස/ඉදිරි හැඹිලිය සඳහා දැනට සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "පියසැරි-තුළ ජාල ඉල්ලීමක් සහිත පිටු දැනට ආපසු/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "පියසැරි-තුල යළි ලබා ගැනීම් ජාල ඉල්ලීමක් සහිත පිටු දැනට ආපසු/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "පියසැරි-තුළ ජාල ඉල්ලීමක් සහිත පිටු දැනට ආපසු/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "පියාසර XHR ජාල ඉල්ලීමක් සහිත පිටු දැනට ආපසු/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "පින්තූරයක් තුළ පින්තූරයක් භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "මුද්‍රණ UI පෙන්වන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "පිටුව 'window.open()' භාවිතයෙන් විවෘත කරන ලද අතර වෙනත් ටැබයක එයට යොමුවක් ඇත, නැතහොත් පිටුව කවුළුවක් විවෘත කළේය."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "පසුපස/ඉදිරි හැඹිලියේ ඇති පිටුව සඳහා විදැහුම්කරණ ක්‍රියාවලිය බිඳ වැටිණි."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "පසුපස/ඉදිරි හැඹිලියේ ඇති පිටුව සඳහා විදැහුම්කරණ ක්‍රියාවලිය විනාශ කරන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ශ්‍රව්‍ය ග්‍රහණ අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "සංවේදක අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "පසුබිම් සමමුහුර්ත කිරීම හෝ යළි ලබා ගැනීම් අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "දැනුම්දීම් අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "ගබඩා ප්‍රවේශය ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "වීඩියෝ ග්‍රහණ අවසර ඉල්ලා ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPS URL යෝජනා ක්‍රමය ඇති පිටු පමණක් හැඹිලිගත කළ හැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "පිටුව පසුපස/ඉදිරි හැඹිලිය තුළ ඇති අතරතුර සේවා සේවකයෙකු විසින් එයට හිමිකම් කියන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "සේවා සේවකයෙකු ආපසු/ඉදිරි හැඹිලිය තුළ ඇති පිටුට MessageEvent යැවීමට උත්සාහ කළේය."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "පිටුවක් පසුපස/ඉදිරි හැඹිලියේ තිබියදී ServiceWorker ලියාපදිංචි කිරීම ඉවත් කරන ලදි."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "සේවා සේවක සක්‍රිය කිරීමක් හේතුවෙන් පිටුව පසුපස/ඉදිරි හැඹිලියෙන් ඉවත් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome යළි ආරම්භ කර ආපසු/ඉදිරි හැඹිලි ඇතුළත් කිරීම් හිස් කරන ලදී."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "පිටුවෙහි iframe එකක් සම්පූර්ණ නොවූ සංචාලනයක් ආරම්භ කළේය."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "උපසම්පත සතුව cache-control:no-cache ඇති පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "උපසම්පත සතුව cache-control:no-store ඇති පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "පිටුව ආපසු/ඉදිරි හැඹිලියේ උපරිම කාලය ඉක්මවා ඇති අතර කල් ඉකුත් විය."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "පිටුව පසුපස/ඉදිරි හැඹිලිය ඇතුළු කිරීමේ කාලය අවසන් විය (දීර්ඝ කාලීනව ධාවනය වන පිටු සැඟවුම් හසුරුවන්නන් නිසා විය හැකිය)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "පිටුවේ ප්‍රධාන රාමුවේ බෑමේ හසුරුවන්නෙක් ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "පිටුවේ උප රාමුවක බෑමේ හසුරුවන්නෙක් ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "බ්‍රව්සරය පරිශීලක නියෝජිත ප්‍රතික්ෂේප කරන ශීර්ෂකය වෙනස් කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "වීඩියෝ හෝ ශ්‍රව්‍ය පටිගත කිරීමට ප්‍රවේශය ලබා දී ඇති පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPSservice භාවිත කරන පිටු දැනට bfcache සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC සහිත පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC භාවිතා කර ඇති නිසා ආපසු/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket සහිත පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket භාවිතා කර ඇති නිසා ආපසු/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport සහිත පිටුවලට පසුපස/ඉදිරි හැඹිලිය ඇතුළු කළ නොහැකිය."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport භාවිතා කර ඇති නිසා ආපසු/ඉදිරියට කෑෂය අබල කර ඇත."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR භාවිත කරන පිටු දැනට පසුපස/ඉදිරි හැඹිලිය සඳහා සුදුසුකම් නොලබයි."}}