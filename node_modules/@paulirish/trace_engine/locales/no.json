{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Autorisasjon kommer ikke til å bli dekket av jokertegnet (*) ved håndtering av CORS-hodet Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "For å slå av Cast-integreringen som brukes som standard, bruk disableRemotePlayback-attributtet i stedet for -internal-media-controls-overlay-cast-button-velgeren."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS-utseendeverdien slider-vertical er ikke standardisert og blir fjernet."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Ressursforespørsler med nettadresser som inneholdt både fjernede mellomromstegn (\\(n|r|t)) og mindre-enn-tegn (<), er blokkert. For å kunne laste inn disse ressursene, fjern linjeskift og gjør mindre-enn-tegn om til kode på steder som i attributtverdiene til elementer."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() er avviklet. Bruk heller det standardiserte API-et Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() er avviklet. Bruk heller det standardiserte API-et Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() er avviklet. Bruk heller det standardiserte API-et nextHopProtocol i Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Informasjonskapsler som inneholder tegnet \\(0|r|n), blir avvist i stedet for å bli avkortet."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Oppmykning av samme opphav-reglene ved å angi document.domain er avviklet og kommer til å være avslått som standard. Dette avviklingsvarselet gjelder en opphavsuavhengig tilgang som ble påslått ved å angi document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Utløsing av window.alert fra iframe-elementer med andre opphav er avviklet og kommer til å bli fjernet i fremtiden."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Utløsing av window.confirm fra iframe-elementer med andre opphav er avviklet og kommer til å bli fjernet i fremtiden."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Støtte for data:-nettadresser i SVGUseElement er avviklet og blir fjernet i fremtiden."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() og watchPosition() fungerer ikke på usikre opphav lenger. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() og watchPosition() er avviklet på usikre opphav. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() fungerer ikke på usikre opphav lenger. For å bruke denne funksjonen bør du vurdere å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Fant en <h1>-tag i et <article>-, <aside>-, <nav>- eller <section>-element som det ikke er angitt noen skriftstørrelse for. Størrelsen på denne overskriftsteksten endres snart i denne nettleseren. Les https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 for mer informasjon."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate er avviklet. Bruk RTCPeerConnectionIceErrorEvent.address eller RTCPeerConnectionIceErrorEvent.port i stedet."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Dette formatet for navigator.credentials.get()-forespørselen om digitale legitimasjoner er avviklet. Oppdater kallet for å bruke det nye formatet."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Selgerens opprinnelse og vilkårlige data fra canmakepayment Service Worker-hendelsen er avviklet og blir fjernet: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Nettstedet forespurte en underressurs fra et nettverk det kun hadde tilgang til på grunn av den privilegerte nettverksposisjonen til brukerne sine. Disse forespørslene eksponerer ikke-offentlige enheter og tjenere for internett, noe som øker risikoen for angrep via forfalskning av tredjepartsforespørsler (CSRF) og/eller informasjonslekkasjer. For å forebygge disse risikoene avvikler Chrome forespørsler til ikke-offentlige underressurser når disse igangsettes fra ikke-sikre kontekster, og kommer til å begynne å blokkere dem."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Feltet dailyUpdateUrl for InterestGroups, som ble sendt til joinAdInterestGroup(), har fått det nye navnet updateUrl, for en mer nøyaktig atferd."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator er avviklet. Bruk Intl.Segmenter i stedet."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS-filer kan ikke lastes inn fra file:-nettadresser med mindre de har filetternavnet .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Bruk av SourceBuffer.abort() for å avbryte den asynkrone områdefjerningen som utføres av remove(), er avviklet på grunn av endringer i spesifikasjonen. Støtten kommer til å bli fjernet i fremtiden. Du bør lytte etter updateend-hendelsen i stedet. abort() er kun ment å avbryte asynkrone medietilføyninger eller tilbakestille parsertilstanden."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Å angi en verdi for MediaSource.duration som er lavere enn det høyeste presentasjonstidsstempelet for noen bufret, kodet ramme, er avviklet på grunn av endringer i spesifikasjonen. <PERSON><PERSON>tte for implisitt fjerning av avkortede, bufrede medier kommer til å bli fjernet i fremtiden. Du bør i stedet utføre eksplisitte remove(newDuration, oldDuration)-kall på alle sourceBuffers, hvor newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI kommer til å be om brukstillatelse selv om sysex ikke er angitt i MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification-API-et kan ikke lenger brukes fra usikre opphav. Vurder å flytte programmet ditt til et sikkert opphav, for eksempel HTTPS. Se https://goo.gle/chrome-insecure-origins for mer informasjon."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Det er ikke lenger mulig å be om tillatelse for Notification-API-et via iframe-elementer med andre opphav. Vurder å be om tillatelse via en toppnivåramme eller åpne et nytt vindu i stedet."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Alternativet imageOrientation: 'none' i createImageBitmap er avviklet. Bruk createImageBitmap med alternativet '{imageOrientation: 'from-image'}' i stedet."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partneren din bruker en foreldet (D)TLS-versjon. Snakk med partneren din for å få rettet dette."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Hvis du angir overflow: visible på img-, video- eller canvas-tagger, kan det hende at de produserer visuelt innhold utenfor elementgrensene. Se https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments er avviklet. Bruk siste liten-installasjon for betalingsbehandlere i stedet."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest-kallet ditt omgikk Content-Security-Policy (CSP)-direktivet connect-src. Denne omgåelsen er avviklet. Legg til identifikatoren for betalingsmåten fra PaymentRequest-API-et (i supportedMethods-feltet) i CSP-direktivet connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent er avviklet. Bruk standardisert navigator.storage i stedet."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> med <picture> som forelder er ugyldig og blir derfor ignorert. Bruk <source srcset> i stedet."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame er en leverandørspesifikk metode. Bruk standardmetoden cancelAnimationFrame i stedet."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame er en leverandørspesifikk metode. Bruk standardmetoden requestAnimationFrame i stedet."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen er avviklet. Bruk Document.fullscreenElement i stedet."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() er avviklet. Bruk Element.requestFullscreen() i stedet."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() er avviklet. Bruk Element.requestFullscreen() i stedet."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() er avviklet. Bruk Document.exitFullscreen() i stedet."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() er avviklet. Bruk Document.exitFullscreen() i stedet."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen er avviklet. Bruk Document.fullscreenEnabled i stedet."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Vi avvikler API-et chrome.privacy.websites.privacySandboxEnabled, men av hensyn til bakoverkompatibilitet forblir det aktivt frem til versjon M113. Bruk chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled og chrome.privacy.websites.adMeasurementEnabled i stedet. Se https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Begrensningen DtlsSrtpKeyAgreement er fjernet. Du har angitt verdien false for denne begren<PERSON>, noe som tolkes som et forsøk på å bruke den fjernede SDES key negotiation-metoden. Denne funksjonaliteten er fjernet. Bruk en tjeneste som støtter DTLS key negotiation i stedet."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Begrensningen DtlsSrtpKeyAgreement er fjernet. Du har angitt verdien true for denne begrensningen. Det<PERSON> har ingen virkning, men du kan fjerne denne begrensningen for ryddighets skyld."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Den tilbakekallbaserte metoden getStats() er avviklet og kommer til å bli fjernet. Bruk den spesifikasjonskompatible metoden getStats() i stedet."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() er avviklet. Bruk Selection.modify() i stedet."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Underressursforespørsler med nettadresser som inneholder innebygd legitimasjon (f.eks. **********************/), er blokkert."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy-alternativet er avviklet og kommer til å bli fjernet."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer kommer til å kreve isolering fra andre opphav. Se https://developer.chrome.com/blog/enabling-shared-array-buffer/ for mer informasjon."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() uten brukeraktivering er avviklet og kommer til å bli fjernet."}, "generated/Deprecation.ts | UnloadHandler": {"message": "«unload»-hendelseslyttere er avviklet og kommer til å bli fjernet."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Utvidelser må slå på isolering fra andre opphav for å fortsette å bruke SharedArrayBuffer. Se https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter-attributtet isFallbackAdapter er avviklet. Bruk GPUAdapterInfo-attributtet isFallbackAdapter i stedet."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 støttes ikke av JSON-svaret i XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synkrone XMLHttpRequest-forespørs<PERSON> på hovedtråden er avviklet på grunn av de negative virkningene på sluttbrukerens opplevelse. For å få mer hjelp, se https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animasjon"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Utseendeforskyvninger oppstår når elementer beveger seg uten at brukeren har gjort noe. [Undersøk årsakene til utseendeforskyvninger](https://web.dev/articles/optimize-cls), for eksempel elementer som legges til, fjernes eller endrer skrifttype mens siden lastes inn."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Forespørsel om skrifttype"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Injisert iframe-element"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Klynge med utseendeforskyvning etter {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Fant ingen årsaker til utseendeforskyvning"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Ingen utseendeforskyvninger"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Årsaker til utseendeforskyvning"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "De vanligste årsakene til utseendeforskyvning"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Verste klynge"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Verste klynge med utseendeforskyvning"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Buffer-TTL"}, "models/trace/insights/Cache.ts | description": {"message": "En lang bufferlevetid kan gjøre at gjentatte besøk på siden din går raskere. [Finn ut mer.](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Ingen forespørsler med ineffektive bufferregler"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} til"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Forespørsel"}, "models/trace/insights/Cache.ts | title": {"message": "Bruk effektive bufferlevetider"}, "models/trace/insights/DOMSize.ts | description": {"message": "Store DOM-er kan øke varigheten på stilberegninger og dynamiske tilpasninger av layouten, noe som påvirker sidens responstid. Store DOM-er øker også minnebruken. [<PERSON> ut hvordan du unngår at DOM-en blir for stor.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "<PERSON><PERSON>t underelementer"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-dybde"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistikk"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimaliser DOM-stø<PERSON>sen"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Elementer totalt"}, "models/trace/insights/DOMSize.ts | value": {"message": "Verdi"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Den første nettverksforespørselen er den viktigste.  Reduser tidsforsinkelsen ved å unngå omdirigeringer, sikre rask tjenerrespons og slå på tekstkomprimering."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON><PERSON> ({PH1} v<PERSON><PERSON><PERSON><PERSON>, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON><PERSON><PERSON> svarte tregt (observert {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Ingen komprimering er brukt"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>ek<PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>er raskt (observert {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Bruker tekstkomprimering"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Responstid for tjeneren"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Tidsforsinkelse for dokumentforespørsel"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Ukomp<PERSON><PERSON> ne<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Dupliserte byte"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Fjern store, dupliserte JavaScript-moduler <PERSON><PERSON>ak<PERSON> for å redusere antall byte som brukes unødvendig av nettverksaktiviteten."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplisert JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "V<PERSON>der å sette [font-display](https://developer.chrome.com/blog/font-display) til swap eller optional for å sikre at teksten er synlig hele tiden. swap kan optimaliseres ytterligere for å motvirke utseendeforskyvninger med [overstyring av skrifttypeverdier](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Skrifttype"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Visning av skrifttype"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Tidstap"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonym)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Mange API-er, som vanlig<PERSON> leser layout<PERSON>, tvinger gjengivelsesmotoren til å sette skriptutfø<PERSON> på pause for å beregne stilen og layouten. Finn ut mer om [tvungen dynamisk tilpasning](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) og avhjelpende tiltak."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stabelspor"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Tvungen dynamisk tilpasning"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Mest tidkrevende funksjonskall"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Totaltid for dynamiske tilpasninger"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[uten attribusjon]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "<PERSON><PERSON> du reduserer nedlastingstiden for bilder, kan det redusere den opplevde innlastingstiden for siden og LCP (største innholdsrike opptegning). [Finn ut mer om optimalisering av bildestørrelser](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON><PERSON><PERSON><PERSON> {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Ingen bilder som kan optimaliseres"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimaliser fi<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} til"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Forbedre bi<PERSON>"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "<PERSON><PERSON> du øker komprimeringsfaktoren for bildet, kan nedlastingsst<PERSON><PERSON><PERSON> bli mindre."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "<PERSON>vis du bruker et moderne bildeformat (WebP, AVIF) eller øker bildekomprimeringsfaktoren, kan nedl<PERSON><PERSON><PERSON><PERSON><PERSON> for dette bildet bli mindre."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Denne bildefilen er større enn nødvendig ({PH1}) for de viste dimensjonene ({PH2}). Bruk responsivt bildeinnhold for å redusere nedlastingsstørrelsen for bildet."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Ved å bruke videoformater i stedet for GIF-er kan du redusere nedlastingsstørrelsen på animert innhold."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Begynn med å undersøke den lengste fasen. [Forsinkelser kan minimeres.](https://web.dev/articles/optimize-inp#optimize_interactions) For å redusere behandlingstiden må du [optimalisere kostnadene på hovedtråden](https://web.dev/articles/optimize-long-tasks). Ofte er dette JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Inndataforsinkelse"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Ingen interaksjoner ble registrert"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Presentasjonsforsinkelse"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Be<PERSON>lingsvarighet"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP (tid fra interaksjon til neste opptegning) etter fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimaliser LCP (største innholdsrike opptegning) ved å gjøre LCP-bildet [synlig](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) umiddelbart i HTML-koden og [unngå bruk av utsatt innlasting](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high er tatt i bruk"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high bør brukes"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "utsatt innlasting er ikke brukt"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Bildet med største innholdsrike opptegning (LCP) ble lastet inn {PH1} etter det tidligste startpunktet."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ingen LCP (største innholdsrike opptegning) ble registrert"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Ingen LCP-ressurs ble oppdaget fordi LCP (største innholdsrike opptegning) ikke er et bilde"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Forespørselen er synlig i det opprinnelige dokumentet"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Oppdagelse av forespørsel med LCP (største innholdsrike opptegning)"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Hver [fase har spesifikke forbedringsstrategier](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideelt sett bør mesteparten av LCP-tiden brukes på innlasting av ressurser, ikke på forsinkelser."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Forsinkelse for gjengivelse av element"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "p75-felt"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ingen LCP (største innholdsrike opptegning) ble registrert"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Forsinkelse ved innlasting av ressurser"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Varighet av ressursinnlasting"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Tid til første byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP (største innholdsrike opptegning) etter fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bortkastede byte"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill- og forvandlinger-kode gjør det mulig for nettlesere i eldre versjoner å bruke nye JavaScript-funksjoner. Mange former for slik kode trengs imidlertid ikke i moderne nettlesere. Vurder å endre JavaScript-kodebyggingsprosessen din slik at den ikke transkompilerer [Baseline](https://web.dev/articles/baseline-and-polyfills)-funksjoner, med mindre du vet at du må støtte eldre nettlesere. [Finn ut hvorfor de fleste nettsteder kan implementere ES6+-kode uten transkompilering](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript i en eldre versjon"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 og HTTP/3 har mange fordeler sammenlignet med HTTP/1.1, for eksempel multipleksing. [Finn ut mer om bruk av moderne HTTP.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Ingen forespørsler brukte HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokoll"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Forespørsel"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderne HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Opphav"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Forespørsel"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Tid"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Anslåtte LCP-besparelser"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Ubrukt forhåndstilkobling. Sjekk at crossorigin-attributtet brukes riktig."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Unngå kjeding av kritiske forespø<PERSON><PERSON>](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) ved å redusere lengden på kjedene, redusere nedlastingsstørrelsen på ressursene eller utsette nedlasting av unødvendige ressurser for å bedre sideinnlastingen."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Legg til hint om [for<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) for de viktigste opphavsstedene dine, men prøv å bruke maksimalt fire."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Forhåndstilkoble kandidater"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON><PERSON><PERSON> tidsforsinkelse for kritisk bane:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Ingen gjengivelsesoppgaver påvirkes av nettverksavhengigheter"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Ingen andre opphav er gode kandidater for forhåndstilkobling"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ingen opprinnel<PERSON> ble forhåndstilkoblet"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Hint om [forh<PERSON><PERSON><PERSON><PERSON><PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hjelper nettleseren med å etablere en tilkobling tidligere i sideinnlastingen, noe som sparer tid når den første forespørselen for den aktuelle plasseringen sendes. Følgende er opprinnelsene siden ble forhåndstilkoblet til."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Forhåndstilkoblede opprinnelser"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tre for nettverket"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Fant mer enn fire preconnect-tilkoblinger. Disse bør brukes sparsomt og kun til de viktigste plasseringene."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Ubrukt forhåndstilkobling. Bruk bare preconnect for opprinnelser som det er sannsynlig at siden kommer til å forespørre."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Unngå kjeding av kritiske forespørsler ved å redusere lengden på kjedene, redusere nedlastingsstørrelsen på ressursene eller utsette nedlasting av unødvendige ressurser for å bedre sideinnlastingen."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Forespørsler blokkerer den innledende gjengivelsen av siden, noe som kan forsinke LCP (største innholdsrike opptegning). [Hvis du utsetter eller fletter inn disse](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources), flyttes disse nettverksforespørslene ut av den kritiske banen."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "<PERSON><PERSON> forespørsler om blokkering av gjengivelse for denne navigeringen"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Forespørsel"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Forespørsler som blokkerer gjengivelse"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "<PERSON><PERSON> kostnadene for omberegning av stiler fortsetter å være høye, kan du senke dem ved å optimalisere velgere. [Optimaliser velgere](https://developer.chrome.com/docs/devtools/performance/selector-stats) som både har lang forløpstid og følger den langsomme banen en stor prosentandel av tiden. Både enklere velgere, færre velgere, en mindre DOM og en mindre omfattende DOM er med på å senke kostnadene ved å beregne elementsamsvar."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON><PERSON><PERSON> tid"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Fant ingen data for CSS-velgere. Du må slå på statistikk for CSS-velgere i innstillingene for ytelsespanelet."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Samsvarsforsøk"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON> sams<PERSON><PERSON>e elementer"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Kostnader for CSS-velger"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Mest tidkrevende velgere"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Totalt"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Tid i hovedtråden"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Tredjepart"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Overføringsstørrelse"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Tredjepartskode kan ha betydelig innvirkning på hvor lang tid det tar å laste inn siden. [Reduser og utsett innlasting av tredjepartskode](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) for å prioritere innholdet på siden din."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Fant ingen tredjeparter"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Tredjeparter"}, "models/trace/insights/Viewport.ts | description": {"message": "Trykkeinteraksjoner kan [forsinkes med opptil 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) hvis det synlige området ikke er optimalisert for mobil."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Forsinkelse av trykk på mobil"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimaliser det synlige området for mobil"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Kun sider som lastes inn via GET-forespørsler, kan lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Kun sider med statuskode 2XX kan bufres."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome oppdaget et forsøk på kjøring av JavaScript mens siden var i bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Sider som bruker har forespurt <PERSON><PERSON><PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Frem-og-tilbake-bufferen er deaktivert via flagg. Gå til chrome://flags/#back-forward-cache for å aktivere den lokalt på denne enheten."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Frem-og-tilbake-bufferen er deaktivert via kommandolinjen."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av utilstrekkelig minne."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Frem-og-tilbake-bufferen støttes ikke av den innbyggende nettleseren."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Frem-og-tilbake-bufferen er deaktivert for forhåndsgjengivelse."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON>n kan ikke buf<PERSON>, ettersom den har en BroadcastChannel-forekomst med registrerte lyttere."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Sider med hodet «cache-control:no-store» kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON><PERSON>n ble tømt med vilje."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON>n ble fjernet fra bufferen slik at en annen side kunne bli bufret."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Sider som inneholder programtillegg, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Udefinert"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Sider som bruker FileChooser-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Sider som bruker File System Access-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Sider som bruker Media Device Dispatcher, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "En mediespiller holdt på å spille av da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Sider som bruker MediaSession-API-et og angir en avspillingsstatus, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Sider som bruker MediaSession-API-et og angir handlingsbehandlere, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av skjermleseren."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Sider som bruker <PERSON><PERSON>, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Sider som bruker Serial-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Sider som bruker WebAuthentication-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Sider som bruker WebBluetooth-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Sider som bruker WebUSB-API-et, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi informasjonskapsler er deaktivert på en side som bruker Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Sider som bruker en dedikert Service Worker eller worklet, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokumentet ble ikke ferdig innlastet før nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> var til stede da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome <PERSON> var til stede da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-destillering pågikk da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM-destilleringsvisningen var til stede da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi utvidelser bruker meldings-API-et."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Utvidelser med tilkoblinger med lang levetid må lukke tilkoblingen før de kan lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Utvidelser med tilkoblinger med lang levetid prøvde å sende meldinger til rammer i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av utvidelser."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "En modal dialogboks for siden, for eksempel en dialogboks om å sende inn et skjema på nytt eller en dialogboks for HTTP-passord, ble vist da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "<PERSON>n hvor det står at enheten er uten nett, ble vist da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Intervensjonsfeltet hvor det står at enheten ikke har nok minne, var til stede da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Det fantes tillatelsesforespørsler da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Blokkering av forgrunnsvinduer var til stede da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing-de<PERSON><PERSON> ble vist da nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing ans<PERSON> denne siden for å være villedende og blokkerte forgrunnsvinduet."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "En Service Worker ble aktivert mens siden var i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av en dokumentfeil."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Sider som bruker <PERSON>, kan ikke lagres i bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON>n ble fjernet fra bufferen slik at en annen side kunne bli bufret."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON>r som har gitt mediestr<PERSON>m<PERSON>g, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Sider med visse typer innebygd innhold (f.eks. PDF-filer) kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Sider som bruker <PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Sider med åpne IndexedDB-tilkoblinger kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av en IndexedDB-hendelse."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Det ble brukt API-er som ikke er kvalifisert."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Sider som utvidelser har injisert JavaScript i, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Sider som utvidelser har injisert StyleSheet i, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Intern feil."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi en JavaScript-nettverksforespørsel har mottatt en ressurs med Cache-Control: no-store-hodet."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Frem-og-tilbake-bufferen er deaktivert på grunn av en keepalive-forespørsel."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON>r som bruker ta<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON>n ble ikke ferdig innlastet før nettleseren navigerte bort."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Sider som har «cache-control:no-cache» i hovedressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Sider som har «cache-control:no-store» i hovedressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigeringen ble avbrutt før siden kunne gjenopprettes fra frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Siden ble fjernet fra bufferen fordi en aktiv nettverkstilkobling mottok for mye data. Chrome begrenser hvor mye data sider kan motta mens de er bufret."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON>r med påg<PERSON>ende fetch()- eller XHR-foresp<PERSON><PERSON><PERSON> kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "<PERSON>n ble fjernet fra frem-og-tilbake-bufferen fordi en aktiv nettverksforespørsel medførte en viderekobling."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Siden ble fjernet fra bufferen fordi en nettverkstilkobling var åpen for lenge. Chrome begrenser hvor lenge sider kan motta data mens de er bufret."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Sider som ikke har noe gyldig svarhode, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigeringen skjedde i en annen ramme enn hovedrammen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Sider med pågående IndexedDB-transaksjoner, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "<PERSON>r med pågående nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Sider med påg<PERSON>e fetch()-nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "<PERSON>r med pågående nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Sider med pågående XHR-nettverksforespørsler kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Sider som bruker <PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Sider som bruker bilde-i-bilde, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "<PERSON>r som viser UI for utskrift, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "<PERSON>n ble åpnet med 'window.open()', og en annen fane har en henvisning til den, eller siden åpnet et vindu."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Gjengivelsesprosessen for siden i frem-og-tilbake-bufferen krasjet."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Gjengivelsesprosessen for siden i frem-og-tilbake-bufferen ble avsluttet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Sider som har bedt om tillatelser til lydopptak, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Sider som har bedt om sensortillate<PERSON>er, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Sider som har bedt om bakgrunnssynkronisering eller fetch()-till<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON>r som har bedt om MIDI-till<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Sider som har bedt om var<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Sider som har bedt om tilgang til lagring, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Sider som har bedt om tillatelser til videoopptak, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Kun sider med HTTP eller HTTPS som nettadresseprotokoll kan bufres."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON>n ble gjort krav på av en Service Worker mens den var i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "En Service Worker prøvde å sende en MessageEvent til siden i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker ble avregistrert mens en side var i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON>n ble fjernet fra frem-og-tilbake-bufferen på grunn av en Service Worker-aktivering."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome ble startet på nytt og slettet oppføringene i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Sider som bruker Shared<PERSON><PERSON><PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Sider som bruker SpeechRecogni<PERSON>, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Sider som bruker SpeechSynthesis, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Et iframe-element på siden startet en navigering som ikke ble fullført."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Sider som har «cache-control:no-cache» i underressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Sider som har «cache-control:no-store» i underressursen sin, kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON>n overskred den maksimale tiden i frem-og-tilbake-bufferen og har utløpt."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Siden fikk tidsavbrudd under lagring i frem-og-tilbake-bufferen (sannsynligvis på grunn av pagehide-behandlere med lang kjøretid)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Siden har en utlastingsbehandler i hovedrammen."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Siden har en utlastingsbehandler i en underramme."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON> har endret hodet for overstyring av brukeragenten."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Sider som har gitt tilgang til opptak av video eller lyd, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Sider som bruker WebDatabase, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Sider som bruker WebHID, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Sider som bruker WebLocks, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Sider som bruker WebNfc, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Sider som bruker WebOTPService, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Sider med WebRTC kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi WebRTC er brukt."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Sider som bruker WebShare, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Sider med WebSocket kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi WebSocket er brukt."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Sider med WebTransport kan ikke lagres i frem-og-tilbake-bufferen."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Frem-og-tilbake-bufferen er deaktivert fordi WebTransport er brukt."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Sider som bruker WebXR, kan for tiden ikke lagres i frem-og-tilbake-bufferen."}}