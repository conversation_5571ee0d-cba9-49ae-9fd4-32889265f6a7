{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "O valor de aspecto do CSS slider-vertical non está estandarizado, polo que se quitará."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "A opción de definir o atributo document.domain para relaxar a política da mesma orixe quedou obsoleta e desactivarase de forma predeterminada. Este aviso de obsolescencia está relacionado cun acceso de orixes cruzadas que se activou ao definir o atributo document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "A acción de activar window.alert desde iframes de orixes cruzadas quedou obsoleta e deixará de estar dispoñible."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "A acción de activar window.confirm desde iframes de orixes cruzadas quedou obsoleta e deixará de estar dispoñible."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "SVGUseElement xa non é compatible cos URL co formato data:, polo que se quitará no futuro."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Os atributos getCurrentPosition() e watchPosition() xa non funcionan nas orixes non seguras. Recomendámosche que cambies a aplicación a unha orixe segura, como HTTPS, para usar esta función. Consulta máis información en https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Os atributos getCurrentPosition() e watchPosition() quedaron obsoletos nas orixes non seguras. Recomendámosche que cambies a aplicación a unha orixe segura, como HTTPS, para usar esta función. Consulta máis información en https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Atopou unha etiqueta <h1> dentro dunha etiqueta <article>, <aside>, <nav> ou <section> que non tiña un tamaño de fonte especificado. Nun futuro próximo, o tamaño deste texto de cabeceira vai cambiar neste navegador. Consulta https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 para obter máis información."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate quedou obsoleto. Mellor usa RTCPeerConnectionIceErrorEvent.address ou RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Este formato da solicitude navigator.credentials.get() de credenciais dixitais está obsoleto. Actualiza a chamada para usar o formato novo."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Os datos arbitrarios e a orixe do comerciante do evento de service worker canmakepayment quedaron obsoletos e quitaranse: topOrigin, paymentRequestOrigin, methodData e modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "O nome do campo dailyUpdateUrl de InterestGroups transmitido a joinAdInterestGroup() cambiou a updateUrl para reflectir o seu comportamento con máis precisión."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "O construtor Intl.v8BreakIterator quedou obsoleto. Mellor usa Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Xa non se pode usar SourceBuffer.abort() para cancelar a retirada de intervalos asíncronos de remove() debido a un cambio na especificación. Deixará de estar dispoñible no futuro. Mellor usa o evento updateend. abort() deseñouse para evitar que se engada contido multimedia de forma asíncrona ou restablecer o estado do analizador."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "A Notification API xa non se pode usar desde orixes non seguras. Recomendámosche que cambies a aplicación a unha orixe segura, como HTTPS. Consulta máis información en https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "O permiso para a Notification API xa non se pode solicitar desde un iframe de orixes cruzadas. Recomendámosche que solicites permiso desde un marco de nivel superior ou que abras unha nova ventá."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "A opción imageOrientation: 'none' de createImageBitmap quedou obsoleta. Proba a usar createImageBitmap coa opción {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Se especificas overflow: visible nas etiquetas img, de vídeo e de lenzo é posible que estas produzan contido visual fóra dos límites do elemento. Consulta https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "A API paymentManager.instruments quedou obsoleta. Mellor usa a instalación xusto a tempo para os controladores de pago."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "A chamada a PaymentRequest omitiu a directiva connect-src da política de seguranza do contido. Esta excepción quedou obsoleta. Engade o identificador do método de pago da API PaymentRequest (no campo supportedMethods) á directiva connect-src da política de seguranza do contido."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent quedou obsoleto. Mellor usa a API estandarizada navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "O atributo <source src> cun <picture> principal non é válido, polo que se ignora. Mellor usa <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "O método webkitCancelAnimationFrame é específico para provedores. Proba a usar o método estándar cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "O método webkitRequestAnimationFrame é específico para provedores. Proba a usar o método estándar requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen quedou obsoleta. Proba a usar Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() quedou obsoleta. Proba a usar Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() quedou obsoleta. Proba a usar Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() quedou obsoleta. Proba a usar Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() quedou obsoleta. Proba a usar Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen quedou obsoleta. Proba a usar Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "A API chrome.privacy.websites.privacySandboxEnabled vai quedar obsoleta, aínda que seguirá activa para ofrecer retrocompatibilidade ata que se lance a versión M113. No seu lugar, usa chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled e chrome.privacy.websites.adMeasurementEnabled. Tes máis información en https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "A restrición DtlsSrtpKeyAgreement deixou de estar dispoñible. Especificaches un valor false para esta restrición, o que se interpreta como un intento de usar o método SDES key negotiation, que deixou de estar dispoñible. Esta función deixou de estar dispoñible. Mellor usa un servizo que admita o atributo DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "A restrición DtlsSrtpKeyAgreement deixou de estar dispoñible. Especificaches un valor true para esta restrición. Esta acción non tivo ningún efecto, pero podes quitar a restrición para manter a organización."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "O método getStats() baseado en retrochamadas quedou obsoleto, polo que se quitará. Proba a usar getStats() segundo as especificacións."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() quedou obsoleta. Proba a usar Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "A opción rtcpMuxPolicy quedou obsoleta e deixará de estar dispoñible."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "O atributo speechSynthesis.speak() sen a activación do usuario quedou obsoleto e deixará de estar dispoñible."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Os detectores de eventos de cancelación de carga quedaron obsoletos, polo que se van quitar."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "O atributo isFallbackAdapter de GPUAdapter quedou obsoleto. No seu lugar utiliza isFallbackAdapter de GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "O elemento síncrono XMLHttpRequest no fío de mensaxes principal quedou obsoleto debido aos efectos negativos que tiña na experiencia do usuario final. Para obter máis axuda, consulta https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animación"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Os cambios de deseño teñen lugar cando os elementos se moven sen que o usuario faga nada. [Investiga as causas dos cambios de deseño](https://web.dev/articles/optimize-cls), por exemplo, que se engadan ou desaparezan elementos ou que cambie o tipo de letra a medida que carga a páxina."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Solicitude de tipo de letra"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Iframe inserido"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Hora de inicio do clúster de cambio de deseño: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Non se puideron detectar as causas do cambio de deseño"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Non hai ningún cambio de deseño"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Causas dos cambios de deseño"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Causas principais no cambio de deseño"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Peor clúster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Peor clúster de cambio e deseño"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL de memoria caché"}, "models/trace/insights/Cache.ts | description": {"message": "Se a memoria caché ten un tempo de vida longo, pódense acelerar as visitas repetidas á túa páxina. [Máis información](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Non hai ningunha solicitude con políticas de memoria caché ineficientes"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} máis"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Solicitar"}, "models/trace/insights/Cache.ts | title": {"message": "Usar tempos de vida da caché eficientes"}, "models/trace/insights/DOMSize.ts | description": {"message": "Un DOM de gran tamaño pode aumentar a duración dos cálculos de estilo e as redistribucións do deseño, o que afecta á capacidade de resposta da páxina. Este tipo de DOM tamén incrementará o uso de memoria. [Consulta máis información sobre como evitar un tamaño de DOM excesivo.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "Elemento"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "A maioría dos elementos secundarios"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Profundidade do DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Estatísticas"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizar o tamaño de DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Elementos totais"}, "models/trace/insights/DOMSize.ts | value": {"message": "Valor"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "A túa primeira solicitude de rede é a máis importante.  Para reducir a latencia, evita redireccións, garante unha resposta rápida do servidor e activa a compresión de texto."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Tiña redireccións ({PH1} redireccións, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "O servidor respondeu lentamente (tempo observado: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Non se aplicou ningunha comprensión"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON> redirecci<PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "O servidor responde rapidamente (tempo observado: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Aplica compresión de texto"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redireccións"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Tempo de resposta do servidor"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latencia da solicitude do documento"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Descarga sen comprimir"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Bytes duplicados"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Or<PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Quita os módulos de JavaScript grandes ou duplicados dos lotes para reducir o número de bytes innecesarios consumidos pola actividade de rede."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript duplicado"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Proba a configurar [font-display](https://developer.chrome.com/blog/font-display) para swap ou optional co fin de garantir que o texto sexa sempre visible. swap pódese optimizar aínda máis para mitigar os cambios de deseño con [anulacións de métricas de tipos de letra](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON><PERSON><PERSON> de letra"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Visualización do tipo de letra"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Tempo perdido"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anónima)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Moitas API, que normalmente len a xeometría do deseño, forzan o motor de renderización para pór en pausa a execución do script e calcular o estilo e o deseño. Obtén máis información sobre a [redistribución forzada](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) e as súas medidas de mitigación."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Segu<PERSON><PERSON> da morea"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Redistribución forzada"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Chamada de función principal"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Tempo total de redistribución"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[sen atri<PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Se se reduce o tempo de descarga das imaxes, pódese mellorar o tempo de carga percibido da páxina e LCP. [Máis información sobre como optimizar o tamaño da imaxe](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (estimación: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Non hai ningunha imaxe optimizable"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} máis"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Mellorar a entrega de imaxes"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Se aumentas o factor de compresión da imaxe, pode mellorar o seu tamaño de descarga."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Usar un formato de imaxe moderno (WebP, AVIF) ou aumentar a compresión da imaxe podería mellorar o tamaño de descarga desta imaxe."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "O ficheiro desta imaxe é máis grande do necesario ({PH1}) para as dimensións mostradas ({PH2}). Usa imaxes de tamaño adaptable para reducir o tamaño de descarga da imaxe."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Usar formatos de vídeo en lugar de GIF pode mellorar o tamaño de descarga do contido animado."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Comeza a investigación pola fase máis longa. [Pódense minimizar os atrasos](https://web.dev/articles/optimize-inp#optimize_interactions). Para reducir a duración do procesamento, [optimiza os custos do subproceso principal](https://web.dev/articles/optimize-long-tasks), que a miúdo é de JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Duración"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Atraso da entrada"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Non se detectou ningunha interacción"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Atraso na presentación"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Duración do procesamento"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP por fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Para optimizar a LCP, fai que a imaxe da LCP sexa [detectable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) desde o HTML de inmediato e [evita a carga diferida](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Aplicouse fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Débese aplicar fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "carga diferida non aplicada"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "A imaxe LCP cargou {PH1} despois do primeiro punto de inicio."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Non se detectou ningún LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Non se detectou ningún recurso de LCP porque a LCP non é unha imaxe"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "A solicitude é detectable no documento inicial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Detección de solicitudes de LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "[Cada fase ten estratexias de mellora específicas](https://web.dev/articles/optimize-lcp#lcp-breakdown). O ideal é que a maioría do tempo de LCP se dedique á carga de recursos, non a atrasos."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Duración"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Atraso na renderización do elemento"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Campo p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Non se detectou ningún LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Atraso na carga de recursos"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Duración da carga de recursos"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP por fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bytes perdidos"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Os polyfills e as conversións de compatibilidade permiten que os navegadores usen novas funcións de JavaScript. <PERSON><PERSON><PERSON> as<PERSON>, moitos non son necesarios para os navegadores máis modernos. Recomendámosche que modifiques o proceso de compilación de JavaScript para que non transpile as funcións de [Baseline](https://web.dev/articles/baseline-and-polyfills), a menos que saibas que debes admitir navegadores antigos. [Descubre por que a maioría de sitios poden poñer en funcionamento código ES6+ sen transpilación](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript antigo"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 e HTTP/3 ofrecen moitas vantaxes con respecto a HTTP/1.1, por exemplo, a multiplexación. [Consulta máis información sobre como usar HTTP moderno](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Ningunha solicitude usou HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocolo"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Solicitar"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP moderno"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Or<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Solicitar"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Or<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Aforros LCP estimados"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Preconexión sen usar. Comproba que o atributo crossorigin se usa correctamente."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Para evitar encadear solicitudes críticas](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains), reduce a lonxitude das cadeas ou o tamaño de descarga dos recursos, ou pospón a descarga de recursos innecesarios para mellorar a carga da páxina."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Engade suxestións de [preconexión](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ás orixes máis importantes, pero tenta non usar máis de catro."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidatos preconectados"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latencia de ruta crítica máxima:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "As dependencias de rede non afectaron a ningunha tarefa de renderización"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Non hai ningunha orixe adicional que sexa boa candidata para a preconexión"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "non hai ningunha orixe preconectada"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "As suxestións de [preconexión](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) axudan ao navegador a establecer unha conexión antes na carga da páxina, o que permite aforrar tempo cando se realiza a primeira solicitude desa orixe. A continuación, móstranse as orixes ás que se preconecta a páxina."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Orixes preconectadas"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Árbore de dependencia de rede"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Atopáronse máis de catro conexións preconnect. Deben utilizarse con moderación e só para as orixes máis importantes."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Preconexión sen usar. Utiliza preconnect só para as orixes que a páxina é probable que solicite."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Para evitar encadear solicitudes críticas, reduce a lonxitude das cadeas ou o tamaño de descarga dos recursos, ou pospón a descarga de recursos innecesarios para mellorar a carga da páxina."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "As solicitudes están bloqueando a renderización inicial da páxina, o que pode atrasar a LCP. Os [aprazamentos ou intercalacións](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) poden sacar estas solicitudes de rede da ruta crítica."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Duración"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Non hai ningunha solicitude de bloqueo da renderización para esta navegación"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Solicitude"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Solicitudes de bloqueo de renderización"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Se os custos de Calcular estilo de novo seguen sendo altos, a optimización de selectores pode reducilos. [Optimiza os selectores](https://developer.chrome.com/docs/devtools/performance/selector-stats) cun tempo transcorrido alto e unha porcentaxe elevada de rutas lentas. Con selectores máis sinxelos, menor cantidade de selectores, un DOM máis pequeno e un DOM máis superficial reduciranse os custos de coincidencia."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Tempo transcorrido"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Non se atoparon datos de selectores de CSS. As estatísticas do selector de CSS deben estar activadas na configuración do panel de rendemento."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Intentos de coincidencia"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Reconto de coincidencias"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Custos do selector de CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Principais selectores"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Tempo do subproceso principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Te<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Tam<PERSON>ño da transferencia"}, "models/trace/insights/ThirdParties.ts | description": {"message": "O código de terceiros pode afectar moito ao rendemento da carga. [Reduce e pospón a carga de código de terceiros](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) para dar prioridade ao contido da túa páxina."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Non se atopou ningún terceiro"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Te<PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "As interaccións de toque poden [atrasarse ata 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) se a ventá de visualización non está optimizada para móbiles."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Atraso de toque en móbiles"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimizar a ventá de visualización para móbiles"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Só cumpren os requisitos para almacenarse na memoria caché de páxinas anteriores e seguintes aquelas que se carguen a través dunha solicitude GET."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Só poden almacenarse na memoria caché as páxinas que teñan un código de estado de 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome detectou un intento de executar JavaScript mentres estaba na memoria caché."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Nestes momentos, as páxinas que solicitaron un AppBanner non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "En chrome://flags está desactivada a memoria caché de páxinas anteriores e seguintes. Para activala de forma local neste dispositivo, vai a chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "A liña de comandos desactivou a memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque non hai suficiente espazo de almacenamento."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "A memoria caché de páxinas anteriores e seguintes non é compatible co delegado."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Desactivouse a memoria caché de páxinas anteriores e seguintes para o prerrenderizador."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "A páxina non pode almacenarse na memoria caché porque ten unha instancia BroadcastChannel con detectores rexistrados."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "As páxinas coa cabeceira cache-control:no-store non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "A memoria caché borrouse de forma intencionada."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Expulsouse esta páxina da memoria caché para que puidese almacenarse nela outra páxina."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Nestes momentos, as páxinas que conteñen complementos non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON> definir"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "As páxinas que usan FileChooser API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "As páxinas que usan File System Access API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "As páxinas que usan o distribuidor de dispositivos multimedia non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Cando se abandonou a páxina, estaba executándose un reprodutor de contido multimedia."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "As páxinas que usan MediaSession API e establecen un estado de reprodución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "As páxinas que usan MediaSession API e establecen controladores de accións non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada debido ao lector de pantalla."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "As páxinas que usan SecurityHandler non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "As páxinas que usan Serial API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "As páxinas que usan WebAuthetication API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "As páxinas que usan WebBluetooth API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "As páxinas que usan WebUSB API non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque as cookies están desactivadas nunha páxina que usa Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Nestes momentos, as páxinas que usan un worklet ou un worker dedicado non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Abandonouse o documento antes de que rematase de cargar."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "O báner de aplicacións estaba aberto despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "O xestor de contrasinais de Chrome estaba aberto despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A destilación de DOM estaba en curso despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "O visor de destilador de DOM estaba aberto despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque as extensións están usando API de mensaxaría."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "As extensións con conexión de longa duración deben pechar a conexión antes de acceder á memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "As extensións con conexión de longa duración tentaron enviar mensaxes a marcos na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada por causa das extensións."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Mostrouse un cadro de diálogo modal, a fin de volver enviar un formulario ou indicar un contrasinal http, para a páxina despois de saír dela."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Mostrouse a páxina sen conexión despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "A barra de intervención de memoria insuficiente estaba aberta despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Houbo solicitudes de permisos despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "O bloqueador de ventás emerxentes estaba aberto despois de saír da páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Mostráronse os detalles da navegación segura despois de saír a páxina."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "A navegación segura considerou que esta páxina era abusiva e bloqueou as ventás emerxentes."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Activouse un service worker mentres a páxina estaba almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Desactivouse a memoria caché das páxinas anteriores e seguintes por un erro do documento."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "As páxinas que usan FencedFrames non se poden almacenar en bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Expulsouse esta páxina da memoria caché para que puidese almacenarse nela outra páxina."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Nestes momentos, as páxinas que concederon acceso á reprodución de contido multimedia en tempo real non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "As páxinas que teñen determinados tipos de contido inserido (por exemplo, PDF) actualmente non cumpren os requisitos da memoria caché de páxinas anteriores e seguintes"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Nestes momentos, as páxinas que usan IdleManager non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Nestes momentos, as páxinas que teñen unha conexión IndexedDB aberta non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada debido a un evento de IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Utilizáronse API que non cumpren os requisitos."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Nestes momentos, as páxinas nas que se incorpora JavaScript a través de extensións non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Nestes momentos, as páxinas nas que se incorpora StyleSheet a través de extensións non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Produciuse un erro interno."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque algunha solicitude de rede de JavaScript recibiu un recurso coa cabeceira Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada debido a unha solicitude de conexión persistente."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Nestes momentos, as páxinas que usan o bloqueo do teclado non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Abandonouse a páxina antes de que rematase de cargar."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "As páxinas cuxo principal recurso ten cache-control:no-cache non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "As páxinas cuxo principal recurso ten cache-control:no-store non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Cancelouse a navegación antes de que a páxina puidese restaurarse desde a memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Expulsouse esta páxina da memoria caché porque se recibían demasiados datos a través dunha conexión de rede activa. Chrome limita a cantidade de datos que pode recibir unha páxina mentres está almacenada na memoria caché."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Nestes momentos, as páxinas que teñen solicitudes fetch() ou XHR en execución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Expulsouse esta páxina da memoria caché de páxinas anteriores e seguintes porque se produciu unha redirección como consecuencia dunha solicitude de rede activa."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Expulsouse esta páxina da memoria caché porque houbo unha conexión de rede que permaneceu aberta demasiado tempo. Chrome limita a cantidade de tempo na que unha páxina pode recibir datos mentres está almacenada na memoria caché."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "As páxinas que non teñen unha cabeceira de resposta válida non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Levouse a cabo un proceso de navegación nun marco distinto do principal."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Nestes momentos, as páxinas con transaccións de IndexedDB en curso non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Nestes momentos, as páxinas cunha solicitude de rede en execución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Nestes momentos, as páxinas cunha solicitude de rede de recuperación en execución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Nestes momentos, as páxinas cunha solicitude de rede en execución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Nestes momentos, as páxinas cunha solicitude de rede XHR en execución non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Nestes momentos, as páxinas que usan PaymentManager non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Nestes momentos, as páxinas que usan a pantalla superposta non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Nestes momentos, as páxinas nas que se mostra a interface de impresión non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "A páxina abriuse mediante window.open() e hai outra pestana cunha referencia a ela, ou ben a páxina abriu unha ventá."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Produciuse un fallo co proceso do renderizador na páxina almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Pechouse o proceso do renderizador na páxina almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Nestes momentos, as páxinas que solicitaron permisos para gravar audio non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Nestes momentos, as páxinas que solicitaron permisos de acceso a sensores non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Nestes momentos, as páxinas que solicitaron permisos de recuperación ou sincronización en segundo plano non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Nestes momentos, as páxinas que solicitaron permisos de acceso a dispositivos MIDI non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Nestes momentos, as páxinas que solicitaron permisos de acceso ás notificacións non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Nestes momentos, as páxinas que solicitaron acceso ao almacenamento non cumpren os requisitos para gardarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Nestes momentos, as páxinas que solicitaron permisos para gravar vídeo non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Só poden almacenarse na memoria caché as páxinas cuxo URL utilice os protocolos HTTP ou HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Un service worker reclamou a páxina mentres estaba almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Un service worker tentou enviarlle un MessageEvent á páxina almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Cancelouse o rexistro de ServiceWorker mentres unha páxina estaba almacenada na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Expulsouse esta páxina da memoria caché de páxinas anteriores e seguintes debido a unha activación dun service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome reiniciou a memoria caché de páxinas anteriores e seguintes, ad<PERSON><PERSON> de borra<PERSON> as súas entradas."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Nestes momentos, as páxinas que usan SharedWorker non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Nestes momentos, as páxinas que usan SpeechRecognizer non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Nestes momentos, as páxinas que usan SpeechSynthesis non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Un iframe da páxina iniciou un proceso de navegación que non se completou."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "As páxinas cuxo subrecurso ten cache-control:no-cache non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "As páxinas cuxo subrecurso ten cache-control:no-store non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "A páxina superou o tempo máximo de almacenamento na memoria caché de páxinas anteriores e seguintes, polo que caducou."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Esgotouse o tempo de espera para que a páxina se almacenase na memoria caché de páxinas anteriores e seguintes (probablemente debido á longa duración dos procesos dos controladores de eventos pagehide)"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "A páxina ten un controlador de cancelación de carga no marco principal."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "A páxina ten un controlador de cancelación de carga nun marco secundario."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "O navegador cambiou a cabeceira de omisión do axente de usuario."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Nestes momentos, as páxinas que concederon acceso para gravar vídeo ou audio non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Nestes momentos, as páxinas que usan WebDatabase non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Nestes momentos, as páxinas que usan WebHID non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Nestes momentos, as páxinas que usan WebLocks non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Nestes momentos, as páxinas que usan WebNfc non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Nestes momentos, as páxinas que usan WebOTPService non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "As páxinas con WebRTC non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque se usou WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Nestes momentos, as páxinas que usan WebShare non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "As páxinas con WebSocket non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque se usou WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "As páxinas con WebTransport non poden almacenarse na memoria caché de páxinas anteriores e seguintes."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "A memoria caché de páxinas anteriores e seguintes está desactivada porque se usou WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Nestes momentos, as páxinas que usan WebXR non se gardan correctamente na memoria caché de páxinas anteriores e seguintes."}}