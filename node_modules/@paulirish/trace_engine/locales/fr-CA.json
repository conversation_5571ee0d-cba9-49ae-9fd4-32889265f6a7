{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "L'autorisation ne sera pas prise en compte par le caractère générique (*) dans le traitement CORS Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "L'attribut disableRemotePlayback doit être utilisé pour désactiver l'intégration Cast par défaut au lieu d'utiliser le sélecteur -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "La valeur d'apparence SMC slider-vertical n'est pas standardisée et sera retirée."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Les demandes de ressources dont les URL contiennent à la fois des caractères blancs retirés \\(n|r|t) et des caractères inférieurs (<) sont bloquées. Veuillez retirer les nouvelles lignes et coder les caractères inférieurs à partir d'endroits tels que les valeurs d'attributs d'éléments afin de charger ces ressources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() est obsolète, utilisez plutôt l'API standardisée : Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() est obsolète, utilisez plutôt l'API standardisée : Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() est obsolète, utilisez plutôt l'API standardisée : nextHopProtocol dans Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Les témoins contenant un caractère \\(0|r|n) seront refusés au lieu d'être tronqués."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "L'assouplissement de la politique d'origine identique en définissant document.domain est obsolète et sera désactivé par défaut. Cet avertissement d'abandon concerne l'accès d'origines multiples qui a été activé en définissant document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Le déclenchement de window.alert à partir des cadres iframes d'origine croisée est obsolète et sera retirée à l'avenir."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Le déclenchement de window.confirm à partir d'iframes d'origine croisée est obsolète et sera retiré à l'avenir."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Prise en charge des données : les URL dans l'élément SVGUseElement sont obsolètes et seront retirées à l'avenir."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() et watchPosition() ne fonctionnent plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, envisagez de faire passer votre application à une origine sécurisée, telle que HTTPS. Pour en savoir plus, consultez la page https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() et watchPosition() sont obsolètes sur les origines non sécurisées. Pour utiliser cette fonctionnalité, envisagez de faire passer votre application à une origine sécurisée, telle que HTTPS. Pour en savoir plus, consultez la page https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ne fonctionne plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, envisagez de faire passer votre application à une origine sécurisée, telle que HTTPS. Pour en savoir plus, consultez la page https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Le site a trouvé une balise <h1> entre des balises <article>, <aside>, <nav> ou <section> qui n'a pas de taille de police définie. La taille de ce texte d'en-tête changera prochainement dans ce navigateur. Consultez https://developer.mozilla.org/fr/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 pour en savoir plus."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate est obsolète. Veuillez utiliser RTCPeerConnectionIceErrorEvent.address ou RTCPeerConnectionIceErrorEvent.port à la place."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ce format pour la requête navigator.credentials.get() pour les authentifiants numériques est obsolète. Veuillez mettre à jour votre appel pour utiliser le nouveau format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "L'origine du marchand et les données arbitraires de l'événement de script de service canmakepayment sont obsolètes et seront retirées : topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Le site Web a demandé une sous-ressource à un réseau auquel il ne pouvait accéder qu'en raison de la position privilégiée de ses utilisateurs sur le réseau. Ces demandes exposent les appareils et les serveurs non publics à Internet, ce qui augmente le risque d'une attaque de falsification de demande intersite (CSRF) ou d'une fuite d'informations. Pour atténuer ces risques, Chrome abandonne les demandes aux sous-ressources non publiques lorsqu'elles sont lancées à partir de contextes non sécurisés, et commencera à les bloquer."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Le champ dailyUpdateUrl de InterestGroups transmis à joinAdInterestGroup() a été renommé en updateUrl, afin de refléter avec plus de précision son comportement."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator est obsolète. Veuillez utiliser la politique Intl.Segmenter à la place."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Les fichiers CSS ne peuvent pas être chargés à partir des URL file:, sauf si elles se terminent par une extension de fichier .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "L'utilisation de SourceBuffer.abort() pour annuler le retrait asynchrone de la plage de remove() est obsolète en raison d'un changement de spécification. La prise en charge sera retirée à l'avenir. Écoutez plutôt l'événement updateend. abort() est destiné uniquement à annuler un ajout multimédia asynchrone ou à réinitialiser l'état de l'analyseur."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "La définition de MediaSource.duration en dessous de l'estampille temporelle de présentation la plus élevée parmi les cadres codés mis en mémoire tampon est obsolète en raison d'un changement de spécification. La prise en charge du retrait implicite des supports tronqués mis en mémoire tampon sera retirée à l'avenir. Vous devez plutôt effectuer un remove(newDuration, oldDuration) explicite sur tous les sourceBuffers, où newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI demandera une autorisation d'utilisation même si le sysex n'est pas spécifié dans le MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "L'API Notification ne peut plus être utilisée à partir d'origines non sécurisées. Envisagez de faire passer votre application à une origine sécurisée, telle que HTTPS. Pour en savoir plus, consultez la page https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "L'autorisation pour l'API Notification ne peut plus être demandée à partir d'un cadre iframe d'origines multiples. Envisagez de demander l'autorisation à partir d'un cadre de niveau supérieur ou d'ouvrir une nouvelle fenêtre à la place."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "L'option imageOrientation: 'none' dans createImageBitmap est obsolète. Veuillez utiliser plutôt createImageBitmap avec l'option '{imageOrientation: 'from-image'}'."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Votre partenaire négocie une version (D)TLS obsolète. Veuillez vérifier auprès de votre partenaire pour résoudre ce problème."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "La spécification de overflow: visible sur les balises img, vidéo et toile peut les amener à produire du contenu visuel en dehors des limites de l'élément. Voir https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments est obsolète. Veuillez plutôt utiliser l'installation JIT (Just In Time) pour les modules de traitement de paiement."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Votre appel PaymentRequest a contourné la directive connect-src de politique de sécurité du contenu (CSP). Ce contournement est obsolète. Veuillez ajouter l'identifiant du mode de paiement de l'API PaymentRequest (dans le champ supportedMethods) à votre directive CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent est obsolète. Veuillez utiliser l'API navigator.storage standardisée à la place."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "L'élément <source src> avec un parent <picture> est incorrect et donc ignoré. Veuillez plutôt utiliser l'élément <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame est propre au fournisseur. Veuillez utiliser plutôt le cadre standard cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame est propre au fournisseur. Veuillez utiliser plutôt le cadre requestAnimationFrame standard."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen est obsolète. Veuillez utiliser plutôt Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() est obsolète. Veuillez utiliser plutôt Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() est obsolète. Veuillez utiliser plutôt Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() est obsolète. Veuillez utiliser plutôt Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() est obsolète. Veuillez utiliser plutôt Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen est obsolète. Veuillez utiliser plutôt Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Nous abandonnons l'API chrome.privacy.websites.privacySandboxEnabled bien qu'elle reste active à des fins de rétrocompatibilité jusqu'à la version M113. Veuillez plutôt utiliser chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled et chrome.privacy.websites.adMeasurementEnabled. Pour en savoir plus, consultez la page https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "La contrainte DtlsSrtpKeyAgreement est retirée. Vous avez spécifié une valeur false pour cette contrainte, ce qui est interprété comme une tentative d'utiliser la méthode retirée SDES key negotiation. Cette fonctionnalité est retirée, utilisez un service qui prend en charge DTLS key negotiation à la place."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "La contrainte DtlsSrtpKeyAgreement est retirée. Vous avez indiqué une valeur true pour cette contrainte, qui n'a eu aucun effet, mais vous pouvez la retirer par souci de netteté."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Le getStats() basé sur le rappel est obsolète et sera retiré. Utilisez plutôt le getStats() conforme aux spécifications."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() est obsolète. Veuillez utiliser plutôt Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Les demandes de sous-ressources dont les URL contiennent des authentifiants intégrés (p. ex. **********************/) sont bloquées."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "L'option rtcpMuxPolicy est obsolète et sera retirée."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer nécessitera une isolation d'origines multiples. Pour plus de détails, consultez la page https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "L'appel de speechSynthesis.speak() sans l'activation de l'utilisateur est obsolète et sera retiré."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Les écouteurs d'événements de déchargement sont obsolètes et seront retirés."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Les extensions doivent opter pour l'isolation d'origines multiples pour continuer à utiliser SharedArrayBuffer. Consultez la page https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "L'attribut isFallbackAdapter GPUAdapter est obsolète, utilisez plutôt l'attribut isFallbackAdapter GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 n'est pas pris en charge par la réponse json dans XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "L'attribut XMLHttpRequest synchrone sur le fil d'exécution principal est obsolète en raison de ses effets néfastes sur l'expérience de l'utilisateur final. Pour obtenir de l'aide, consultez la page https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animation"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Les décalages de la mise en page se produisent lorsque des éléments se déplacent sans aucune interaction de l'utilisateur. [Étudiez les causes des décalages de la mise en page](https://web.dev/articles/optimize-cls), telles que l'ajout ou le retrait d'éléments, ou la modification de leur police au cours du chargement de la page."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Demande de police"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Iframe injecté"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Grappe de décalages de la mise en page à {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Impossible de détecter les causes des changements de mise en page"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Aucun changement de mise en page"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Causes du décalage de la mise en page"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Principaux coupables du décalage de la mise en page"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON> grappe"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Grappe de décalages de la mise en page la plus mauvaise"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Durée de vie du cache"}, "models/trace/insights/Cache.ts | description": {"message": "Une longue durée de vie du cache peut accélérer les visites répétées sur votre page. [Apprenez-en plus.](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Aucune requête avec des politiques de cache inefficaces"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} autres"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Utiliser des durées de vie de cache efficaces"}, "models/trace/insights/DOMSize.ts | description": {"message": "Un DOM volumineux peut augmenter la durée des calculs de style et des ajustements de la mise en page, ce qui a une incidence sur la réactivité des pages. Un DOM volumineux augmentera aussi l'utilisation de la mémoire. [Découvrez comment éviter les DOM d'une taille excessive.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "É<PERSON>ment"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "La plupart des enfants"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Profondeur DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistique"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimiser la taille du DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Total des éléments"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Votre première requête réseau est la plus importante.  Réduisez sa latence en évitant les redirections, ce qui garantit une réponse rapide du serveur, et en activant la compression de texte."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Avait des redirections ({PH1} redirections, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Le serveur a répondu lentement (valeur observée : {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Aucune compression appliquée"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON> les redirections"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Le serveur répond rapidement (valeur observée : {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Applique la compression de texte"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redirections"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON><PERSON> du serveur"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latence de demande de document"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Téléchargement non compressé"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Octets dupliqués"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Source"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Retirez les modules JavaScript volumineux et en double des ensembles pour réduire les octets inutiles consommés par l'activité du réseau."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript dupliqué"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Pensez à définir la valeur [font-display](https://developer.chrome.com/blog/font-display) à swap ou à optional pour vous assurer que le texte est toujours visible. swap peut être optimisé davantage pour atténuer les décalages de la mise en page avec des [remplacements de mesures des polices](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Police"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Affichage des polices"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Temps perdu"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonyme)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "De nombreuses API, lisant généralement la géométrie de la mise en page, obligent le moteur de rendu à suspendre l'exécution du script afin de calculer le style et la mise en page. Apprenez-en plus sur l'[ajustement forcé de la mise en page](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) et ses mesures d'atténuation."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON> de <PERSON>"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Ajustement forcé de la mise en page"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Appel de fonction supérieure"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Temps total d'ajustement de la mise en page"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[non attribuées]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Réduire le temps de téléchargement des images peut améliorer le temps de chargement de la page et du LCP. [En savoir plus sur l'optimisation de la taille des images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON> estimée : {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Aucune image optimisable"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimiser la taille du fichier"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} autres"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Améliorez la diffusion des images"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "L'augmentation du facteur de compression de l'image pourrait améliorer sa taille de téléchargement."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "L'utilisation d'un format d'image moderne (WebP, AVIF) ou l'augmentation de la compression de l'image pourrait améliorer sa taille de téléchargement."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ce fichier d'image est plus volumineux que nécessaire ({PH1}) pour ses dimensions affichées ({PH2}). Utilisez des images réactives pour réduire la taille du téléchargement des images."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "L'utilisation de vidéos au lieu de fichiers GIF peut améliorer la taille de téléchargement du contenu animé."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Commencez votre enquête par la phase la plus longue. [Les retards peuvent être minimisés.](https://web.dev/articles/optimize-inp#optimize_interactions) Pour réduire la durée de traitement, [optimisez les coûts du fil d'exécution principal](https://web.dev/articles/optimize-long-tasks), qui sont souvent causés par le JavaScript."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "<PERSON><PERSON><PERSON>'en<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Aucune interaction détectée"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Phase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON>sent<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Durée du traitement"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP par phase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimisez la mesure LCP en rendant l'image LCP immédiatement [visible](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) à partir du HTML et en [évitant le chargement différé](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high appliquée"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high doit être appliquée"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "Chargement différé non appliqué"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "L'image LCP a été chargée {PH1} après le premier point de départ."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Aucun LCP détecté"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Aucune ressource LCP détectée, car le LCP n'est pas une image"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "La demande est détectable dans le document initial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Découverte de requête LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Chaque [phase comporte des stratégies d'amélioration précises](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idéalement, la majeure partie du temps LCP devrait être consacrée au chargement des ressources, et non aux retards."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON><PERSON><PERSON> de l'élément"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75ᵉ centile"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Aucun LCP détecté"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Phase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON><PERSON> de chargement de la ressource"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Durée de chargement des ressources"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Temps jusqu'au premier octet"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP par phase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Octets gaspillés"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Les émulateurs de navigateur Web et les transformations permettent aux navigateurs plus anciens d'utiliser de nouvelles fonctionnalités JavaScript. Cependant, beaucoup d'entre eux ne sont pas nécessaires pour les navigateurs modernes. Envisagez de modifier votre processus de création JavaScript pour ne pas transpiler les fonctionnalités [Baseline](https://web.dev/articles/baseline-and-polyfills), à moins que vous sachiez que vous devez prendre en charge les navigateurs plus anciens. [Découvrir pourquoi la plupart des sites peuvent déployer du code ES6+ sans transpilation](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript classique"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 et HTTP/3 offrent de nombreux avantages par rapport à HTTP/1.1, comme le multiplexage. [Apprenez-en plus sur l'utilisation du protocole HTTP moderne.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Aucune requête n'a utilisé HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocole"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP moderne"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origine"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Source"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Économies LCP estimées"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Préconnexion non utilisée. Vérifiez que l'attribut crossorigin est utilisé correctement."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Évi<PERSON><PERSON> d'enchaîner les requêtes critiques](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) en réduisant la longueur des chaînes, en réduisant la taille de téléchargement des ressources ou en différant le téléchargement de ressources inutiles pour améliorer le chargement des pages."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Ajoutez des indices de [préconnexion](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) à vos origines les plus importantes, mais essayez d'en utiliser quatre tout au plus."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidats à la préconnexion"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latence maximale du chemin critique :"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Aucune tâche de rendu n'est touchée par les dépendances réseau"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Aucune origine supplémentaire n'est un bon candidat pour la préconnexion"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "aucune origine n'était préconnectée"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Les indices de [préconnexion](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) aident le navigateur à établir une connexion plus tôt dans le chargement de la page, ce qui permet de gagner du temps lors de la première demande pour cette origine. Voici les origines auxquelles la page est préconnectée."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origines préconnectées"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Arbre de dépendance du réseau"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Plus de quatre connexions preconnect ont été trouvées. Celles-ci doivent être utilisées avec parcimonie et uniquement aux origines les plus importantes."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Préconnexion non utilisée. Utilisez seulement preconnect pour les origines que la page est susceptible de demander."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Évi<PERSON>z d'enchaîner les requêtes critiques en réduisant la longueur des chaînes, en réduisant la taille de téléchargement des ressources ou en différant le téléchargement de ressources inutiles pour améliorer le chargement des pages."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Les requêtes bloquent le rendu initial de la page, ce qui peut retarder l'élément LCP. [Le report ou l'intégration](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) peut déplacer ces requêtes réseau hors du chemin essentiel."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Aucune demande de blocage de rendu pour cette navigation"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Demandes de blocage du rendu"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Si Recalculer le style engendre toujours des coûts élevés, l'optimisation du sélecteur peut les réduire. [Optimisez les sélecteurs](https://developer.chrome.com/docs/devtools/performance/selector-stats) avec des pourcentages élevés de temps écoulé et de chemin lent. Des sélecteurs plus simples, moins de sélecteurs, un DOM plus petit et un DOM moins profond réduisent tous les coûts de correspondance."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Temps écoulé"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Aucune donnée du sélecteur SMC n'a été trouvée. Les statistiques du sélecteur SMC doivent être activées dans les paramètres du panneau de performances."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Tentatives de correspondances"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Nombre de correspondances"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Coûts du sélecteur CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Meilleurs sélecteurs"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Temps du fil d'exécution principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Tiers"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Le code tiers peut avoir une grande incidence sur les performances de chargement. [R<PERSON><PERSON><PERSON><PERSON> et différez le chargement du code tiers](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) pour accorder la priorité au contenu de votre page."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Aucun tiers trouvé"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Tiers"}, "models/trace/insights/Viewport.ts | description": {"message": "Les interactions tactiles peuvent être [retardées jusqu'à 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) si la fenêtre d'affichage n'est pas optimisée pour les appareils mobiles."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "<PERSON><PERSON><PERSON> du toucher sur l'appareil mobile"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimiser la fenêtre d'affichage pour les appareils mobiles"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Seules les pages chargées au moyen d'une requête GET peuvent être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Seules les pages dont le code d'état est 2XX peuvent être mises en cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome a détecté une page qui a tenté d'exécuter du JavaScript alors qu'elle était mise en cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Les pages qui ont demandé un élément AppBanner ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Des indicateurs désactivent la mise en cache complète bidirectionnelle. Accédez à la page chrome://flags/#back-forward-cache pour l'activer localement sur cet appareil."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "La mise en cache complète bidirectionnelle est désactivée par la ligne de commande."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "La mise en cache complète bidirectionnelle est désactivée en raison d'une quantité insuffisante de mémoire."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Le délégué ne prend pas en charge la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "La mise en cache complète bidirectionnelle est désactivée pour la fonctionnalité de prérendu."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "La page ne peut pas être mise en cache en raison d'une instance de BroadcastChannel auprès d'écouteurs inscrits."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Les pages qui contiennent un en-tête cache-control:no-store ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Le cache a été effacé intentionnellement."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "La page a été éjectée du cache pour permettre à une autre page d'être mise en cache."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Les pages qui contiennent des plugiciels ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Non défini"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Les pages qui utilisent l'API FileChooser ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Les pages qui utilisent l'API File System Access ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Les pages qui utilisent Media Device Dispatcher ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Un lecteur multimédia était en cours d'utilisation lorsque l'utilisateur quittait la page."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Les pages qui utilisent l'API MediaSession et qui définissent un état de lecture ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Les pages qui utilisent l'API MediaSession et des modules de traitement d'actions définies ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "La mise en cache complète bidirectionnelle est désactivée en raison du lecteur d'écran."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Les pages qui utilisent SecurityHandler ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Les pages qui utilisent l'API Serial ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Les pages qui utilisent l'API WebAuthetication ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Les pages qui utilisent l'API WebBluetooth ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Les pages qui utilisent l'API WebUSB ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "La mise en cache complète bidirectionnelle est désactivée, car les témoins sont désactivés sur une page qui utilise Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Les pages qui utilisent un script de traitement dédié ou un groupe de tâches ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Le chargement du document ne s'est pas terminé avant que l'utilisateur la quitte."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Une bannière d'application était présente lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Le gestionnaire de mots de passe Chrome était présent lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Le processus d'extraction de DOM Distiller était en cours lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Le lecteur DOM Distiller était présent lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "La mise en cache complète bidirectionnelle est désactivée en raison d'extensions utilisant Messaging API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Les extensions ayant une connexion de longue durée devraient fermer la connexion avant d'utiliser la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Des extensions ayant une connexion de longue durée ont tenté d'envoyer des messages aux cadres dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "La mise en cache complète bidirectionnelle est désactivée en raison d'extensions."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "La boîte de dialogue modale comme le nouvel envoi d'un formulaire ou la boîte de dialogue du mot de passe du protocole http était affichée lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "La page hors ligne était affichée lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "La barre d'intervention concernant la mémoire insuffisante était présente lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Des demandes d'autorisation étaient présentes lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Un bloqueur de fenêtre contextuelle était présent lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Les détails de la navigation sécurisée étaient affichés lorsque l'utilisateur a quitté la page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "La navigation sécurisée considère que cette page est abusive et a bloqué la fenêtre contextuelle."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Un script de service a été activé alors que la page était en mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "La mise en cache complète bidirectionnelle est désactivée à cause d'une erreur liée au document."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Les pages qui utilisent FencedFrames ne peuvent pas être stockées au moyen de la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "La page a été éjectée du cache pour permettre à une autre page d'être mise en cache."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Les pages sur lesquelles vous pouvez accéder au contenu du flux multimédia ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Les pages contenant certains types de contenu intégré (p. ex. les PDF) ne sont actuellement pas admissibles à la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Les pages qui utilisent IdleManager ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Les pages qui ont une connexion IndexedDB active ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Le cache arrière/avant est désactivé en raison d'un événement IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Des API inadmissibles ont été utilisées."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "À l'heure actuelle, les pages dans lesquelles des extensions ont intégré JavaScript ne sont pas admissibles à la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "À l'heure actuelle, les pages dans lesquelles des extensions ont intégré un StyleSheet ne sont pas admissibles pour la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON> interne."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "La mise en cache complète bidirectionnelle est désactivée, car une requête réseau JavaScript a reçu une ressource avec l'en-tête Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "La mise en cache complète bidirectionnelle est désactivée en raison d'une demande de connexion persistante."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Les pages qui utilisent le verrouillage du clavier ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Le chargement de la page ne s'est pas terminé avant que l'utilisateur la quitte."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Les pages dont la ressource principale contient la valeur cache-control:no-cache ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Les pages dont la ressource principale contient la valeur cache-control:no-store ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "La navigation a été annulée avant que la page puisse être restaurée de la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "La page a été éjectée du cache parce qu'une connexion réseau active a reçu trop de données. Chrome impose une limite quant à la quantité de données qu'une page peut recevoir alors qu'elle est mise en cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Les pages qui contiennent une demande-réseau XHR ou de récupération() en cours ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "La page a été éjectée de la mise en cache complète bidirectionnelle en raison d'une demande réseau active qui inclut une redirection."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "La page a été éjectée du cache en raison d'une connexion réseau ouverte trop longtemps. Chrome impose une limite de temps durant laquelle une page peut recevoir des données alors qu'elle est mise en cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Les pages qui n'ont pas d'en-têtes de réponse valides ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "La navigation s'est produite dans un cadre autre que le cadre principal."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Les pages qui sont en train d'exécuter des transactions relatives à une base de données indexées ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Les pages qui comprennent une demande réseau en cours ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Les pages qui comprennent une demande réseau de récupération en cours ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Les pages qui comprennent une demande réseau en cours ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Les pages qui comprennent une demande réseau XHR en cours ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Les pages qui utilisent PaymentManager ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Les pages qui utilisent l'incrustation d'image ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Les pages qui affichent une IU relative à l'impression ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "La page a été ouverte au moyen de window.open(), et un autre onglet y fait référence ou la page a ouvert une fenêtre."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Le processus de rendu pour la page en mise en cache complète bidirectionnelle a planté."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Le processus de rendu pour la page en mise en cache complète bidirectionnelle a été arrêté."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Les pages qui ont demandé des autorisations de capture audio ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Les pages qui ont demandé des autorisations d'accès à des capteurs ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Les pages qui ont demandé les autorisations de synchronisation ou de récupération en arrière-plan ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Les pages qui ont demandé des autorisations d'accès aux appareils MIDI ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Les pages qui ont demandé des autorisations d'accès aux notifications ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Les pages qui ont demandé un accès à l'espace de stockage ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Les pages qui ont demandé des autorisations de capture vidéo ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Seules les pages dont le schéma d'URL est HTTP ou HTTPS peuvent être mises en cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "La page a été demandée par un script de service alors qu'elle était mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Un script de service a tenté d'envoyer une propriété MessageEvent à la page en mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Un ServiceWorker n'était pas inscrit alors qu'une page était en mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "La page a été éjectée de la mise en cache complète bidirectionnelle en raison de l'activation d'un script de service."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome a redémarré et a effacé les entrées de mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Les pages qui utilisent SharedWorker ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Les pages qui utilisent SpeechRecognizer ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Les pages qui utilisent SpeechSynthesis ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Un élément iframe sur la page a démarré une navigation qui n'a pas été terminée."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Les pages dont la sous-ressource contient la valeur cache-control:no-cache ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Les pages dont la sous-ressource contient la valeur cache-control:no-store ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "La page a dépassé le temps maximal alloué pour la mise en cache complète bidirectionnelle et a expiré."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "La page a dépassé le délai pour utiliser la mise en cache complète bidirectionnelle (probablement en raison de modules de traitement pagehide de longue durée)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "La page contient un module de traitement de déchargement dans son cadre principal."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "La page contient un module de traitement de déchargement dans un sous-cadre."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Le navigateur a changé l'en-tête de remplacement de l'agent utilisateur."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Les pages qui ont accordé l'autorisation d'enregistrement vidéo ou audio ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Les pages qui utilisent WebDatabase ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Les pages qui utilisent WebHID ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Les pages qui utilisent WebLocks ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Les pages qui utilisent WebNfc ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Les pages qui utilisent WebOTPService ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Les pages qui font appel à la technologie WebRTC ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "La mise en cache complète bidirectionnelle est désactivée, car WebRTC a été utilisé."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Les pages qui utilisent WebShare ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Les pages qui font appel à la technologie WebSocket ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "La mise en cache complète bidirectionnelle est désactivée, car WebSocket a été utilisée."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Les pages qui utilisent WebTransport ne peuvent pas être enregistrées dans la mise en cache complète bidirectionnelle."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "La mise en cache complète bidirectionnelle est désactivée, car WebTransport a été utilisée."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Les pages qui utilisent WebXR ne peuvent actuellement pas être enregistrées dans la mise en cache complète bidirectionnelle."}}