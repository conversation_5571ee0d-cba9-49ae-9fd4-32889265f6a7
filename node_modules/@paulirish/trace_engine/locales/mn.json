{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers-н боловсруулалтад зөвшөөрлийг төлөөлөх тэмдгээр (*) далдлахгүй."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Өгөгдмөл Cast-н интеграцыг идэвхгүй болгохын тулд -internal-media-controls-overlay-cast-button сонгогчийг ашиглахын оронд disableRemotePlayback атрибутыг ашиглах хэрэгтэй."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS-н харагдах байдлын slider-vertical утгыг стандартчлаагүй бөгөөд хасна."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "URL-ууд нь хассан хоосон зайн \\(n|r|t) тэмдэгт болон багын тэмдэгтийн (<) аль алиныг агуулсан нөөцийн хүсэлтүүдийг блоклосон. Эдгээр нөөцийг ачаалахын тулд шинэ мөрийг хасаж, багын тэмдэгтийг элементийн атрибутын утга зэрэг газруудаас кодлоно уу."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes()-г зогсоосон бөгөөд оронд нь стандартжуулсан API ашиглана уу: Навигацын хугацаа 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes()-г зогсоосон бөгөөд оронд нь стандартжуулсан API: ашиглана уу: Будах хугацаа."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes()-г зогсоосон бөгөөд оронд нь стандартжуулсан API ашиглана уу: Навигацын хугацаа 2 дахь nextHopProtocol."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) тэмдэгтийг агуулж буй күүкинүүдийг танахын оронд татгалзана."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain тохиргоогоор ижил эхийн бодлогыг сулруулахыг зогсоосон бөгөөд өгөгдмөлөөр идэвхгүй болгоно. Энэ зогсоох сануулга нь document.domain тохиргоог идэвхжүүлсэн хөндлөнгийн эхийн хандалтад зориулагдсан."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Хөндлөнгийн эхийн iframe-с window.alert-г идэвхжүүлэхийг зогсоосон бөгөөд ирээдүйд хасна."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Хөндлөнгийн эхийн iframe-с window.confirm-г идэвхжүүлэхийг зогсоосон бөгөөд ирээдүйд хасна."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Өгөгдлийн тусламж: SVGUseElement-н URL-уудыг зогсоосон бөгөөд үүнийг ирээдүйд хасна."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() болон watchPosition() цаашид аюултай эх дээр ажиллахаа больсон. Энэ онцлогийг ашиглахын тулд та аппликэйшнээ HTTPS зэрэг аюулгүй эх рүү сэлгэх талаар бодож үзэх хэрэгтэй. Илүү дэлгэрэнгүй мэдээллийг https://goo.gle/chrome-insecure-origins дээрээс харна уу."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() болон watchPosition()-г аюултай эх дээр зогсоосон. Энэ онцлогийг ашиглахын тулд та аппликэйшнээ HTTPS зэрэг аюулгүй эх рүү сэлгэх талаар бодож үзэх хэрэгтэй. Илүү дэлгэрэнгүй мэдээллийг https://goo.gle/chrome-insecure-origins дээрээс харна уу."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() нь цаашид аюултай эх дээр ажиллахаа больсон. Энэ онцлогийг ашиглахын тулд та аппликэйшнээ HTTPS зэрэг аюулгүй эх рүү сэлгэх талаар бодож үзэх хэрэгтэй. Илүү дэлгэрэнгүй мэдээллийг https://goo.gle/chrome-insecure-origins дээрээс харна уу."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<Аrticle>, <aside>, <nav>, <section> дотроос <h1> шошгыг олсон бөгөөд энэ нь заасан фонтын хэмжээгүй байна. Ойрын ирээдүйд энэ хөтчид уг гарчгийн текстийн хэмжээг өөрчилнө. Нэмэлт мэдээллийг https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 дээрээс харна уу."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate-г зогсоосон. Оронд нь RTCPeerConnectionIceErrorEvent.address эсвэл RTCPeerConnectionIceErrorEvent.port-г ашиглана уу."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Дижита<PERSON> мандат үнэмлэхийн navigator.credentials.get() хүсэлтэд зориулсан энэ форматыг зогсоосон. Шинэ форматыг ашиглахын тулд дуудлагаа шинэчилнэ үү."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Худалдаачны эх болон canmakepayment үйлчилгээний ажилтны үйл явдлын дурын өгөгдлийг зогсоосон бөгөөд хасна: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Уг вебсайт сүлжээнээс зөвхөн хэрэглэгчдийнхээ тусгай эрхтэй сүлжээний байрлалын улмаас хандаж болох дэд нөөцийг хүссэн. Эдгээр хүсэлт нь олон нийтийн бус төхөөрөмжүүд болон серверүүдийг интернэтэд задруулж, сайт хоорондын хүсэлтийг хуурамчаар үйлдэх (CSRF) халдлага болон/эсвэл мэдээлэл задрах эрсдлийг нэмэгдүүлдэг. Эдгээр эрсдлийг бууруулахын тулд аюулгүй хам сэдвээс эхлүүлсэн үед Chrome олон нийтийн бус дэд нөөцийн хүсэлтүүдийг зогсоож, тэдгээрийг блоклож эхэлнэ."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup() руу дамжуулсан InterestGroups-н dailyUpdateUrl талбарын зан үйлийг илүү оновчтой тусгахын тулд үүний нэрийг updateUrl болгож өөрчилсөн."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator-г зогсоосон. Оронд нь Intl.Segmenter-г ашиглана уу."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "file: URL-ууд нь .css файл өргөтгөлөөр дуусаагүйгээс бусад тохиолдолд тэдгээрээс CSS-г ачаалах боломжгүй."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Тодорхойлолтын өөрчлөлтийн улма<PERSON>с SourceBuffer.abort()-г ашиглан remove()-н синхрон мужийг хасахыг зогсоосон. Тусламжийг ирээдүйд хасна. Та оронд нь updateend үйл явдлыг сонсох хэрэгтэй. abort() нь зөвхөн синхрон медиа хавсаргахыг таслах эсвэл задлан шинжлэгчийн төлөвийг шинэчлэх зориулалттай."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Тодорхой өөрчлөлтийн улмаас аливаа буферлэж кодолсон фреймийн дээд үзүүлэнгийн хугацаа тэмдэглэгчийн доор MediaSource.duration-г тохируулахыг зогсоосон. Танаж буферлэсэн медиагийн далд хасалтын тусламжийг ирээдүйд хасна. Та оронд нь newDuration < oldDuration байгаа бүх sourceBuffers дээр ил remove(newDuration, oldDuration)-г гүйцэтгэх хэрэгтэй."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Sysex-г MIDIOptions-д заагаагүй байсан ч веб MIDI ашиглах зөвшөөрөл хүснэ."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Мэдэгдлийн API-г цаашид аюултай эхээс ашиглах боломжгүй болж магадгүй. Та аппликэйшнаа HTTPS зэрэг аюулгүй эх рүү сэлгэх талаар бодож үзэх хэрэгтэй. Илүү дэлгэрэнгүй мэдээллийг https://goo.gle/chrome-insecure-origins дээрээс харна уу."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Мэдэгдлийн API-н зөвшөөрлийг цаашид хөндлөнгийн эхийн iframe-с хүсэх боломжгүй байж магадгүй. Та оронд нь дээд түвшиний фреймээс зөвшөөрөл хүсэх эсвэл шинэ цонх нээж үзэх талаар бодож үзэх хэрэгтэй."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "CreateImageBitmap дахь imageOrientation: 'none' сонголтыг зогсоосон. Оронд нь '{imageOrientation: 'from-image'}' сонголттой createImageBitmap-г ашиглана уу."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Таны түнш хуучир<PERSON>ан (D)TLS хувилбарыг хэлэлцэж байна. Үүнийг засуулахын тулд түншээсээ лавлана уу."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Зураг, видео болон канвасын шошго дээр overflow: visible-г зааснаар тэдгээрийг элементийн хүрээнээс гадуур визуал контент үүсгэхэд хүргэж магадгүй. Tа https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md-г харна уу."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments-г зогсоосон. Оронд нь төлбөр хариуцагчид цагтаа суулгахыг ашиглана уу."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Таны PaymentRequest дуудлага Контентын аюулгүй байдлын бодлого (CSP)-ын connect-src удирдамжийг алгассан. Энэ алгасах үйлдлийг зогсоосон. PaymentRequest API-с (supportedMethods талбарт) CSP-н connect-src удирдамждаа төлбөрийн хэрэгслийн танигч нэмнэ үү."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent-г зогсоосон. Оронд нь стандар<PERSON><PERSON><PERSON>улсан navigator.storage-г ашиглана уу."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> эхтэй <source src> нь хүчингүй тул үүнийг үл хэрэгссэн. Оронд нь <source srcset>-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame нь нийлүүлэгчид зориулагдсан. Оронд нь стандарт cancelAnimationFrame-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame нь нийлүүлэгчид зориулагдсан. Оронд нь стандарт requestAnimationFrame-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen-г зогсоосон. Оронд нь Document.fullscreenElement-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen()-г зогсоосон. Оронд нь Element.requestFullscreen()-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen()-г зогсоосон. Оронд нь Element.requestFullscreen()-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen()-г зогсоосон. Оронд нь Document.exitFullscreen()-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen()-г зогсоосон. Оронд нь Document.exitFullscreen()-г ашиглана уу."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen-г зогсоосон. Оронд нь Document.fullscreenEnabled-г ашиглана уу."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Бид chrome.privacy.websites.privacySandboxEnabled API-г зогсоож байгаа ч үүнийг M113-г шинээр гаргах хүртэл хуучин тохиргоонд нийцүүлэх байдлаар идэвхтэй хэвээр үлдээнэ. Оронд нь chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled болон chrome.privacy.websites.adMeasurementEnabled-г ашиглана уу. Та https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled-г харна уу."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement хязгаарлалтыг хассан. Та энэ хязгаарлалтад false утгыг заасан бөгөөд үүнийг хассан SDES key negotiation аргыг ашиглах оролдлого гэж ойлгосон. Энэ функцийг хассан. Оронд нь DTLS key negotiation-г дэмждэг үйлчилгээг ашиглана уу."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement хязгаарлалтыг хассан. Та энэ хязгаарлалтад true утгыг заасан бөгөөд энэ нь ямар ч нөлөөгүй байсан хэдий ч та энэ хязгаарлалтыг цэгцтэй байдлын үүднээс хасах боломжтой."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Буцааж залгахад тулгуурласан getStats()-г зогсоoсон бөгөөд хасна. Оронд нь тодорхойлолтод нийцсэн getStats()-г ашиглана уу."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand()-г зогсоосон. Оронд нь Selection.modify()-г ашиглана уу."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL-ууд нь мандат үнэмлэх (ж.нь **********************/) агуулсан дэд нөөцийн хүсэлтүүдийг блоклосон."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy сонголтыг зогсоосон бөгөөд хасна."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer нь хөндлөнгийн эхийн тусгаарлалт шаардана. Илүү дэлгэрэнгүйг https://developer.chrome.com/blog/enabling-shared-array-buffer/ дээрээс харна уу."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Хэрэглэгчийн идэвхжүүлэлтгүйгээр speechSynthesis.speak()-г зогсоосон бөгөөд хасна."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Ачаалахаа болих үйл явдлын сонсогчдыг зогсоосон бөгөөд хасна."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Shared<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-г үргэлжүүлэн ашиглахын тулд өргөтгөлүүд хөндлөнгийн эхийн тусгаарлалтад нэгдэх хэрэгтэй. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/-г харна уу."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter атрибутыг зогсоосон. Оронд нь GPUAdapterInfo isFallbackAdapter атрибутыг ашиглана уу."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16-г XMLHttpRequest-д хариуны json-с дэмждэггүй"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Үндсэн уялдаат дээрх XMLHttpRequest синхрон нь эцсийн хэрэглэгчийн туршлагад сөрөг нөлөө үзүүлдэг тул үүнийг зогсоосон. Илүү их тусламж авах бол https://xhr.spec.whatwg.org/-г шалгана уу."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимаци"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Хэрэглэгчийн аливаа харилцан үйлдэлгүйгээр элемент шилжихэд бүдүүвчийн шилжилт тохиолддог. Элемент нэмэх, ха<PERSON><PERSON><PERSON>, хуудас ачаалахад тухайн элементийн фонт өөрчлөгдөх зэрэг [бүдүүвчийн шилжилтийн шалтгааныг шалгана уу](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Фонтын хүсэлт"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Бүдүүвчийн шилжи<PERSON><PERSON><PERSON>н кластер @ {PH1}-д эхэлнэ"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Бүдүүвчийн шилжилтийн ямар ч сэжигтэн олж чадсангүй"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Ямар ч бүдүүвчийн шилжилт байхгүй"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Бүдүүвчийн шилжилтийн шалтгаан"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Дээд талын бүдүүвчийн шилжилтийн буруутан"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>н муу кластер"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Ха<PERSON>гийн муу бүдүүвчийн шилжилтийн кластер"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Завсрын санах ойн TTL"}, "models/trace/insights/Cache.ts | description": {"message": "Өгөгдлийг завсрын санах ойд удаан хугацаанд хадгалах нь таны хуудсанд давтан зочлохыг хурдасгах боломжтой. [Нэмэлт мэдээлэл авна уу](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Үр ашиггүй завсрын санах ойн бодлоготой холбоотой ямар ч хүсэлт байхгүй"}, "models/trace/insights/Cache.ts | others": {"message": "Өөр {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Хүсэлт"}, "models/trace/insights/Cache.ts | title": {"message": "Завсрын санах ойд өгөгдөл хадгалах үр ашигтай хугацааг ашиглах"}, "models/trace/insights/DOMSize.ts | description": {"message": "Том DOM нь загварын тооцоолол, бүдүүвчийн дахин тооцооллын үргэлжлэх хугацааг нэмэгдүүлэх боломжтой ба хуудасны хариу үйлдэлт байдалд нөлөөлдөг. Том DOM нь мөн санах ойн ашиглалтыг нэмэгдүүлнэ. [DOM-н хэт том хэмжээнээс хэрхэн зайлсхийхийг мэдэж авна уу](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Элемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Ихэнх дэд элемент"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-н гүн"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистик"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM-н хэмжээг оновчлох"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Нийт элемент"}, "models/trace/insights/DOMSize.ts | value": {"message": "Утга"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Таны эхний сүлжээний хүсэлт хамгийн чухал юм.  Дахин чиглүүлэхээс зайлсхийж, серверийн хурдан хариултыг баталгаажуулж, текстийн шахалтыг идэвхжүүлснээр хоцролтыг нь багасгана уу."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Дахин чиглүүлэх хүсэлтүүдтэй ({PH1} дахин чиглүүлэх хүсэлт, +{PH2}) байсан"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер удаан хариу үйлдэл үзүүлсэн ({PH1}-г ажигласан)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Шахалт ашиглаагүй"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Дахин чиглүүлэхээс зайлсхийнэ"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер хэт хурдан хариу үйлдэл үзүүлсэн ({PH1}-г ажигласан)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Текст шахалтыг ашиглана"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> чиглүүлэх"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Серверийн хариу өгөх хугацаа"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Баримт бичгийн хүсэлтийн хоцролт"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Шахаагүй татаж авсан файл"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> байт"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Эх сурвалж"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Сүлжээний үйл ажиллага<PERSON>наас зарцуулах шаардлагагүй байтыг багасгахын тулд том, хуулбар JavaScript-н модулийг багцаас хасна уу."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Дав<PERSON><PERSON><PERSON>дсан JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Текстийн тогтвортой харагдахыг баталгаажуулахын тулд [font-display](https://developer.chrome.com/blog/font-display)-г swap эсвэл optional болгон тохируулах талаар бодож үзнэ үү. swap-г [фонтын хэмжигдэхүүний дарах механизмаар](https://developer.chrome.com/blog/font-fallbacks) бүдүүвчийн шилжилтийг багасгахын тулд нэмж оновчлох боломжтой."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Фонт"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Фонт үзүүлэх"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Үр дүнгүй өнгөрүүлсэн хугацаа"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(нэргүй)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Ихэвчлэн бүдүүвчийн геометрийг унших олон API загвар, бүдүүвчийг тооцоолохын тулд скриптийн гүйцэтгэлийг түр зогсоохоор системийг рэндэрлэхийг хүчилдэг. [Хүчилсэн дахин тооцоолол](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts), түүний бууруулалтын талаар нэмэлт мэдээлэл авна уу."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Стекийн мөр"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Хүчилсэн дахин тооцоолол"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Ха<PERSON><PERSON><PERSON>н их хугацаа зарцуулдаг функцийн дуудлага"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Да<PERSON><PERSON>н тооцоолох нийт хугацаа"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[хама<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ха<PERSON> больсон]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Зургийн татах хугацааг багасгах нь хуудас, LCP-н илрүүлсэн ачаалах хугацааг сайжруулах боломжтой. [Зургийн хэмжээг оновчлох талаар нэмэлт мэдээлэл авах](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (тооцоолсон: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Оновчлох боломжтой ямар ч зураг байхгүй"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Файлын хэмжээг оновчлох"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Өөр {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Зураг хүргэхийг сайжруулах"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Зургийн шахалтын харьцааг нэмэгдүүлэх нь энэ зургийн татах хэмжээг сайжруулж болно."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Орчин үеийн дүрсийн форматыг (WebP, AVIF) ашиглах, зургийн шахалтыг нэмэгдүүлэх нь энэ зургийн татах хэмжээг сайжруулж болно."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Энэ зургийн файл үзүүлсэн хэмжээснийхээ хувьд ({PH2}) шаардлагатай хэмжээнээс ({PH1}) том байна. Зураг татах хэмжээг багасгахын тулд хариу үйлдэлт зургийг ашиглана уу."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF-н оронд видеоны форматыг ашиглах нь анимацижуулсан контентын татах хэмжээг сайжруулах боломжтой."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Хамгийн урт үе шатаар шалгаж эхэлнэ үү. [Саатлыг багасгах боломжтой](https://web.dev/articles/optimize-inp#optimize_interactions). Боловсруулалтын үргэлжлэх хугацааг багасгахын тулд [үндсэн дэд процессын зардлыг оновчилно уу](https://web.dev/articles/optimize-long-tasks). Ихэвчлэн JS байдаг."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Үргэлжлэх хугацаа"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Оролтын саатал"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Ямар ч харилцан үйлдэл илэрсэнгүй"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Үе шат"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Үзүүлэнгийн саатал"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Боловсруулалтын үргэлжлэх хугацаа"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP-г үе шатаар задлах"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP-н системийн хуулбарыг HTML-с тэр даруй [илрэх](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) болгосноор LCP-г оновчилж, [залхуу ачааллаа<PERSON> зайлсхийгээрэй](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high applied"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high-г ашиглах ёстой"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "залхуу ачааллыг ашиглаагүй"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP-н системийн хуулбарыг хамгийн эрт эхлэх цэгээс {PH1}-н дараа ачаалсан."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ямар ч LCP илэрсэнгүй"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP нь зураг биш тул ямар ч LCP-н нөөц илрээгүй"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Хүсэлтийг эх баримт бичгээс олох боломжтой"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP хүсэлтийн танилцах хайлт"}, "models/trace/insights/LCPPhases.ts | description": {"message": "[Үе шат бүрд сайжруулалтын тодорхой стратеги бий](https://web.dev/articles/optimize-lcp#lcp-breakdown). Хамгийн тохиромжтой нь LCP-н ихэнх цагийг саатал дотор бус нөөцийг ачаалахад зарцуулах ёстой."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Үргэлжлэх хугацаа"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Элементийг рэндэрлэх саатал"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Талбарын 75 дахь перцентиль"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ямар ч LCP илэрсэнгүй"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Үе шат"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Нөөцийн ачааллын саатал"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Нөөцийн ачааллын үргэлжлэх хугацаа"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "<PERSON><PERSON><PERSON>ий байт хүртэлх хугацаа"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Үе шатта<PERSON><PERSON><PERSON><PERSON>р LCP-г харуулах"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипт"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Үр дүнгүй байт"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill, хувиргалт нь хуучин хөтчид JavaScript-н шинэ онцлогуудыг ашиглах боломжийг олгодог. Гэсэн хэдий ч ихэнх нь орчин үеийн хөтчид шаардлагагүй. Та хуучин хөтчийг дэмжих ёстойг мэдэж байгаагаас бусад тохиолдолд JavaScript-н бүтээх процессоо [Суурь](https://web.dev/articles/baseline-and-polyfills) онцлогуудыг хөрвүүлэхгүй байхаар өөрчлөх талаар бодож үзнэ үү. [Ихэнх сайт яагаад ES6+ кодыг хөрвүүлэхгүйгээр түгээн байрлуулж чаддаг талаар мэдэж авах](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Уламжлалт JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2, HTTP/3 нь HTTP/1.1-тэй харьцуулахад нягтруулга зэрэг олон үр өгөөжийг санал болгодог. [Орчин үеийн HTTP-г ашиглах талаар нэмэлт мэдээлэл авна уу](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Ямар ч хүсэлт HTTP/1.1-г ашиглаагүй"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Хүсэлт"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Орчин үеийн HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Эх"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Хүсэлт"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Эх сурвалж"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Цаг"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Тооцоолсон LCP-н хэмнэлт"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Ашиглаагүй урьдчилан холболт. crossorigin атрибутыг зохих ёсоор ашигласан эсэхийг шалгана уу."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "Хуудасны ачааллыг сайжруулахын тулд хэлхээний уртыг багасгаж, нөөцийн татах хэмжээг багасгаж, шаардлагагүй нөөцийг татахыг хойшлуулж [чухал хүсэлтийн хэлхээ үүсгэхээс зайлсхийнэ үү](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Ха<PERSON>гийн чухал эхдээ [урьдчилан холбох](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) заавар нэмнэ үү. Гэхдээ 4-өөс ихгүйг ашиглахаар оролдоно уу."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Урьдчи<PERSON><PERSON><PERSON> холбох боломжит эх"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Чухал замын дээд хоцролт:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Сүлжээний хамаара<PERSON>д ямар ч рэндэрлэх ажил өртөөгүй"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Я<PERSON><PERSON><PERSON> ч нэмэлт эх урьдчилан холбоход хангалттай сайн биш байна"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ямар ч эхийг урьдчилан холбоогүй"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[Урьдчила<PERSON> холбох](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) заавар хөтчид хуудас ачаалах явцад эрт холболт тогтооход тусалдаг нь тухайн эхэд анхны хүсэлт гаргахад цаг хэмнэдэг. Дараах нь хуудасны урьдчилан холбогдсон эхүүд юм."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Урьдчил<PERSON>н холбогдсон эх"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Сүлжээний хамаарлын мод"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4-өөс олон preconnect холболт олдлоо. Эдгээрийг арвилан хэмнэж, зөвхөн хамгийн чухал эхэд ашиглах хэрэгтэй."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Ашиглаагүй урьдчи<PERSON><PERSON><PERSON> холболт. Хуудасны хүсэлт гаргах магадлалтай эхэд л preconnect-г ашиглана уу."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Хуудасны ачааллыг сайжруулахын тулд хэлхээний уртыг багасгаж, нөөцийн татах хэмжээг багасгаж, шаардлагагүй нөөцийг татахыг хойшлуулж чухал хүсэлтийн хэлхээ үүсгэхээс зайлсхийнэ үү."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Хүсэлтүүд нь хуудасны анхны рэндэрийг блоклож байгаа бөгөөд энэ нь LCP-г саатуулж магадгүй. [Хойшлуулах эсвэл мөрд оруулах](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) нь эдгээр сүлжээний хүсэлтийг чухал замаас гаргах боломжтой."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Үргэлжлэх хугацаа"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Энэ навигацын хувьд ямар ч рэндэр блоклох хүсэлт байхгүй"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Хүсэлт"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Рэндэрлэ<PERSON>ийг блоклох хүсэлт"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Хэрэв Дахин тооцоолох загварын зардал өндөр хэвээр байвал сонгогчийн оновчлол зардлыг багасгах боломжтой. Их хугацаа зарцуулсан болон удаан замын өндөр %-тай [сонгогчдыг оновчилно уу](https://developer.chrome.com/docs/devtools/performance/selector-stats). Илүү энгийн сонгогч, цөөн сонгогч, жижиг DOM, гүехэн DOM бүгд тааруулах зардлыг багасгана."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Зарцу<PERSON><PERSON><PERSON><PERSON><PERSON> хугацаа"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Я<PERSON>ар ч CSS сонгогчийн өгөгдөл олдсонгүй. CSS сонгогчийн статистикийг гүйцэтгэлийн түр зуурын самбарын тохиргоонд идэвхжүүлэх шаардлагатай."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Таа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> оролдлогo"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Тохирлын тоо"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS-н сонгогчийн зардал"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Ха<PERSON><PERSON><PERSON>н их хугацаа зарцуулсан сонгогчид"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Үндсэн уялдаа<PERSON>ын хугацаа"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Гу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> тал"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Шилжүүлэх хэмжээ"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Гуравдагч талын код нь ачааллын гүйцэтгэлд нэлээд нөлөөлөх боломжтой. Хуудасныхаа контентыг чухалчлахын тулд [гуравдагч талын кодыг ачаалах хугацааг багасгаж, хойшлуулна уу](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Гуравда<PERSON>ч тал олдсонгүй"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Гу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> тал"}, "models/trace/insights/Viewport.ts | description": {"message": "Хэрэв харагдах хэсгийг гар утсанд зориулж оновчлоогүй бол товшилтын харилцан үйлдлийг [300 хүртэлх мс саатуулж](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) магадгүй."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Гар утасны товшилтын саатал"}, "models/trace/insights/Viewport.ts | title": {"message": "Гар утасны харагдах хэсгийг оновчлох"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Зөвхөн GET хүсэлтээр ача<PERSON><PERSON><PERSON><PERSON><PERSON> хуудаснууд back/forward cache-г ашиглах эрхтэй."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Зөвхөн 2XX статус кодтой хуудаснуудыг завсрын санах ойд хадгалах боломжтой."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome нь завсрын санах ойд байх үед JavaScript-г гүйцэтгэх гэсэн оролдлого ирлүүллээ."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner-н хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Back/forward cache-г дарцгаас идэвхгүй болгосон. Энэ төхөөрөмж дээр үүнийг дотоодод идэвхжүүлэхийн тулд chrome://flags/#back-forward-cache-д зочилно уу."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Back/forward cache-г тушаалын мөрөөс идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Санах ой хангалтгүй тул back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Back/forward cache-г төлөөлөгчөөс дэмждэггүй."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Back/forward cache-г урьдчилан рэндэрлэгчид идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Хуудсанд бүртгүүлсэн сонсогчидтой BroadcastChannel-н инстанс байгаа тул үүнийг завсрын санах ойд хадгалах боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Cache-control:no-store-той хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Санах ойг санаатайгаар цэвэрлэсэн."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Өөр хуудсыг завсрын санах ойд хадгалахыг зөвшөөрөхийн тулд уг хуудсыг завсрын санах ойгоос хассан."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Залга<PERSON><PERSON> агуулж буй хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Тодорхойлоогүй"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API-г ашигладаг хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Файлын системийн хандалтын API-г ашигладаг хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Медиа төхөөрөмжийн диспетчер ашигла<PERSON><PERSON><PERSON> хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Шил<PERSON><PERSON>ж явах үед медиа тоглуулагчийг тоглуулж байсан."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API-г ашигладаг, дахин тоглуулах төлөвийг тохируул<PERSON><PERSON>н хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API-г ашигладаг, үйлдэл хариуцагчийг тохируул<PERSON>ан хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Back/forward cache-г дэлгэц уншигчийн улмаас идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler-г ашиг<PERSON>а<PERSON><PERSON><PERSON> хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Цуваа API-г ашигладаг хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API-г ашигладаг хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API-г ашигладаг хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API-г ашигла<PERSON>а<PERSON> хуудаснууд back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store-г ашигладаг хуудсанд күүкиг идэвхгүй болгосон тул back/forward cache-с идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Зориулалтын ажилтан эсвэл ажлын хэсгийн ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Шил<PERSON><PERSON>ж явахаас өмнө документыг ачаалж дуусаагүй."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Шил<PERSON><PERSON>ж явах үед аппын баннер байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Шил<PERSON><PERSON>ж явах үед Chrome-н Нууц үгний менежер байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Шил<PERSON><PERSON>ж явах үед DOM-г шүүж байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Шил<PERSON><PERSON>ж явах үед DOM-н шүүлтүүрийн үзэгч байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Өргөтгөл нь мессеж бичих API-г ашиглаж байсан тул back/forward cache идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Удаан хугацаа<PERSON>ы холболттой өргөтгөл холболтыг back/forward cache-г оруулахаас өмнө хаах ёстой."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Удаан хугацааны холболттой өргөтгөл back/forward cache-д фрейм рүү мессеж илгээхээр оролдсон."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Өргөтгөлийн улмаас back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Шилжиж явах үед маягтын дахин мэдүүлэг эсвэл http нууц үгний харилцах цонх зэрэг зайлшгүй харилцах цонхыг хуудсанд зориулан харуулсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Шил<PERSON><PERSON>ж явах үед офла<PERSON>н хуудсыг харуулсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Шил<PERSON><PERSON>ж явах үед Out-Of-Memory Intervention хэсэг байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Шил<PERSON><PERSON>ж явах үед зөвшөөрлийн хүсэлт байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Шил<PERSON><PERSON>ж явах үед попап блоклогч байсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Шил<PERSON><PERSON>ж явах үед Аюулгүй хөтчийн дэлгэрэнгүйг харуулсан."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Аюулгүй хөтөч энэ хуудсыг зөрчилтэй гэж үзсэн бөгөөд попапыг блоклосон."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> back/forward cache-д байх үед үйлчилгээний ажилтныг идэвхжүүлсэн."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Документын алдааны улмаас back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames-г ашиглаж буй хуудаснуудыг bfcache-д хадгалах боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Өөр хуудсыг завсрын санах ойд хадгалахыг зөвшөөрөхийн тулд уг хуудсыг завсрын санах ойгоос хассан."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Медиа дамжуулалтын хандах эрх олгосон хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Зарим төрлийн контентыг оруул<PERSON><PERSON>н хуудас (жишээ нь: PDF) одоогоор back/forward cache хийх зохих эрхгүй байна."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Нээлттэй IndexedDB-н холболттой хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB арга хэмжээний улмаас back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Эрхгүй API-ууд ашигласан."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Өргөтгөлөөс JavaScript-г оруу<PERSON><PERSON><PERSON><PERSON> хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Өргөтгөлөөс StyleSheet-г оруу<PERSON><PERSON><PERSON><PERSON> хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Дотоод алдаа."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "JavaScript-н сүлжээний зарим хүсэлт Cache-Control: no-store толгой хэсэгтэй нөөц хүлээн авсан тул back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Keepalive хүсэлтийн улмаас back/forward cache-г идэвхгүй болгосон"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Гарын түгжээг ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Шил<PERSON><PERSON>ж явахаас өмнө хуудсыг ачаалж дуусаагүй."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Үндсэн нөөц нь cache-control:no-cache-гүй хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Үндсэн нөөц нь cache-control:no-store-гүй хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Хуудсыг back/forward cache-с сэргээх боломжтой болохоос өмнө навигацыг цуцалсан."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Идэвхтэй сүлжээний холболт хэт их өгөгдөл хүлээн авсан тул хуудсыг завсрын санах ойгоос хассан. Chrome нь завсрын санах ойд хадгалах үед хуудасны хүлээн авах өгөгдлийн хэмжээг хязгаарлана."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Идэвхтэй дуудалт () эсвэл XHR-тай хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Идэвхтэй сүлжээний хүсэлт нь дахин чиглүүлэхийг хамаарсан тул уг хуудсыг back/forward cache-с хассан."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Сүлжээний холболт хэт удаан нээлттэй байсан тул уг хуудсыг завсрын санах ойгоос хассан. Chrome нь завсрын санах ойд хадгалах үед хуудасны өгөгдөл хүлээн авч болох хугацааг хязгаарлана."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Хүчинтэй хариултын толгойгүй хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Үндсэн фреймээс өөр фреймд навигац хийсэн."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Үргэлжилж буй индексжүүлсэн DB гүйлгээтэй хуудас одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Сүлжээний идэвхтэй хүсэлттэй хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Сүлжээг дуудах идэвхтэй хүсэлттэй хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Сүлжээний идэвхтэй хүсэлттэй хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR сүлжээний идэвхтэй хүсэлттэй хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Дэлгэц доторх дэлгэцийг ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Хэвлэлийн UI ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Хуудсыг 'window.open()'-г ашиглан нээсэн бөгөөд өөр табд үүний лавлагаа байгаа эсвэл хуудас цонх нээсэн байна."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Back/forward cache дахь хуудасны рэндэрлэгч явц гэмтсэн."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Back/forward cache дахь хуудасны рэндэрлэгч явцыг устгасан."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Аудио бичих зөвшөөрлийн хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Мэдрэгчийн зөвшөөрлийн хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Дэвсгэрт синк хийх эсвэл зөвшөөрөл дуудах хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MDI-н зөвшөөрлийн хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Мэдэгдлийн зөвшөөрлийн хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Хадгалах санд хандах хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Видео бичих зөвшөөрлийн хүсэлт тавьсан хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Зөвхөн URL-н схем нь HTTP / HTTPS байх хуудаснуудыг завсрын санах ойд хадгалах боломжтой."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Хууд<PERSON>ыг back/forward cache-д байх үед нь үйлчилгээний ажилтан авсан."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Үйлчилгээний ажил<PERSON>а<PERSON> back/forward cache-д байг<PERSON><PERSON> хуудас руу MessageEvent илгээхээр оролдсон."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Хуудсыг back/forward cache-д байх үед ServiceWorker-г бүртгэлээс хассан."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Үйлчилгээний ажилтны идэвхжилтийн улм<PERSON><PERSON><PERSON> хуудсыг back/forward cache-с хассан."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome back/forward cache-н оролтуудыг дахин эхлүүлж, цэвэрлэсэн."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Хуудсан дээрх iframe дуусаагүй навигацыг эхлүүлсэн."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Дэд нөөц нь cache-control:no-cache-тай хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Дэд нөөц нь cache-control:no-store-той хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> back/forward cache-д байх цагийн хязгаараасаа давсан бөгөөд хугацаа нь дууссан."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> back/forward cache-г оруулах үед завсар<PERSON><PERSON><PERSON><PERSON><PERSON> (удаан ажиллуулсан хуудас нуугчийн хариуцагчаас үүдсэн байж магадгүй)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Хуудас үндсэн фреймд ачаалахаа болих явцыг хариуцагчтай байна."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Хууда<PERSON> дэд фреймд ачаалахаа болихыг хариуцагчтай байна."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Хөтөч хэрэглэгчийн агентын дарах толгой хэсгийг өөрчилсөн."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Видео эсвэл аудио бичихэд хандах эрх олгосон хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID-г ашигла<PERSON>а<PERSON> хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService-г ашигладаг хуудаснууд одоогоор bfcache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC-тай хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC-г ашиг<PERSON><PERSON><PERSON><PERSON><PERSON> тул back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare-г ашигла<PERSON>а<PERSON> хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket-той хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket-г ашиг<PERSON><PERSON><PERSON><PERSON><PERSON> тул back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport-той хуудаснууд back/forward cache-д орох боломжгүй."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport-г ашиг<PERSON><PERSON><PERSON><PERSON><PERSON> тул back/forward cache-г идэвхгүй болгосон."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR-г ашигладаг хуудаснууд одоогоор back/forward cache-г ашиглах эрхгүй."}}