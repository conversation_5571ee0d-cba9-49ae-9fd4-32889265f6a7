{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Birtingargildi CSS slider-vertical er ekki staðlað og verður fjarlægt."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() er ú<PERSON><PERSON>, nota skal stöðluð forritaskil í staðinn: nextHopProtocol í tímasetningu flettingar 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Úrelt er að slaka á reglu fyrir sama uppruna með því að stilla document.domain og slökkt verður á þessu sjálfkrafa. Þessi úreldingarviðvörun er fyrir aðgang þvert á uppruna sem kveikt var á með því að stilla document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Virkjun window.alert frá iframe þvert á uppruna er úrelt og verður fjarlægð í framtíðinni."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Virkjun window.confirm frá iframe þvert á uppruna er úrelt og verður fjarlægð í framtíðinni."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Stuðningur fyrir gögn: Vefslóðir í SVGUseElement er úrelt og verður fjarlægð í framtíðinni."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() og watchPosition() virka ekki lengur á ótraustum uppruna. Til að nota þennan eiginleika skaltu íhuga að flytja forritið yfir á öruggan uppruna, t.d. HTTPS. Frekari upplýsingar eru á https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() og watchPosition() eru úrelt í ótraustum uppruna. Til að nota þennan eiginleika skaltu íhuga að flytja forritið yfir á öruggan uppruna, t.d. HTTPS. Frekari upplýsingar eru á https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() virkar ekki lengur á ótraustum uppruna. Til að nota þennan eiginleika skaltu íhuga að flytja forritið yfir á öruggan uppruna, t.d. HTTPS. Frekari upplýsingar eru á https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<PERSON><PERSON> <h1> merki innan <article>, <aside>, <nav>, eða <section> sem er ekki með tilgreindri leturstærð. Stærð þessarar fyrirsagnar mun breytast í þessum vafra í náinni framtíð. Sjá https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 fyrir nánari upplýsingar."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate er úrelt. Nota skal RTCPeerConnectionIceErrorEvent.address eða RTCPeerConnectionIceErrorEvent.port í staðinn."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "<PERSON><PERSON> snið fyrir beiðni um stafræn skilríki, navigator.credentials.get(), er úrelt. Uppfærðu til að nota nýja sniðið."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Uppruna- og handahófsgögn söluaðila úr canmakepayment tilviki þjónustuskriftu eru úrelt og verða fjarlægð: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Reiturinn dailyUpdateUrl í InterestGroups sem var fluttur í joinAdInterestGroup() var endurnefndur updateUrl til að endurspegla virkni hans betur."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator er úrelt. Nota skal Intl.Segmenter í staðinn."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Notkun SourceBuffer.abort() til að hætta við fjarlægingu á ósamstilltu sviði remove() er úrelt vegna breytingar tæknilýsingar. Stuðningur verður fjarlægður síðar meir. Þú ættir að hlusta á updateend tilvikið í staðinn. abort() er aðeins ætlað að hætta við ósamstillta efnisviðbót eða endurstilla þáttunarstöðu."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "<PERSON>kki má lengur nota forritaskil tilkynninga frá ótraustum uppruna. Íhugaðu að færa forritið yfir á öruggan uppruna, t.d. HTTPS. Frekari upplýsingar eru á https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Óv<PERSON>t er að beðið verði um heimild fyrir forritaskilum tilkynninga frá iframe þvert á uppruna. Íhugaðu að biðja um heimild frá ramma á efsta stigi eða opna nýjan glugga í staðinn."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Valkosturinn imageOrientation: 'none' í createImageBitmap er úreltur. Nota skal createImageBitmap með valkostinum '{imageOrientation: 'from-image'}' í staðinn."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Þegar overflow: visible er tilgreint fyrir merkin „img, video og canvas“ búa þau hugsanlega til myndefni utan marka einingarinnar. Sjá https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments er úrelt. Notaðu JIT-uppsetningu (Just In Time) fyrir greiðslumiðlara í staðinn."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Kallið PaymentRequest sneiddi hjá connect-src-tilskipun efnisöryggisstefnunnar (CSP). Þessi hjásneiðing er úrelt. Bættu auðkenni greiðslumáta úr forritaskilunum PaymentRequest (í supportedMethods-reitnum) við connect-src-tilskipun efnisöryggisstefnunnar (CSP)."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent er úrelt. Nota skal staðlað navigator.storage í staðinn."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> með <picture> yfireiningu er ógilt og því hunsað. Nota skal <source srcset> í staðinn."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame er sértækt fyrir sölu<PERSON>ð<PERSON>. Nota skal staðlað cancelAnimationFrame í staðinn."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame er sértækt fyrir sölu<PERSON>ð<PERSON>. Nota skal staðlað requestAnimationFrame í staðinn."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen er úrelt. Nota skal Document.fullscreenElement í staðinn."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() er úrelt. Nota skal Element.requestFullscreen() í staðinn."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() er úrelt. Nota skal Element.requestFullscreen() í staðinn."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() er úrelt. Nota skal Document.exitFullscreen() í staðinn."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() er úrelt. Nota skal Document.exitFullscreen() í staðinn."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen er úrelt. Nota skal Document.fullscreenEnabled í staðinn."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "<PERSON>ið úreldum forritaskilin chrome.privacy.websites.privacySandboxEnabled en þau verða áfram virk til að viðhalda samhæfi við eldri útgáfur fram að útgáfu M113. Nota skal chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled og chrome.privacy.websites.adMeasurementEnabled í staðinn. Sjá https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Takmörkunin DtlsSrtpKeyAgreement var fjarlægð. Þú tilgreindir false gildi fyrir þessa takmörkun sem er túlkað sem tilraun til að nota aðferðina SDES key negotiation sem var fjarlægð. <PERSON><PERSON>i eiginleiki var fjarlægður. Nota skal þjónustu sem styður DTLS key negotiation í staðinn."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Takmörkunin DtlsSrtpKeyAgreement var fjarlægð. Þú tilgreindir true gildi fyrir þessa takmörkun sem hafði engin áhrif en þú getur fjarlægt þessa takmörkun til að hreinsa til."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() sem byggist á svarhringingu er úrelt og verður fjarlægt. Nota skal getStats() sem samræmist tæknilýsingu í staðinn."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() er úrelt. Nota skal Selection.modify() í staðinn."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Valkosturinn rtcpMuxPolicy er úreltur og verður fjarlægður."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() án virkjunar notanda er úrelt og verður fjarlægt."}, "generated/Deprecation.ts | UnloadHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> fjarlægingartilvika eru úreltir og verða fjarlægðir."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter-eigindin er <PERSON>, notaðu GPUAdapterInfo isFallbackAdapter-eigindina í staðinn."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Samstilling XMLHttpRequest í aðalþræði er úrelt vegna skaðlegra áhrifa á upplifun notenda. Frekari aðstoð má finna á https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Uppsetningarhliðranir eiga sér stað þegar einingar hreyfast án inngrips notanda. [Kannaðu orsakir uppsetningarhliðrana](https://web.dev/articles/optimize-cls), eins og hvort einingum hefur verið bætt við, þær fjarlægðar eða hvort leturgerð hefur breyst við hleðslu síðunnar."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Beiðni um leturgerð"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "<PERSON>kki tókst að greina ástæðu fyrir breytingu á uppsetningu"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON>hliðrani<PERSON>"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Or<PERSON>r <PERSON>"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "<PERSON><PERSON><PERSON><PERSON> my<PERSON>ar ekki <PERSON>t"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON> klasi"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> klasi uppse<PERSON>ningarhlið<PERSON>ar"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL í skyndiminni"}, "models/trace/insights/Cache.ts | description": {"message": "Langur líftími skyndiminnis getur hraðað endurteknum heimsóknum á síðuna. [Nánar](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Engar beiðnir með ó<PERSON>lvirkum skyndiminnisreglum"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} í viðbót"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Be<PERSON>ð<PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Nota skilvirkan skyndiminnislíftíma"}, "models/trace/insights/DOMSize.ts | description": {"message": "Stórt DOM-tré getur lengt útreikninga á endurröðun stíla og uppsetninga og þar af leiðandi haft áhrif á aðlögunarhæfni síðu. Stórt DOM-tré mun einnig auka minnisnotkun. [<PERSON><PERSON><PERSON> þér hvernig þú kemur í veg fyrir að vera með of stórt DOM-tré](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Eining"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Flestar undireiningar"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Dýpt DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "T<PERSON>lfræ<PERSON><PERSON><PERSON>lýsingar"}, "models/trace/insights/DOMSize.ts | title": {"message": "Fínstilla stærð DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Heildarfjöldi e<PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "<PERSON><PERSON><PERSON> netkerfisbeiðnin þín er sú mikilvægasta.  <PERSON><PERSON>tu biðtíma hennar til að forðast frams<PERSON>ar, tryggja hraða svörun þjóns og virkja textaþjöppun."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Var framsend ({PH1} framsendingar, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON><PERSON><PERSON> svaraði hægt ({PH1} mældust)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Engin þjöppun notuð"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Forðast framsendingar"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ar hratt ({PH1} mældust)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "<PERSON><PERSON><PERSON><PERSON> texta"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Biðt<PERSON><PERSON> skja<PERSON>"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "<PERSON>þ<PERSON><PERSON><PERSON> niður<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> bæti"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Fjarlægðu stórar og endurteknar JavaScript-einingar úr pökkum til að minnka óþarfa bætanotkun vegna netvirkni."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Afritað JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "<PERSON><PERSON><PERSON> að stilla [font-display](https://developer.chrome.com/blog/font-display) á swap eða optional til að tryggja að textinn sé ávallt sýnilegur. Hægt er að fínstilla swap enn frekar til að draga úr uppsetningarhliðrunum með því að [hnekkja mæligildum leturgerða](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Let<PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Leturbirting"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(nafnlaus)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "<PERSON><PERSON><PERSON>, y<PERSON><PERSON><PERSON> þau sem lesa rúmfræði up<PERSON>, þ<PERSON>a teikni<PERSON> til að gera hlé á keyrslu skriftu til að reikna út stíl og uppsetningu. Nánar um [þvingaða endurröðun](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) og hvernig má koma í veg fyrir hana."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Staflaröð"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Helsta aðgerðarkall"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[án <PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Að minnka niðurhalstíma mynda getur bætt hleðslutíma síðunnar og LCP. [Nánar um fínstillingu myndstærða](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON>æ<PERSON>. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON><PERSON> myndir sem hægt er að fínstilla"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Fínstilla skráarstærð"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} í viðbót"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "<PERSON><PERSON>ð gæti bætt niðurhalsst<PERSON>rð þess<PERSON>r myndar að auka myndþj<PERSON>ppun."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Nútímal<PERSON><PERSON> myndas<PERSON>ð (WebP, AVIF) eða aukin myndþjöppun gæti bætt niðurhalsstærð þessarar myndar."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON><PERSON><PERSON> er stærri en ({PH1}) birtingarstærðir ({PH2}) hennar. Notaðu aðl<PERSON>gunar<PERSON><PERSON><PERSON> myndir til að minnka niðurhalsstærð myndarinnar."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Hægt er að bæta niðurhalsstærð hrey<PERSON>myndaefnis með því að nota vídeósnið í stað GIF."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Notaðu lengsta fasann til að kafa dýpra. [Hægt er að lágmarka tafir](https://web.dev/articles/optimize-inp#optimize_interactions). [Fínstilltu kostnað við aðalþr<PERSON><PERSON>inn](https://web.dev/articles/optimize-long-tasks), oft JS, til að draga úr úrvinnslutíma."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON>g<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Inntaksseinkun"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON>gin gagn<PERSON><PERSON> greind"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fasi"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Tímalengd vinnslu"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP eftir fasa"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Fínstilltu LCP með því að gera LCP-myndina [sýnilega](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) beint úr HTML og [forðast þannig tafir á hleðslu](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority = hátt"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "<PERSON>a ætti „fetchpriority=high“"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ta<PERSON>ni h<PERSON> var ekki beitt"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP-mynd hlaðið {PH1} eftir fyrsta upphafsstað."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ekkert LCP greindist"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Engin LCP-gögn fundust því þetta LCP er ekki mynd"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Beiðni er sýnileg í upphaflega skjalinu"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP-uppgötvunarbeiðni"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Hver [fasi er með sérstakar endurbætingaraðferðir](https://web.dev/articles/optimize-lcp#lcp-breakdown). Helst ætti mestum hluta LCP-tímans að vera varið í hleðslu gagna, ekki í tafir."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON>g<PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON><PERSON><PERSON> hleð<PERSON>lu e<PERSON>ar"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Reitur p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ekkert LCP greindist"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fasi"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON><PERSON> hleð<PERSON>lu gagna"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gagna"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "<PERSON><PERSON><PERSON> fram að fyrsta bæti"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP eftir fasa"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Skrifta"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill-kóðar og umbreytingar gera eldri vöfrum kleift að nota nýja JavaScript-eiginleika. Hins vegar þurfa nútímavafrar yfirleitt ekki á þeim að halda. Íhugaðu að breyta JavaScript-kóðunarferlinu þínu þannig að þú umbreytir ekki og vistþýðir eiginleika á [grunn<PERSON><PERSON><PERSON><PERSON>](https://web.dev/articles/baseline-and-polyfills) nema þú vitir að þú munir þurfa að styðja eldri vafra. [Kynntu þér hvers vegna flest vefsvæði geta notað ES6+ kóða án umbreytingar og vistþýðingar](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Eldra JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 og HTTP/3 bjóða upp á margs konar fríðindi fram yfir HTTP/1.1, eins og multiplexing. [Nánar um að nota nútímalegt HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Be<PERSON>ðnir notuðu ekki HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Samskiptaregla"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Be<PERSON>ð<PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Nútímalegt HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Be<PERSON>ð<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Áætlaður LCP-sparnaður"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Ónotuð fortenging. Gak<PERSON>u úr skugga um að crossorigin-eigindin sé rétt notuð."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Forð<PERSON>u að tengja mikilvægar beiðnir saman](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) með því að stytta keðjur, minnka stærð niðurhals á tilföngum eða fresta niðurhali ónauðsynlegra tilfanga til að bæta síðuhleð<PERSON>."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> [fortengdum](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) leiðréttingum við mikilvægasta upprunann en reyndu að nota ekki fleiri en fjórar."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> sem hentar til fortengingar"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Hámarksbiðt<PERSON><PERSON> s<PERSON>ó<PERSON>:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Netkerfisákvæði hafa ekki áhrif á nein verkefni í bið"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "<PERSON><PERSON>n frekari uppruni hentar til fortengingar"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "enginn forteng<PERSON>r <PERSON>i"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[Fortengdar](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) leiðréttingar hjálpa vafranum að koma á tengingu fyrr þegar síðu er hlaðið, sem sparar tíma þegar fyrsta beiðni um upprunann er send. Eftirfarandi uppruni sem síðan fortengdist."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Fortengdur <PERSON>i"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Ákvæðatré netkerfis"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Fleiri en fjórar preconnect-tengingar fundust. <PERSON><PERSON><PERSON> ætti að nota sparlega og aðeins fyrir mikilvægasta upprunann."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Ónot<PERSON>ð fortenging. Aðeins nota preconnect fyrir uppruna sem líklegt er að síðan biðji um."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Forðast<PERSON> að tengja mikilvægar beiðnir saman með því að stytta keðjur, minnka stærð niðurhals á tilföngum eða fresta niðurhali ónauðsynlegra tilfanga til að bæta síðuhleð<PERSON>lu."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Beiðnir loka á upphaflega hleðslu síðu sem kann að valda töfum á LCP. [Frestun eða innfelling](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) getur fært þessar netkerfisbeiðnir út fyrir nauðsynlega slóð."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON>g<PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Engar beiðnir lokuðu á teiknun fyrir þessa flettingu"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Be<PERSON>ð<PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "<PERSON><PERSON><PERSON> lo<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "<PERSON><PERSON> kostnaður við endurreikning stíls er hár getur fínstilling vals dregið úr honum. [Fínstilltu valið](https://developer.chrome.com/docs/devtools/performance/selector-stats) með bæði löngum gangtíma og hárri % hægslóðar. Einfaldara val, færri valkostir, smærra DOM og grynnra DOM munu draga úr samsvarandi kostnaði."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Liðinn tími"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Engin CSS-valgögn fundust. Kveikt þarf að vera á tölfræðiupplýsingum CSS-vals í stillingum afkastasvæðis."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Tilraunir til samsvörunar"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Kostnaður við CSS-val"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Hels<PERSON> val"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Samtals"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON><PERSON> a<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Flutningsstærð"}, "models/trace/insights/ThirdParties.ts | description": {"message": "<PERSON><PERSON>ði þriðja aðila getur haft veruleg áhrif á hleðslu. [<PERSON>yttu kóða þriðja aðila og frestaðu hleðslu hans](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) til að setja efni síðunnar þinnar í forgang."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON>t efni frá þriðju aðilum fannst"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ila<PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "Smelligagnvirkni gæti [tafist um allt að 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ef glugginn er ekki fínstilltur fyrir snjalltæki."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Seinkun í snjalltækjaviðbragði"}, "models/trace/insights/Viewport.ts | title": {"message": "Fínstilla glugga fyrir s<PERSON>æki"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON><PERSON><PERSON> er hægt að vista síður sem er hlaðið í gegnum GET-beiðni í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "<PERSON><PERSON><PERSON><PERSON> er hægt að vista síður með stöðukóða 2XX í skyndiminni."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome greindi tilraun til að keyra JavaScript í skyndiminni."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um AppBanner í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Flögg hafa slökkt á skyndiminni til baka/áfram. Opnaðu chrome://flags/#back-forward-cache til að virkja það staðbundið í þessu tæki."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Skipanalínan hefur slökkt á skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Slökkt er á skyndiminni til baka/áfram vegna of l<PERSON><PERSON><PERSON> minnis."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Fulltrúi styður ekki sky<PERSON> til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Skyndiminni til baka/áfram er óvirkt fyrir forteiknun."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Ek<PERSON> er hægt að vista síðuna í skyndiminni vegna þess að hún inniheldur BroadcastChannel-tilvik með skráðum hlustendum."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "<PERSON>k<PERSON> er hægt að vista síður með hausinn cache-control:no-store í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Skyndiminnið var hreinsað viljandi."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> var flutt úr skyndiminninu til að koma annarri síðu fyrir."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Eins og stendur er ekki hægt að vista síður sem innihalda viðbætur í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Óskilgreint"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Ekki er hægt að vista síður sem nota forritaskil FileChooser í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "<PERSON>k<PERSON> er hægt að vista síður sem nota File System Access-forritaskil í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Ekki er hægt að vista síður sem nota verkefnastjóra geymslumiðla í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Efnisspilari var í gangi þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Ekki er hægt að vista síður sem nota forritaskil MediaSession og stilla spilunarstöðu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Ekki er hægt að vista síður sem nota forritaskil MediaSession og stilla hjálparforrit aðgerða í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Slökkt er á skyndiminni til baka / áfram vegna skj<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Eins og stendur er ekki hægt að vista síður sem nota SecurityHandler í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "<PERSON>kki er hægt að vista síður sem nota forritaskil Serial í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Ekki er hægt að vista síður sem nota forritaskil WebAuthetication í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Ekki er hægt að vista síður sem nota forritaskil WebBluetooth í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Ekki er hægt að vista síður sem nota forritaskil WebUSB í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Slökkt er á skyndiminni til baka/áfram vegna þess að slökkt er á fótsporum á síðu sem notar Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Eins og stendur er ekki hægt að vista síður sem nota tilg<PERSON>inda skriftu (dedicated worker) eða ein<PERSON>lda skriftu (worklet) í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Skja<PERSON>ð hlóðst ekki að fullu áður en flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Forritaborði var opinn þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Aðgangsorðastjórnun Chrome var opin þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-samantekt var í gangi þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Skoðun DOM-saman<PERSON>ktar var opin þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Slökkt er á skyndiminni til baka / áfram vegna viðbóta sem nota forritaskil skilaboða."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Við<PERSON><PERSON>tur með langlífa tengingu ættu að loka tengingunni áður en þær eru vistaðar í skyndiminni til baka / áfram."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Við<PERSON><PERSON>tur með langlífa tengingu reyndu að senda skilaboð til ramma í skyndiminni til baka / áfram."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Slökkt er á skyndiminni til baka / áfram vegna viðb<PERSON>ta."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Viðbragðsgluggi á borð við endursendingu eyðublaðs eða HTTP-aðgangsorðsgluggi var birtur á síðunni þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "<PERSON><PERSON>ða án nettengingar var birt þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „<PERSON>ni fullt“ var opin þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Heimildabeið<PERSON> voru til staðar þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Sprettigluggavörn var til staðar þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Upplýsingar öruggrar vefskoðunar voru birtar þegar flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Örugg vefskoðun tengdi þessa síðu við misnotkun og lokaði á sprettiglugga."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (service worker) var virkjuð á meðan síðan var í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Slökkt er á skyndiminni til baka / áfram vegna villu í skjali."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON>ur sem nota FencedFrames er ekki hægt að geyma í skyndiminni til baka / áfram."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> var flutt úr skyndiminninu til að koma annarri síðu fyrir."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Eins og stendur er ekki hægt að vista síður sem heimila aðgang að efnisstraumi í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Eins og stendur eru síður sem innihalda vissar gerðir af innfelldu efni (t.d. PDF-skjöl) ekki gjaldgengar fyrir skyndiminni til baka / áfram."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Eins og stendur er ekki hægt að vista síður sem nota IdleManager í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Eins og stendur er ekki hægt að vista síður með opna IndexedDB-tengingu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Slökkt er á skyndiminni til baka / áfram vegna IndexedDB-tilviks"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "<PERSON><PERSON><PERSON> voru notuð."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Eins og stendur er ekki hægt að vista síður þar sem JavaScript er komið fyrir með viðbótum í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Eins og stendur er ekki hægt að vista síður þar sem StyleSheet er komið fyrir með viðbótum í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON>ri villa."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Slökkt er á skyndiminni til baka/áfram vegna þess að <PERSON>Script-netbeiðni fékk gögn með Cache-Control: no-store-haus."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Slökkt er á skyndiminni til baka / áfram vegna beiðni um að halda tengingu."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Eins og stendur er ekki hægt að vista síður sem nota lyklaborðslás í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON><PERSON><PERSON><PERSON> hlóðst ekki að fullu áður en flett var í burtu."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Ekki er hægt að vista síður með aðaltilfangið cache-control:no-cache í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Ekki er hægt að vista síður með aðaltilfangið cache-control:no-store í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON>tt var við flettingu áður en hægt var að endurheimta síðuna úr skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> var flutt úr skyndiminni vegna þess að virk nettenging tók við of miklu gagnamagni. Chrome takmarkar gagnamagnið sem síða getur tekið við í skyndiminni."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Eins og stendur er ekki hægt að vista síður sem eru með fetch() eða XHR í vinnslu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "<PERSON><PERSON><PERSON><PERSON> var flutt úr skyndiminni til baka/áfram vegna þess að virk netkerfisbeiðni fól í sér frams<PERSON>u."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Síðan var flutt úr skyndiminninu vegna þess að nettenging var opin of lengi. Chrome takmarkar tímann sem síða getur tekið við gögnum í skyndiminni."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Ekki er hægt að vista síður sem eru með ógildan svarhaus í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Fletting á<PERSON> sér stað í ramma sem er ekki að<PERSON>n."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Eins og stendur er ekki hægt að vista síður með viðvarandi, skráðar DB-færslur í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Eins og stendur er ekki hægt að vista síður með netkerfisbeiðni í vinnslu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Eins og stendur er ekki hægt að vista síður með beiðnir um að sækja netkerfi í vinnslu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Eins og stendur er ekki hægt að vista síður með netkerfisbeiðni í vinnslu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Eins og stendur er ekki hægt að vista síður með XHR-netkerfisbeiðni í vinnslu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Eins og stendur er ekki hægt að vista síður sem nota PaymentManager í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Eins og stendur er ekki hægt að vista síður sem nota mynd í mynd í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Ekki er hægt að vista síður sem birta prentviðmót í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "<PERSON><PERSON><PERSON><PERSON> var opnuð með „window.open()“ og annar flipi vísar á síðuna, eða síðan opnaði glugga."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "<PERSON>run varð við teiknu<PERSON>li síðu í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Teiknunar<PERSON>li síðu í skyndiminni til baka/áfram var stöðvað."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um heimild fyrir hljóðupptöku í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um skynjaraheimildir í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um að samstilla í bakgrunni eða að sækja heimildir í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um MIDI-heimildir í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um tilkynningaheimildir í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um aðgang að geymslurými í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Eins og stendur er ekki hægt að vista síður sem biðja um heimild fyrir myndskeiðsupptöku í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "<PERSON><PERSON><PERSON><PERSON> er hægt að vista síður með HTTP- / HTTPS-vefslóðarskema í skyndiminni."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (service worker) gerð<PERSON> til<PERSON> til síðunnar á meðan hún var í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (service worker) reyndi að senda síðu í skyndiminni til baka/áfram MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (service worker) var afskráð á meðan síða var í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON><PERSON><PERSON> var flutt úr skyndiminni til baka/áfram vegna virkjunar þj<PERSON> (service worker)."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome var endurræstur og hreinsaði færslur í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Eins og stendur er ekki hægt að vista síður sem nota SharedWorker í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Eins og stendur er ekki hægt að vista síður sem nota SpeechRecognizer í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Eins og stendur er ekki hægt að vista síður sem nota SpeechSynthesis í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe á síðunni hóf flettingu sem var ekki lokið við."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Ek<PERSON> er hægt að vista síður með undirtilfangið cache-control:no-cache í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Ek<PERSON> er hægt að vista síður með undirtilfangið cache-control:no-store í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON><PERSON><PERSON> fór y<PERSON><PERSON> í skyndiminni til baka/áfram og rann út á tíma."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "<PERSON><PERSON><PERSON><PERSON> rann út á tíma við vistun í skyndiminni til baka/áfram (líklega vegna langvarandi keyrslu pagehide-hjálparforrita)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON> er með hre<PERSON> (unload handler) í aðalrammanum."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "<PERSON><PERSON><PERSON><PERSON> er með hre<PERSON> (unload handler) í undirramma."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON> br<PERSON> hnekkingarhaus aðgangsbúnaðar."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Eins og stendur er ekki hægt að vista síður sem hafa veitt aðgang að mynd- og hljóðupptökum í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebDatabase í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebHID í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebLocks í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "<PERSON>k<PERSON> er hægt að vista síður sem nota WebNfc í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebOTPService í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Ekki er hægt að vista síður með WebRTC í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Slökkt er á skyndiminni til baka/áfram vegna þess að WebRTC var notað."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebShare í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Ekki er hægt að vista síður með WebSocket í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Slökkt er á skyndiminni til baka/áfram vegna þess að WebSocket var notað."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Ek<PERSON> er hægt að vista síður með WebTransport í skyndiminni til baka/áfram."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Slökkt er á skyndiminni til baka/áfram vegna þess að WebTransport var notað."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Eins og stendur er ekki hægt að vista síður sem nota WebXR í skyndiminni til baka/áfram."}}