{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "A autorização não vai estar abrangida pelo símbolo de caráter universal (*) no processamento do atributo Access-Control-Allow-Headers de CORS (partilha de recursos de origem cruzada)."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Deve usar o atributo disableRemotePlayback em vez do seletor -internal-media-controls-overlay-cast-button para desativar a integração do Cast predefinida."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "O valor de aspeto de CSS slider-vertical não está padronizado e vai ser removido."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Os pedidos de recursos cujos URLs continham os carateres \\(n|r|t) de espaços em branco removidos e o limite mínimo de carateres (<) foram bloqueados. Remova as novas linhas e codifique o limite mínimo de carateres de locais como valores de atributos de elementos para carregar estes recursos."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "O atributo chrome.loadTimes() foi descontinuado. Em alternativa, use a API padronizada: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "O atributo chrome.loadTimes() foi descontinuado. Em alternativa, use a API padronizada: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "O atributo chrome.loadTimes() foi descontinuado. Em alternativa, use a API padronizada: nextHopProtocol no Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Os cookies que contêm um caráter \\(0|r|n) vão ser rejeitados em vez de truncados."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "O relaxamento da política da mesma origem ao definir o atributo document.domain foi descontinuado e vai ser desativado por predefinição. Este aviso de descontinuação é para um acesso de origem cruzada que foi ativado ao definir o atributo document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "O acionamento da função window.alert a partir de iFrames de origem cruzada foi descontinuado e vai ser removido no futuro."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "O acionamento da função window.confirm a partir de iFrames de origem cruzada foi descontinuado e vai ser removido no futuro."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Suporte de dados: os URLs em SVGUseElement foram descontinuados e vão ser removidos no futuro."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Os atributos getCurrentPosition() e watchPosition() já não funcionam em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Os atributos getCurrentPosition() e watchPosition() foram descontinuados em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "O atributo getUserMedia() já não funciona em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Foi encontrada uma etiqueta <h1> dentro de um <article>, <aside>, <nav> ou uma <section> que não tem um tamanho de tipo de letra especificado. O tamanho deste texto do cabeçalho vai mudar neste navegador num futuro próximo. Consulte https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 para mais informações."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "O atributo RTCPeerConnectionIceErrorEvent.hostCandidate foi descontinuado. Em alternativa, use o atributo RTCPeerConnectionIceErrorEvent.address ou RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Este formato para o pedido navigator.credentials.get() de credenciais digitais foi descontinuado. Atualize a sua chamada para usar o novo formato."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "A origem do comerciante e os dados arbitrários do evento do service canmakepayment worker foram descontinuados e vão ser removidos: topOrigin, paymentRequestOrigin, methodData e modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "O Website pediu um subrecurso de uma rede à qual só podia aceder devido à posição da rede privilegiada dos respetivos utilizadores. Estes pedidos expõem servidores e dispositivos não públicos à Internet, aumentando o risco de um ataque de falsificação de pedido entre sites (CSRF) e/ou de uma fuga de informações. Para mitigar estes riscos, o Chrome descontinua pedidos para subrecursos não públicos quando forem iniciados por contextos não seguros e vai começar a bloqueá-los."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "O nome do campo dailyUpdateUrl de InterestGroups transmitido a joinAdInterestGroup() foi mudado para updateUrl, de modo a refletir com maior precisão o respetivo comportamento."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "O atributo Intl.v8BreakIterator foi descontinuado. Em alternativa, use Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Não é possível carregar o CSS a partir dos URLs file:, exceto se terminarem numa extensão de ficheiro .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "A utilização do atributo SourceBuffer.abort() para interromper a remoção do intervalo assíncrono do atributo remove() foi descontinuado devido a uma alteração na especificação. O suporte vai ser removido no futuro. Em alternativa, deve ouvir o evento updateend. O atributo abort() destina-se apenas a interromper um estado de analisador de reposição ou anexação de suporte assíncrono."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "A definição do atributo MediaSource.duration abaixo da data/hora de apresentação mais elevada de quaisquer frames codificados no buffer foi descontinuada devido a uma alteração na especificação. O suporte para uma remoção implícita do suporte no buffer truncado vai ser removido no futuro. Em alternativa, deve executar um atributo remove(newDuration, oldDuration) explícito em todos os sourceBuffers, em que newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "A MIDI da Web vai pedir uma autorização de utilização mesmo que o sysex não esteja especificado no atributo MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "A API Notification já não pode ser usada a partir de origens inseguras. Deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "A autorização para a API Notification pode já não ser pedida a partir de um iFrame de origem cruzada. Em alternativa, deve considerar pedir autorização a partir de um frame de nível superior ou abrir uma nova janela."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "A opção imageOrientation: 'none' em createImageBitmap foi descontinuada. Em alternativa, use createImageBitmap com a opção \"{imageOrientation: 'from-image'\\}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "O seu parceiro está a negociar uma versão do (D)TLS (Transport Layer Security) obsoleta. Contacte o seu parceiro para corrigir isto."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Especificar overflow: visible em etiquetas img, video e canvas pode fazer com que produzam conteúdo visual fora dos limites do elemento. Consulte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "O atributo paymentManager.instruments foi descontinuado. Em alternativa, use a instalação just-in-time para os controladores de pagamento."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "A sua chamada de PaymentRequest ignorou a diretiva da Política de Segurança de Conteúdos (CSP) connect-src. Esta ação de ignorar foi descontinuada. Adicione o identificador do método de pagamento da API PaymentRequest (no campo supportedMethods) à diretiva da CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "O atributo StorageType.persistent foi descontinuado. Em alternativa, use o atributo navigator.storage padronizado."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "O atributo <source src> com um <picture> superior é inválido e, por isso, vai ser ignorado. Em alternativa, use <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "O método webkitCancelAnimationFrame é específico do fornecedor. Em alternativa, use o método cancelAnimationFrame padrão."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "O método webkitRequestAnimationFrame é específico do fornecedor. Em alternativa, use o método requestAnimationFrame padrão."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "A função HTMLVideoElement.webkitDisplayingFullscreen foi descontinuada. Em alternativa, use Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "A API HTMLVideoElement.webkitEnterFullScreen() foi descontinuada. Em alternativa, use a API Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "A API HTMLVideoElement.webkitEnterFullscreen() foi descontinuada. Em alternativa, use a API Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "A API HTMLVideoElement.webkitExitFullScreen() foi descontinuada. Em alternativa, use a API Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "A API HTMLVideoElement.webkitExitFullscreen() foi descontinuada. Em alternativa, use a API Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "A função webkitSupportsFullscreen foi descontinuada. Em alternativa, use a função Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Vamos descontinuar a API chrome.privacy.websites.privacySandboxEnabled, mas esta permanece ativa para retrocompatibilidade até ao lançamento M113. Em alternativa, use chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled e chrome.privacy.websites.adMeasurementEnabled. Veja https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "A restrição DtlsSrtpKeyAgreement foi removida. Especificou um valor false para esta restrição, que é interpretado como uma tentativa de usar o método SDES key negotiation removido. Esta funcionalidade foi removida. Em alternativa, use um serviço que suporte o atributo DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "A restrição DtlsSrtpKeyAgreement foi removida. Especificou um valor true para esta restrição, que não teve qualquer efeito, mas pode remover a restrição para fins de organização."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "O método getStats() baseado em chamadas de resposta foi descontinuado e vai ser removido. Em alternativa, use o método getStats() em conformidade com a especificação."}, "generated/Deprecation.ts | RangeExpand": {"message": "A API Range.expand() foi descontinuada. Em alternativa, use a API Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Os pedidos de subrecursos cujos URLs continham credenciais incorporadas (por exemplo, **********************/) foram bloqueados."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "A opção rtcpMuxPolicy foi descontinuada e vai ser removida."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "O atributo SharedArrayBuffer vai requerer isolamento de origem cruzada. Consulte https://developer.chrome.com/blog/enabling-shared-array-buffer/ para obter mais detalhes."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "O atributo speechSynthesis.speak() sem a ativação do utilizador foi descontinuado e vai ser removido."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Os ouvintes de eventos unload foram descontinuados e vão ser removidos."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "As extensões devem aceitar o isolamento de origem cruzada para continuar a usar o atributo SharedArrayBuffer. Consulte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "O atributo isFallbackAdapter de GPUAdapter foi descontinuado. Em alternativa, use o atributo isFallbackAdapter de GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "O formato UTF-16 não é suportado por JSON de resposta no atributo XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "A API XMLHttpRequest síncrona na thread principal foi descontinuada devido aos respetivos efeitos adversos na experiência do utilizador final. Para obter mais ajuda, consulte https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animação"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "As mudanças de esquema ocorrem quando os elementos se movem sem nenhuma interação do utilizador. [Investigue as causas das mudanças de esquema](https://web.dev/articles/optimize-cls), como a adição ou a remoção de elementos ou a alteração dos respetivos tipos de letra à medida que a página é carregada."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Pedido de tipo de letra"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "iFrame injetado"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Tempo de início do cluster de mudança de esquema: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Não foi possível detetar nenhuma causa da mudança de esquema"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Sem <PERSON> de esquema"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Causas da mudança de esquema"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Principais causas da mudança de esquema"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Pior cluster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Pior cluster de mudança de esquema"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL da cache"}, "models/trace/insights/Cache.ts | description": {"message": "Uma longa duração total da cache pode acelerar as visitas repetidas à sua página. [<PERSON><PERSON> mais](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Sem pedidos com políticas de cache ineficientes"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON> {<PERSON><PERSON>}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Pedido"}, "models/trace/insights/Cache.ts | title": {"message": "Use durações totais de cache eficientes"}, "models/trace/insights/DOMSize.ts | description": {"message": "Um DOM grande pode aumentar a duração dos cálculos de estilo e dos ajustes de esquema, o que afeta a capacidade de resposta da página. Um DOM grande também aumenta a utilização de memória. [Saiba como evitar um tamanho excessivo do DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elemento"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "A maioria dos elementos secundários"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Profundidade do DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Estatística"}, "models/trace/insights/DOMSize.ts | title": {"message": "Otimize o tamanho do DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Total de elementos"}, "models/trace/insights/DOMSize.ts | value": {"message": "Valor"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "O primeiro pedido de rede é o mais importante.  Reduza a latência evitando redirecionamentos, assegurando uma resposta rápida do servidor e ativando a compressão de texto."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Tinha redirecionamentos ({PH1} redirecionamentos, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "O servidor respondeu lentamente ({PH1} observados)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nenhuma compressão aplicada"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Evita redirecionamentos"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "O servidor responde rapidamente ({PH1} observados)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Aplica compressão de texto"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redirecionamentos"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Tempo de resposta do servidor"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latência do pedido de documento"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Transferência não comprimida"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Bytes duplicados"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Origem"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Remova módulos de JavaScript grandes e duplicados de pacotes para reduzir bytes desnecessários consumidos pela atividade da rede."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript duplicado"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Considere definir [font-display](https://developer.chrome.com/blog/font-display) como swap ou optional para garantir que o texto é visível de forma consistente. É possível otimizar ainda mais o swap para mitigar as mudanças de esquema com [substituições de métricas de tipos de letra](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON><PERSON><PERSON> de letra"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Apresentação do tipo de letra"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Tempo perdido"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anónimo)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Muitas APIs, que normalmente leem a geometria do esquema, forçam o motor de renderização a pausar a execução do script para calcular o estilo e o esquema. Saiba mais sobre o [ajuste forçado](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) e as respetivas mitigações."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Ra<PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON><PERSON>çado"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Chamada de função principal"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Tempo de ajuste total"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[não atribuída]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Reduzir o tempo de transferência das imagens pode melhorar o tempo de carregamento percecionado da página e o LCP. [Saiba como otimizar o tamanho da imagem](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Estimativa: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON><PERSON><PERSON><PERSON> imagem a otimizar"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Otimize o tamanho do ficheiro"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON> {<PERSON><PERSON>}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Melhore a entrega de imagens"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Aumentar o fator de compressão da imagem pode melhorar o tamanho de transferência desta imagem."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Usar um formato de imagem moderno (WebP ou AVIF) ou aumentar a compressão da imagem pode melhorar o tamanho de transferência desta imagem."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Este ficheiro de imagem é maior do que precisa de ser ({PH1}) para as respetivas dimensões apresentadas ({PH2}). Use imagens adaptáveis para reduzir o tamanho de transferência da imagem."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Usar formatos de vídeo em vez de GIFs pode melhorar o tamanho de transferência do conteúdo animado."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Comece a investigar com a fase mais longa. [Os atrasos podem ser minimizados](https://web.dev/articles/optimize-inp#optimize_interactions). Para reduzir a duração do processamento, [otimize os custos da thread principal](https://web.dev/articles/optimize-long-tasks), muitas vezes JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Duração"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Atraso na entrada"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nenhuma interação detetada"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Atraso na apresentação"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Duração do processamento"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP por fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Otimize o LCP tornando a imagem do LCP [detetável](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) imediatamente a partir do HTML e [evitando o carregamento em diferido](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "valor fetchpriority=high aplicado"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Deve ser aplicado o valor fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "carregamento em diferido não aplicado"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "A imagem de LCP carregou {PH1} após o ponto de início mais antigo."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nenhum LCP detetado"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nenhum recurso de LCP detetado porque o LCP não é uma imagem"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "O pedido é detetável no documento inicial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Deteção de pedidos de LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Cada [fase tem estratégias de melhoria específicas](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idealmente, a maior parte do tempo de LCP deve ser gasto no carregamento dos recursos e não nos atrasos."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Duração"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Atraso de renderização do elemento"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Campo do percentil 75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nenhum LCP detetado"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Atraso no carregamento do recurso"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Duração do carregamento do recurso"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP por fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bytes desperdiçados"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Os polyfills e as transformações permitem que os navegadores mais antigos usem novas funcionalidades de JavaScript. No entanto, muitos não são necessários para os navegadores modernos. Considere modificar o processo de compilação de JavaScript para não transpilar as funcionalidades do [Baseline](https://web.dev/articles/baseline-and-polyfills), a menos que saiba que tem de ser compatível com navegadores mais antigos. [Saiba por que motivo a maioria dos sites pode implementar código ES6+ sem transpilação](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript antigo"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "O HTTP/2 e o HTTP/3 oferecem muitas vantagens em relação ao HTTP/1.1, como a multiplexagem. [Saiba mais sobre a utilização do HTTP moderno](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Nenhum pedido usou HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocolo"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Pedido"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP moderno"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origem"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Pedido"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Fonte"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Poupança de LCP estimada"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Pré-ligação não usada. Verifique se o atributo crossorigin está a ser usado corretamente."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Evite encadear pedidos críticos](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) reduzindo o tamanho das cadeias, reduzindo o tamanho de transferência dos recursos ou adiando a transferência de recursos desnecessários para melhorar o carregamento de página."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Adicione instruções de [pré-ligação](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) às suas origens mais importantes, mas experimente usar, no máximo, 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidatas a pré-ligação"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latência máxima de caminho crítico:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nenhuma tarefa de renderização afetada por dependências de rede"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Não existem origens adicionais que sejam boas candidatas para pré-ligação"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "não foram estabelecidas pré-ligações com origens"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "As instruções de [pré-ligação](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ajudam o navegador a estabelecer uma ligação mais cedo no carregamento de página, poupando tempo quando é feito o primeiro pedido para essa origem. Seguem-se as origens às quais a página estabeleceu uma pré-ligação."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origens pré-ligadas"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Árvore de dependências de rede"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Foram encontradas mais de 4 ligações de preconnect. Estas ligações devem ser usadas com moderação e apenas para as origens mais importantes."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Pré-ligação não usada. Use preconnect apenas para origens que a página provavelmente vai pedir."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Evite encadear pedidos críticos reduzindo o tamanho das cadeias, reduzindo o tamanho de transferência dos recursos ou adiando a transferência de recursos desnecessários para melhorar o carregamento de página."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Os pedidos estão a bloquear a renderização inicial da página, o que pode atrasar o LCP. [Adiar ou colocar inline](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) pode remover estes pedidos de rede do caminho crítico."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Duração"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nenhum pedido de bloqueio de renderização para esta navegação"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Pedido"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Pedidos de bloqueio de renderização"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Se os custos de Recalcular estilo permanecerem elevados, a otimização do seletor pode reduzi-los. [Otimize os seletores](https://developer.chrome.com/docs/devtools/performance/selector-stats) com um tempo decorrido elevado e uma percentagem de caminho lento elevada. Seletores mais simples, menos seletores, um DOM menor e um DOM mais superficial reduzem os custos de correspondência."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Tempo decorrido"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Não foram encontrados dados do seletor CSS. As estatísticas do seletor CSS têm de estar ativadas nas definições do painel de desempenho."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Tentativas de correspondência"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Contagem de correspondências"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Custos do seletor CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Principais se<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Tempo do thread principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Te<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "O código de terceiros pode afetar significativamente o desempenho de carregamento. [Reduza e adie o carregamento de código de terceiros](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) para dar prioridade ao conteúdo da sua página."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Não foram encontrados terceiros"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Te<PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "As interações com toques podem ter um [atraso de até 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) se a área visível não estiver otimizada para dispositivos móveis."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Atraso de toque em dispositivos móveis"}, "models/trace/insights/Viewport.ts | title": {"message": "Otimize a área visível para dispositivos móveis"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON>as as páginas carregadas através de um pedido GET são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Apenas as páginas com um código de estado de 2XX podem ser colocadas em cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "O Chrome detetou uma tentativa de execução do JavaScript enquanto a página estava na cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Atualmente, as páginas que solicitaram um AppBanner não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "A cache para a frente/para trás foi desativada por sinalizações. Visite chrome://flags/#back-forward-cache para a ativar localmente neste dispositivo."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "A cache para a frente/para trás foi desativada pela linha de comandos."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "A cache para a frente/para trás foi desativada devido a memória insuficiente."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "A cache para a frente/para trás não é suportada pelo delegado."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "A cache para a frente/para trás foi desativada pelo pré-renderizador."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Não é possível colocar a página em cache porque tem uma instância BroadcastChannel com ouvintes registados."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Não é possível adicionar páginas com um cabeçalho cache-control:no-store à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "A cache foi limpa intencionalmente."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "A página foi removida da cache para permitir que outra página fosse colocada em cache."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Atualmente, as páginas que contêm plug-ins não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON> definido"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "As páginas que utilizam a API FileChooser não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "As páginas que utilizam a API File System Access não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Atualmente, as páginas que utilizam o Media Device Dispatcher não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Um leitor de multimédia estava em reprodução quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "As páginas que utilizam a API MediaSession e definem um estado de reprodução não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "As páginas que utilizam a API MediaSession e definem controladores de ações não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "A cache para a frente/para trás foi desativada devido ao leitor de ecrã."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "As páginas que utilizam SecurityHandler não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "As páginas que utilizam a API Serial não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "As páginas que utilizam a API WebAuthetication não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "As páginas que utilizam a API WebBluetooth não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "As páginas que utilizam a API WebUSB não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "A cache para a frente/para trás foi desativada porque os cookies estão desativados numa página que usa Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Atualmente, as páginas que utilizam uma trabalhadora ou worklet dedicados não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "O documento não concluiu o carregamento antes de sair do mesmo."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "A faixa da app estava presente quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "O Gestor de palavras-passe do Chrome estava presente quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A destilação DOM estava em curso quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "O Visualizador DOM Distiller estava presente quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "A cache para a frente/para trás foi desativada devido a extensões que utilizam a API de mensagens."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "As extensões com ligações de longa duração devem fechar a ligação antes de entrar na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "As extensões com ligações de longa duração tentaram enviar mensagens para frames na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "A cache para a frente/para trás foi desativada devido às extensões."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "A caixa de diálogo modal, como a caixa de diálogo da palavra-passe HTTP ou reenvio do formulário, foi apresentada para a página quando saiu da mesma."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "A página offline foi apresentada quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "A barra Intervenção sem memória estava presente quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Houve pedidos de autorização quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "O bloqueador de pop-ups estava presente quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "<PERSON>s detalhes da Navegação segura foram apresentados quando saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "A Navegação segura considerou esta página abusiva e bloqueou o pop-up."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Um service worker foi ativado enquanto a página estava na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "A cache para a frente/para trás foi desativada devido a um erro do documento."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Não é possível armazenar páginas com FencedFrames na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "A página foi removida da cache para permitir que outra página fosse colocada em cache."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Atualmente, as páginas que concederam acesso à stream de multimédia não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Atualmente, as páginas com determinados tipos de conteúdo incorporado (por exemplo, PDFs) não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Atualmente, as páginas que utilizam IdleManager não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Atualmente, as páginas com uma ligação IndexedDB aberta não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "A cache para a frente/para trás foi desativada devido a um evento IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Foram utilizadas APIs não elegíveis."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Atualmente, as páginas em que a folha de estilos JavaScript é injetada por extensões não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Atualmente, as páginas em que a folha de estilos StyleSheet é injetada por extensões não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON>rro interno."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "A cache para a frente/para trás foi desativada porque alguns pedidos de rede JavaScript receberam o recurso com o cabeçalho Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "A cache para a frente/para trás foi desativada devido a um pedido keep-alive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Atualmente, as páginas que utilizam bloqueio do teclado não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "A página não concluiu o carregamento antes de sair da mesma."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-cache à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-store à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "A navegação foi cancelada antes de a página poder ser restaurada a partir da cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "A página foi removida da cache porque uma ligação de rede ativa recebeu demasiados dados. O Chrome limita a quantidade de dados que uma página pode receber enquanto está em cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Atualmente, as páginas com fetch() ou XHR em processamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "A página foi removida da cache para a frente/para trás porque um pedido de rede ativo envolveu um redirecionamento."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "A página foi removida da cache porque uma ligação de rede ficou aberta por demasiado tempo. O Chrome limita o período de tempo durante o qual uma página pode receber dados enquanto está em cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Não é possível adicionar páginas que não têm um cabeçalho de resposta válido à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "A navegação ocorreu num frame diferente do frame principal."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Atualmente, as páginas com transações DB indexadas em curso não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Atualmente, as páginas com um pedido de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Atualmente, as páginas com um pedido de obtenção de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Atualmente, as páginas com um pedido de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Atualmente, as páginas com um pedido de rede XHR em processamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Atualmente, as páginas que utilizam PaymentManager não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Atualmente, as páginas que utilizam a funcionalidade ecrã no ecrã não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Atualmente, as páginas que utilizam a IU de impressão não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "A página foi aberta através de \"window.open()\" e é referenciada por outro separador ou a página abriu uma janela."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "O processo de renderização da página na cache para a frente/para trás falhou."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "O processo de renderização da página na cache para a frente/para trás foi interrompido."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Atualmente, as páginas que solicitaram autorizações de captura de áudio não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Atualmente, as páginas que solicitaram autorizações de sensores não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Atualmente, as páginas que solicitaram a sincronização em segundo plano ou autorizações de obtenção não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Atualmente, as páginas que solicitaram autorizações MIDI não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Atualmente, as páginas que solicitaram autorizações de notificações não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Atualmente, as páginas que solicitaram acesso ao armazenamento não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Atualmente, as páginas que solicitaram autorizações de captura de vídeo não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Apenas as páginas com um esquema de URL HTTP/HTTPS podem ser colocadas em cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "A página foi reivindicada por um service worker enquanto estava na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Um service worker tentou enviar um MessageEvent para a página na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "O registo do ServiceWorker foi anulado enquanto a página estava na cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "A página foi removida da cache para a frente/para trás devido à ativação de um service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "O Chrome foi reiniciado e limpou as entradas da cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Atualmente, as páginas que utilizam SharedWorker não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Atualmente, as páginas que utilizam SpeechRecognizer não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Atualmente, as páginas que utilizam SpeechSynthesis não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Um iFrame na página iniciou uma navegação que não foi concluída."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm sub-recursos com cache-control:no-cache à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm sub-recursos com cache-control:no-store à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "A página excedeu o tempo máximo na cache para a frente/para trás e expirou."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "A página expirou ao ser adicionada à cache para a frente/para trás (provavelmente devido aos controladores pagehide em execução há muito tempo)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "A página tem um controlador de descarregamento no frame principal."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "A página tem um controlador de descarregamento num subframe."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "O navegador alterou o cabeçalho de substituição do agente do utilizador."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Atualmente, as páginas que concederam acesso à gravação de vídeo ou áudio não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Atualmente, as páginas que utilizam WebDatabase não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Atualmente, as páginas que utilizam WebHID não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Atualmente, as páginas que utilizam WebLocks não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Atualmente, as páginas que utilizam WebNfc não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Atualmente, as páginas que utilizam WebOTPService não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Não é possível adicionar páginas com WebRTC à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "A cache para a frente/para trás foi desativada porque o WebRTC foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Atualmente, as páginas que utilizam WebShare não são elegíveis para a cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Não é possível adicionar páginas com WebSocket à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "A cache para a frente/para trás foi desativada porque o WebSocket foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Não é possível adicionar páginas com WebTransport à cache para a frente/para trás."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "A cache para a frente/para trás foi desativada porque o WebTransport foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Atualmente, as páginas que utilizam WebXR não são elegíveis para a cache para a frente/para trás."}}