{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "На авторизацію під час обробки CORS Access-Control-Allow-Headers не поширюватиметься символ підстановки (*)."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Щоб вимкнути інтеграцію Google Cast за умовчанням, використовуйте атрибут disableRemotePlayback замість засобу вибору -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Значення зовнішнього вигляду CSS slider-vertical не стандартизоване й буде вилучене."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Запити ресурсів до URL-адрес, які містять вилучені пробіли \\(n|r|t) та знаки менше (<), блокуються. Щоб завантажити ці ресурси, вилучіть символи нового рядка з таких місць, як значення атрибутів елементів, і закодуйте знаки менше."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() не підтримується. Натомість використовуйте стандартизований інтерфейс API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() не підтримується, натомість використовуйте стандартизований інтерфейс API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() не підтримується. Натомість використовуйте стандартизований метод API: nextHopProtocol у специфікації Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Файли cookie, які містять символ \\(0|r|n), будуть відхилятися, а не скорочуватися."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Правило одного джерела більше не можна буде послаблювати, налаштувавши document.domain, і цю функцію буде вилучено. Це сповіщення про припинення міждоменного доступу, активованого завдяки налаштуванню document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Запуск window.alert із міждоменних елементів iframe не підтримується, і в майбутньому цю функцію буде вилучено."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Запуск window.confirm із міждоменних елементів iframe не підтримується, і в майбутньому цю функцію буде вилучено."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Обробка даних: підтримку URL-адрес у SVGUseElement припинено, і в майбутньому цю функцію буде вилучено."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() і watchPosition() більше не працюють із незахищеними джерелами. Щоб користуватися цією функцією, перенесіть свій додаток на захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() і watchPosition() з незахищених джерел не підтримуються. Щоб користуватися цією функцією, перенесіть свій додаток на захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() більше не працює з незахищеними джерелами. Щоб користуватися цією функцією, перенесіть свій додаток на захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Знайдено тег <h1> у тегах <article>, <aside>, <nav> або <section>. Розмір шрифту не вказано. Незабаром розмір цього заголовка буде змінено у вебпереглядачі. Щоб дізнатися більше, перегляньте цю сторінку: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate не підтримується. Натомість використовуйте RTCPeerConnectionIceErrorEvent.address або RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Цей формат запиту navigator.credentials.get() для цифрових облікових даних більше не підтримується. Оновіть свій виклик, щоб використовувати новий формат."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Джерело продавця й довільні дані події Service Worker canmakepayment не підтримуються. Їх буде вилучено: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Веб-сайт надіслав запит на субресурс у мережі, доступній лише завдяки повноваженням її користувачів. Такі запити відкривають інтернет-доступ до приватних пристроїв і серверів, що збільшує ризик атак із підробкою міжсайтових запитів (CSRF) і/або витоку інформації. Щоб знизити ці ризики, Chrome ігнорує запити до приватних субресурсів, які надходять із небезпечного контексту, і почне блокувати їх."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Поле InterestGroups у структурі dailyUpdateUrl, призначене виклику joinAdInterestGroup(), було перейменовано на updateUrl, щоб точніше відображати його поведінку."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator не підтримується. Натомість використовуйте Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS не можна завантажити з URL-адрес file:, які не мають розширення файлу .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "У зв’язку зі зміною специфікацій скасування асинхронного вилучення діапазону елемента remove() через SourceBuffer.abort() не підтримується. У майбутньому цю функцію буде вилучено. Натомість вам слід керуватися подією updateend. abort() слід використовувати лише для того, щоб скасувати асинхронне додавання медіафайлів або скинути стан синтаксичного аналізатора."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "У зв’язку зі зміною специфікацій налаштування значення MediaSource.duration нижче за найвищу відображувану позначку часу будь-яких закодованих фреймів у буфері не підтримується. У майбутньому неявне вилучення стиснених медіафайлів із буфера не підтримуватиметься. Натомість застосовуйте явну функцію remove(newDuration, oldDuration) для всіх елементів sourceBuffers, де newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Навіть якщо в MIDIOptions не вказано значення SysEx, веб-сайти проситимуть дозвіл на використання MIDI."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification API більше не можна використовувати з незахищених джерел. Перенесіть свій додаток на захищене джерело, наприклад HTTPS. Докладніше читайте на сторінці https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Дозвіл на Notification API більше не можна запитувати з міждоменних елементів iframe. Запитайте дозвіл із фрейму верхнього рівня або відкрийте нове вікно."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Опція imageOrientation: 'none' у параметрі createImageBitmap не підтримується. Натомість використовуйте опцію '{imageOrientation: 'from-image'}'."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Ваш партнер використовує застарілу версію (D)TLS. Щоб вирішити проблему, зверніться до партнера."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Якщо вказати властивість overflow: visible у тегах img, video й canvas, візуальний контент може відображатися за межами цих елементів. Докладніше: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments не підтримується. Натомість скористайтесь актуальним API для обробників платежів."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Ваш виклик PaymentRequest обійшов директиву connect-src Правил щодо безпеки контенту (CSP). Такий обхід не підтримується. Додайте в директиву CSP connect-src ідентифікатор способу оплати з PaymentRequest API (у полі supportedMethods)."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent не підтримується. Натомість використовуйте стандартний метод navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Елемент <source src> із батьківським елементом <picture> недійсний і тому ігнорується. Натомість використовуйте <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Метод webkitCancelAnimationFrame залежить від постачальника. Натомість використовуйте стандартний метод cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Метод webkitRequestAnimationFrame залежить від постачальника. Натомість використовуйте стандартний метод requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Метод HTMLVideoElement.webkitDisplayingFullscreen не підтримується. Натомість використовуйте метод Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Метод HTMLVideoElement.webkitEnterFullScreen() не підтримується. Натомість використовуйте метод Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Метод HTMLVideoElement.webkitEnterFullscreen() не підтримується. Натомість використовуйте метод Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Метод HTMLVideoElement.webkitExitFullScreen() не підтримується. Натомість використовуйте метод Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Метод HTMLVideoElement.webkitExitFullscreen() не підтримується. Натомість використовуйте метод Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Метод HTMLVideoElement.webkitSupportsFullscreen не підтримується. Натомість використовуйте метод Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Ми припиняємо підтримку chrome.privacy.websites.privacySandboxEnabled API. Однак цей інтерфейс залишатиметься активним, щоб забезпечувати зворотну сумісність, доки не вийде версія M113. Натомість використовуйте chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled і chrome.privacy.websites.adMeasurementEnabled. Докладнішу інформацію наведено на сторінці https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Обмеження DtlsSrtpKeyAgreement вилучено. Ви вказали значення false для цього обмеження, що ми витлумачили як спробу застосувати вилучений метод \"SDES key negotiation\". Цю функцію вилучено; натомість використовуйте сервіс, що підтримує \"DTLS key negotiation\"."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Обмеження DtlsSrtpKeyAgreement вилучено. Ви вказали значення true, що не вплинуло на це обмеження, але можете вилучити його для більшої ясності."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Метод getStats() на основі зворотного виклику не підтримується, і його буде вилучено. Натомість використовуйте метод, що відповідає специфікаціям."}, "generated/Deprecation.ts | RangeExpand": {"message": "Метод Range.expand() не підтримується. Натомість використовуйте метод Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Запити субресурсів за URL-адресами, що містять вставлені облікові дані (наприклад, **********************/), блокуються."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Опція rtcpMuxPolicy не підтримується, її буде вилучено."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "Для SharedArrayBuffer вимагатиметься ізоляція від міждоменних джерел. Докладніше читайте на сторінці https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Виклик speechSynthesis.speak() без активації користувача не підтримується, і цю функцію буде вилучено."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Вивантаження блоків прослуховування подій не підтримується, і їх буде вилучено."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Для розширень потрібно ввімкнути ізоляцію від міждоменних джерел, щоб і надалі використовувати SharedArrayBuffer. Докладніше читайте на сторінці https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Атрибут GPUAdapter isFallbackAdapter більше не підтримується. Натомість використовуйте атрибут GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 не підтримується відповіддю JSON у XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхронний виклик XMLHttpRequest в основному потоці не підтримується, оскільки негативно впливає на взаємодію з кінцевим користувачем. Докладніше читайте на сторінці https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анімація"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Зміщення макета відбувається, коли елементи переміщуються без взаємодії з користувачем. [Дізнайтеся причини зміщення макета](https://web.dev/articles/optimize-cls), наприклад додавання або видалення елементів чи зміна шрифтів під час завантаження сторінки."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Запит на шрифт"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Вставлено iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Кластер зміщення макета ({PH1})"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Не вдалося виявити причини зміщення макета"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Немає зміщень макета"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Причини зміщення макета"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Основні причини зміщення макета"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Най<PERSON><PERSON><PERSON><PERSON><PERSON> кластер"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Кластер із найвищим показником зміщення макета"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL кешу"}, "models/trace/insights/Cache.ts | description": {"message": "Якщо зберігати кеш за тривалий період часу, сторінки можуть завантажуватися швидше під час повторних відвідувань. [Докладніше.](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Немає запитів із неефективними правилами кешування"}, "models/trace/insights/Cache.ts | others": {"message": "Ще {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Запит"}, "models/trace/insights/Cache.ts | title": {"message": "Використовуйте ефективні значення TTL кешу"}, "models/trace/insights/DOMSize.ts | description": {"message": "Через велике дерево DOM обчислення стилів і перекомпонування макетів може тривати довше, що впливає на адаптивність сторінки. Крім того, у такому разі використовується більше пам’яті. [Дізнайтесь, як уникнути надмірного розміру DOM.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "Елемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Більшість дочірніх елементів"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Глиб<PERSON>на моделі DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистика"}, "models/trace/insights/DOMSize.ts | title": {"message": "Оптимізуйте розмір DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Усього елементів"}, "models/trace/insights/DOMSize.ts | value": {"message": "Значення"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Перший запит мережі найважливіший.  Щоб зменшити його затримку, уникайте переспрямувань, забезпечте швидку відповідь сервера й увімкніть стиснення тексту."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Містить переспрямування ({PH1}, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер відповідає повільно ({PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Без стиснення"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Уникає переспрямувань"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер відповідає швидко ({PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Застосовано стиснення тексту"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Переспрямування"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Час відповіді сервера"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Затримка запиту на документ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Завантаження без стиснення"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Повторювані байти"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Видаліть великі копії модулів JavaScript із пакетів, щоб зменшити кількість непотрібних байтів під час мережевої активності."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Повторювані джерела JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Щоб текст завжди був видимим, змініть значення властивості [font-display](https://developer.chrome.com/blog/font-display) на swap або optional. Щоб зменшити зміщення макета, можна додатково оптимізувати swap за допомогою [перевизначення показників шрифту](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON>ри<PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Відображення шрифту"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Витрачений час"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(анонімно)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Багато API, які зазвичай читають геометрію макета, змушують систему відрисовки призупиняти виконання скрипту, щоб обчислити стиль і макет. Докладніше про [примусове перекомпонування](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) і способи його пом’якшення."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Трасування стека"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Примусове перекомпонування"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Виклик функції, що займає найбільше часу"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Загальний час перекомпонування"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[не визначено]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Зменшення часу завантаження зображень може прискорити візуалізацію сторінки й покращити показник LCP. [Докладніше про оптимізацію розміру зображень.](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (приблизний розмір – {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Немає зображень, які можна оптимізувати"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Оптимізація розміру файлу"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Ще {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Покращте показ зображень"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Збільште коефіцієнт стиснення зображення, щоб зменшити його розмір."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Якщо використовувати сучасний формат зображень (WebP, AVIF) або збільшити їх стиснення, розмір завантажуваного зображення може зменшитися."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Розмір файлу зображення ({PH1}) більший, ніж потрібно для показу ({PH2}). Щоб зменшити розмір завантаження, використовуйте адаптивні зображення."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Якщо використовувати відеоформати замість GIF, розмір завантаженого анімованого контенту може зменшитися."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Почніть аналіз із найдовшої фази. [Затримки можна зменшити.](https://web.dev/articles/optimize-inp#optimize_interactions) Щоб скоротити час обробки, [оптимізуйте витрати основного потоку](https://web.dev/articles/optimize-long-tasks), часто JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Тривалість"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Затримка відповіді"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Взаємодій не виявлено"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Ета<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Затримка перед відображенням відповіді"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Тривалість обробки"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Показник INP за етапами"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Щоб оптимізувати показник LCP, зробіть зображення LCP [видимим](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) у HTML-коді відразу й [не використовуйте відкладене завантаження](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Застосовано параметр fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Потрібно застосувати значення fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "відкладене завантаження не застосовується"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Зображення LCP завантажилося за {PH1} від першої початкової точки."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP не виявлено"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Не виявлено ресурсів LCP, оскільки LCP не є зображенням"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Запит можна знайти в початковому документі"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Виявлення запитів LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Для кожного етапу передбачено [конкретні стратегії оптимізації](https://web.dev/articles/optimize-lcp#lcp-breakdown). В ідеалі більшість часу LCP має витрачатися на завантаження ресурсів, не в межах затримок."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Тривалість"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Затримка відображення елемента"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75-й процентиль реальних користувачів"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP не виявлено"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Ета<PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Затримка завантаження ресурсу"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Тривалість завантаження ресурсу"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Показник LCP за етапами"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипт"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Заощаджені байти"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Поліфіли й перетворення дають вебпереглядачам старіших версій змогу використовувати нові функції JavaScript. Однак багато з них не потрібні для сучасних вебпереглядачів. Змініть процес складання JavaScript, щоб не транспілювати функції [базового набору](https://web.dev/articles/baseline-and-polyfills), якщо вам не потрібно підтримувати вебпереглядачі старіших версій. [Дізнайтеся, чому більшість сайтів можуть розгортати код ES6+ без транспілювання](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Застарілі функції JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "Протоколи HTTP/2 й HTTP/3 мають низку переваг над HTTP/1.1, наприклад мультиплексування. [Докладніше про використання сучасного протоколу HTTP.](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Не було запитів із використанням протоколу HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Запит"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Сучасний протокол HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Запит"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Очікуване заощадження LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Невикористане джерело для попереднього підключення. Переконайтеся, що атрибут crossorigin використовується правильно."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Щоб уникнути утворення ланцюжків критичних запитів](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains), зменште довжину ланцюжків і розмір завантажень або відкладіть завантаження непотрібних ресурсів. Це допоможе пришвидшити завантаження сторінки."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Додайте підказки [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) для найважливіших джерел (бажано використовувати щонайбільше 4)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Варіанти джерел для попереднього підключення"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Максимальна затримка критичного шляху:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Немає завдань відрисовки, на які впливають залежності мережі"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Немає додаткових джерел, для яких варто налаштувати попереднє підключення"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "немає попередньо підключених джерел"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Підказки [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) допомагають вебпереглядачу встановлювати з’єднання на початку завантаження сторінки, що заощаджує час під час першого запиту до відповідного джерела. Нижче наведено джерела, до яких сторінка підключатиметься заздалегідь."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Попередньо підключені джерела"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Дерево залежностей мережі"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Знайдено більше ніж 4 підключення preconnect. Такі засоби слід використовувати зрідка й лише для найважливіших джерел."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Невикористане джерело для попереднього підключення. Використовуйте підказки preconnect тільки для джерел, яким сторінка, імовірно, надсилатиме запити."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Щоб уникнути утворення ланцюжків критичних запитів, зменште довжину ланцюжків і розмір завантажень або відкладіть завантаження непотрібних ресурсів. Це допоможе пришвидшити завантаження сторінки."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Запити блокують початкове відображення сторінки, що може викликати затримку завантаження елементів LCP. За допомогою [відкладання або вставки](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) можна усунути ці запити мережі з критичного шляху."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Тривалість"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Немає запитів, які блокують відображення цієї навігації"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Запит"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Запити, які блокують відображення"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Якщо витрати на повторне обчислення стилю залишаються високими, їх можна зменшити за допомогою оптимізації селектора. [Оптимізуйте селектори](https://developer.chrome.com/docs/devtools/performance/selector-stats), які мають і тривалий час виконання, і високий відсоток повільних шляхів. Щоб зменшити витрати на зіставлення, використовуйте менше селекторів, обирайте їх простіші варіанти, а також надавайте перевагу компактнішій і менш розгалуженій архітектурі інтерфейсу DOM."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Мин<PERSON><PERSON>о часу"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Дані селектора CSS не знайдено. Увімкніть статистику селектора CSS у налаштуваннях панелі ефективності."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Кількість спроб"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Кількість збігів"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Витрати на селектор CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Найкращі селектори"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Усього"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Час основного ланцюжка"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Сторонній постачальник"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Розмір передавання"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Сторонній код може значно погіршити швидкість завантаження. [Зменште обсяг стороннього коду й відкладіть його завантаження](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), щоб надати пріоритет контенту сторінки."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Сторонніх розробників не знайдено"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Сторонн<PERSON>й код"}, "models/trace/insights/Viewport.ts | description": {"message": "Якщо область перегляду не оптимізовано для мобільних пристроїв, взаємодія за допомогою дотику може відбуватися із затримкою [до 300 мс](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Затримка натискання на мобільному пристрої"}, "models/trace/insights/Viewport.ts | title": {"message": "Оптимізуйте область перегляду для мобільних пристроїв"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Лише сторінки, завантажені через запити GET, підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Можна кешувати лише сторінки з кодом статусу 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Веб-переглядач Chrome виявив спробу виконання JavaScript для сторінки в кеші."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які надіслали запит на AppBanner, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Зворотний кеш вимкнено експериментальними параметрами. Щоб увімкнути його локально на цьому пристрої, перейдіть на сторінку chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Зворотний кеш вимкнено командним рядком."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Зворотний кеш вимкнено через недостатній обсяг пам’яті."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Зворотний кеш не підтримується делегатом."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Зворотний кеш вимкнено засобом попередньої обробки."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Сторінку не можна додати в кеш, оскільки вона містить екземпляр BroadcastChannel із зареєстрованими прослуховувачами."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Сторінки із заголовком cache-control:no-store не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кеш було навмисно очищено."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Сторінку вилучено з кешу, щоб можна було кешувати іншу сторінку."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Сторінки з плагінами наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Не визначено"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Сторінки, які використовують FileChooser API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Сторінки, які використовують File System Access API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Сторінки, які використовують диспетчер носія, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Медіапрогравач відтворював контент під час виходу."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Сторінки, які використовують MediaSession API та для яких вибрано статус відтворення, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Сторінки, які використовують MediaSession API та для яких налаштовано обробники дій, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Зворотний кеш вимкнено через програму зчитування з екрана."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які використовують SecurityHandler, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Сторінки, які використовують Serial API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Сторінки, які використовують WebAuthetication API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Сторінки, які використовують WebBluetooth API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Сторінки, які використовують WebUSB API, не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Зворотний кеш вимкнено, оскільки файли cookie не дозволено на сторінці, де використовується Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Стор<PERSON>нк<PERSON>, які використовують Dedicated Worker або Worklet, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Документ не було завантажено перед виходом."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Під час виходу відображався банер додатка."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Під час виходу відображався Менеджер паролів Chrome."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Під час виходу тривала дистиляція DOM."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Під час виходу працював засіб перегляду DOM Distiller."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Зворотний кеш вимкнено через розширення, що використовують Messaging API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Розширення з постійним з’єднанням мають розірвати його перед переходом у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Розширення з постійним з’єднанням намагалися надіслати повідомлення фреймам у зворотному кеші."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Зворотний кеш вимкнено через розширення."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Під час виходу на сторінці відображалося діалогове вікно (наприклад, для повторного надсилання форми чи стосовно пароля http)."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Під час виходу відображалася офлайн-сторінка."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Під час виходу відображалася панель втручання через нестачу пам’яті."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Під час виходу відображалися запити на дозволи."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Під час виходу працював блокувальник спливаючих вікон."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Під час виходу відображалася інформація від Безпечного перегляду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Безпечний перегляд визначив, що ця сторінка містить порушення, і заблокував спливаюче вікно."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Синтаксис Service Worker активовано, коли сторінка була у зворотному кеші."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Зворотний кеш вимкнено через помилку документа."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Стор<PERSON>нки, які використовують FencedFrames, не можна зберегти у зворотному кеші"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Сторінку вилучено з кешу, щоб можна було кешувати іншу сторінку."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Сторінки, які надали доступ до потоку медіа, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Сторінки з певними типами вбудованого контенту (наприклад, файлами PDF) зараз не підтримують зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які використовують IdleManager, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Сторінки з активованим з’єднанням IndexedDB наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Зворотний кеш вимкнено через подію IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Використано непридатні API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Сторінки, на які розширення вставили фрагменти JavaScript, зараз не підтримуються для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Стор<PERSON>нк<PERSON>, на які розширення вставили фрагмент StyleSheet, зараз не підтримуються для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Внутрішня помилка."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Зворотний кеш вимкнено, оскільки за певним запитом мережі JavaScript отримано ресурс із заголовком Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Зворотний кеш вимкнено через запит повідомлення keepalive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Сторінки, які використовують блокування клавіатури, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Сторінка не завершила завантаження перед тим, як ви вийшли з неї."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Сторінки, головний ресурс яких містить cache-control:no-store, не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Сторінки, головний ресурс яких містить cache-control:no-store, не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Перехід скасовано до того, як сторінку було відновлено зі зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Сторінку вилучено з кешу, оскільки активне з’єднання з мережею отримало забагато даних. У Chrome діють обмеження щодо обсягу даних, який кешована сторінка може отримувати."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Сторінки з надісланими запитами fetch() або XHR наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Сторінку вилучено зі зворотного кешу, оскільки активний запит мережі містив переспрямування."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Сторінку вилучено з кешу, оскільки з’єднання з мережею тривало занадто довго. У Chrome діють обмеження щодо тривалості часу, упродовж якого кешована сторінка може отримувати дані."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Сторінки, які не мають дійсного заголовка відповіді, не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Відбувся перехід не в головному фреймі."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Сторінки з активними індексованими трансакціями DB наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Сторінки з надісланими запитами мережі наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Сторінки з надісланими запитами мережі fetch наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Сторінки з надісланими запитами мережі наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Сторінки з надісланими запитами мережі XHR наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Сто<PERSON><PERSON><PERSON><PERSON><PERSON>, які використовують PaymentManager, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Сторінки, які використовують функцію \"Картинка в картинці\", наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Стор<PERSON>нк<PERSON>, на яких відображається інтерфейс друку, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Сторінку відкрито за допомогою \"window.open()\", а інша вкладка має посилання на неї, або сторінка відкрила вікно."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Відбувся збій під час процесу обробки для цієї сторінки у зворотному кеші."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Процес обробки для цієї сторінки у зворотному кеші завершився."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Сторінки, які надіслали запит на дозвіл записувати аудіо, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Стор<PERSON>нки, які надіслали запит на доступ до датчиків, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Сторінки, які надіслали запит на синхронізацію у фоновому режимі чи запит на отримання даних, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Сторінки, які надіслали запит на доступ до пристроїв MIDI, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Сторінки, які надіслали запит на доступ до сповіщень, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Сторінки, які надіслали запит на доступ до пам’яті, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Сторінки, які надіслали запит на дозвіл записувати відео, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "У кеш можна додавати лише сторінки, чиї схеми URL-адрес – це HTTP / HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Синтаксис Service Worker заявив права на цю сторінку, а її додано у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Синтаксис Service Worker намагався надіслати MessageEvent сторінці у зворотному кеші."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Реєстрацію ServiceWorker скасовано, коли сторінка була у зворотному кеші."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Сторінку вилучено зі зворотного кешу через активацію синтаксису Service Worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome перезапущено, записи зворотного кешу очищено."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують SharedWorker, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Стор<PERSON><PERSON><PERSON><PERSON>, які використовують SpeechRecognizer, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Сторінки, які використовують SpeechSynthesis, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Елемент iframe на сторінці розпочав перехід, який не було завершено."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Сторінки, субресурси яких містять cache-control:no-cache, не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Сторінки, субресурси яких містять cache-control:no-store, не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Ця сторінка перевищила максимальний час у зворотному кеші, її термін дії завершився."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Час очікування сторінки під час додавання у зворотний кеш минув (імовірно, через довготривалу роботу обробників приховування сторінки)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Сторінка містить обробник вивантаження в основному фреймі."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Сторінка містить обробник вивантаження в додатковому фреймі."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Веб-переглядач змінив заголовок перевизначення агента користувача."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Сторінки, які надали дозвіл на запис відео чи аудіо, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Сторінки, які використовують WebDatabase, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Сторінки, які використовують WebHID, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Сторінки, які використовують WebLocks, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Стор<PERSON>н<PERSON><PERSON>, які використовують WebNfc, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Стор<PERSON>нки, які використовують WebOTPService, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Сторінки з WebRTC не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Зворотний кеш вимкнено, оскільки використовується WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Сторінки, які використовують WebShare, наразі не підходять для зворотного кешу."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Сторінки з WebSocket не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Зворотний кеш вимкнено, оскільки використовується WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Сторінки з WebTransport не можна додавати у зворотний кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Зворотний кеш вимкнено, оскільки використовується WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Сторінки, які використовують WebXR, наразі не підходять для зворотного кешу."}}