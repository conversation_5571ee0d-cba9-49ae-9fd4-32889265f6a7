{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Овлашћење неће бити покривено симболом џокерског знака (*) у оквиру CORS управљања атрибутом Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Атрибут disableRemotePlayback треба да се користи да би се онемогућила подразумевана интеграција за пребацивање уместо коришћења бирача -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Вредност изгледа CSS-а slider-vertical није стандардизована и биће уклоњена."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Захтеви за ресурсе чији URL-ови су садржали уклоњене знакове за размак (\\(n|r|t)) и знакове „мање-од“ (<) су блокирани. Уклоните нове редове и кодирајте знакове „мање-од“ из извора као што су вредности атрибута елемената да би се учитали ти ресурси."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Атрибут chrome.loadTimes() је застарео, па користите стандардизовани API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Атрибут chrome.loadTimes() је застарео, па користите стандардизовани API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Атрибут chrome.loadTimes() је застарео, па користите стандардизовани API: nextHopProtocol за Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Колачићи који садрже \\(0|r|n) знак ће бити одбијени, а не скраћени."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Попуштање смерница за исто порекло подешавањем атрибута document.domain је застарело и биће подразумевано онемогућено. Ово упозорење о застаревању је за приступ различитог порекла који је омогућен подешавањем document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Покретање window.alert из iframe-ова различитог порекла је застарело и уклониће се у будућности."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Покретање window.confirm из iframe-ова различитог порекла је застарело и уклониће се у будућности."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Подршка за податке: URL-ови у SVGUseElement-у су застарели и биће уклоњени у будућности."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() иwatchPosition() више не раде са небезбедним пореклом. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() иwatchPosition() су застарели или небезбедни извори. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() више не ради са небезбедним пореклом. Да бисте користили ову функцију, размислите о пребацивању апликације на безбедно порекло као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Пронађена је ознака <h1> у оквиру чланка <article>, <aside>, <nav> или одељка <section> који нема наведену величину фонта. Величина текста овог наслова ће се ускоро мењати у овом прегледачу. Више информација потражите на https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "API RTCPeerConnectionIceErrorEvent.hostCandidate је застарео. Уместо њега користите RTCPeerConnectionIceErrorEvent.address или RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Овај формат за navigator.credentials.get() захтев за дигиталне акредитиве је застарео. Ажурирајте позив да бисте користили нови формат."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Порекло продавца и произвољни подаци из догађаја сервисeра canmakepayment су застарели и биће уклоњени: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Веб-сајт је затражио подизвор са мреже којој је могао да приступи само због привилеговане мрежне позиције својих корисника. Ови захтеви излажу уређаје и сервере који нису јавни интернету, чиме се повећава ризик од напада фалсификовањем захтева са других сајтова (CSRF) и/или цурења информација. Да би ублажио ове ризике, Chrome застарева захтеве ка подизворима који нису јавни када се покрену из небезбедног контекста и почеће да их блокира."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Поље dailyUpdateUrl из структуре InterestGroups које је пренето у структуру joinAdInterestGroup() преименовано је у updateUrl, што прецизније одражава његово понашање."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator је застарели тип. Уместо њега користите Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS не може да се учита са file: URL-ова ако се не завршавају екстензијом фајла .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Коришћење атрибута SourceBuffer.abort() да би се отказало уклањање асинхроног опсега за remove() је застарело због промене спецификације. Подршка ће се уклонити у будућности. Уместо њега треба да слушате догађај updateend. abort() има за циљ само да откаже додавање асинхроних медија или да ресетује стање рашчлањивача."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Подешавање атрибута MediaSource.duration испод највише временске ознаке презентације било којих баферованих кодираних оквира је застарело због промене спецификације. Подршка за имплицитно уклањање скраћеног баферованог медијског садржаја ће се уклонити у будућности. Уместо тога треба да извршите експлицитни remove(newDuration, oldDuration) на све sourceBuffers, где је newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI ће затражити дозволу за коришћење чак и ако SysEx није наведен у атрибуту MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "API за обавештења више не сме да се користи из незбезбедног порекла. Размислите о пребацивању апликације на безбедно порекло, као што је HTTPS. Погледајте https://goo.gle/chrome-insecure-origins за више детаља."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Дозвола за API за обавештења више не може да се тражи од iframe-а различитог порекла. Размислите о томе да затражите дозволу од оквира највишег нивоа или да отворите нови прозор."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Опција imageOrientation: 'none' у ставци createImageBitmap је застарела. Користите createImageBitmap са опцијом {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Ваш партнер користи застарелу (D)TLS верзију. Проверите са партнером да бисте исправили ово."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Ако наведете overflow: visible на ознакама img, video и canvas, оне могу да доведу до прављења визуелног садржаја ван граница елемента. Погледајте https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments је застарели API. Боље да користите једнократно инсталирање за обрађиваче плаћања."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest позив је заобишао директиву connect-src за смернице за безбедност садржаја (CSP). Ово заобилажење је застарело. Додајте идентификатор начина плаћања из API-ја PaymentRequest (у пољу supportedMethods) у директиву connect-src за CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "API StorageType.persistent је застарео. Користите стандардизовани navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Атрибут <source src> са надређеним елементом <picture> је неважећи, па се игнорише. Уместо њега користите <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame зависи од продавца. Уместо њега користите стандардни cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame зависи од продавца. Уместо њега користите стандардни requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen је застарео. Уместо тога користите Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() је застарео. Уместо тога користите Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() је застарео. Уместо тога користите Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() је застарео. Уместо тога користите Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() је застарео. Уместо тога користите Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen је застарео. Уместо тога користите Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Обустављамо API chrome.privacy.websites.privacySandboxEnabled, али ће остати активан за компатибилност уназад до издања M113. Уместо тога, користите chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled и chrome.privacy.websites.adMeasurementEnabled. Погледајте https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ограничење DtlsSrtpKeyAgreement је уклоњено. Навели сте вредност false за ово ограничење, што се тумачи као покушај коришћења уклоњеног метода SDES key negotiation. Ова функција је уклоњена, па користите услугу која подржава DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ограничење DtlsSrtpKeyAgreement је уклоњено. Навели сте вредност true за ово ограничење, што није имало ефекта, али можете да уклоните ово ограничење ради прегледности."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() заснован на повратном позиву је застарео и биће уклоњен. Уместо тога користите getStats() који је у складу са спецификацијама."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() је застарео. Уместо њега користите Select.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Захтеви за подресурсе чији URL-ови садрже уграђене акредитиве (нпр. **********************/) су блокирани."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Опција rtcpMuxPolicy је застарела и биће уклоњена."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer ће захтевати изолацију од приступа из других извора. Погледајте https://developer.chrome.com/blog/enabling-shared-array-buffer/ за више детаља."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Атрибут speechSynthesis.speak() без активације корисника је застарео и уклониће се."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Ослушкивачи догађаја уклањања су застарели и биће уклоњени."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Додаци треба да омогуће изолацију од приступа из других извора да бисте и даље користили SharedArrayBuffer. Погледајте https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Атрибут GPUAdapter isFallbackAdapter је застарео, па користите атрибут GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON за одговор не подржава UTF-16 у атрибуту XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхрони XMLHttpRequest у главној нити је застарео због негативног утицаја на доживљај крајњег корисника. Додатну помоћ потражите на https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимација"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "До промена распореда долази када се елементи померају без икакве интеракције корисника. [Истражите узроке промена распореда](https://web.dev/articles/optimize-cls), као што су додавање и уклањање елемената или промена њихових фонтова док се страница учитава."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Захтев за фонт"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Уметнути iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Скуп промена распореда: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Није откривен ниједан узрок промене распореда"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Нема промена распореда"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Узроци промене распореда"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Најчешћи узроци промене распореда"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Елемент слике без величине"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Најгори скуп"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Скуп са најлошијим променама распореда"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Време преживљавања кеша"}, "models/trace/insights/Cache.ts | description": {"message": "Дуго трајање кеша може да убрза поновне посете страници. [Сазнајте више](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Нема захтева са неефикасним смерницама за кеширање"}, "models/trace/insights/Cache.ts | others": {"message": "јо<PERSON> {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Користите ефикасна трајања кеширања"}, "models/trace/insights/DOMSize.ts | description": {"message": "Велики DOM може да повећа трајање израчунавања стилова и преобликовања изгледа, што утиче на прилагодљивост странице. Велики DOM ће такође повећати коришћење меморије. [Сазнајте како да избегнете прекомерну величину DOM-а](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Елемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Највише подређених елемената"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM дубина"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистика"}, "models/trace/insights/DOMSize.ts | title": {"message": "Оптимизујте величину DOM-а"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Укупан број елемената"}, "models/trace/insights/DOMSize.ts | value": {"message": "Вредност"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Први захтев за мрежу је најважнији.  Смањите кашњење избегавањем преусмеравања, обезбеђивањем брзог одговора сервера и омогућавањем компресије текста."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Било је преусмеравања ({PH1} преусмеравања, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер је одговорио споро (уочено: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Није примењено компримовање"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Избегава преусмеравања"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер одговара брзо (уочено {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Примењује се компресија текста"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Преусмеравања"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Време одзива сервера"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Кашњење захтева за документ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Некомпримовано преузимање"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Дуплирани бајтови"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Извор"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Уклоните велике, дуплиране JavaScript модуле из пакета да бисте смањили непотребну потрошњу података током мрежних активности."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Дупликат JavaScript-а"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Размислите о томе да подесите [font-display](https://developer.chrome.com/blog/font-display) на swap или optional како бисте се уверили да је текст увек видљив. swap може да се додатно оптимизује како би се ублажиле промене изгледа помоћу [замена показатеља фонта](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Фонт"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Приказ фонта"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Протраћено време"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(анонимно)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Многи API-ји, који обично читају геометрију распореда, приморавају механизам за рендеровање да паузира извршавање скрипте да би израчунали стил и распоред. Сазнајте више о [принудном преобликовању](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) и његовом ублажавању."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Ста<PERSON>е стека"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Принудно преобликовање"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Најчешћи позив функције"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Укупно време преобликовања"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[неприписано]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Смањивање времена преузимања слика може да побољша видљиву брзину учитавања странице и LCP. [Сазнајте више о оптимизовању величине слике](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (проц. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Нема слика које могу да се оптимизују"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Оптимизујте величину фајла"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "јо<PERSON> {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Побољшајте испоруку слика"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Повећање фактора компресије слике може да побољша величину ове преузете слике."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Коришћење модерног формата слике (WebP, AVIF) или повећање компресије слике може да побољша величину ове преузете слике."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Овај фајл слике је већи него што треба да буде ({PH1}) за приказане димензије ({PH2}). Користите прилагодљиве слике да бисте смањили величину слике за преузимање."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Ако користите видео формате уместо GIF-ова, можете да побољшате величину преузимања анимираног садржаја."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Почните да истражујете са најдужом фазом. [Кашњења могу да се умање](https://web.dev/articles/optimize-inp#optimize_interactions). Да бисте смањили трајање обраде, [оптимизујте трошкове главне нити](https://web.dev/articles/optimize-long-tasks), често JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Траја<PERSON>е"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Кашњење уноса"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Није откривена ниједна интеракција"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Фаза"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Кашњење презентације"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Трајање обраде"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP по фази"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Оптимизујте LCP тако што ћете [омогућити откривање](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) LCP слике из HTML-а одмах и [избегавати спора учитавања](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Примењено је fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Треба да се примени fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "споро учитавање није примењено"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Слика LCP-а је учитана {PH1} после најраније почетне тачке."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Није откривен LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP ресурс није откривен јер LCP није слика"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Захтев је видљив у почетном документу"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Откривање LCP захтева"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Свака [фаза има специфичне стратегије побољшања](https://web.dev/articles/optimize-lcp#lcp-breakdown). Идеално би било да се већина времена LCP-а потроши на учитавање ресурса, а не на кашњења."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Траја<PERSON>е"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Кашњење приказивања елемента"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Поље p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Није откривен LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Фаза"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Кашњење учитавања ресурса"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Трајање учитавања ресурса"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP према фази"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипта"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Неискоришћени бајтови"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Полифили и трансформације омогућавају да старије верзије прегледача користе нове функције JavaScript-а. Међутим, многи нису неопходни за модерне прегледаче. Размислите о томе да модификујете процес прављења JavaScript-а тако да не транспајлира [референтни](https://web.dev/articles/baseline-and-polyfills) скуп функција, осим ако знате да мора да подржава старије прегледаче. [Сазнајте зашто већина сајтова може да користи ES6+ кôд без транспајлирања](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Стара верзија JavaScript-а"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 и HTTP/3 имају бројне предности у односу на HTTP/1.1, као што је мултиплексовање. [Сазнајте више о коришћењу модерног HTTP-а](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Нема захтева који користе HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Модеран HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Извор"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Извор"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Време"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Процењена уштеда LCP-а"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Некоришћено повезивање унапред. Проверите да ли је атрибут crossorigin правилно употребљен."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Избегавајте прављење ланаца критичних захтева](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) тако што ћете смањити дужину ланаца, смањити величину преузимања за ресурсе или одложити преузимање ресурса који нису неопходни ради бржег учитавања странице."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Додајте савете за [повезивање унапред](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) са најважнијим изворима, али пробајте да користите највише 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Кандидати за повезивање унапред"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Максимално кашњење критичне путање:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Зависности мреже нису утицале на задатке рендеровања"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Ниједан додатни извор није добар кандидат за повезивање унапред"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "Ниједан извор није унапред повезан"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Савети за [повезивање унапред](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) помажу прегледачу да успостави везу раније током учитавања странице, што штеди време када се упути први захтев за тај извор. У наставку су наведени извори са којима је страница повезана унапред."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Унапред повезани извори"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Стабло мрежне зависности"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Пронађено је више од 4 повезивања preconnect. Она треба да се користе ретко и само до најважнијих извора."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Некоришћено повезивање унапред. Користите preconnect само за изворе које ће страница вероватно захтевати."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Избегавајте прављење ланаца критичних захтева тако што ћете смањити дужину ланаца, смањити величину преузимања за ресурсе или одложити преузимање ресурса који нису неопходни ради бржег учитавања странице."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Захтеви блокирају први приказ странице, што може да одложи LCP. [Одлагање или уграђивање](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) може да премести ове мрежне захтеве са критичне путање."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Траја<PERSON>е"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Нема захтева за блокирање рендеровања за ову навигацију"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Захтеви за блокирање рендеровања"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ако трошкови поновног израчунавања стила остану високи, оптимизација бирача може да их смањи. [Оптимизујте бираче](https://developer.chrome.com/docs/devtools/performance/selector-stats) са великим протеклим временом и великим % споре путањe. Једноставни бирачи, мање бирача, мањи, као и мање дубок DOM садржај; све то ће смањити трошкове подударања."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Протекло време"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Није пронађен ниједан податак CSS бирача. Статистика CSS бирача мора да буде омогућена у подешавањима окна за учинак."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Покушаји подударања"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Број подударања"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Трошкови CSS бирача"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Најважнији бирачи"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Укупно"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Време главне нити"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Трећа страна"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Величина преноса"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Кôд независног добављача може значајно да утиче на учинак учитавања. [Смањите и одложите учитавање кода независног добављача](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) да бисте утврдили приоритете садржаја странице."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Није пронађена ниједна трећа страна"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Треће стране"}, "models/trace/insights/Viewport.ts | description": {"message": "Интеракције додиром могу да буду [одложене за највише 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ако област приказа није оптимизована за мобилне уређаје."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Кашњење при додиру на мобилном уређају"}, "models/trace/insights/Viewport.ts | title": {"message": "Оптимизујте област приказа за мобилне уређаје"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Само странице учитане преко GET захтева испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Само странице са кодом статуса 2XX могу да се кеширају."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome је открио покушај извршавања JavaScript-а док је био у кешу."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Странице које су захтевале AppBanner тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Ознаке су онемогућиле кеширање целе странице. Посетите chrome://flags/#back-forward-cache да бисте га омогућили локално на уређају."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Командна линија је онемогућила кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Кеширање целе странице је онемогућено због недовољно меморије."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Делегат не подржава кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Функција за приказивање унапред је онемогућила кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Ова страница не може да се кешира јер има инстанцу BroadcastChannel са регистрованим слушаоцима."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Странице са заглављем cache-control:no-store не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кеш је намерно обрисан."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Страница је уклоњена из кеша да би се дозволило кеширање друге странице."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Странице које имају додатне компоненте тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Недефинисано"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Странице које користе FileChooser API тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Странице које користе API за приступ систему фајлова тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Странице које користе диспечер за медијски уређај не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Репродукција из медија плејера је била у току при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Странице које користе MediaSession API и подешавају статус репродукције не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Странице које користе MediaSession API и подешавају обрађиваче радњи не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Чит<PERSON>ч екрана је онемогућио кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Странице које користе SecurityHandler тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Странице које користе Serial API тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Странице које користе WebAuthetication API тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Странице које користе WebBluetooth API тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Странице које користе WebUSB API тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Кеширање целе странице је онемогућено зато што су колачићи онемогућени на страници која користи Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Странице које користе предвиђени обрађивач или радни задатак тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Документ није довршио учитавање пре напуштања документа."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "App Banner је био активан при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome менаџер лозинки је био активан при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM дестилација је била у току при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer је био активан при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Кеширање целе странице је онемогућено јер су додаци користили API за размену порука."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Додаци са трајном везом треба да затворе везу пре кеширања целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Додаци са трајном везом су покушали да шаљу поруке оквирима у кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Кеширање целе странице је онемогућено због додатака."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Модални дијалог као што је поновно слање обрасца или дијалог за HTTP лозинку је приказан при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Оф<PERSON><PERSON><PERSON>н страница је приказана при напуштању."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Трака за интервенцију у вези са недостатком меморије је била присутна при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Дошло је до слања захтева за дозволе при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Откривен је блокатор искачућих прозора при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Приказани су детаљи о Безбедном прегледању при напуштању странице."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Безбедно прегледање је октрило да ова страница садржи злоупотребу и блокирало је искачући прозор."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Сервисер је активиран док је страница била у процесу кеширања целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Кеширање целе странице је онемогућено због грешке у документу"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Странице које користе FencedFrames не могу да се складиште у кешу целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Страница је уклоњена из кеша да би се дозволило кеширање друге странице."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Странице које су одобриле приступ за стримовање медија тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Странице које имају одређене врсте уграђеног садржаја (нпр. PDF-ови) тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Странице које користе IdleManager тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Странице које имају отворену IndexedDB везу тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Кеширање целе странице је онемогућено због IndexedDB догађаја."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Користе се API-ји који не испуњавају услове."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Странице на којима се JavaScript умеће помоћу додатака тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Странице на којима се StyleSheet умеће помоћу додатака тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Интерна грешка."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Кеширање целе странице је онемогућено јер је неки захтев за JavaScript мрежу примио ресурс са заглављем Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Кеширање целе странице је онемогућено због захтева за одржавање линка."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Странице које користе закључавање тастатуре тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Страница није довршила учитавање пре напуштања странице."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Странице чији главни ресурс има cache-control:no-cache не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Странице чији главни ресурс има cache-control:no-store не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Кретање је отказано пре него што је страница могла да буде враћена из кеша целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Страница је уклоњена из кеша јер је активна мрежна веза примила превише података. Chrome ограничава количину података коју страница може да прими док је кеширана."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Странице које имају преузимање() или XHR у току не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Страница је уклоњена из кеширања целе странице јер је активан мрежни захтев обухватао преусмеравање."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Страница је уклоњена из кеша јер је мрежна веза била предуго отворена. Chrome ограничава време које страница има за примање података док је кеширана."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Странице које немају исправно заглавље одговора не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Кретање се десило у оквиру који није главни оквир."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Странице са активним индексираним DB трансакцијама тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Странице са активним захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Странице са активним захтевом за преузимање мреже тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Странице са активним захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Странице са активним XHR захтевом за мрежу тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Странице које користе PaymentManager тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Странице које користе функцију Слика у слици тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Странице које приказују кориснички интерфејс за штампање тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Страница је отворена помоћу метода window.open(), а друга картица садржи референцу на њу или је страница отворила прозор."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Отказао је процес рендеровања за страницу у кешу целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Процес рендеровања за страницу у кешу целе странице је прекинут."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Странице које захтевају дозволе за снимање аудио садржаја тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Странице које захтевају дозволе за сензоре тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Странице које захтевају синхронизацију у позадини или дозволе за преузимање тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Странице које захтевају дозволе за MIDI тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Странице које захтевају дозволе за обавештења тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Странице које захтевају приступ меморијском простору тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Странице које захтевају дозволе за снимање видеа тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Само странице чија шему URL-а је HTTP или HTTPS могу да се кеширају."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Страницу је преузео сервисер док је кеширање целе странице у току."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Сервисер је покушао да пошаље страницу која је у процесу кеширања целе странице атрибуту MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Регистрација за ServiceWorker је опозвана док је било у току кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Страница је уклоњена из кеширања целе странице због активације сервисера."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome је рестартовао и обрисао уносе кеширања целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Странице које користе SharedWorker тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Странице које користе SpeechRecognizer тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Странице које користе SpeechSynthesis тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe на странице је започео кретање које се није завршило."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Странице чији подресурс има cache-control:no-cache не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Странице чији подресурс има cache-control:no-store не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Страница је премашила максимално време за кеширање целе странице и истекла је."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Истекло је време да страница приступи кеширању целе странице (вероватно због обрађивача сакривања странице који су дуго били покренути)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Страница има unload обрађивач у главном оквиру."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Страница има unload обрађивач у подоквиру."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Прегледач је променио заглавље замене корисничког агента."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Странице које су одобриле приступ за снимање видео или аудио садржаја тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Странице које користе WebDatabase тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Странице које користе WebHID тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Странице које користе WebLocks тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Странице које користе WebNfc тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Странице које користе WebOTPService тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Странице са WebRTC-ом не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Кеширање целе странице је онемогућено зато што је коришћен WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Странице које користе WebShare тренутно не испуњавају услове за кеширање целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Кеширање целих страница са WebSocket-ом није могуће."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Кеширање целе странице је онемогућено јер је коришћен WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Странице са WebTransport-ом не могу да приступе кеширању целе странице."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Кеширање целе странице је онемогућено јер је коришћен WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Странице које користе WebXR тренутно не испуњавају услове за кеширање целе странице."}}