{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers હૅન્ડલ કરવામાં વાઇલ્ડ કાર્ડ પ્રતીક (*) દ્વારા અધિકરણ કવર કરવામાં આવશે નહીં."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ડિફૉલ્ટ કાસ્ટ એકીકરણના વિકલ્પને બંધ કરવા માટે -internal-media-controls-overlay-cast-button પસંદગીકર્તાને બદલે disableRemotePlayback વિશેષતાનો ઉપયોગ કરવો જોઈએ."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSSના દેખાવના મૂલ્ય slider-verticalને સ્ટૅન્ડર્ડ અનુસાર કરવામાં આવ્યું નથી અને તેને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "જેમના URLsમાં કાઢી નાખવામાં આવેલા વ્હાઇટસ્પેસ \\(n|r|t) અક્ષર અને 'આના કરતાં ઓછા'નું ચિહ્ન (<) એમ બન્ને શામેલ હોય, એવા સંસાધનોની વિનંતીઓને બ્લૉક કરવામાં આવી છે. આ સંસાધનોને લોડ કરવા માટે કૃપા કરીને વિશેષતા ધરાવતા ઘટકના મૂલ્યો જેવા સ્થાનોમાંથી નવી લાઇન કાઢી નાખો અને 'આના કરતાં ઓછા'ના ચિહ્નને એન્કોડ કરો."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes()નો ઉપયોગ ટાળવામાં આવ્યો છે, તેને બદલે પ્રમાણિત API: નૅવિગેશન ટાઇમિંગ 2નો ઉપયોગ કરો."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() ટાળવામાં આવ્યો છે, તેને બદલે પ્રમાણિત API: પેઇન્ટ ટાઇમિંગનો ઉપયોગ કરો."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes()નો ઉપયોગ ટાળવામાં આવ્યો છે, તેને બદલે નૅવિગેશન ટાઇમિંગ 2માં પ્રમાણિત API: nextHopProtocolનો ઉપયોગ કરો."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) અક્ષરો ધરાવતી કુકીને ટૂંકી કરવાને બદલે કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain સેટ કરીને ક્રોસ-ઑરિજિન ધરાવતી પૉલિસી શિથિલ કરવાની પ્રક્રિયા ટાળવામાં આવી છે અને તેને ડિફૉલ્ટ તરીકે બંધ કરવામાં આવશે. ટાળવા સંબંધિત આ ચેતવણી document.domain સેટ કરીને ચાલુ કરવામાં આવેલા ક્રોસ-ઑરિજિન ઍક્સેસ માટે છે."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "ક્રોસ ઑરિજિન iframesમાંથી window.alertને ટ્રિગર કરવાનું ટાળવામાં આવ્યું છે અને ભવિષ્યમાં તેને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "ક્રોસ ઑરિજિન iframesમાંથી window.confirmને ટ્રિગર કરવાનું ટાળવામાં આવ્યું છે અને ભવિષ્યમાં તેને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ડેટા માટે સપોર્ટ: SVGUseElementમાં URLsને ટાળવામાં આવ્યા છે અને ભવિષ્યમાં તેમને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "હવે getCurrentPosition() અને watchPosition() અસુરક્ષિત ઑરિજિન પર કામ કરતા નથી. આ સુવિધાનો ઉપયોગ કરવા માટે તમારે તમારી ઍપ્લિકેશનને HTTPS જેવા સુરક્ષિત ઑરિજિન પર સ્વિચ કરવાનું વિચારવું જોઈએ. વધુ વિગતો માટે https://goo.gle/chrome-insecure-origins જુઓ."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "અસુરક્ષિત ઑરિજિન પર getCurrentPosition() અને watchPosition()ની સુવિધા ટાળવામાં આવી છે. આ સુવિધાનો ઉપયોગ કરવા માટે તમારે તમારી ઍપ્લિકેશનને HTTPS જેવા સુરક્ષિત ઑરિજિન પર સ્વિચ કરવાનું વિચારવું જોઈએ. વધુ વિગતો માટે https://goo.gle/chrome-insecure-origins જુઓ."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "હવે getUserMedia() અસુરક્ષિત ઑરિજિન પર કામ કરતું નથી. આ સુવિધાનો ઉપયોગ કરવા માટે તમારે તમારી ઍપ્લિકેશનને HTTPS જેવા સુરક્ષિત ઑરિજિન પર સ્વિચ કરવાનું વિચારવું જોઈએ. વધુ વિગતો માટે https://goo.gle/chrome-insecure-origins જુઓ."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> અથવા <section>ની અંદર એક <h1> ટૅગ મળ્યો જેના ફૉન્ટનું કોઈ સ્પષ્ટ કરેલું કદ નથી. નજીકના ભવિષ્યમાં આ બ્રાઉઝરમાં આ હેડિંગ ટેક્સ્ટનું કદ બદલાશે. વધુ માહિતી માટે https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 જુઓ."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidateનો ઉપયોગ ટાળવામાં આવ્યો છે. તેને બદલે કૃપા કરીને RTCPeerConnectionIceErrorEvent.address અથવા RTCPeerConnectionIceErrorEvent.portનો ઉપયોગ કરો."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ડિજિટલ ઓળખપત્રો માટે navigator.credentials.get() વિનંતી માટેનું આ ફૉર્મેટ કાયમ માટે બંધ કરવામાં આવ્યું છે, કૃપા કરીને નવા ફૉર્મેટનો ઉપયોગ કરવા માટે તમારા કૉલને અપડેટ કરો."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment સર્વિસ વર્કરની ઇવેન્ટમાંથી લેવામાં આવેલો વેપારી ઑરિજિન અને આર્બિટ્રરી ડેટા ટાળવામાં આવ્યો છે અને તેને કાઢી નાખવામાં આવશે: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "વેબસાઇટ દ્વારા તેના વપરાશકર્તાઓના નેટવર્કની વિશિષ્ટ સ્થિતિને કારણે તે ઍક્સેસ કરી શકે માત્ર એવા જ નેટવર્કમાંથી પેટા સંસાધનની વિનંતી કરવામાં આવી છે. આવી વિનંતીઓ ઇન્ટરનેટ પર બિન સાર્વજનિક ડિવાઇસ અને સર્વરની માહિતી જાહેર કરતી હોવાથી બનાવટી ક્રૉસ-સાઇટ વિનંતીનો હુમલો (CSRF) અને/અથવા માહિતી લીક થવાનું જોખમ વધી જાય છે. આવા જોખમોને ઘટાડવા માટે, જ્યારે અસુરક્ષિત સંદર્ભોમાંથી બિન સાર્વજનિક પેટા સંસાધનોને વિનંતીઓ કરવામાં આવે ત્યારે Chrome તેમને ટાળે છે અને તેમને બ્લૉક કરવાનું શરૂ કરી દેશે."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroupsનું dailyUpdateUrl ફીલ્ડ જે joinAdInterestGroup()ને પાસ કરવામાં આવ્યું હતું, તેનું નામ બદલીને updateUrl રાખવામાં આવ્યું છે, જેથી તેની વર્તણૂક વધુ સચોટ રીતે દર્શાવી શકાય."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIteratorનો ઉપયોગ કાયમ માટે બંધ કરવામાં આવ્યો છે. તેને બદલે કૃપા કરીને Intl.Segmenterનો ઉપયોગ કરો."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "file: URLsમાંથી CSSને ત્યાં સુધી લોડ કરી શકાતી નથી કે જ્યાં સુધી તેની સમાપ્તિમાં .css ફાઇલના એક્સ્ટેન્શન ન હોય."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "સ્પષ્ટીકરણમાં ફેરફારને કારણે remove()ની અસિંક્રોનસ રેંજ કાઢી નાખવાનું રદ કરવા માટે SourceBuffer.abort()નો ઉપયોગ ટાળવામાં આવ્યો છે. ભવિષ્યમાં સપોર્ટ કાઢી નાખવામાં આવશે. તમારે તેને બદલે updateend ઇવેન્ટ સાંભળવી જોઈએ. abort()નો હેતુ માત્ર અસિંક્રોનસ મીડિયાનું જોડાણ રદ કરવા માટે અથવા વિશ્લેષકની સ્થિતિ રીસેટ કરવા માટે છે."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "સ્પષ્ટીકરણમાં ફેરફારને કારણે બફર કરેલી કોઈપણ કોડેડ ફ્રેમમાં સૌથી વધુ પ્રસ્તુતિવાળા ટાઇમસ્ટેમ્પની નીચે MediaSource.durationને સેટ કરવાનું ટાળવામાં આવ્યું છે. ટૂંકા કરવામાં આવેલા બફર મીડિયાને જાણ કર્યા વિના કાઢી નાખવા માટેનો સપોર્ટ ભવિષ્યમાં કાઢી નાખવામાં આવશે. તેને બદલે જ્યાં newDuration < oldDuration હોય ત્યાં તમારે બધા sourceBuffers પર સ્પષ્ટ રીતે remove(newDuration, oldDuration) લાગુ કરવું જોઈએ."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptionsમાં જો સિસેક્સનો ઉલ્લેખ કરવામાં આવ્યો ન હોય, તો પણ તેનો ઉપયોગ કરવા માટે Web MIDI પરવાનગી માગશે."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "હવે Notification APIનો ઉપયોગ અસુરક્ષિત ઑરિજિનમાંથી કરી શકાશે નહીં. તમારે તમારી ઍપ્લિકેશનને HTTPS જેવા સુરક્ષિત ઑરિજિન પર સ્વિચ કરવાનું વિચારવું જોઈએ. વધુ વિગતો માટે https://goo.gle/chrome-insecure-origins જુઓ."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "હવે ક્રોસ-ઑરિજિન iframeમાંથી Notification API માટે પરવાનગીની વિનંતી કરી શકાશે નહીં. તેને બદલે તમારે ટોચના લેવલની વિનંતીની ફ્રેમમાંથી અથવા નવી વિન્ડો ખોલીને પરવાનગીની વિનંતી કરવાનો વિચાર કરવો જોઈએ."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmapમાં imageOrientation: 'none' વિકલ્પ કાયમ માટે બંધ કરવામાં આવ્યો છે. તેને બદલે કૃપા કરીને '{imageOrientation: 'from-image'}' વિકલ્પની સાથે createImageBitmapનો ઉપયોગ કરો."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "તમારા ભાગીદાર જૂના (D)TLS વર્ઝનનો ઉપયોગ કરી રહ્યાં છે. કૃપા કરીને ચેક કરો કે તમારા ભાગીદાર આમાં સુધારો કરી લે."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video અને canvas ટૅગ પર overflow: visible સ્પષ્ટ કરવાથી, તેઓ ઘટકની સીમાની બહાર વિઝ્યુઅલ કન્ટેન્ટ બનાવે તેમ બની શકે છે. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md જુઓ."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instrumentsની સુવિધા બંધ કરવામાં આવી છે. કૃપા કરીને વિકલ્પ તરીકે પેમેન્ટ હૅન્ડલર માટે તાત્કાલિક (JIT) ઇન્સ્ટૉલનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "તમારા PaymentRequest કૉલ થકી કન્ટેન્ટ-સિક્યુરિટી-પૉલિસી (CSP) connect-src નિર્દેશ બાયપાસ થયો. આ બાયપાસ ટાળવામાં આવ્યો છે. કૃપા કરીને PaymentRequest APIમાંથી (supportedMethods ફીલ્ડમાં) તમારા CSP connect-src નિર્દેશમાં ચુકવણી પદ્ધતિ ઓળખકર્તા ઉમેરો."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistentનો ઉપયોગ ટાળવામાં આવ્યો છે. તેને બદલે કૃપા કરીને પ્રમાણિત navigator.storageનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> પેરેન્ટ ધરાવતો <source src> અમાન્ય છે અને તેથી તેને અવગણવામાં આવ્યો છે. તેને બદલે કૃપા કરીને <source srcset>નો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame વિક્રેતાલક્ષી છે. તેને બદલે, કૃપા કરીને સ્ટૅન્ડર્ડ cancelAnimationFrameનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame વિક્રેતાલક્ષી છે. તેને બદલે, કૃપા કરીને સ્ટૅન્ડર્ડ requestAnimationFrameનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ટાળવામાં આવ્યું છે. તેને બદલે કૃપા કરીને Document.fullscreenElementનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ટાળવામાં આવ્યું છે. તેને બદલે, કૃપા કરીને Element.requestFullscreen() insteadનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ટાળવામાં આવ્યું છે. તેને બદલે, કૃપા કરીને Element.requestFullscreen() insteadનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ટાળવામાં આવ્યું છે. તેને બદલે, કૃપા કરીને Document.exitFullscreen()નો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ટાળવામાં આવ્યું છે. તેને બદલે, કૃપા કરીને Document.exitFullscreen()નો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ટાળવામાં આવ્યું છે. તેને બદલે કૃપા કરીને Document.fullscreenEnabledનો ઉપયોગ કરો."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "અમે API chrome.privacy.websites.privacySandboxEnabledને ટાળી રહ્યાં છીએ, જો કે તે M113 રિલીઝ થાય ત્યાં સુધી બૅકવર્ડ સુસંગતતા માટે સક્રિય રહેશે. તેને બદલે, કૃપા કરીને chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled અને chrome.privacy.websites.adMeasurementEnabledનો ઉપયોગ કરો. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled જુઓ."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement મર્યાદા કાઢી નાખવામાં આવી છે. આ મર્યાદા માટે તમે false મૂલ્યનો ઉલ્લેખ કર્યો છે, જેનો અર્થ એવો લેવામાં આવ્યો છે કે કાઢી નાખવામાં આવેલી SDES key negotiation પદ્ધતિનો ઉપયોગ કરવાનો પ્રયાસ થયો છે. આ સુવિધા કાઢી નાખવામાં આવી છે; તેને બદલે DTLS key negotiationને સપોર્ટ કરતી સેવાનો ઉપયોગ કરો."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement મર્યાદા કાઢી નાખવામાં આવી છે. આ મર્યાદા માટે તમે true મૂલ્યનો ઉલ્લેખ કર્યો છે, તેની આમ તો કોઈ અસર થતી નથી, પરંતુ વ્યવસ્થિત રાખવા માટે તમે આ મર્યાદા કાઢી નાખી શકો છો."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "કૉલબૅક આધારિત getStats() ટાળવામાં આવ્યું છે અને તેને કાઢી નાખવામાં આવશે. તેને બદલે spec-compliant getStats()નો ઉપયોગ કરો."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ટાળવામાં આવ્યું છે. તેને બદલે કૃપા કરીને Selection.modify()નો ઉપયોગ કરો."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "જેમના URLsમાં લૉગ ઇન વિગતો (દા.ત. **********************/) શામેલ કરવામાં આવી હોય, એવા પેટા સંસાધનની વિનંતીઓને બ્લૉક કરવામાં આવી છે."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicyનો વિકલ્પ ટાળવામાં આવ્યો છે અને તેને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer માટે ક્રોસ ઑરિજિન આઇસોલેશન જરૂરી રહેશે. વધુ વિગતો માટે https://developer.chrome.com/blog/enabling-shared-array-buffer/ જુઓ."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "વપરાશકર્તા દ્વારા સક્રિય કર્યા વિના speechSynthesis.speak()નો ઉપયોગ ટાળવામાં આવ્યો છે અને તેને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | UnloadHandler": {"message": "અનલોડ ઇવેન્ટના લિન્સરને કાયમ માટે બંધ કરવામાં આવ્યા છે અને તેમને કાઢી નાખવામાં આવશે."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBufferનો ઉપયોગ કરવાનું ચાલુ રાખવા માટે એક્સ્ટેન્શનને ક્રોસ-ઑરિજિન આઇસોલેશનમાં જઈને પસંદ કરવું જોઈએ. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ જુઓ."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter વિશેષતા કાયમ માટે બંધ કરવામાં આવી છે, તેના બદલે GPUAdapterInfo isFallbackAdapter વિશેષતાનો ઉપયોગ કરો."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16ને XMLHttpRequestમાં રિસ્પૉન્સ json દ્વારા સપોર્ટ આપવામાં આવતો નથી"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "વાપરનારના અનુભવ પર થનારા હાનિકારક પ્રભાવોને કારણે મુખ્ય થ્રેડ પર સિંક્રોનસ XMLHttpRequestનો ઉપયોગ ટાળવામાં આવ્યો છે. વધુ સહાય માટે, https://xhr.spec.whatwg.org/ ચેક કરો."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ઍનિમેશન"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "લેઆઉટ શિફ્ટ ત્યારે જ થાય છે જ્યારે કોઈપણ વપરાશકર્તાની ક્રિયાપ્રતિક્રિયા વિના એલિમેન્ટ ખસેડવામાં આવે. [લેઆઉટ શિફ્ટના કારણોની તપાસ કરો](https://web.dev/articles/optimize-cls), જેમ કે એલિમેન્ટ ઉમેરવામાં આવે છે, કાઢી નાખવામાં આવે છે અથવા તેમના પેજ લોડ થાય છે તેમ ફૉન્ટમાં ફેરફાર થાય છે."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ફૉન્ટ માટેની વિનંતી"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "દાખલ કરવામાં આવેલી iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "લેઆઉટ શિફ્ટ ક્લસ્ટર @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "લેઆઉટ શિફ્ટ માટે જવાબદાર કારણોની ભાળ મેળવી શક્યા નથી"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "કોઈ લેઆઉટ શિફ્ટ નથી"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "લેઆઉટ શિફ્ટ માટે જવાબદાર કારણો"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "લેઆઉટ શિફ્ટ માટેના ટોચના જવાબદાર કારણો"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "કદ વિનાનું છબીનું એલિમેન્ટ"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "સૌથી ખરાબ ક્લસ્ટર"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "સૌથી ખરાબ લેઆઉટ શિફ્ટ ક્લસ્ટર"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "કૅશ મેમરી TTL"}, "models/trace/insights/Cache.ts | description": {"message": "એક લાંબી કૅશ મેમરી લાઇફલાઇનથી તમારા પેજ પર વારંવાર મુલાકાતો ઝડપી બની શકે છે. [વધુ જાણો](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "અકાર્યક્ષમ કૅશ મેમરી પૉલિસીઓ ધરાવતી કોઈ વિનંતીઓ નથી"}, "models/trace/insights/Cache.ts | others": {"message": "અન્ય {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "વિનંતી કરો"}, "models/trace/insights/Cache.ts | title": {"message": "કાર્યક્ષમ કૅશ મેમરી લાઇફટાઇમનો ઉપયોગ કરો"}, "models/trace/insights/DOMSize.ts | description": {"message": "મોટા DOMને કારણે શૈલીની ગણતરી અને લેઆઉટ રીફ્લોની અવધિમાં વધારો થઈ શકે છે, જે પેજના રિસ્પૉન્સ આપવાની પ્રક્રિયાને અસર કરે છે. મોટું DOM મેમરીનો વપરાશ પણ વધારશે. [DOMના વધુ પડતા કદને કેવી રીતે ટાળવું તે જાણો](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "ઘટક"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "મોટા ભાગના પેટા એલિમેન્ટ"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOMની ઊંડાઈ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "આંકડા"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOMનું કદ ઑપ્ટિમાઇઝ કરો"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "કુલ એલિમેન્ટ"}, "models/trace/insights/DOMSize.ts | value": {"message": "મૂલ્ય"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "નેટવર્ક માટેની તમારી પહેલી વિનંતી સૌથી મહત્ત્વપૂર્ણ છે.  રીડાયરેક્ટ કરવાનું ટાળીને, સર્વરના ઝડપી જવાબની ખાતરી કરીને અને ટેક્સ્ટને નાની કરવાની સુવિધા ચાલુ કરીને, તેની વિલંબતા ઘટાડો."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "રીડાયરેક્ટની વિનંતીઓ હતી ({PH1} રીડાયરેક્ટ, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "સર્વરે ધીમેથી જવાબ આપ્યો (અવલોકન કરેલો સમય {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "નાની કરવાની કોઈ પ્રક્રિયા લાગુ કરવામાં આવી નથી"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "રીડાયરેક્ટ થવાના કિસ્સા ટાળ્યા છે"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "સર્વર ઝડપથી જવાબ આપે છે (અવલોકન કરેલો સમય {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ટેક્સ્ટને નાની કરવાની પ્રક્રિયા લાગુ કરવામાં આવી છે"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "રીડાયરેક્ટ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "સર્વરનો જવાબ આપવાનો સમય"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "દસ્તાવેજની વિનંતીમાં વિલંબતા"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "કમ્પ્રેસ કર્યા વિના ડાઉનલોડ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ડુપ્લિકેટ કરેલી બાઇટ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "સૉર્સ"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "નેટવર્ક ઍક્ટિવિટી દ્વારા ઉપયોગમાં લેવાતા બિનજરૂરી બાઇટને ઘટાડવા માટે બંડલમાંથી મોટા, ડુપ્લિકેટ JavaScript મૉડ્યૂલ કાઢી નાખો."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ડુપ્લિકેટ કરેલી JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "ટેક્સ્ટ સતત દેખાતી રહે તેની ખાતરી કરવા માટે [font-display](https://developer.chrome.com/blog/font-display)ને swap પર કે optional પર સેટ કરવાનું વિચારો. લેઆઉટ શિફ્ટને ઘટાડવા માટે, [ફૉન્ટ મેટ્રિક ઓવરરાઇડ](https://developer.chrome.com/blog/font-fallbacks)ની મદદથી swapને વધુ ઑપ્ટિમાઇઝ કરી શકાય છે."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ફૉન્ટ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ફૉન્ટ ડિસ્પ્લે"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "બગડેલો સમય"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(અનામી)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "શૈલી અને લેઆઉટની ગણતરી કરવા માટે સામાન્ય રીતે લેઆઉટની ભૂમિતિને વાંચતા ઘણા APIs, રેન્ડરિંગ એન્જિનને સ્ક્રિપ્ટનો અમલ કરવાનું થોભાવવા માટે દબાણ કરે છે. [જરૂરી બનાવવામાં આવેલા રીફ્લો](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) અને તેના ઘટાડા વિશે વધુ જાણો."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "સ્ટૅક ટ્રેસ"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "જરૂરી બનાવવામાં આવેલો રીફ્લો"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "સૌથી વધુ સમય લેતો ફંક્શન કૉલ"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "રીફ્લોનો કુલ સમય"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[સાંકળી ન શકાયેલા કાર્યો]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "છબીઓને ડાઉનલોડ કરવાનો સમય ઘટાડવાથી, પેજ અને LCP લોડ થવાના, અનુભૂતિ કરેલા સમયમાં સુધારો થઈ શકે છે. [છબીનું કદ ઑપ્ટિમાઇઝ કરવા વિશે વધુ જાણો](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (અંદાજિત {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ઑપ્ટિમાઇઝ કરી શકાય એવી કોઈ છબી નથી"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ફાઇલનું કદ ઑપ્ટિમાઇઝ કરો"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "અન્ય {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "છબીની ડિલિવરીને બહેતર બનાવો"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "છબીને નાનું કરવાનું પ્રમાણ વધારવાથી, આ છબીના ડાઉનલોડના કદમાં સુધારો થઈ શકે છે."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "આધુનિક છબી ફૉર્મેટ (WebP, AVIF)નો ઉપયોગ કરવાથી અથવા છબીને નાનું કરવાનું પ્રમાણ વધારવાથી, આ છબીના ડાઉનલોડના કદમાં સુધારો થઈ શકે છે."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "આ છબી ફાઇલના બતાવેલા પરિમાણો હોવા જોઈએ ({PH1}) તેના કરતાં મોટા છે ({PH2}). છબીના ડાઉનલોડનું કદ ઘટાડવા માટે રિસ્પૉન્સિવ છબીઓનો ઉપયોગ કરો."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFsને બદલે વીડિયો ફૉર્મેટનો ઉપયોગ કરવાથી, ઍનિમેટેડ કન્ટેન્ટના ડાઉનલોડના કદમાં સુધારો થઈ શકે છે."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "સૌથી લાંબા તબક્કા માટે તપાસ કરવાનું શરૂ કરો. [વિલંબોને શક્ય તેટલા ઘટાડી શકાય છે](https://web.dev/articles/optimize-inp#optimize_interactions). પ્રક્રિયા કરવાની અવધિ ઘટાડવા માટે, [મુખ્ય થ્રેડના ખર્ચને ઑપ્ટિમાઇઝ કરો](https://web.dev/articles/optimize-long-tasks), જે મોટેભાગે JS હોય છે."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "અવધિ"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ઇનપુટમાં વિલંબ"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "કોઈ ક્રિયાપ્રતિક્રિયાની ભાળ મળી નથી"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "તબક્કો"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "પ્રસ્તુતિમાં વિલંબ"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "ચાલુ પ્રક્રિયાની અવધિ"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "તબ્બકા મુજબ INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTMLમાંથી LCP છબીને તરત જ [શોધવાયોગ્ય](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) બનાવીને અને [ધીમું લોડિંગ ટાળીને](https://web.dev/articles/lcp-lazy-loading) LCPને ઑપ્ટિમાઇઝ કરો"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=ઉચ્ચ લાગુ કરી છે"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high લાગુ કરવું જોઈએ"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "લેઝિ લોડ પ્રોપર્ટી લાગુ કરી નથી"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "સૌથી વહેલા પ્રારંભિક સમય પછી {PH1}માં LCPની છબી લોડ થઈ."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "કોઈ LCPની ભાળ મળી નથી"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "કોઈ LCP સંસાધનની ભાળ મળી નથી કારણ કે LCP એ કોઈ છબી નથી"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "પ્રારંભિક દસ્તાવેજમાં વિનંતીને શોધી શકાય છે"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP વિનંતીની વિસ્તૃત શોધ"}, "models/trace/insights/LCPPhases.ts | description": {"message": "દરેક [તબક્કો સુધારણાની ચોક્કસ વ્યૂહરચનાઓ ધરાવે છે](https://web.dev/articles/optimize-lcp#lcp-breakdown). આદર્શ રીતે, મોટાભાગનો LCP સમય સંસાધનોને લોડ કરવામાં પસાર થવો જોઈએ, વિલંબમાં નહીં."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "અવધિ"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "એલિમેન્ટ રેન્ડર કરવામાં વિલંબ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ફીલ્ડ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "કોઈ LCPની ભાળ મળી નથી"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "તબક્કો"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "સંસાધન લોડ થવામાં વિલંબ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "સંસાધન લોડ કરવાની અવધિ"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "પહેલા બાઇટ માટેનો સમય"}, "models/trace/insights/LCPPhases.ts | title": {"message": "તબક્કા મુજબ LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "સ્ક્રિપ્ટ"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "બગડેલી બાઇટ"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill અને ટ્રાન્સફોર્મ જૂના બ્રાઉઝરની, નવી JavaScriptની સુવિધાઓનો ઉપયોગ કરવાની ક્ષમતા ચાલુ કરે છે. જોકે, આધુનિક બ્રાઉઝર માટે અનેકની જરૂર નથી. [બેઝલાઇન](https://web.dev/articles/baseline-and-polyfills) સુવિધાઓને ટ્રાન્સપાઇલ ન કરવા માટે તમારી JavaScript બિલ્ડ પ્રક્રિયામાં ફેરફાર કરવાનું વિચારો, સિવાય કે તમને ખબર હોય કે તમારે જૂના બ્રાઉઝરને સપોર્ટ કરવો જ જોઈએ. [મોટાભાગની સાઇટ ટ્રાન્સપાઇલ કર્યા વિના ES6+ કોડ કેમ લાગુ કરી શકે છે તે જાણો](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "જૂની JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/1.1ની તુલનામાં HTTP/2 અને HTTP/3 ઘણા લાભ આપે છે, જેમ કે મલ્ટીપ્લેક્સિંગ. [મૉર્ડન HTTPનો ઉપયોગ કરવા વિશે વધુ જાણો](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "કોઈ વિનંતીઓએ HTTP/1.1નો ઉપયોગ કર્યો નથી"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "પ્રોટોકૉલ"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "વિનંતી કરો"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "મોડર્ન HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ઑરિજિન"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "વિનંતી કરો"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "સૉર્સ"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "સમય"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "અંદાજિત LCP બચત"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "ઉપયોગમાં ન લેવાયેલું પ્રીકનેક્ટ. ચેક કરો કે crossorigin વિશેષતાનો યોગ્ય રીતે ઉપયોગ કરવામાં આવે છે."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "ચેનની લંબાઈ ઘટાડીને, સંસાધનોને ડાઉનલોડ કરવાના કદને ઘટાડીને અથવા પેજને લોડ કરવાની પ્રક્રિયાને બહેતર બનાવવા માટે બિનજરૂરી સંસાધનોને ડાઉનલોડ કરવાની પ્રક્રિયાને મુલતવી રાખીને [નોંધપાત્ર વિનંતીઓની ચેન બનાવવાનું ટાળો](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "તમારા સૌથી મહત્વપૂર્ણ ઑરિજિનમાં [પ્રીકનેક્ટ કરો](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) હિન્ટ ઉમેરો, પણ 4થી વધુ હિન્ટનો ઉપયોગ કરવાનો પ્રયાસ કરશો નહીં."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "કેન્ડિડેટને પ્રીકનેક્ટ કરો"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "મહત્ત્વપૂર્ણ પાથની મહત્તમ વિલંબતા:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "નેટવર્ક ડિપેન્ડેન્સીને કારણે રેન્ડરિંગના કોઈપણ કાર્ય પર અસર થઈ નથી"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "પ્રીકનેક્ટ કરવા માટે કોઈ વધારાનું ઑરિજિન સારું કેન્ડિડેટ ન હતું"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "કોઈ ઑરિજિન પ્રીકનેક્ટેડ ન હતું"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[પ્રીકનેક્ટ કરો](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) હિન્ટ બ્રાઉઝરને પેજ લોડ કરવામાં વહેલા કનેક્શન સ્થાપિત કરવામાં મદદ કરે છે, જેનાથી તે ઑરિજિન માટે પહેલી વિનંતી કરવામાં આવે ત્યારે સમય બચે છે. નીચે એવા ઑરિજિન આપ્યા છે, જેની સાથે પેજ પહેલેથી પ્રીકનેક્ટેડ છે."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "પ્રીકનેક્ટેડ ઑરિજિન"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "નેટવર્ક ડિપેન્ડેન્સી ટ્રી"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4 preconnect કનેક્શન કરતાં વધુ કનેક્શન મળ્યા હતા. આનો ઉપયોગ ઓછો અને માત્ર સૌથી મહત્ત્વપૂર્ણ ઑરિજિન સુધી જ થવો જોઈએ."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "ઉપયોગમાં ન લેવાયેલું પ્રીકનેક્ટ. પેજ દ્વારા જે ઑરિજિનની વિનંતી કરવાની સંભાવના હોય, માત્ર તેના માટે જ preconnectનો ઉપયોગ કરો."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "ચેનની લંબાઈ ઘટાડીને, સંસાધનોને ડાઉનલોડ કરવાના કદને ઘટાડીને અથવા પેજને લોડ કરવાની પ્રક્રિયાને બહેતર બનાવવા માટે બિનજરૂરી સંસાધનોને ડાઉનલોડ કરવાની પ્રક્રિયાને મુલતવી રાખીને નોંધપાત્ર વિનંતીઓની ચેન બનાવવાનું ટાળો."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "વિનંતીઓ પેજના પ્રારંભિક રેન્ડર થવાને બ્લૉક કરી રહી છે, જેનાથી LCPમાં વિલંબ થઈ શકે છે. [મુલતવી રાખીને કે ઇનલાઇન કરીને](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) નેટવર્કની આ વિનંતીઓને મહત્ત્વપૂર્ણ પાથની બહાર ખસેડી શકાય છે."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "અવધિ"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "આ નૅવિગેશન માટે રેન્ડર બ્લૉક કરવાની કોઈ વિનંતી નથી"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "વિનંતી"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "બ્લૉક કરવાની વિનંતીઓને રેન્ડર કરો"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "જો પુનઃગણતરીની શૈલીનો ખર્ચ વધારે રહે, તો પસંદગીકર્તા ઑપ્ટિમાઇઝેશન તેને ઘટાડી શકે છે. વધુ વીતેલો સમય અને વધુ ધીમો-પાથ %, આ બન્ને ધરાવતા [પસંદગીકર્તાઓને ઑપ્ટિમાઇઝ કરો](https://developer.chrome.com/docs/devtools/performance/selector-stats). વધુ સરળ પસંદગીકર્તાઓ, ઓછા પસંદગીકર્તાઓ, નાનો DOM અને ઓછો વ્યાપક DOM મેળ કરવાના તમામ ખર્ચમાં ઘટાડો કરશે."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "વીતેલો સમય"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS પસંદગીકર્તાનો કોઈ ડેટા મળ્યો નથી. પર્ફોર્મન્સ પૅનલના સેટિંગમાં CSS પસંદગીકર્તાની આંકડાકીય માહિતી ચાલુ કરેલી હોવી જરૂરી છે."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "મેળ કરવાના પ્રયાસો"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "મેળ થયેલા એલિમેન્ટની સંખ્યા"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS પસંદગીકર્તા સંબંધિત ખર્ચ"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ટોચના પસંદગીકર્તા"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "કુલ"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "મુખ્ય થ્રેડનો સમય"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "ત્રીજો પક્ષ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ટ્રાન્સફરનું કદ"}, "models/trace/insights/ThirdParties.ts | description": {"message": "ત્રીજા પક્ષનો કોડ, લોડ થવાના પર્ફોર્મન્સને નોંધપાત્ર રીતે અસર કરી શકે છે. તમારા પેજના કન્ટેન્ટને પ્રાધાન્યતા આપવા માટે, [ત્રીજા પક્ષના કોડનું લોડિંગને ઘટાડો અને તેને વિલંબિત કરો](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "ત્રીજા પક્ષનું કોઈ કન્ટેન્ટ મળ્યું નથી"}, "models/trace/insights/ThirdParties.ts | title": {"message": "ત્રીજા પક્ષો"}, "models/trace/insights/Viewport.ts | description": {"message": "જો મોબાઇલ માટે વ્યૂપોર્ટને ઑપ્ટિમાઇઝ ન કરવામાં આવ્યું હોય, તો ટૅપ કરવાની ક્રિયાપ્રતિક્રિયાઓ [300 મિલિસેકન્ડ સુધી વિલંબિત](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) થઈ શકે છે."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "મોબાઇલ ટૅપમાં વિલંબ"}, "models/trace/insights/Viewport.ts | title": {"message": "મોબાઇલ માટે વ્યૂપોર્ટને ઑપ્ટિમાઇઝ કરો"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "માત્ર કોઈ GET વિનંતી મારફતે લોડ કરેલા પેજ બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવે છે."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "માત્ર 2XXનો સ્ટેટસ કોડ ધરાવનારા પેજને કૅશ મેમરીમાં સાચવી શકાય છે."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "કૅશ મેમરીમાં હોવા દરમિયાન JavaScript ચલાવવાનો પ્રયાસ કરવામાં આવ્યો હોવાની Chrome દ્વારા ભાળ મેળવવામાં આવી."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "જે કોઈ પેજ દ્વારા કોઈ AppBannerની વિનંતી કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ચિહ્નિત કરવાની સુવિધા દ્વારા બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે. આ ડિવાઇસ પર તેને સ્થાનિક રીતે ચાલુ કરવા માટે chrome://flags/#back-forward-cacheની મુલાકાત લો."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "આદેશ લાઇન દ્વારા બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "અપૂરતી મેમરી હોવાને કારણે બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "બૅક/ફૉરવર્ડ કૅશની સુવિધાને નિયુક્ત દ્વારા સપોર્ટ આપવામાં આવતો નથી."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "પ્રીરેન્ડરર માટે બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "નોંધણીકૃત સાંભળનારાઓ સાથે BroadcastChannel આવૃત્તિ હોવાને કારણે આ પેજ કૅશ મેમરીમાં સાચવી શકાશે નહીં."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store હેડર ધરાવતા પેજ બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "કૅશ મેમરી જાણી જોઈને સાફ કરવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "અન્ય કોઈ પેજને તેમાં ઉમેરી શકાય એ માટે આ પેજને કૅશ મેમરીમાંથી કાઢી નાખવામાં આવ્યું."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "જે કોઈ પેજમાં પ્લગ-ઇનનો સમાવેશ હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "અવ્યાખ્યાયિત"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "જે કોઈ પેજમાં FileChooser APIનો ઉપયોગ કરવામાં આવ્યો હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "જે કોઈ પેજ File System Access APIનો ઉપયોગ કરતા હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "જે કોઈ પેજ Media Device Dispatcherનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "પેજમાંથી બહાર નીકળતી વખતે કોઈ મીડિયા પ્લેયર ચાલી રહ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "જે કોઈ પેજ MediaSession APIનો ઉપયોગ કરતા હોય અને પ્લેબૅકનું સ્ટેટસ સેટ કરતા હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "જે કોઈ પેજ MediaSession API અને સેટ કરેલી ઍક્શનના હૅન્ડલરનો ઉપયોગ કરતા હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "સ્ક્રીન રીડરના કારણે બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "જે કોઈ પેજ SecurityHandlerનો ઉપયોગ કરતા હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "જે કોઈ પેજમાં Serial APIનો ઉપયોગ કરવામાં આવ્યો હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "જે કોઈ પેજમાં WebAuthentication APIનો ઉપયોગ કરવામાં આવ્યો હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "જે કોઈ પેજમાં WebBluetooth APIનો ઉપયોગ કરવામાં આવ્યો હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "જે કોઈ પેજમાં WebUSB APIનો ઉપયોગ કરવામાં આવ્યો હોય, તે બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "બૅક/ફૉરવર્ડ કૅશ બંધ છે કારણ કે Cache-Control: no-storeનો ઉપયોગ કરતા પેજ પર કુકી બંધ છે."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "જે કોઈ પેજ કોઈ સમર્પિત વર્કર કે વર્કલેટનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "પેજમાંથી બહાર નીકળવા પહેલાં દસ્તાવેજના લોડ થવાની પ્રક્રિયા પૂર્ણ થઈ ન હતી."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે ઍપ બૅનર હાજર હતું."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે Chrome પાસવર્ડ મેનેજર હાજર હતું."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે DOM ડિસ્ટિલેશનની પ્રક્રિયા ચાલુ હતી."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે DOM ડિસ્ટિલર વ્યૂઅર હાજર હતું."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "મેસેજિંગ APIનો ઉપયોગ કરતા એક્સ્ટેન્શનને કારણે બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "બૅક-ફૉરવર્ડ કૅશ દાખલ કરતા પહેલાં, લાંબા ગાળાનું કનેક્શન ધરાવતા એક્સ્ટેન્શન દ્વારા કનેક્શન બંધ કરવામાં આવવું જોઈએ."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "લાંબા ગાળાનું કનેક્શન ધરાવતા એક્સ્ટેન્શન દ્વારા બૅક-ફૉરવર્ડ કૅશમાંની ફ્રેમને મેસેજ મોકલવાનો પ્રયાસ કરવામાં આવ્યો હતો."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "એક્સ્ટેન્શનના કારણે બૅક/ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે પેજ માટે, ફોર્મ રિસબમિશન અથવા http પાસવર્ડ સંવાદ જેવો મૉડલ સંવાદ બતાવવામાં આવ્યો હતો."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે ઑફલાઇન પેજ બતાવવામાં આવ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે આઉટ-ઑફ-મેમરી ઇન્ટરવેન્શન બાર હાજર હતો."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે પરવાનગીની વિનંતીઓ કરવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે પૉપઅપ બ્લૉકર હાજર હતું."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "નૅવિગેટ કરીને દૂર જતી વખતે Safe Browsingની વિગતો બતાવવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing દ્વારા આ પેજને દુરુપયોગ માનીને પૉપઅપ બ્લૉક કરવામાં આવ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "આ પેજ જ્યારે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં હતું, ત્યારે કોઈ સર્વિસ વર્કરની સેવાને સક્રિય કરવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "કોઈ દસ્તાવેજની ભૂલને કારણે બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFramesનો ઉપયોગ કરતા પેજ bfcacheમાં સ્ટોર કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "અન્ય કોઈ પેજને તેમાં ઉમેરી શકાય એ માટે આ પેજને કૅશ મેમરીમાંથી કાઢી નાખવામાં આવ્યું."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "જે કોઈ પેજ દ્વારા મીડિયા સ્ટ્રીમ કરવાનો ઍક્સેસ આપવામાં આવ્યો હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "જે પેજમાં અમુક ચોક્કસ પ્રકારોનું શામેલ કરેલું કન્ટેન્ટ હોય, (દા.ત. PDFs), તે હાલમાં બૅક-ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતું નથી."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "જે કોઈ પેજ IdleManagerનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "જે કોઈ પેજ કોઈ ઓપન IndexedDB કનેક્શન ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB ઇવેન્ટને કારણે બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "અયોગ્ય APIsનો ઉપયોગ કરવામાં આવ્યો હતો."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "જે કોઈ પેજમાં એક્સ્ટેન્શન દ્વારા JavaScriptને શામેલ કરવામાં આવેલી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "જે કોઈ પેજમાં એક્સ્ટેન્શન દ્વારા StyleSheetને શામેલ કરવામાં આવેલી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "આંતરિક ભૂલ."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "બૅક/ફૉરવર્ડ કૅશ બંધ છે કારણ કે કેટલીક JavaScript નેટવર્કની વિનંતીએ Cache-Control: no-store હેડર સાથેનું સંસાધન પ્રાપ્ત કર્યું છે."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "કોઈ keepaliveની વિનંતીને કારણે બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ કરવામાં આવી છે."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "જે કોઈ પેજ કીબોર્ડ લૉકનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "પેજમાંથી બહાર નીકળવા પહેલાં પેજના લોડ થવાની પ્રક્રિયા પૂર્ણ થઈ ન હતી."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "જે કોઈ પેજના મુખ્ય સંસાધન cache-control:no-cache હોય, તે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "જે કોઈ પેજના મુખ્ય સંસાધન cache-control:no-store હોય, તે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાંથી પેજને રિસ્ટોર કરવામાં આવે એ પહેલાં નૅવિગેશન રદ કરવામાં આવ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "કોઈ સક્રિય નેટવર્ક કનેક્શન દ્વારા વધુ પડતો ડેટા મેળવવાને કારણે આ પેજને કૅશ મેમરીમાંથી કાઢી નાખવામાં આવ્યું હતું. કૅશ મેમરીમાં હોય ત્યારે કોઈ પેજ કેટલો ડેટા મેળવી શકે તે, Chrome દ્વારા નિયંત્રિત કરવામાં આવે છે."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "જે કોઈ પેજ XHR કે નેટવર્ક મેળવવાની પ્રક્રિયામાં હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "નેટવર્કની કોઈ સક્રિય વિનંતીમાં રીડાયરેક્ટનો સમાવેશ હોવાને કારણે આ પેજને બૅક/ફૉરવર્ડ કૅશ મેમરીમાંથી કાઢી નાખવામાં આવ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "કોઈ નેટવર્ક કનેક્શન ઘણા લાંબા સમય માટે ખુલ્લું રહેવાને કારણે આ પેજને કૅશ મેમરીમાંથી કાઢી નાખવામાં આવ્યું હતું. કૅશ મેમરીમાં હોય ત્યારે કોઈ પેજ કેટલા સમયમાં કેટલો ડેટા મેળવી શકે તે, Chrome દ્વારા નિયંત્રિત કરવામાં આવે છે."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "જે કોઈ પેજ કોઈ માન્ય પ્રતિસાદ હેડર ધરાવતા ન હોય, તે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "મુખ્ય ફ્રેમ સિવાય કોઈ અન્ય ફ્રેમમાં નૅવિગેશનની પ્રક્રિયા બનવા પામી."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "જે કોઈ પેજ અનુક્રમિત કરેલા ચાલુ DB વ્યવહારો ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "જે કોઈ પેજ નેટવર્કની પ્રક્રિયામાં હોય તેવી વિનંતી ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "જે કોઈ પેજ નેટવર્ક મેળવવાની પ્રક્રિયામાં હોય તેવી વિનંતી ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "જે કોઈ પેજ નેટવર્કની પ્રક્રિયામાં હોય તેવી વિનંતી ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "જે કોઈ પેજ XHR નેટવર્કની પ્રક્રિયામાં હોય તેવી વિનંતી ધરાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "જે કોઈ પેજ PaymentManagerનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "જે કોઈ પેજ ચિત્ર-માં-ચિત્રની સુવિધાનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "જે કોઈ પેજ પ્રિન્ટિંગ UI બતાવતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "'window.open()' વડે આ પેજ ખોલવામાં આવ્યું હતું તેમજ અન્ય ટૅબ તેનો સંદર્ભ ધરાવે છે અથવા આ પેજ દ્વારા કોઈ વિન્ડો ખોલવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાંના પેજની રેન્ડર કરવાની પ્રક્રિયા ક્રૅશ થઈ."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાંના પેજની રેન્ડર કરવાની પ્રક્રિયા બંધ કરવામાં આવી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "જે કોઈ પેજ દ્વારા ઑડિયો કૅપ્ચર કરવાની પરવાનગીઓ માટે વિનંતી કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "જે કોઈ પેજ દ્વારા સેન્સર પરવાનગીઓ વિશે વિનંતીઓ કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "જે કોઈ પેજ દ્વારા બૅકગ્રાઉન્ડ સિંક કે મેળવવાની પરવાનગીઓ વિશે વિનંતી કરવામાં આવતી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "જે કોઈ પેજ દ્વારા MIDI પરવાનગીઓ વિશે વિનંતીઓ કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "જે કોઈ પેજ દ્વારા નોટિફિકેશન પરવાનગીઓ વિશે વિનંતીઓ કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "જે કોઈ પેજ દ્વારા સ્ટોરેજના ઍક્સેસની વિનંતી કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "જે કોઈ પેજ દ્વારા વીડિયો કૅપ્ચર કરવાની પરવાનગીઓ માટે વિનંતી કરવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "જે કોઈ પેજના URLની સ્કીમ HTTP / HTTPS હોય, માત્ર તેને જ કૅશ મેમરીમાં સાચવી શકાશે."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાં હોવા દરમિયાન કોઈ સર્વિસ વર્કર દ્વારા આ પેજ માટે દાવો કરવામાં આવ્યો હતો."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાં રહેલા પેજને કોઈ સર્વિસ વર્કર દ્વારા MessageEvent મોકલવાનો પ્રયાસ કરવામાં આવ્યો."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "કોઈ પેજ જ્યારે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં હતું, ત્યારે ServiceWorker નોંધણી વિના હતું."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "કોઈ સર્વિસ વર્કરની સક્રિયતાને કારણે આ પેજને બૅક/ફૉરવર્ડ કૅશમાંથી કાઢી નાખવામાં આવ્યું હતું."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome ફરીથી શરૂ થયું હતું અને બૅક/ફૉરવર્ડ કૅશ મેમરીમાંની બધી એન્ટ્રી સાફ કરવામાં આવી હતી."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "જે કોઈ પેજ SharedWorkerનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "જે કોઈ પેજ SpeechRecognizerનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "જે કોઈ પેજ SpeechSynthesisનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "પેજ પરના કોઈ iframe દ્વારા નૅવિગેશનની શરૂ કરવામાં આવેલી પ્રક્રિયા પૂર્ણ થઈ ન શકી."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "જે કોઈ પેજના પેટાસંસાધન cache-control:no-cache હોય, તે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "જે કોઈ પેજના પેટાસંસાધન cache-control:no-store હોય, તે બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "આ પેજ દ્વારા બૅક/ફૉરવર્ડ કૅશ મેમરીમાં મહત્તમથી વધુ સમય વિતાવવામાં આવ્યો હતો અને તેથી તેની સમયસીમા સમાપ્ત થઈ હતી."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ થતી વખતે આ પેજની સમયસીમા સમાપ્ત થઈ હતી (મોટા ભાગે લાંબા ગાળાના પેજહાઇડ હૅન્ડલરના કારણે)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "મુખ્ય ફ્રેમમાં આ પેજ અનલોડ હૅન્ડલર ધરાવે છે."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "કોઈ પેટા ફ્રેમમાં આ પેજ અનલોડ હૅન્ડલર ધરાવે છે."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "બ્રાઉઝર દ્વારા વપરાશકર્તા એજન્ટ ઓવરરાઇડના હેડરમાં ફેરફાર કરવામાં આવ્યો."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "જે કોઈ પેજને વીડિયો કે ઑડિયો રેકોર્ડ કરવાના ઍક્સેસની પરવાનગી આપવામાં આવી હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "જે કોઈ પેજમાં WebDatabaseનો ઉપયોગ કરવામાં આવ્યો હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "જે કોઈ પેજ WebHIDનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "જે કોઈ પેજ WebLocksનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "જે કોઈ પેજ WebNfcનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "જે કોઈ પેજ WebOTPServiceનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC ધરાવતા પેજ બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTCનો ઉપયોગ કરવામાં આવ્યો હોવાથી બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ છે."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "જે કોઈ પેજ WebShareનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket ધરાવતા પેજ બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocketનો ઉપયોગ કરવામાં આવ્યો હોવાથી બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ છે."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport ધરાવતા પેજ બૅક/ફૉરવર્ડ કૅશ મેમરીમાં દાખલ કરી શકાતા નથી."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransportનો ઉપયોગ કરવામાં આવ્યો હોવાથી બૅક-ફૉરવર્ડ કૅશની સુવિધા બંધ છે."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "જે કોઈ પેજ WebXRનો ઉપયોગ કરતા હોય, તે હાલમાં બૅક/ફૉરવર્ડ કૅશ માટે યોગ્યતા ધરાવતા નથી."}}