{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers ہینڈلنگ میں اجازت وائلڈ کارڈ کی علامت (*) سے کور نہیں ہوگی۔"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ڈیفالٹ کاسٹ انٹیگریشن کو غیر فعال کرنے کے لیے -internal-media-controls-overlay-cast-button سلیکٹر استعمال کرنے کے بجائے disableRemotePlayback انتساب کا استعمال کیا جانا چاہیے۔"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS کی ہئیت کی قدر slider-vertical معیاری نہیں ہے اور اسے ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ان وسائل کی درخواستیں جن کے URLs میں وہائٹ اسپیس \\(n|r|t) حروف اور (<) سے کم حروف دونوں شامل ہیں مسدود کر دیئے گئے ہیں۔ ان وسائل کو لوڈ کرنے کے لیے براہ کرم نئی لائنوں کو ہٹائیں اور عنصر کی خصوصیت کی قدروں جیسے مقامات سے کم حروف کو ان کوڈ کریں۔"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() پرنا ہو چکا ہے، اس کے بجائے معیاری API استعمال کریں: نیویگیشن ٹائمنگ 2۔"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() پرانا ہو چکا ہے، اس کے بجائے معیاری API استعمال کریں: پینٹ ٹائمنگ۔"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() پرانا ہو چکا ہے، اس کے بجائے معیاری API استعمال کریں: نیویگیشن ٹائمنگ 2 میں nextHopProtocol۔"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) حرف پر مشتمل کوکیز کو مختصر کرنے کے بجائے مسترد کر دیا جائے گا۔"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain کو ترتیب دے کر مماثل اوریجن پالیسی میں نرمی کرنا پرانا ہو چکا ہے، اور بطور ڈیفالٹ غیر فعال ہو جائے گا۔ اس فرسودگی کا انتباہ ایک کراس اوریجن رسائی کے لیے ہے جسے document.domain کو ترتیب دے کر فعال کیا گیا تھا۔"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "کراس اوریجن iframes سے متحرک کرنے والے window.alert کو فرسودہ کر دیا گیا ہے اور اسے مستقبل میں ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "کراس اوریجن iframes سے متحرک کرنے والے window.confirm کو ختم کر دیا گیا ہے اور اسے مستقبل میں ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ڈیٹا کے لیے سپورٹ: SVGUseElement میں URLs فرسودہ ہیں اور انہیں مستقبل میں ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() اور watchPosition() اب غیر محفوظ ماخذ پر کام نہیں کرتا ہے۔ اس خصوصیت کو استعمال کرنے کے لیے، آپ کو اپنی ایپلیکیشن کو محفوظ ماخذ، جیسے HTTPS پر تبدیل کرنے پر غور کرنا چاہیے۔ مزید تفصیلات کے لیے https://goo.gle/chrome-insecure-origins دیکھیں۔"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() اور watchPosition() غیر محفوظ مآخذ پر پرانے ہو چکے ہیں۔ اس خصوصیت کو استعمال کرنے کے لیے، آپ کو اپنی ایپلیکیشن کو محفوظ ماخذ، جیسے HTTPS پر تبدیل کرنے پر غور کرنا چاہیے۔ مزید تفصیلات کے لیے https://goo.gle/chrome-insecure-origins دیکھیں۔"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() اب غیر محفوظ ماخذ پر کام نہیں کرتا ہے۔ اس خصوصیت کو استعمال کرنے کے لیے، آپ کو اپنی ایپلیکیشن کو محفوظ ماخذ، جیسے HTTPS پر تبدیل کرنے پر غور کرنا چاہیے۔ مزید تفصیلات کے لیے https://goo.gle/chrome-insecure-origins دیکھیں۔"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>، ‏<aside>، ‏<nav> یا <section> کے اندر ایک <h1> ٹیگ پایا گیا جس پر متعین فونٹ سائز موجود نہیں ہے۔ مستقبل قریب میں اس براؤزر میں اس سرخی کا ٹیکسٹ سائز تبدیل ہو جائے گا۔ مزید معلومات کیلئے https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 دیکھیں۔"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate پرانا ہو چکا ہے۔ براہ کرم اس کے بجائے RTCPeerConnectionIceErrorEvent.address یا RTCPeerConnectionIceErrorEvent.port استعمال کریں۔"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ڈیجیٹل اسناد کیلئے navigator.credentials.get()‎ درخواست کا یہ فارمیٹ بند ہے، براہ کرم نیا فارمیٹ استعمال کرنے کیلئے اپنی کال کو اپ ڈیٹ کریں۔"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment سروس ورکر ایونٹ سے مرچنٹ اوریجن اور بے قاعدہ ڈیٹا پرانا ہے اور اسے ہٹا دیا جائے گا: topOrigin، paymentRequestOrigin، methodData، modifiers۔"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "ویب سائٹ نے ایسے نیٹ ورک سے وسائل کی درخواست کی جس تک وہ صرف اپنے صارف کے منفرد نیٹ ورک مقام کی وجہ سے رسائی کر سکتی تھی۔ یہ درخواستیں نجی آلات اور سرورز کو انٹرنیٹ پر بے نقاب کرتی ہیں، جس سے کراس سائٹ درخواست جعل سازی (CSRF) کے حملوں، اور/یا معلومات کے افشاء کا خطرہ بڑھ جاتا ہے۔ ان خطرات کو کم کرنے کے لیے، Chrome غیر محفوظ سیاق و سباق سے شروع ہونے پر غیر عوامی ذیلی وسائل کی درخواستوں کو مسترد کر دیتا ہے، اور انہیں مسدود کرنا شروع کر دے گا۔"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups کے dailyUpdateUrl فیلڈ کو joinAdInterestGroup() پر پاس کیا گیا ہے، اس کے برتاؤ کو زیادہ درست طریقے سے ظاہر کرنے کے لیے، اس کا نام تبدیل کر کے updateUrl کر دیا گیا ہے۔"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator پرانا ہو چکا ہے۔ براہ کرم اس کے بجائے Intl.Segmenter استعمال کریں۔"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS کو file: URLs سے لوڈ نہیں کیا جا سکتا جب تک کہ وہ .css فائل ایکسٹینشن کے ساتھ ختم نہ ہوں۔"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove() کی غیر مطابقت پذیر رینج کو ہٹانے کے عمل کو روکنے کیلئے SourceBuffer.abort() کا استعمال کرنا خصوصیت میں تبدیلی کی وجہ سے پرانا ہو گیا ہے۔ مستقبل میں سپورٹ ہٹا لیا جائے گا۔ اس کے بجائے آپ کو updateend ایونٹ سننا چاہیے۔ abort() کا مقصد صرف غیر مطابقت پذیر میڈیا منسلکہ کو منسوخ کرنا یا پارسر کی صورت حال کو دوبارہ ترتیب دینا ہے۔"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "تخصیصات میں تبدیلی کی وجہ سے، کسی بھی بفر شدہ کوڈڈ فریمز کے لیے MediaSource.duration کو اعلی ترین پیشکش ٹائم سٹیمپ سے کم پر سیٹ کرنا پرانا ہو چکا ہے۔ مختصر اور بفر شدہ میڈیا کو واضح طور پر ہٹانے کے لیے مستقبل میں سپورٹ ہٹا دیا جائے گا۔ اس کے بجائے آپ کو تمام sourceBuffers پر remove(newDuration, oldDuration) کو قطعی طور پر انجام دینا چاہیے، جہاں newDuration < oldDuration ہے"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions میں sysex کا تعین نہ کیے جانے پر بھی، ویب MIDI اسے استعمال کرنے کی اجازت طلب کرے گی۔"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "نوٹیفکیشن API کو اب غیر محفوظ ماخذ سے مزید استعمال نہیں کیا جا سکتا ہے۔ آپ کو اپنی ایپلیکیشن کو ایک محفوظ اوریجن، جیسے HTTPS پر سوئچ کرنے پر غور کرنا چاہیے۔ مزید تفصیلات کے لیے https://goo.gle/chrome-insecure-origins دیکھیں۔"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "نوٹیفکیشن API کی اجازت کی مزید درخواست کراس اوریجن iframe سے نہیں کی جا سکتی ہے۔ آپ کو اعلی لیول کے فریم سے اجازت کی درخواست کرنے، یا اس کے بجائے ایک نئی ونڈو کھولنے پر غور کرنا چاہیے۔"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "‫createImageBitmap میں موجود imageOrientation: 'none' اختیار فرسودہ ہے۔ براہ کرم اس کے بجائے '{imageOrientation: 'from-image'}' اختیار کے ساتھ createImageBitmap کا استعمال کریں۔"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "آپ کا پارٹنر ایک پرانے ‎(D)TLS ورژن پر بات چیت کر رہا ہے۔ اس کو درست کروانے کے لیے براہ کرم اپنے پارٹنر سے رابطہ کریں۔"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img، ‏video اور canvas کے ٹیگز پر overflow: visible کی وضاحت کرنے سے ان کے لیے عنصر کی حدود سے باہر ویژوئل مواد تیار کرنے کا سبب بن سکتا ہے۔ See https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments پرانا ہو چکا ہے۔ براہ کرم اس کے بجائے ادائیگی کے ہینڈلرز کیلئے صحیح وقت پر انسٹال کریں کا استعمال کریں۔"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "آپ کی PaymentRequest کال نے مواد سے متعلق سیکیورٹی پالیسی (CSP) connect-src ڈائریکٹیو کو بائی پاس کیا۔ یہ بائی پاس پرانا ہے۔ براہ کرم PaymentRequest API (supportedMethods فیلڈ میں) کے ادائیگی کے طریقہ شناخت کار کو اپنے CSP connect-src ڈائریکٹیو میں شامل کریں۔"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent پرانا ہو چکا ہے۔ براہ کرم اس کے بجائے معیاری navigator.storage استعمال کریں۔"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> والد/والدہ <source src> کے ساتھ غلط ہے اور اس لیے اسے نظر انداز کر دیا گیا ہے۔ براہ کرم اس کے بجائے <source srcset> استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame وینڈر سے مخصوص ہے۔ براہ کرم اس کے بجائے معیاری cancelAnimationFrame استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame وینڈر سے مخصوص ہے۔ براہ کرم اس کے بجائے معیاری requestAnimationFrame استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen فرسودہ کر دیا گيا ہے۔ براہ کرم اس کے بجائے Document.fullscreenElement استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen()‎ فرسودہ ہے۔ براہ کرم اس کے بجائے Element.requestFullscreen()‎ استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen()‎ فرسودہ ہے۔ براہ کرم اس کے بجائے Element.requestFullscreen()‎ استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen()‎ فرسودہ ہے۔ براہ کرم اس کے بجائے Document.exitFullscreen()‎ استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen()‎ فرسودہ ہے۔ براہ کرم اس کے بجائے Document.exitFullscreen()‎ استعمال کریں۔"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen فرسودہ ہے براہ کرم اس کے بجائے Document.fullscreenEnabled استعمال کریں۔"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "ہم API chrome.privacy.websites.privacySandboxEnabled کو فرسودہ کر رہے ہیں، اگرچہ یہ M113 کے ریلیز ہونے تک بیک ورڈ موافقت کے لیے فعال رہے گا۔ اس کے بجائے، براہ کرم chrome.privacy.websites.topicsEnabled، chrome.privacy.websites.fledgeEnabled اور chrome.privacy.websites.adMeasurementEnabled استعمال کریں۔ https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled ملاحظہ کریں۔"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "رکاوٹ DtlsSrtpKeyAgreement کو ہٹا دیا گیا ہے۔ آپ نے اس رکاوٹ کے لیے ایک false قدر متعین کی ہے، جس کی ترجمانی یہ ہے کہ آپ نے ہٹائی گئی SDES key negotiation پالیسی کو استعمال کرنے کی کوشش کی۔ یہ فعالیت ہٹا دی گئی ہے؛ اس کے بجائے ایسی سروس استعمال کریں جو DTLS key negotiation کو سپورٹ کرتی ہو۔"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "رکاوٹ DtlsSrtpKeyAgreement کو ہٹا دیا گیا ہے۔ آپ نے اس رکاوٹ کے لیے ایک true قدر متعین کی ہے، جس کا کوئی اثر نہیں ہوا، لیکن آپ صاف ستھرا کرنے کے غرض سے اس رکاوٹ کو ہٹا سکتے ہیں۔"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "کال بیک پر مبنی getStats()‎ فرسودہ ہے اور اسے ہٹا دیا جائے گا۔ اس کے بجائے خصوصیات کے موافق getStats()‎ استعمال کریں۔"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand()‎ فرسودہ ہے۔ براہ کرم اس کے بجائے Selection.modify()‎ استعمال کریں۔"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ذیلی وسائل کی درخواستیں جن کے URLs میں سرایت شدہ اسناد ہیں (جیسے **********************/) کو مسدود کر دیا گیا ہے۔"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy اختیار پرانا ہو چکا ہے اور اسے ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer کو کراس اوریجن علیحدگی کی ضرورت ہوگی۔ مزید تفصیلات کے لیے https://developer.chrome.com/blog/enabling-shared-array-buffer/ دیکھیں۔"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "صارف کی فعالیت کے بغیر speechSynthesis.speak() پرانا ہو چکا ہے اور اسے ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | UnloadHandler": {"message": "ان لوڈ ایونٹ لسنرز پرانے ہو چکے ہیں اور انہیں ہٹا دیا جائے گا۔"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer کا استعمال جاری رکھنے کے لیے، ایکسٹینشنز کو کراس اوریجن علیحدگی کا انتخاب کرنا چاہیے۔ https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ ملاحظہ کریں۔"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "‫GPUAdapter isFallbackAdapter وصف بند ہو گیا ہے، اس کے بجائے GPUAdapterInfo isFallbackAdapter وصف کا استعمال کریں۔"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 XMLHttpRequest پر json کے جواب سے تعاون یافتہ نہیں ہے"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "انتہائی صارف کے تجربے پر اس کے مضر اثرات ہونے کی وجہ سے اصل تھریڈ پر مطابقت پذیر XMLHttpRequest کو فرسودہ کر دیا گیا ہے۔ مزید مدد کے لیے، https://xhr.spec.whatwg.org/ چیک کریں۔"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "اینیمیشن"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "لے آؤٹ شفٹس اس وقت ہوتے ہیں جب عناصر کسی صارف کے تعامل کی عدم موجودگی میں حرکت کرتے ہیں۔ [لے آؤٹ شفٹس کے اسباب کی تفتیش کریں](https://web.dev/articles/optimize-cls) جیسے کہ صفحہ لوڈ ہوتے وقت عناصر کا شامل کیا جانا، ہٹایا جانا یا ان کے فونٹس کا تبدیل ہونا۔"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "فونٹ کی درخواست"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "انجیکٹ کردہ فریم"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "لے آؤٹ شفٹ کلسٹر @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "کسی لے آؤٹ شفٹ کلپرٹ کا پتہ نہیں چل سکا"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "کوئی لے آؤٹ شفٹ نہیں ہے"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "لے آؤٹ شفٹ کلپرٹس"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "سر فہرست لے آؤٹ شفٹ کلپرٹس"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "غیر سائز کا تصویری عنصر"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "بدترین کلسٹر"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "بدترین لے آؤٹ شفٹ کلسٹر"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "‫<PERSON><PERSON>"}, "models/trace/insights/Cache.ts | description": {"message": "ایک طویل cache لائف ٹائم آپ کے صفحہ پر مکرر ملاحظات کی رفتار میں اضافہ کر سکتا ہے۔ [مزید جانیں](https://web.dev/uses-long-cache-ttl/)۔"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "غیر مؤثر cache پالیسیوں والی کوئی درخواست نہیں ہے"}, "models/trace/insights/Cache.ts | others": {"message": "‫{PH1} دیگر"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "درخواست کریں"}, "models/trace/insights/Cache.ts | title": {"message": "مؤثر cache لائف ٹائمز استعمال کریں"}, "models/trace/insights/DOMSize.ts | description": {"message": "ایک بڑے DOM سے طرز کی کیلکولیشنز اور لے آؤٹ ری فلوز کے دورانیے میں اضافہ ہو سکتا ہے جس سے صفحہ کی کارکردگی متاثر ہو گی۔ بڑے DOM سے میموری کے استعمال میں بھی اضافہ ہوگا۔ [ضرورت سے زیادہ DOM سائز سے بچنے کا طریقہ جانیں](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)۔"}, "models/trace/insights/DOMSize.ts | element": {"message": "عنصر"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "زیاد تر بچے"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "‫DOM کی گہرائی"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "اعداد و شمار"}, "models/trace/insights/DOMSize.ts | title": {"message": "‫DOM سائز کو بہتر بنائیں"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "کل عناصر"}, "models/trace/insights/DOMSize.ts | value": {"message": "قدر"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "آپ کی پہلی نیٹ ورک کی درخواست انتہائی اہم ہے۔  ری ڈائریکٹس سے گریز کرکے، سرور کے تیز ردعمل کو یقینی بنا کر اور ٹیکسٹ کمپریشن کو فعال کرکے اس کی تاخیر کو کم کریں۔"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "ری ڈائریکٹس تھے ({PH1} ری ڈائریکٹس، ‎+{PH2}‎)"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "سرور نے سست ردعمل ظاہر کیا ({PH1} تک مشاہدہ کیا)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "کوئی کمپریشن لاگو نہیں کیا گیا"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "ری ڈائریکٹس کو گریز کرتا ہے"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "سرور نے تیز ردعمل ظاہر کیا ({PH1} تک مشاہدہ کیا)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ٹیکسٹ کمپریشن کا اطلاق ہوتا ہے"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "ری ڈائریکٹس"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "سرور کے جواب کا وقت"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "دستاویز کی درخواست میں تاخیر"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "اَن کمپریسڈ ڈاؤن لوڈ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ڈپلیکیٹ شدہ بائٹس"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ما<PERSON>ذ"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "نیٹ ورک کی سرگرمی کے ذریعے استعمال ہونے والی غیر ضروری بائٹس کو کم کرنے کیلئے، بنڈلز سے بڑے اور ڈپلیکیٹ JavaScript ماڈیولز کو ہٹائیں۔"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ڈپلیکیٹ شدہ JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "[font-display](https://developer.chrome.com/blog/font-display) کو swap یا optional پر سیٹ کرنے پر غور کریں تاکہ یہ یقینی بنایا جا سکے کہ ٹیکسٹ مستقل طور پر مرئی ہے۔ [فونٹ میٹرک اوور رائیڈز](https://developer.chrome.com/blog/font-fallbacks) کے ساتھ لے آؤٹ شفٹس کو کم کرنے کے لیے مزید بہتر بنایا جا سکتا ہےswap۔"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "فونٹ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "فونٹ ڈسپلے"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "ضائع کردہ وقت"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(گمنام)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "کئی APIs، خاص طور پر لے آؤٹ جیومیٹری پڑھنا، طرز اور لے آؤٹ کو کیلکولیٹ کرنے کیلئے رینڈرنگ انجن سے اسکرپٹ پر عملدر آمد کو زبردستی موقوف کروا دیتی ہیں۔ [فورس کردہ ری فلو](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) اور اس کی تخفیفات کے بارے میں مزید جانیں۔"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "اسٹیک ٹریس"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "فورس کردہ ری فلو"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "سر فہرست فنکشن کال"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "ری فلو کا کل وقت"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[کوئی انتساب نہیں]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "تصاویر کے ڈاؤن لوڈ کے وقت کو کم کرنا صفحہ کے ظاہری لوڈ ٹائم اور LCP کو بہتر بنا سکتا ہے۔ [تصویر کے سائز کو بہتر بنانے کے بارے میں مزید جانیں](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (تخمینی {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "بہترین بنانے کے قابل کوئی تصویر نہیں ہے"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "فائل سائز کو بہتر بنائیں"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "‫{PH1} دیگر"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "تصویر کی ڈیلیوری کو بہتر بنائیں"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "تصویر کے کمپریشن فیکٹر کو بڑھانے سے اس تصویر کا ڈاؤن لوڈ سائز بہتر ہو سکتا ہے۔"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "تصویر کے جدید فارمیٹ (WebP, AVIF) کا استعمال کرنے سے یا تصویر کے کمپریشن میں اضافہ کرنے سے اس تصویر کا ڈاؤن لوڈ سائز بہتر ہو سکتا ہے۔"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "یہ امیج فائل ({PH1}) اس کے ڈسپلے کردہ ڈائمینشنز ({PH2}) کیلئے ضرورت سے زیادہ بڑی ہے۔ تصویر کے ڈاؤن لوڈ سائز کو کم کرنے کیلئے ریسپانسیو تصاویر کا استعمال کریں۔"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "‫GIFs کے بجائے ویڈیو فارمیٹس کا استعمال کرنے سے اینیمیٹڈ مواد کا ڈاؤن لوڈ سائز بہتر ہو سکتا ہے۔"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "طویل ترین مرحلہ کے ساتھ تحقیقات شروع کریں۔ [تاخیر کو کم کیا جا سکتا ہے](https://web.dev/articles/optimize-inp#optimize_interactions)۔ پروسیسنگ کا دورانیہ کم کرنے کے لیے، [اصل تھریڈ کے اخراجات کو بہتر بنائیں](https://web.dev/articles/optimize-long-tasks)، اکثر JS۔"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "دورانیہ"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ان پٹ میں تاخیر"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "کوئی تعامل نہیں ملا"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "مر<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "پیشکش کی تاخیر"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "پروسیسنگ کا دورانیہ"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "مرحلہ وار INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "‫LCP امیج کو HTML سے فوری طور پر [قابل دریافت](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) بنا کر اور [لیزی لوڈنگ سے بچ کر](https://web.dev/articles/lcp-lazy-loading) LCP کو بہتر بنائیں"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority = اعلی لاگو"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "‫fetchpriority=high کو لاگو کیا جانا چاہیے"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "لیزی لوڈ کا اطلاق نہیں کیا گیا"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "ابتدائی نقطہ آغاز کے بعد LCP تصویر {PH1} لوڈ کی گئی۔"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "کوئی LCP نہیں ملا"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "کوئی LCP وسیلہ کا پتہ نہیں چلا کیونکہ LCP کوئی تصویر نہیں ہے"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "درخواست ابتدائی دستاویز میں قابل دریافت ہے"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "‫LCP دریافت کی درخواست"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ہر [مرحلہ میں بہتری کی مخصوص حکمت عملی ہوتی ہے](https://web.dev/articles/optimize-lcp#lcp-breakdown)۔ مثالی طور پر، LCP کا زیادہ تر وقت وسائل کو لوڈ کرنے میں صرف کیا جانا چاہیے، نہ کہ تاخیر میں۔"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "دورانیہ"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "عنصر کے رینڈر میں تاخیر"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "فیلڈ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "کوئی LCP نہیں ملا"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "مر<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "وسیلہ لوڈ کرنے میں تاخیر"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "وسائل لوڈ کرنے کا دورانیہ"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "پہلی بائٹ تک لگنے والا وقت"}, "models/trace/insights/LCPPhases.ts | title": {"message": "مرحلہ وار LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "اسکرپٹ"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "ضائع کردہ بائٹس"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "پولی فِلز اور ٹرانسفارمز پرانے براؤزرز کو نئی JavaScript خصوصیات استعمال کرنے کی اجازت دیتے ہیں۔ تاہم، ان میں سے کئی جدید براؤزرز کیلئے ضروری نہیں ہیں۔ اپنے JavaScript بلڈ پروسیس میں [بیس لائن](https://web.dev/articles/baseline-and-polyfills) خصوصیات کو ٹرانسپائل نہ کرنے کیلئے ترمیم کرنے پر غور کریں، الا یہ کہ آپ جانتے ہوں کہ آپ کیلئے پرانے براؤزر کا تعاون کرنا لازمی ہے۔ [جانیں کہ کیوں زیادہ تر سائٹس ٹرانسپائل کئے بغیر ES6+‎ کوڈ تعینات کر سکتی ہیں](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "پرانا JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 اور HTTP/3 ‏HTTP/1.1 کے مقابلے میں کئی فوائد کی پیشکش کرتے ہیں جیسے ملٹی پلیکسنگ۔ [جدید HTTPS استعمال کرنے کے بارے میں مزید جانیں](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)۔"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "کسی درخواست نے HTTP/1.1 استعمال نہیں کیا"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "پروٹوکول"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "درخواست کریں"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "ج<PERSON><PERSON><PERSON> HTTPS"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "اوریجن"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "درخواست کریں"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ما<PERSON>ذ"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "وقت"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "تخمینی LCP بچت"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "غیر استعمال شدہ پہلے سے منسلک لنکس۔ چیک کریں کہ crossorigin وصف صحیح طریقے سے استعمال ہوا ہے۔"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "صفحہ لوڈ کرنے کی اہلیت کو بہتر بنانے کیلئے چینز کی طوالت کو کم کر کے، وسائل کے ڈاؤن لوڈ سائز کو کم کر کے یا غیر ضروری وسائل کی ڈاؤن لوڈ کو مؤخر کر کے [اہم درخواستوں کی چیننگ سے گریز کریں](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)۔"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "اپنے اہم ترین اوریجنز میں [پیشگی منسلک ہونے](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) کے اشارے شامل کریں، لیکن کوشش کریں کہ 4 سے زیادہ استعمال نہ کریں۔"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "پری کنیکٹ کے امیدوار"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "اہم پاتھ کی زیادہ سے زیادہ تاخیر:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "نیٹ ورک ڈیپینڈنسیز کے ذریعے کوئی رینڈرنگ ٹاسک متاثر نہیں ہوا"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "کوئی اضافی اوریجن پیشگی منسلک ہونے کیلئے اچھا امیدوار نہیں ہے"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "کوئی اوریجن پیشگی منسلک نہیں ہے"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[پیشگی منسلک ہونے](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) کے اشارے کا صفحہ لوڈ ہونے کے ابتدائی مرحلے میں کنکشن قائم کرنے میں براؤزر کی مدد کرتے ہیں، جس کی وجہ سے اُس اوریجن کیلئے پہلی درخواست کرنے پر وقت کی بچت ہوتی ہے۔ نیچے وہ اوریجنز دئے گئے ہیں جن سے صفحہ پیشگی منسلک ہے۔"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "پہلے سے منسلک ماخذات"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "نیٹ ورک ڈیپینڈنسی ٹری"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "‫4 سے زیادہ preconnect کنکشنز کا پتا چلا۔ ان کا استعمال احتیاط سے اور صرف اہم ترین اوریجنز کیلئے کیا جانا چاہیے۔"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "غیر استعمال شدہ پری کنیکٹ۔ preconnect کا استعمال صرف ان اوریجنز کیلئے کریں جن سے صفحے کے درخواست کرنے کا امکان ہو۔"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "صفحہ لوڈ کرنے کی اہلیت کو بہتر بنانے کیلئے چینز کی طوالت کو کم کر کے، وسائل کے ڈاؤن لوڈ سائز کو کم کر کے یا غیر ضروری وسائل کی ڈاؤن لوڈ کو مؤخر کر کے اہم درخواستوں کی چیننگ سے گریز کریں۔"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "درخواستیں صفحہ کے ابتدائی رینڈر کو روک رہی ہیں، جس سے LCP میں تاخیر ہو سکتی ہے۔ [موخر کرنا یا ان لائننگ کرنا](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ان نیٹ ورک کی درخواستوں کو اہم راستے سے ہٹا سکتا ہے۔"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "دورانیہ"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "اس نیویگیشن کے لیے کوئی رینڈر مسدود کرنے کی درخواستیں نہیں ہیں"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "درخواست کریں"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "مسدود کرنے کی درخواستیں رینڈر کریں"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "اگر ریکیلکیولیٹ اسٹائل کے اخراجات زیادہ رہتے ہیں تو منتخب کنندہ کی بہتری انہیں کم کر سکتی ہے۔ زیادہ گزرا ہوا وقت اور زیادہ سلو پاتھ کا % دونوں کے ساتھ [منتخب کنندگان کو بہتر بنائیں۔](https://developer.chrome.com/docs/devtools/performance/selector-stats) آسان منتخب کنندگان، کم منتخب کنندگان، ایک چھوٹا DOM، اور ہلکا DOM سبھی مماثل اخراجات کو کم کر دیں گے۔"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "گزرا ہوا وقت"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "کوئی CSS منتخب کنندہ کا ڈیٹا نہیں ملا۔ کارکردگی پینل کی ترتیبات میں CSS منتخب کنندہ کے اعداد و شمار کو فعال کرنے کی ضرورت ہے۔"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "مماثلت کی کوششیں"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "مماثلت کی تعداد"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "‫CSS منتخب کنندہ کے اخراجات"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "سر فہرست منتخب کنندگان"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "کُل"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "اصل تھریڈ کا وقت"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "فریق ثالث"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ٹرانسفر کا سائز"}, "models/trace/insights/ThirdParties.ts | description": {"message": "فریق ثالث کوڈ لوڈ کی کارکردگی کو نمایاں طور پر متاثر کر سکتا ہے۔ اپنے صفحے کے مواد کو ترجیح دینے کیلئے، [فریق ثالث کوڈ کی لوڈنگ کو کم اور موخر کریں](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)۔"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "کوئی فریق ثالث نہیں ملا"}, "models/trace/insights/ThirdParties.ts | title": {"message": "فریقین ثالث"}, "models/trace/insights/Viewport.ts | description": {"message": "اگر ویوپورٹ موبائل کے لیے بہتر کردہ نہیں ہے تو تھپتھپاہٹ کے تعاملات میں [‎300 ms تک کی تاخیر](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ہو سکتی ہے۔"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "موبائل تھپتھپانے میں تاخیر"}, "models/trace/insights/Viewport.ts | title": {"message": "موبائل کے لیے ویوپورٹ کو بہتر بنائیں"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "صرف GET درخواست کے ذریعے لوڈ کردہ صفحات ہی بیک/فارورڈ کیش کیلئے اہل ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "صرف 2XX کے اسٹیٹس کوڈ والے صفحات ہی کیش کئے جا سکتے ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome کو JavaScript کے کیش میں رہتے ہوئے اسے رو بہ عمل لانے کی کوشش کا پتا چلا۔"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner کی درخواست کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "جھنڈوں نے بیک/فارورڈ کیش کو غیر فعال کر دیا ہے۔ اس آلے پر اسے مقامی طور پر فعال کرنے کیلئے chrome://flags/#back-forward-cache ملاحظہ کریں۔"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "کمانڈ لائن نے بیک/فارورڈ کیش کو غیر فعال کر دیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "ناکافی میموری ہونے کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "بیک/فارورڈ کیش ڈیلیگیٹ کے ذریعے تعاون یافتہ نہیں ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "پری رینڈرر کیلئے بیک/فارورڈ کیش غیر فعال ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "صفحے کو کیش نہیں کیا جا سکتا کیونکہ اس میں رجسٹرڈ سامعین والا ایک BroadcastChannel نمونہ موجود ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store ہیڈر کے حامل صفحات بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "کیش کو جان بوجھ کر صاف کیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "دوسرے صفحے کو کیش ہونے کی اجازت دینے کی غرض سے صفحہ بے دخل ہو گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "پلگ انز پر مشتمل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON>ر متعینہ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "فائل سسٹم ایکسیس API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "میڈیا آلہ ڈسپیچر استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "صفحے سے دور نیویگیٹ کرنے پر ایک میڈیا پلیئر چل رہا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API استعمال کرنے اور پلے بیک اسٹیٹ سیٹ کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API استعمال کرنے اور ایکشن ہینڈلر سیٹ کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "اسکرین قاری کی وجہ سے بیک/فارورڈ کیش غیر فعال ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API استعمال کرنے والے صفحات بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "بیک فارورڈ کیش غیر فعال ہے کیونکہ کوکیز Cache-Control: no-store استعمال کرنے والے صفحات پر غیر فعال ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "ایک وقف کردہ ورکر یا ورک لیٹ استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "صفحے سے دور نیویگیٹ کرنے سے پہلے دستاویز کے لوڈ ہونے کا عمل پورا نہیں ہوا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "دور نیویگیٹ کرنے پر ایپ بینر موجود تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "دور نیویگیٹ کرنے پر Chrome پاس ورڈ مینیجر موجود تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "دور نیویگیٹ کرنے پر DOM کی کشید جاری تھی۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "دور نیویگیٹ کرنے پر DOM ڈسٹلر ناظر موجود تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "پیغام رسانی API کا استعمال کر کے ایکسٹینشنز کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "دیرپا کنکشن والی ایکسٹینشنز کو بیک/فارورڈ کیش میں داخل ہونے سے پہلے کنکشن کو بند کر دینا چاہیے۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "دیرپا کنکشن والی ایکسٹینشنز نے فریموں کو بیک/فارورڈ کیش میں پیغامات بھیجنے کی کوشش کی۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "ایکسٹینشنز کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ماڈل ڈائیلاگ جیسے کہ فارم دوبارہ جمع کرانا یا http پاس ورڈ ڈائیلاگ دور نیویگیٹ کرنے پر صفحہ کیلئے دکھایا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "آف لائن صفحہ دور نیویگیٹ کرنے پر دکھایا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "دور نیویگیٹ کرنے پر آؤٹ آف میموری انٹروینشن بار موجود تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "دور نیویگیٹ کرنے پر اجازت کی درخواستیں تھیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "دور نیویگیٹ کرنے پر پاپ اپ بلاکر موجود تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "دور نیویگیٹ کرنے پر محفوظ براؤزنگ کی تفصیلات دکھائی گئیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "محفوظ براؤزنگ نے اس صفحہ کو ناگوار اور مسدود کردہ پاپ اپ سمجھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "صفحے کے بیک/فارورڈ کیش میں ہونے کے دوران ایک سروس ورکر فعال کیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "دستاویز میں خرابی کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames استعمال کرنے والے صفحات کو bfcache میں اسٹور نہیں کیا جا سکتا۔"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "دوسرے صفحے کو کیش ہونے کی اجازت دینے کی غرض سے صفحہ بے دخل ہو گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "میڈیا کے سلسلے تک رسائی فراہم کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "ایسے صفحات جن میں مخصوص قسم کا سرایت شدہ مواد ہے (جیسے PDFs) فی الحال بیک/فارورڈ کیش کے لیے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "اوپن IndexedDB کنکشن کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB کی ایونٹ کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا۔"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "نااہل APIs کا استعمال کیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "ایسے صفحات جن میں ایکسٹینشنز کے ذریعے JavaScript انجیکٹ کیا گیا ہے، وہ فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "ایسے صفحات جن میں ایکسٹینشنز کے ذریعے StyleSheet انجیکٹ کیا گیا ہے، وہ فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "داخلی خرابی۔"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "بیک/فارورڈ کیش غیر فعال ہے کیونکہ کچھ JavaScript نیٹ ورک کی درخواست کو Cache-Control: no-store ہیڈر کے ساتھ ماخذ موصول ہوا۔"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "keepalive کی درخواست کی وجہ سے بیک/فارورڈ کیش کو غیر فعال کر دیا گیا۔"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "کی بورڈ لاک استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "صفحے سے دور نیویگیٹ کرنے سے پہلے صفحے کے لوڈ ہونے کا عمل پورا نہیں ہوا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ایسے صفحات جن کے مرکزی وسائل میں cache-control:no-cache ہے، وہ بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ایسے صفحات جن کے مرکزی وسائل میں cache-control:no-store ہے، وہ بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "قبل اس کے کہ صفحے کو بیک/فارورڈ کیش سے بحال کیا جائے، نیویگیشن کو منسوخ کر دیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "ایک فعال نیٹ ورک کنکشن کے ڈھیر سارا ڈیٹا وصول کرنے کی وجہ سے صفحہ کیش سے بے دخل ہو گیا تھا۔ Chrome اس بات کو محدود کرتا ہے کہ ایک صفحہ کیشڈ ہونے کے دوران کتنا ڈیٹا وصول کر سکتا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "اِن فلائٹ ‎Fetch()‎ یا XHR کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "ایک فعال نیٹ ورک کی درخواست میں ری ڈائریکٹ شامل ہونے کی وجہ سے صفحہ بیک/فارورڈ کیش سے بے دخل ہو گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ایک نیٹ ورک کنکشن کے زیادہ دیر تک کھلے رہنے کی وجہ سے صفحہ کیش سے بے دخل ہو گیا تھا۔ ایک صفحہ کیشڈ ہونے کے دوران کتنے وقت تک ڈیٹا وصول کر سکتا ہے، Chrome اس وقت کو محدود کرتا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "ایسے صفحات جن کے پاس درست ریسپانس ہیڈ نہیں ہے، وہ بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "نیویگیشن کا عمل مرکزی فریم کے علاوہ کسی دوسرے فریم میں واقع ہوا۔"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "جاری انڈیکسڈ DB ٹرانزیکشنز کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "اِن فلائٹ نیٹ ورک کی درخواست کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "اِن فلائٹ Fetch نیٹ ورک کی درخواست کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "اِن فلائٹ نیٹ ورک کی درخواست کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "اِن فلائٹ XHR نیٹ ورک کی درخواست کے حامل صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "'تصویر میں تصویر' استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "پرنٹنگ UI دکھانے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "صفحے کو window.open() کا استعمال کر کے کھولا گیا تھا اور دوسرے ٹیب میں اس کا حوالہ موجود تھا یا صفحے نے کوئی ونڈو کھولی تھی۔"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "بیک/فارورڈ کیش میں صفحے کیلئے رینڈرر کا پروسیس ناکام ہو گیا۔"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "بیک/فارورڈ کیش میں صفحے کیلئے رینڈرر کے پروسیس کو ختم کر دیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ایسے صفحات جنہوں نے آڈیو کیپچر کرنے کی اجازتوں کی درخواست کی ہے، وہ فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "سینسر کی اجازتوں کی درخواست کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "ایسے صفحات جنہوں نے پس منظر سِنک یا اجازتیں حاصل کرنے کی درخواست دی ہے، وہ فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI کی اجازتوں کی درخواست کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "اطلاعات کی اجازتوں کی درخواست کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "اسٹوریج تک رسائی کی درخواست کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "ایسے صفحات جنہوں نے ویڈیو کیپچر کرنے کی اجازتوں کی درخواست کی ہے، وہ فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "صرف ایسے صفحات کو کیش کیا جا سکتا ہے جن کی URL اسکیم HTTP / HTTPS ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "کسی سروس ورکر نے اس صفحے پر اس وقت دعوی کیا تھا جب یہ بیک/فارورڈ کیش میں تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "ایک سروس ورکر نے MessageEvent کو ایک ایسے صفحے پر بھیجنے کی کوشش کی جو بیک/فارورڈ کیش میں ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ایک صفحے کے بیک/فارورڈ کیش میں رہنے کے دوران ServiceWorker کا رجسٹریشن ختم کر دیا گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "ایک سروس ورکر کی فعالیت کی وجہ سے صفحہ بیک/فارورڈ کیش سے بے دخل ہو گیا تھا۔"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome دوبارہ شروع ہوا اور اس نے بیک/فارورڈ کیش کی مندرجات صاف کر دئے۔"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "صفحے پر موجود iframe نے ایک ایسی نیویگیشن شروع کی جو مکمل نہیں ہوئی۔"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "ایسے صفحات جن کے ذیلی وسائل میں cache-control:no-cache ہے، وہ بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "ایسے صفحات جن کے ذیلی وسائل میں cache-control:no-store ہے، وہ بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "صفحہ بیک/فارورڈ کیش میں زیادہ سے زیادہ وقت سے تجاوز کر گیا اور اس کی میعاد ختم ہو گئی تھی۔"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "بیک/فارورڈ کیش درج کرتے وقت صفحہ ٹائم آؤٹ ہو گیا (ممکنہ طور پر ایسا دیر تک چلنے والے پیج ہائیڈ ہینڈلرز کی وجہ سے ہوا ہو)۔"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "صفحے کے مرکزی فریم میں ایک ان لوڈ ہینڈلر ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "صفحے کے ذیلی فریم میں ایک ان لوڈ ہینڈلر ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "براؤزر نے صارف کے ایجنٹ کے اوور رائیڈ ہیڈر کو تبدیل کر دیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "ویڈیو یا آڈیو ریکارڈ کرنے کی سہولت تک رسائی فراہم کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService استعمال کرنے والے صفحات فی الوقت bfcache کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC کے حامل صفحات بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "بیک/فارورڈ کیش غیر فعال ہے کیونکہ WebRTC کا استعمال کیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket کے حامل صفحات بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "بیک/فارورڈ کیش غیر فعال ہے کیونکہ WebSocket کا استعمال کیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport کے حامل صفحات بیک/فارورڈ کیش درج نہیں کر سکتے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "بیک/فارورڈ کیش غیر فعال ہے کیونکہ WebTransport کا استعمال کیا گیا ہے۔"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR استعمال کرنے والے صفحات فی الوقت بیک/فارورڈ کیش کیلئے اہل نہیں ہیں۔"}}