{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Авторизация не будет выполняться, если в заголовке CORS Access-Control-Allow-Headers вместо необходимых данных указан подстановочный знак (*)."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Чтобы отключить интеграцию Google Cast по умолчанию, вместо селектора -internal-media-controls-overlay-cast-button используйте атрибут disableRemotePlayback."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Значение CSS slider-vertical не стандартизировано и будет удалено."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Запросы ресурсов, в URL которых содержатся и удаленные пробельные символы (\\(n|r|t)), и знаки \"меньше\" (<), блокируются. Чтобы загружать такие ресурсы, удалите символы новой строки и используйте коды для знаков \"меньше\" в таких местах URL, где, например, указываются значения атрибутов элементов."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Метод chrome.loadTimes() больше не поддерживается. Вместо него используйте стандартный API Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "API chrome.loadTimes() больше не поддерживается. Вместо него используйте стандартный API Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Метод chrome.loadTimes() больше не поддерживается. Вместо него используйте стандартный API nextHopProtocol в Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Файлы cookie с символом \\(0|r|n) не обрезаются, а отклоняются."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Уменьшать требования правила ограничения источника за счет изменения document.domain не рекомендуется. По умолчанию эта возможность будет отключена. Это предупреждение о прекращении поддержки относится к доступу из другого источника, который включен с помощью document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Вызов функции window.alert из окон iframe в других источниках не поддерживается. В дальнейшем эта возможность будет удалена."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Вызов функции window.confirm из окон iframe в других источниках не поддерживается. В дальнейшем эта возможность будет удалена."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "URL из SVGUseElement больше не поддерживаются и в дальнейшем будут удалены."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Методы getCurrentPosition() и watchPosition() больше не работают с небезопасными источниками. Чтобы использовать их, задайте для приложения безопасный источник, например HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Методы getCurrentPosition() и watchPosition() в небезопасных источниках больше не поддерживаются. Чтобы использовать их, задайте для приложения безопасный источник, например HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "Метод getUserMedia() больше не работает с небезопасными источниками. Чтобы использовать его, задайте для приложения безопасный источник, например HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "В элементе <article>, <aside>, <nav> или <section> найден тег <h1>, для которого не указан размер шрифта. Размер этого заголовка в текущем браузере изменится в будущем. Подробнее: https://developer.mozilla.org/ru/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate больше не поддерживается. Используйте RTCPeerConnectionIceErrorEvent.address или RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Текущий формат запроса navigator.credentials.get() для получения цифровых учетных данных устарел. Используйте в параметрах вызова новый формат."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Страна продавца и случайные данные из события service workercanmakepayment устарели и будут удалены: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Сайт запросил подресурс из сети, доступ к которому можно получить только благодаря расширенным сетевым разрешениям пользователей. Такие запросы делают внутренние устройства и серверы доступными через интернет, что повышает риск подделки межсайтовых запросов (CSRF) и утечки информации. Чтобы предотвратить такие ситуации, в Chrome прекращается поддержка запросов к внутренним подресурсам, которые инициируются в небезопасных контекстах. В будущем такие запросы будут блокироваться."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Поле dailyUpdateUrl в структуре InterestGroups, переданное в joinAdInterestGroup(), было переименовано в updateUrl, чтобы лучше отражать его поведение."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator больше не поддерживается. Вместо него используйте Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Загрузить CSS с помощью URL в формате file: можно, только если у имени файла расширение .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Использовать метод SourceBuffer.abort() для отмены асинхронного удаления диапазона, выполняемого в remove(), не рекомендуется из-за изменений в стандарте. В будущем поддержка этого метода будет прекращена. Руководствуйтесь событием updateend. Метод abort() предназначен только для отмены асинхронного добавления медиаданных или сброса состояния синтаксического анализатора."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Устанавливать для MediaSource.duration значение меньше максимальной временной метки в воспроизводимых медиаданных для любых закодированных фреймов в буфере не рекомендуется из-за изменений стандарта. В будущем поддержка неявного удаления обрезанных медиаданных в буфере будет прекращена. Вместо этого придется явно выполнять метод remove(newDuration, oldDuration) для всех объектов sourceBuffers, для которых newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI запрашивает разрешение на использование, даже если в объекте MIDIOptions не указан параметр sysex."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Использовать Notification API из небезопасных источников больше нельзя. Укажите для своего приложения безопасный источник, например HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Из окон iframe в других источниках больше нельзя запрашивать разрешение на использование Notification API. Запросите разрешение у фрейма верхнего уровня или откройте новое окно."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Для метода createImageBitmap свойство {imageOrientation: 'none'} больше не поддерживается. Используйте createImageBitmap со свойством \"{imageOrientation: 'from-image'}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Ваш партнер обменивается данными с использованием устаревшей версии (D)TLS. Сообщите ему о необходимости это исправить."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Если назначить свойство overflow: visible для элементов img, video или canvas, их визуальный контент может выйти за пределы границ. Подробности: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments больше не поддерживается. Используйте актуальный API для обработки платежей."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Вызов PaymentRequest API нарушает директиву Content-Security-Policy (CSP) connect-src. Этот метод обхода директивы устарел. Добавьте идентификатор способа оплаты из PaymentRequest API в вашу директиву CSP connect-src, в поле supportedMethods."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent больше не поддерживается. Используйте стандартный метод navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Элемент <source src> с родительским объектом <picture> недействителен, поэтому игнорируется. Используйте <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Метод webkitCancelAnimationFrame связан с определенным поставщиком. Используйте стандартный метод cancelAnimationFrame вместо него."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Метод webkitRequestAnimationFrame связан с определенным поставщиком. Используйте стандартный метод requestAnimationFrame вместо него."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Метод HTMLVideoElement.webkitDisplayingFullscreen больше не поддерживается. Используйте Document.fullscreenElement вместо него."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Метод HTMLVideoElement.webkitEnterFullScreen() больше не поддерживается. Используйте Element.requestFullscreen() вместо него."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Метод HTMLVideoElement.webkitEnterFullscreen() больше не поддерживается. Используйте Element.requestFullscreen() вместо него."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Метод HTMLVideoElement.webkitExitFullScreen() больше не поддерживается. Используйте Document.exitFullscreen() вместо него."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Метод HTMLVideoElement.webkitExitFullscreen() больше не поддерживается. Используйте Document.exitFullscreen() вместо него."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Метод HTMLVideoElement.webkitSupportsFullscreen больше не поддерживается. Используйте Document.fullscreenEnabled вместо него."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Мы прекращаем поддержку chrome.privacy.websites.privacySandboxEnabled API. Он будет работать в рамках обратной совместимости до выпуска M113. Вместо этого API используйте chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled и chrome.privacy.websites.adMeasurementEnabled. Подробнее: https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ограничение DtlsSrtpKeyAgreement удалено. Вы указали для него значение false, которое интерпретируется как попытка использовать неподдерживаемый метод SDES key negotiation. Эта возможность удалена. Вместо нее используйте сервис, поддерживающий DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ограничение DtlsSrtpKeyAgreement удалено. Вы указали для него значение true, которое ни на что не влияет, поэтому его можно удалить."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Callback-функция getStats() больше не поддерживается и будет удалена. Используйте соответствующую спецификации функцию getStats() вместо нее."}, "generated/Deprecation.ts | RangeExpand": {"message": "Метод Range.expand() больше не поддерживается. Используйте Selection.modify() вместо него."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Запросы подресурсов, в URL которых содержатся встроенные учетные данные (например, **********************/), блокируются."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Параметр rtcpMuxPolicy больше не поддерживается и будет удален."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "Для SharedArrayBuffer требуется изоляция от междоменных источников. Подробнее: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Вызов метода speechSynthesis.speak() без активации пользователем не поддерживается. В дальнейшем эта возможность будет удалена."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Прослушиватели событий выгрузки устарели и будут удалены."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Теперь, чтобы использовать объект SharedArrayBuffer, в расширениях необходимо включить изоляцию от междоменных источников. Подробнее: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Атрибут GPUAdapter isFallbackAdapter устарел. Используйте вместо него атрибут GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "В объекте JSON ответа XMLHttpRequest не поддерживает UTF-16."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхронные вызовы метода XMLHttpRequest в основном потоке больше не поддерживаются, так как они отрицательно влияют на удобство работы пользователей. Подробнее: https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимация"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Смещение макета происходит, когда элементы передвигаются без взаимодействия с пользователем. Это может быть связано с добавлением и удалением элементов или изменением их шрифтов при загрузке страницы. [Выясните причины сдвигов.](https://web.dev/articles/optimize-cls)"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Запрос шрифта"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Внедрение окна iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Кластер смещения макета (начало: {PH1})"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Не удалось выявить причины смещений макета"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Смещений макета не обнаружено"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Причины смещения макета"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Основные причины смещения макета"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> кластер"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Х<PERSON><PERSON><PERSON><PERSON> кластер смещения макета"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Время жизни кеша"}, "models/trace/insights/Cache.ts | description": {"message": "Благодаря долгому времени хранения кеша страница может быстрее загружаться при повторных посещениях. [Подробнее…](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Нет запросов с неэффективными правилами кеширования"}, "models/trace/insights/Cache.ts | others": {"message": "Ещё {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Запрос"}, "models/trace/insights/Cache.ts | title": {"message": "Выбирайте эффективный период хранения кеша"}, "models/trace/insights/DOMSize.ts | description": {"message": "Из-за большого размера дерева DOM может замедляться расчет стиля и компоновка макета. Это повлияет на скорость отклика страницы и увеличит объем используемой памяти. Узнайте, [как уменьшить размер дерева DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Элемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Больше всего дочерних элементов"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Глубина DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистические показатели"}, "models/trace/insights/DOMSize.ts | title": {"message": "Оптимизация размера DOM-дерева"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Всего элементов"}, "models/trace/insights/DOMSize.ts | value": {"message": "Значение"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Ваш первый сетевой запрос – самый важный.  Уменьшайте задержку, избегая переадресации, ускоряя ответ сервера и используя сжатие текста."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Зафиксированы переадресации ({PH1}, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер отвечает медленно. Фактическая скорость – {PH1}."}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Без сжатия"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Не используются переадресации"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер отвечает быстро. Фактическая скорость – {PH1}."}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Применяется сжатие текста"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Переадресации"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Время ответа сервера"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Задержка при запросе документа"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Скачивание несжатого файла"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Дублирующиеся байты"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Источник"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Чтобы сократить расход трафика, удалите из пакетов большие повторяющиеся модули JavaScript."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Дублирующийся код JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Рекомендуем использовать свойство [font-display](https://developer.chrome.com/blog/font-display) со значением swap или optional, чтобы весь текст был одинаково виден. Значение swap можно дополнительно оптимизировать, чтобы с помощью [переопределения показателей шрифта](https://developer.chrome.com/blog/font-fallbacks) уменьшить смещения макета."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON>ри<PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Используемые шрифты"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Потери времени"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(анонимная)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Многие API, прежде всего считывающие геометрию макета, принуждают движок отрисовки приостановить выполнение скриптов, чтобы рассчитать стиль и макет страницы. Подробнее [о принудительной компоновке и способах ее избежать](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)…"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Трассировка стека"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Принудительная компоновка"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Самый времязатратный вызов функции"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Общее время компоновки"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[без атрибуции]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Если изображения будут быстрее скачиваться, то время загрузки страницы и значение LCP могут стать меньше. Узнайте, [как оптимизировать размер изображений](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (около {PH2})."}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Изображения нельзя оптимизировать"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Оптимизация размера файла"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Ещё {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Улучшите загрузку изображений"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Если увеличить коэффициент сжатия изображения, размер файла будет меньше."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Если использовать современный графический формат (WebP или AVIF) или увеличить коэффициент сжатия изображения, размер файла будет меньше."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Размер изображения ({PH1}) превышает размер контейнера ({PH2}). Используйте адаптивный стиль, чтобы размер графического файла был меньше."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Если использовать не GIF, а видеоформаты, размер файла с анимированным контентом будет меньше."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Начните анализ с самого длинного этапа. Задержки [можно уменьшить](https://web.dev/articles/optimize-inp#optimize_interactions). Чтобы сократить продолжительность обработки, [оптимизируйте затраты основного потока](https://web.dev/articles/optimize-long-tasks). Обычно они относятся к JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Продолжительность"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Задержка ввода"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Взаимодействия не обнаружены"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Эта<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Задержка вывода ответа на экран"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Длительность обработки"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP по этапам"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Оптимизируйте LCP, сделав возможным [находить](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) самое крупное изображение прямо из HTML-кода. Также советуем [избегать отложенной загрузки](https://web.dev/articles/lcp-lazy-loading)."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Свойству fetchpriority присвоено значение \"high\""}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Требуется значение fetchpriority=high."}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "Не удалось применить отложенную загрузку."}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Время загрузки самого крупного изображения: {PH1}."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Значение LCP не обнаружено"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Ресурс LCP не найден, так как с ним связано не изображение"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Запрос можно найти в исходном документе"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Информация об LCP и связанных запросах"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Для каждого этапа [есть стратегии улучшения](https://web.dev/articles/optimize-lcp#lcp-breakdown). В идеале большая часть времени, требуемого на полную отрисовку элементов LCP, должна тратиться на загрузку ресурсов, а не на задержки."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Продолжительность"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Задержка при отрисовке элемента"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75-й процентиль данных"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Значение LCP не обнаружено"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Эта<PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Задержка загрузки ресурса"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Продолжительность загрузки ресурса"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP по этапам"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипт"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Потерянные байты"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Полифилы и преобразования позволяют работать с новыми возможностями JavaScript в более старых браузерах. Однако для современных браузеров большинство из них не требуется. Если поддержка более старых браузеров не требуется, возможно стоит изменить процесс сборки JavaScript, чтобы не транспилировать функции [Baseline](https://web.dev/articles/baseline-and-polyfills). Узнайте, [почему большинство сайтов могут использовать код ES6+ без транспилирования](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)."}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Устаревший код JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 и HTTP/3 имеют много преимуществ перед HTTP/1.1, например мультиплексирование. Подробнее [о современных версиях HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)…"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Нет запросов с использованием HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Запрос"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Современная версия HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Источник"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Запрос"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Источник"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Время"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Приблизительная экономия LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Неиспользуемое предварительное подключение. Убедитесь, что атрибут crossorigin настроен правильно."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Старайтесь избегать цепочек критических запросов.](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) Чтобы ускорить загрузку страницы, сократите количество запросов, уменьшите размер скачиваемых ресурсов или отложите скачивание ненужных ресурсов."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Добавьте к самым значимым источникам подсказки для [предварительного подключения](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/), но не более четырех."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Варианты предварительного подключения"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Максимальная задержка критического пути:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Нет задач отрисовки, на которые повлияло дерево зависимостей в сети."}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Отсутствуют дополнительные источники и хорошие варианты для предварительного подключения"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "нет предварительно подключенных источников"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Подсказки [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) помогают браузеру установить соединение на более ранних этапах загрузки страницы, что экономит время при первом запросе к источнику. Далее перечислены источники, к которым предварительно подключилась страница."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Предварительно подключенные источники"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Дерево зависимостей в сети"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Обнаружено больше четырех предварительно подключенных ссылок типа preconnect. Используйте их умеренно и только для наиболее значимых источников."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Неиспользуемое предварительное подключение. Применяйте preconnect только для источников, которые будут запрашиваться с наибольшей вероятностью."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Избегайте цепочек критических запросов. Чтобы ускорить загрузку страниц, рекомендуется сократить количество запросов, уменьшить размер скачиваемых ресурсов или отложить скачивание ненужных ресурсов."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Запросы блокируют обработку при первоначальной загрузке страницы. Это может привести к увеличению значения LCP. [Отсрочка или встраивание](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) могут избавить от негативного воздействия этих сетевых запросов."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Продолжительность"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "За<PERSON><PERSON><PERSON><PERSON><PERSON>, блокирующих обработку страницы, не обнаружено"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Запрос"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Запросы, блокирующие отрисовку страницы"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Если затраты на повторный расчет стиля остаются высокими, оптимизация селектора может их снизить. [Оптимизируйте селекторы](https://developer.chrome.com/docs/devtools/performance/selector-stats) с большим временем выполнения и высоким процентом медленного пути. Затраты на сопоставление можно снижать, используя более простые селекторы и сокращая их количество, а также за счет более компактной структуры DOM."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Прошедшее время"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Данные селектора CSS не обнаружены. Включите этот параметр в настройках панели производительности."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Попытки найти совпадение"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Совпадения"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Затраты на селектор CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Основные селекторы"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Всего"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Время выполнения в основном потоке"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Сторонний поставщик"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Объем передаваемых данных"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Сторонний код может сильно замедлить загрузку страниц сайта. [Сократите и отсрочьте загрузку стороннего кода,](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) чтобы ускорить показ своего контента."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Сторонний контент не найден"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Сторонний код"}, "models/trace/insights/Viewport.ts | description": {"message": "Если область просмотра не оптимизирована для мобильных устройств, при касании экрана возможна [задержка до 300 мс](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Задержка нажатия"}, "models/trace/insights/Viewport.ts | title": {"message": "Оптимизация области просмотра для мобильных устройств"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "В возвратный кеш можно добавить только страницы, загруженные с помощью GET-запроса."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Можно кешировать только страницы с кодом статуса 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome обнаружил попытку выполнить код JavaScript в кеше."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Страницы, которые запросили AppBanner, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Возвратный кеш отключен в разделе экспериментальных параметров Chrome. Введите chrome://flags/#back-forward-cache в адресной строке, чтобы включить кеш на этом устройстве."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Возвратный кеш отключен с помощью командной строки."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Возвратный кеш отключен, так как недостаточно памяти."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Возвратный кеш не поддерживается представителем."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Возвратный кеш отключен для системы предварительного отображения."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Страницу нельзя кешировать, так как у нее есть экземпляр BroadcastChannel с зарегистрированными прослушивателями."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Страницы с заголовком cache-control:no-store нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кэш был намеренно очищен."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Страница удалена из кеша, чтобы освободить место для другой страницы."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Страницы с плагинами в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Нет данных"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Страницы, которые используют FileChooser API, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Страницы, которые используют File System Access API, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Страницы, которые используют диспетчер медиаустройств, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Медиапроигрыватель воспроизводил контент во время перехода на другую страницу."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Страницы, которые используют MediaSession API и установили состояние воспроизведения, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Страницы, которые используют MediaSession API и для которых настроены обработчики действий, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Возвратный кеш отключен, так как работает программа чтения с экрана."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Страницы, которые используют SecurityHandler, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Страницы, которые используют Serial API, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Страницы, которые используют WebAuthetication API, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Страницы, которые используют WebBluetooth API, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Страницы, которые используют WebUSB API, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Возвратный кеш отключен, так как на странице, использующей Cache-Control: no-store, отключены файлы cookie."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Страницы, которые используют Dedicated Worker или Worklet, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Переход со страницы выполнен до завершения загрузки документа."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "При переходе на другую страницу был показан баннер приложения."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "При переходе на другую страницу работал Менеджер паролей Chrome."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "При переходе на другую страницу выполнялся процесс DOM Distiller."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "При переходе на другую страницу работало средство просмотра DOM Distiller."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Возвратный кеш отключен, так как расширения используют API для обмена сообщениями."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Расширения должны закрывать долговременные подключения перед записью возвратного кеша."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Расширения с долговременными подключениями попытались отправлять сообщения в фреймы в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Возвратный кеш отключен из-за работы расширений."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "При переходе на другую страницу было показано модальное диалоговое окно (например, связанное с повторной отправкой формы или паролем в протоколе HTTP)."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "При переходе на другую страницу была показана ее офлайн-версия."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "При переходе на другую страницу была показана строка с сообщением о нехватке памяти."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "При переходе на другую страницу обнаружены запросы разрешений."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "При переходе на другую страницу работал блокировщик всплывающих окон."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "При переходе на другую страницу были показаны данные Безопасного просмотра."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Сервис \"Безопасный просмотр\" заблокировал всплывающее окно, так как страница содержит недопустимый контент."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Скрипт Service Worker был активирован, когда страница находилась в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Возвратный кеш отключен из-за ошибки документа."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Страницы, на которых используется элемент FencedFrames, нельзя сохранить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Страница удалена из кеша, чтобы освободить место для другой страницы."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Страницы, которые предоставили доступ к трансляции мультимедиа, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Страницы с определенными типами встроенного контента (например, PDF-файлами) пока не поддерживают возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Страницы, которые используют IdleManager, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Страницы с открытым подключением IndexedDB в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Возвратный кеш отключен из-за события IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Использовались недопустимые API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Страницы, в которые с помощью расширений внедрен JavaScript, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Страницы, в которые с помощью расширений внедрена StyleSheet, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Внутренняя ошибка."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Возвратный кеш отключен, так как в ответ на сетевой запрос JavaScript был получен ресурс с заголовком Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Возвратный кеш отключен из-за запроса keepalive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Страницы, которые используют блокировку клавиатуры, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Переход со страницы выполнен до завершения ее загрузки."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Страницы, в основном ресурсе которых есть заголовок cache-control:no-cache, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Страницы, в основном ресурсе которых есть заголовок cache-control:no-store, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Страницу не удалось восстановить из возвратного кеша до отмены перехода."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Страница удалена из кеша, так как через активное сетевое подключение было получено слишком много данных. Chrome ограничивает объем данных, передаваемых на кешируемую страницу."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Страницы, которые используют событие inflight fetch() или интерфейс XHR, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Страница удалена из возвратного кеша, так как активный сетевой запрос был выполнен с переадресацией."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Страница удалена из кеша, так как подключение к сети было открыто слишком долго. Chrome ограничивает время получения данных кешируемой страницей."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Страницы без действительного заголовка ответа нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Переход выполнен в фрейме, отличном от основного."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Страни<PERSON><PERSON>, на которой выполняются транзакции индексированной базы данных, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Страницы с активным сетевым запросом в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Страницы с активным сетевым запросом на извлечение в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Страницы с активным сетевым запросом в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Страницы с активным сетевым запросом XHR в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Страницы, которые используют PaymentManager, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Страницы, которые используют картинку в картинке, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Страницы, где показан интерфейс печати, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Страница открыта с помощью window.open(), и на нее ссылается другая вкладка, или страница открыта в другом окне."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Процесс отрисовки страницы завершился ошибкой в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Процесс отрисовки для страницы в возвратном кеше остановлен."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Страницы, которые запросили разрешения на запись аудио, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Страницы, запросившие разрешения на доступ к датчикам, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Страницы, которые запросили разрешения на фоновую синхронизацию или извлечение, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Страницы, запросившие разрешения на доступ к MIDI-устройствам, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Страницы, запросившие разрешения на доступ к уведомлениям, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Страницы, запросившие доступ к хранилищу, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Страницы, которые запросили разрешения на запись видео, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Можно кешировать только страницы со схемой URL HTTP или HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Страница была запрошена скриптом Service Worker, когда находилась в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Скрипт Service Worker пытался отправить свойство MessageEvent на страницу в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Регистрация Service Worker была отменена, когда страница находилась в возвратном кеше."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Страница удалена из возвратного кеша из-за активации Service Worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Браузер Chrome перезапущен. Все записи возвратного кеша удалены."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Страницы, которые используют SharedWorker, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Страницы, которые используют SpeechRecognizer, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Страницы, которые используют SpeechSynthesis, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Окно iframe на странице запустило переход, который не завершился."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Страницы, в подресурсе которых есть заголовок cache-control:no-cache, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Страницы, в подресурсе которых есть заголовок cache-control:no-store, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Время нахождения страницы в возвратном кеше истекло, так как превысило максимально допустимое."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Время добавления страницы в возвратный кеш истекло (возможно, обработчики событий pagehide выполняются слишком долго)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "В основном фрейме страницы есть обработчик выгрузки."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "В субфрейме страницы есть обработчик выгрузки."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Браузер изменил заголовок переопределения агента пользователя."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Страницы, которые предоставили право на запись видео или аудио, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Страницы, которые используют WebDatabase, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Страницы, которые используют WebHID, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Страницы, которые используют WebLocks, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Страницы, которые используют WebNFC, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Страницы, которые используют WebOTPService, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Страницы, которые используют WebRTC, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Возвратный кеш отключен, потому что используется WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Страницы, которые используют WebShare, в настоящее время нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Страницы, которые используют интерфейс WebSocket, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Возвратный кеш отключен, потому что используется WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Страницы, которые используют WebTransport, нельзя добавить в возвратный кеш."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Возвратный кеш отключен, потому что используется WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Страницы, которые используют WebXR, в настоящее время нельзя добавить в возвратный кеш."}}