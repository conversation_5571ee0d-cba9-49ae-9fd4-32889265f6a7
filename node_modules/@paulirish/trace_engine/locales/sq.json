{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Autorizimi nuk do të mbulohet nga simboli gjithëpërfshirës (*) në trajtimin e CORS Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Duhet të përdoret atributi disableRemotePlayback për të çaktivizuar integrimin e parazgjedhur të Cast në vend të përdorimit të përzgjedhësit -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Vlera e paraqitjes së CSS slider-vertical nuk është e standardizuar dhe do të hiqet."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Kërkesat e burimit, URL-të e të cilave përfshinin karakteret e hequra të hapësirës së bardhë \\(n|r|t) dhe karakteret \"më pak se\" (<), jan<PERSON> bllo<PERSON>. Hiq rreshtat e rinj dhe kodo karakteret \"më pak se\" nga vende të tilla, si p.sh. vlerat e atributeve të elementeve, për të ngarkuar këto burime."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() është zhvlerësuar, kështu që përdor më mirë API-në e standardizuar: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() është zhvlerësuar, kështu që përdor më mirë API-në e standardizuar: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() është zhvlerësuar, kështu që përdor më mirë API-në e standardizuar: nextHopProtocol te Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Kukit që përfshijnë një karakter \\(0|r|n) do të refuzohen në vend që të shkurtohen."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Zbutja e politikës për origjinën e njëjtë me anë të cilësimit document.domain është zhvlerësuar dhe do të çaktivizohet si parazgjedhje. Ky paralajmërim zhvlerësimi është për një qasje me origjinë të kryqëzuar që është aktivizuar me anë të cilësimit document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Aktivizimi i window.alert nga kuadrot iframe me origjina të kryqëzuara është zhvlerësuar dhe do të hiqet në të ardhmen."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Aktivizimi i window.confirm nga kuadrot iframe me origjina të kryqëzuara është zhvlerësuar dhe do të hiqet në të ardhmen."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Mbështetja është zhvlerësuar dhe do të hiqet në të ardhmen për të dhënat: URL-të në SVGUseElement."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() dhe watchPosition() nuk funksionojnë më në origjina të pasigurta. Për të përdorur këtë veçori, duhet të marrësh në konsideratë ta kalosh aplikacionin tënd te një origjinë më e sigurt, si p,.sh. HTTPS. Shiko https://goo.gle/chrome-insecure-origins për më shumë detaje."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() dhe watchPosition() janë zh<PERSON>ar në origjina të pasigurta. Për të përdorur këtë veçori, duhet të marrësh në konsideratë ta kalosh aplikacionin tënd te një origjinë më e sigurt, si p,.sh. HTTPS. Shiko https://goo.gle/chrome-insecure-origins për më shumë detaje."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() nuk funksionon më në origjina të pasigurta. Për të përdorur këtë veçori, duhet të marrësh në konsideratë ta kalosh aplikacionin tënd te një origjinë më e sigurt, si p,.sh. HTTPS. Shiko https://goo.gle/chrome-insecure-origins për më shumë detaje."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "U gjet një etiketë <h1> brenda një etikete <article>, <aside>, <nav> ose <section> që nuk ka një madhësi të specifikuar të fontit. Madhësia e tekstit të këtij titulli do të ndryshohet në këtë shfletues në të ardhmen e afërt. Shiko https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 për më shumë informacione."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate është zhvlerësuar. Përdor RTCPeerConnectionIceErrorEvent.address ose RTCPeerConnectionIceErrorEvent.port më mirë."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ky format për kërkesën navigator.credentials.get() për kredencialet dixhitale është i zhvlerësuar. Përditëso thirrjen për të përdorur formatin e ri."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Origjina e tregtarit dhe të dhënat arbitrare nga ngjarja canmakepayment e punonjësit të shërbimit janë zhvlerësuar dhe do të hiqen: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Uebsajti kërkonte një nënburim nga një rrjet ku mund të kishte qasje vetëm për shkak të pozicionit të privilegjuar të rrjet të përdoruesve të tij. Këto kërkesa i ekspozojnë pajisjet dhe serverët jopublikë në internet, duke rritur rrezikun për një sulm me falsifikimin e një kërkese nga një sajt i jashtëm (CSRF) dhe/ose rrjedhjen e informacioneve. Për t'i minimizuar këto rreziqe, Chrome i zhvlerëson kërkesat për nënburimet jopublike kur nisen nga kontekste jo të sigurta dhe do të fillojë t'i bllokojë ato."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Fusha dailyUpdateUrl e InterestGroups e kaluar te joinAdInterestGroup() është riemërtuar si updateUrl për të pasqyruar në mënyrë më të saktë sjelljen e saj."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator është zhvlerësuar. Përdor Intl.Segmenter më mirë."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS nuk mund të ngarkohet nga URL-të e file: përveçse nëse përfundojnë me një shtesë skedari .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Përdorimi i SourceBuffer.abort() për të ndërprerë heqjen e intervalit josinkron të remove() është zhvlerësuar për shkak të ndryshimit të specifikimit. Mbështetja do të hiqet në të ardhmen. Duhet të dëgjosh ngjarjen updateend më mirë. abort() është planifikuar vetëm për të ndërprerë një shtim josinkron të medias ose për të rivendosur gjendjen e analizuesit."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Caktimi i MediaSource.duration nën vulën kohore më të lartë të prezantimit për çdo kuadër të koduar dhe të ruajtur në ndërmemorie është zhvlerësuar për shkak të ndryshimit të specifikimit. Mbështetja për heqjen e tërthortë të medias së shkurtuar të ruajtur në ndërmemorie do të hiqet në të ardhmen. Duhet të kryesh më mirë heqjen e qartë të remove(newDuration, oldDuration) në të gjitha elementet me sourceBuffers ku gjendet newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDI për ueb do të kërkojë leje për përdorimin edhe nëse mesazhi sysex nuk është specifikuar në MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "API-ja e njoftimeve nuk mund të përdoret më për origjinat e pasigurta. Duhet të marrësh parasysh ta kalosh aplikacionin tënd në një origjinë të sigurt, si p.sh. HTTPS. Shiko https://goo.gle/chrome-insecure-origins për më shumë detaje."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Leja për Notification API nuk mund të kërkohet më nga një kuadër iframe me origjinë të kryqëzuar. Duhet të marrësh në konsideratë të kërkosh leje nga një kuadër i nivelit më të lartë ose të hapësh një dritare të re më mirë."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opsioni imageOrientation: 'none' në createImageBitmap është zhvlerësuar. Përdor më mirë createImageBitmap me opsionin \"{imageOrientation: 'from-image'}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partneri yt po negocion për një version të vjetruar (D)TLS. Kontakto me partnerin për ta rregulluar këtë."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Specifikimi i karakteristikës overflow: visible në etiketat \"img\", \"video\" dhe \"canvas\" mund të bëjë që të shfaqen përmbajtje vizuale jashtë kufijve të elementit. Shiko https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments është zhvlerësuar. Përdor më mirë instalimin në kohë për përpunuesit e pagesave."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Thirrja jote PaymentRequest anashkaloi direktivën Content-Security-Policy (CSP) connect-src. Ky anashkalim është zhvlerësuar. Shto identifikuesin e mënyrës së pagesës nga PaymentRequest API (në fushën supportedMethods) te direktiva CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent është zhvlerësuar. Përdor më mirë një version të standardizuar të navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> me një prind <picture> është i pavlefshëm dhe, për këtë arsye, është shpërfillur. Përdor <source srcset> më mirë."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame është specifike sipas shitësit Përdor më mirë metodën standarde cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame është specifike sipas shitësit. Përdor më mirë metodën standarde requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen është zhvlerësuar. Përdor më mirë Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() është zhvlerësuar. Përdor më mirë Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() është zhvlerësuar. Përdor më mirë Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() është zhvlerësuar. Përdor më mirë Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() është zhvlerësuar. Përdor më mirë Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen është zhvlerësuar Përdor më mirë Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Ne po e heqim nga përdorimi API-në chrome.privacy.websites.privacySandboxEnabled, megjithëse ajo do të qëndrojë aktive për përputhshmëri me versionet e mëparshme deri në publikimin e M113. Në vend të saj, përdor chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled dhe chrome.privacy.websites.adMeasurementEnabled. <PERSON><PERSON> https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Kufizimi DtlsSrtpKeyAgreement është hequr. Ke specifikuar një vlerë false për këtë kufizim, gjë që interpretohet si një përpjekje për të përdorur metodën e hequr për SDES key negotiation. Ky funksionalitet është hequr; përdor një shërbim që mbështet DTLS key negotiation më mirë."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Kufizimi DtlsSrtpKeyAgreement është hequr. Ke specifikuar një vlerë true për këtë kufizim, gjë që nuk ka asnjë efekt, por mund ta heqësh këtë kufizim për rregullsi."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "callback-based getStats() është zhvlerësuar dhe do të hiqet. Përdor më mirë spec-compliant getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() është zhvlerësuar. Përdor më mirë Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Kërkesat e nënburimit, URL-të e të cilave përfshijnë kredenciale të integruara (p.sh. **********************/), jan<PERSON> b<PERSON>."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opsioni rtcpMuxPolicy është zhvlerësuar dhe do të hiqet."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer do të kërkojë i<PERSON>limin e origjinës së kryqëzuar. Shiko https://developer.chrome.com/blog/enabling-shared-array-buffer/ për më shumë detaje."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() pa aktivizimin e përdoruesit është zhvlerësuar dhe do të hiqet."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Dëgjuesit e ngjarjes së shkarkimit janë zhvlerësuar dhe do të hiqen."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Shtesat duhet të zgjedhin i<PERSON> e origjinës së kryqë<PERSON>ar për të vazhduar të përdoret SharedArrayBuffer. Shiko https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atributi isFallbackAdapter i GPUAdapterattribute është i zhvlerësuar, përdor më mirë atributin isFallbackAdapter të GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 nuk mbështetet nga skedari json i përgjigjes në XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "XMLHttpRequest i sinkronizuar në bashkëbisedimin kryesor është zhvlerësuar për shkak të efekteve të dëmshme në përvojën e përdoruesit fundor. Për më shumë ndihmë, kontrollo https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacion"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Zhvendosjet e strukturës ndodhin kur elementet zhvendosen pa asnjë ndërveprim të përdoruesit. [Heto shkaqet e zhvendosjeve të strukturës](https://web.dev/articles/optimize-cls), si p.sh. shtimi, heqja e elementeve ose ndryshimi i fonteve të tyre me ngarkimin e faqes."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Kërk<PERSON><PERSON> për fontin"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON> iframe i injektuar"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Grupimi i zhvendosjeve të strukturës @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Përgjegjësit për zhvendosjet e strukturës nuk mund të zbuloheshin"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Nuk ka zhvendosje të strukturës"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Përgjegjësit për zhvendosjet e strukturës"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Përgjegjësit kryesorë për zhvendosjet e strukturës"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Grupimi më i keq"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Grupimi më i keq i zhvendosjeve të strukturës"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Periudha e jetëgjatësisë (TTL) e memories specifike"}, "models/trace/insights/Cache.ts | description": {"message": "<PERSON><PERSON>ë per<PERSON> e gjatë jetëgjatësie për memorien specifike mund t'i shpejtojë vizitat e përsëritura në faqen tënde. [Mëso më shumë](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nuk ka asnjë kërkesë me politika të memories specifike joefikase"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} të tjerë"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "K<PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "<PERSON><PERSON><PERSON>r periudha efikase të jetëgjatësisë për memorien specifike"}, "models/trace/insights/DOMSize.ts | description": {"message": "Një DOM i madh mund të rritë kohëzgjatjen e llogaritjeve të stilit dhe të riparaqitjeve të strukturës, duke ndi<PERSON>ar në reagueshmërinë e faqeve. Një DOM i madh do të rritë gjithashtu përdorimin e memories. [Mëso se si të shmangësh një madhësi të tepërt të DOM-it](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elementi"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Shumica elemente dytësore"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Thellësia e DOM-it"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizo madhësinë e DOM-it"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Elementet në total"}, "models/trace/insights/DOMSize.ts | value": {"message": "Vlera"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Kërkesa jote e parë e rrjetit është më e rëndësishmja.  <PERSON><PERSON><PERSON><PERSON><PERSON>n duke shman<PERSON><PERSON> <PERSON><PERSON><PERSON>, duke si<PERSON><PERSON><PERSON> një përgjigje të shpejtë të serverit, si dhe duke aktiviz<PERSON>r ngjeshjen e tekstit."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Kishte ridrejtime ({PH1} ridrejtime, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Serveri u përgjigj ngadalë (vëzhguar {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nuk është zbatuar ng<PERSON>ja"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Serveri përgjigjet shpejt (vëzhguar {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Zbaton ngjeshjen e tekstit"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Ridrejtimet"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Koha e përgjigjes e serverit"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Vonesa e kërkesës për dokumentin"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Shkarkim i pangjeshur"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Bajtet e dublikuara"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Hiq modulet e mëdha dublikatë të JavaScript nga paketat për të reduktuar bajtet e konsumuara nga aktiviteti i rrjetit."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript i dublikuar"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Ki parasysh që të caktosh [font-display](https://developer.chrome.com/blog/font-display) në swap ose optional për t'u siguruar që teksti të jetë vazhdimisht i dukshëm. swap mund të optimizohet më tej për të minimizuar zhvendosjet e strukturës me [anulimet e metrikës së fonteve](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Shfaqja e fontit"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Koha e shpërdoruar"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonim)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Shumë API, që zakonisht lexojnë gjeometrinë e strukturës, e detyrojnë motorin e paraqitjes që të vendosë në pauzë ekzekutimin e skriptit për të llogaritur stilin dhe strukturën. Mëso më shumë rreth [riparaqitjes së detyruar](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) dhe masave parandaluese për të."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Gjurmimi i stivës"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Riparaqitje e detyruar"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "T<PERSON>rja e funksionit kryesor"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Koha e riparaqitjes në total"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[pa atribut]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Zvogëlimi i kohës së shkarkimit të imazheve mund të përmirësojë kohën e perceptuar të ngarkimit për faqen dhe LCP-në. [Mëso më shumë për optimizimin e madhësisë së imazheve](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (përafë<PERSON>isht {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nuk ka imazhe të optimizueshme"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimizo madhësinë e skedarit"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} të tjerë"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Përmirësoi dorëzimin e imazheve"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Rritja e faktorit të ngjeshjes së imazhit mund të përmirësojë madhësinë e shkarkimit të këtij imazhi."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Përdorimi i një formati modern të imazheve (WebP, AVIF) ose rritja e ngjeshjes së imazheve mund të përmirësojë madhësinë e shkarkimit të këtij imazhi."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ky skedar imazhi është më i madh se ç'duhet të jetë ({PH1}) për përmasat e tij të shfaqura ({PH2}). Përdor imazhet reaguese për të zvogëluar madhësinë e shkarkimit të imazhit."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Përdorimi i formateve të videove në vend të imazheve GIF mund të përmirësojë madhësinë e shkarkimit të përmbajtjes së animuar."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Fillo të hetosh me fazën më të gjatë. [Vonesat mund të minizohen](https://web.dev/articles/optimize-inp#optimize_interactions). Për të zvogëluar kohëzgjatjen e përpunimit, [optimizo kostot e procesit kryesor](https://web.dev/articles/optimize-long-tasks), shpesh në JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Kohëzgjatja"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Vonesa e hyrjes"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nuk u zbulua asnjë ndërveprim"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Faza"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Vonesa e prezantimit"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Kohëzgjatja e përpunimit"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP-ja sipas fazës"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizo LCP-në duke e bër<PERSON> imazhin e LCP-së men<PERSON> [të zbulueshëm](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) nga protokolli HTML dhe [duke shmangur ngark<PERSON> e kurs<PERSON>](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=z<PERSON>uar në nivel të lartë"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "<PERSON><PERSON>t të zbatohet fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "Ngarkimi i kursyer nuk është zbatuar"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Imazhi i LCP-së u ngarkua {PH1} pas pikës më të hershme të nisjes."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nuk u zbulua LCP-ja"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nuk u zbulua asnjë burim i LCP-së pasi LCP-ja nuk është një imazh"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Kërkesa mund të zbulohet në dokumentin origjinal"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Zbulimi i kërkesës së LCP-së"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Çdo [fazë ka strategji specifike për përmirësimin](https://web.dev/articles/optimize-lcp#lcp-breakdown). Në mënyrë ideale, pjesa më e madhe e kohës së LCP-së duhet të harxhohet për ngarkimin e burimeve, jo te von<PERSON>."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Kohëzgjatja"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Vonesa e paraqitjes së elementit"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Fusha në percentilen e 75-të"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nuk u zbulua LCP-ja"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Faza"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Vonesa e ngarkimit të burimit"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Kohëzgjatja e ngarkimit të burimit"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Ko<PERSON> për në bajtin e parë"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP-ja sipas fazës"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bajtet e shpërdoruara"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Kodet polifil dhe transformimet bëjnë të mundur që shfletuesit më të vjetër të përdorin veçoritë e reja të JavaScript. Sidoqoftë, shumë prej tyre nuk janë të nevojshëm për shfletuesit modernë. Ki parasysh ta modifikosh procesin e ndërtimit të JavaScript që të mos transformohen veçoritë [bazë](https://web.dev/articles/baseline-and-polyfills), përveçse nëse e di që duhet të mbështeten shfletuesit më të vjetër. [Mëso se pse shumica e sajteve mund të përdorin kodin ES6+ pa kryer transformimin e gjuhës së programimit](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Versioni i vjetër i JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 dhe HTTP/3 ofrojnë shumë përfitime në HTTP/1.1, si p.sh. transmetimi i shumanshëm. [Mëso më shumë rreth përdorimit të protokollit modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Asnjë kërkesë nuk ka përdorur HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "K<PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Versioni modern i HTTP-së"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "K<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Kursimet e përllogaritura për LCP-në"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Lidhja paraprake nuk u përdor. Kontrollo që atributi crossorigin të përdoret siç duhet."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[S<PERSON>g kërkesat kritike në formë zinxhiri](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) duke zvo<PERSON><PERSON><PERSON><PERSON> gjat<PERSON><PERSON> e zinxhir<PERSON><PERSON>, duke zvo<PERSON><PERSON><PERSON><PERSON> madh<PERSON>sin<PERSON> e burimeve ose duke <PERSON><PERSON><PERSON> sh<PERSON> e burimeve të panevojshme për të përmirësuar ngarkimin e faqeve."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "<PERSON><PERSON>o sugjerime për [<PERSON><PERSON><PERSON><PERSON> paraprake](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) për origjinat më të rëndësishme, por përpiqu të përdorësh jo më shumë se 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Lidh paraprakisht kandidatët"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Vonesa maksimale e shtegut kritik:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nuk është ndikuar asnjë detyrë e paraqitjes nga varësitë e rrjetit"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Asnjë origjinë tjetër nuk është kandidate e mirë për lidhjen paraprake"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "asnjë or<PERSON>jinë nuk është lidhur paraprakisht"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Sugjer<PERSON>t për [lidh<PERSON><PERSON> paraprake](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ndihmojnë shfletuesin të vendosë një lidhje më herët në ngarkimin e faqes, duke kursyer kohë kur bëhet kërkesa e parë për atë origjinë. Sa më poshtë janë origjinat me të cilat lidhet paraprakisht faqja."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origjina të lid<PERSON>a paraprakisht"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Pema e varësisë së rrjetit"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "U gjetën më shumë se 4 lidhje të preconnect. Këto duhet të përdoren rrallë dhe vetëm për origjinat më të rëndësishme."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Lidhja paraprake nuk u përdor. Përdor vetëm preconnect për origjinat që mund të kërkojë faqja."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Shmang kërkesat kritike në formë zinxhiri duke z<PERSON><PERSON><PERSON><PERSON><PERSON> gjat<PERSON>sin<PERSON> e zinxhirë<PERSON>, duke z<PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON> e burimeve ose duke <PERSON><PERSON><PERSON> sh<PERSON> e burimeve të panevojshme për të përmirësuar ngarkimin e faqeve."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Kërkesat po bllokojnë paraqitjen fillestare të faqes, gjë që mund të vonojë LCP-në. [Shtyrja ose ndërfutja](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) mund t'i zhvendosë këto kërkesa të rrjetit jashtë shtegut kritik."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Kohëzgjatja"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nuk ka asnjë kërkesë për bllokimin e paraqitjes për këtë navigim"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Kërkesë"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Kërkesat që bllokojnë paraqitjen"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Nëse kostot e rillogaritjes së stilit mbeten të larta, optimizimi i përzgjedhësit mund t'i zvogëlojë ato. [Optimizo përzgjedhësit](https://developer.chrome.com/docs/devtools/performance/selector-stats) me kohë të kaluar të lartë dhe përqindje të lartë të shtegut të ngadaltë. Përzgjedhësit më të thjeshtë, numri më i vogël i përzgjedhësve, një model më i vogël DOM dhe një model më pak i thellë DOM do të zvogëlojnë kostot e përputhjes."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON> e kaluar"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nuk u gjetën të dhëna të përzgjedhësit të CSS-së. Statistikat e përzgjedhësit të CSS-së duhet të aktivizohen te cilësimet e panelit të performancës."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Përpjekjet për përputhje"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Numri i përputhjeve"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Kostot e përzgjedhësit të CSS-së"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Përzgjedhësit kryesorë"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Totali"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Koha e nënprocesit kryesor"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Pala e tretë"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Madhësia e transferimit"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Kodi i palës së tretë mund të ndikojë ndjeshëm në performancën e ngarkesës. [Zvogëlo dhe shtyje ngarkimin e kodit të palëve të treta](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) për t'i dhënë përparësi përmbajtjes së faqes sate."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Nuk u gjetën palë të treta"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Palët e treta"}, "models/trace/insights/Viewport.ts | description": {"message": "Ndërveprimet me trokitje mund [të vonohen me deri në 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) nëse porta e shikimit nuk optimizohet për celular."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Vonesa e trokitjes në celular"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimizo portën e shikimit për celular"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Vetëm faqet e ngarkuara nëpërmjet një kërkese me metodën GET janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Në memorien specifike mund të ruhen vetëm faqet me një kod statusi 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome zbuloi një përpjekje për të ekzekutuar JavaScript brenda memories specifike."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Faqet që kanë kërkuar një AppBanner nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Memoria specifike e faqes së plotë është çaktivizuar nga flamurët. Vizito chrome://flags/#back-forward-cache për ta aktivizuar lokalisht në këtë pajisje."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Memoria specifike e faqes së plotë është çaktivizuar nga rreshti i komandës."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Memoria specifike e faqes së plotë u çaktivizua për shkak të memories së pamjaftueshme."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Memoria specifike e faqes së plotë nuk mbështetet nga delegati."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Memoria specifike e faqes së plotë është çaktivizuar për paraqitësin paraprak."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON>aqja nuk mund të ruhet në memorien specifike pasi ka një shembull të BroadcastChannel me dëgjues të regjistruar."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Faqet me kokë cache-control:no-store nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Memoria specifike u pastrua me q<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Faqja u nxor jashtë memories specifike për të lejuar ruajtjen e një faqeje tjetër në memorien specifike."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Faqet që përmbajnë përbërës shtesë nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "E papërcaktuar"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Faqet që përdorin FileChooser API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Faqet që përdorin File System Access API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Faqet që përdorin Media Device Dispatcher nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON> luajt<PERSON>s mediash po luante gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Faqet që përdorin MediaSession API dhe caktojnë një gjendje luajtjeje nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Faqet që përdorin MediaSession API dhe caktojnë përpunues veprimesh nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Memoria specifike e faqes së plotë është çaktivizuar për shkak të lexuesit të ekranit."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Faqet që përdorin SecurityHandler nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Faqet që përdorin Serial API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Faqet që përdorin WebAuthetication API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Faqet që përdorin WebBluetooth API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Faqet që përdorin WebUSB API nuk janë të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Memoria specifike e faqes së plotë është çaktivizuar sepse kukit janë çaktivizuar në një faqe që përdor Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Faqet që përdorin një punonjës të dedikuar ose worklet nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokumenti nuk e përfundoi ngarkimin përpara daljes prej tij."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Banderola e aplikacioneve ishte e pranishme gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "\"Menaxheri i fjalëkalimeve\" i Chrome ishte i pranishëm gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Distilimi i DOM ishte në vazhdim gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Shikuesi i distiluesit të DOM ishte i pranishëm gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Memoria specifike e faqes së plotë u çaktivizua për shkak të shtesave që përdorin API të mesazheve."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Shtesat me lidhje afatgjatë duhet ta mbyllin lidhjen përpara se të futen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Shtesat me lidhje afatgjatë u përpoqën t'i dërgonin mesazhe kuadrove në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Memoria specifike e faqes së plotë u çaktivizua për shkak të shtesave."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Dialogu modal, si p.sh. ridërgimi i formularit ose dialogu i fjalëkalimit http u shfaq për faqen gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Faqja offline u shfaq gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Shiriti i ndërhyrjes për mbarimin e memories ishte i pranishëm gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Kishte kërkesa për autorizim g<PERSON>të dal<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Bllokuesi i dritareve kërcyese ishte i pranishëm gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Detajet e shfletimit të sigurt u shfaqën gjatë daljes."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Shfletimi i sigurt e konsideroi këtë faqe abuzive dhe bllokoi dritaren kërcyese."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Një përpunues shërbimi u aktivizua kur faqja ishte në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Memoria specifike e faqes së plotë është çaktivizuar për shkak të një gabimi në dokument."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Faqet që përdorin FencedFrames nuk mund të ruhen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Faqja u nxor jashtë memories specifike për të lejuar ruajtjen e një faqeje tjetër në memorien specifike."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Faqet që kanë dhënë qasje në transmetimin e mediave nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Faqet që kanë lloje të caktuara përmbajtjesh të integruara (p.sh. PDF-të) nuk kualifikohen aktualisht për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Faqet që përdorin IdleManager nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Faqet që kanë një lidhje të hapur IndexedDB nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Memoria specifike e faqes së plotë është çaktivizuar për shkak të një ngjarjeje IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "<PERSON><PERSON> përdorur API të papërshtatshme."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Faqet ku JavaScript injektohet nga shtesat nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Faqet ku StyleSheet injektohet nga shtesat nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Gabim i brendshëm."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Memoria specifike e faqes së plotë është çaktivizuar sepse një kërkesë e rrjetit JavaScript mori një burim me kokën Cache-Control: no-store"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Memoria specifike e faqes së plotë është çaktivizuar për shkak të një kërkese për procesin e mbajtjes aktive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Faqet që përdorin \"Kyçjen e tastierës\" nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Faqja nuk e përfundoi ngarkimin përpara se të dilej jashtë saj."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, b<PERSON><PERSON> i të cilave ka cache-control:no-cache, nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, b<PERSON><PERSON> i të cilave ka cache-control:no-store, nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigimi u anulua përpara se faqja të mund të restaurohej nga memoria specifike e faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Faqja është nxjerrë jashtë memories specifike pasi një lidhje aktive rrjeti mori shumë të dhëna. Chrome e kufizon sasinë e të dhënave që mund të marrë një faqe kur është e ruajtur në memorien specifike."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Faqet që kanë një kërkesë fetch() ose XHR në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Faqja u nxor jashtë memories specifike të faqes së plotë pasi një kërkesë aktive rrjeti përmbante një ridrejtim."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Faqja u nxor jashtë memories specifike pasi një lidhje rrjeti ishte e hapur për një kohë shumë të gjatë. Chrome e kufizon sasinë e kohës gjatë së cilës mund të marrë të dhëna një faqe kur është e ruajtur në memorien specifike."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Faqet që nuk kanë një kokë përgjigjeje të vlefshme nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigimi u krye në një kornizë të ndryshme nga korniza kryesore."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Faqet me transaksione të indeksuara DB në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Faqet me një kërkesë rrjeti në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Faqet me një kërkesë rrjeti fetch() në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Faqet me një kërkesë rrjeti në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Faqet me një kërkesë rrjeti XHR në proces nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Faqet që përdorin PaymentManager nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Faqet që përdorin \"Figur<PERSON> brenda figurës\" nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Faqet që tregojnë Printing UI nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Faqja është hapur duke p<PERSON><PERSON><PERSON><PERSON> \"window.open()\" dhe një skedë tjetër ka një referencë tek ajo ose faqja është hapur në një dritare."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Procesi i paraqitësit për faqen në memorien specifike të faqes së plotë pësoi një ndërprerje aksidentale."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Procesi i paraqitësit për faqen në memorien specifike të faqes së plotë u mbyll me forcë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Faqet që kanë kërkuar leje për regjistrimin e audios nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Faqet që kanë kërkuar leje për sensorin nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Faqet që kanë kërkuar sinkronizimin në sfond ose lejet e marrjes nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Faqet që kanë kërkuar leje për MIDI nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Faqet që kanë kërkuar leje për njoftime nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Faqet që kanë kërkuar qasje në hapësirën ruajtëse nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Faqet që kanë kërkuar leje për regjistrim videoje nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Në memorien specifike mund të ruhen vetëm faqet, skema e URL-së e të cilave është HTTP/HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Faqja është pretenduar nga një punonjës shërbimi ndërsa ndodhet në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Një punonjës shërbimi u përpoq t'i dërgonte një MessageEvent faqes në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker ishte çregjistruar ndërsa një faqe ishte në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Faqja u nxor jashtë memories specifike të faqes së plotë për shkak të aktivizimit të një punonjësi shërbimi."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome u rinis dhe i pastroi hyrjet e memories specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Faqet që përdorin SharedWorker nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Faqet që përdorin SpeechRecognizer nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Faqet që përdorin SpeechSynthesis nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Një iframe në faqe filloi një navigim që nuk u përfundua."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON>aq<PERSON>, nënburimi i të cilave ka cache-control:no-cache, nuk mund të hyjnë në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Faqet nënburimi i të cilave ka cache-control:no-store nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON><PERSON> kishte tejkaluar kohën maksimale në memorien specifike të faqes së plotë dhe kishte skaduar."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Faqes i mbaroi koha gjatë vendosjes në memorien specifike të faqes së plotë (ndoshta për shkak të përpunuesve pagehide të ekzekutuar për një kohë të gjatë)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Faqja ka një përpunues shkarkimi në kornizën kryesore."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Faqja ka një përpunues shkarkimi në një nënkornizë."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Shfletuesi ka ndr<PERSON>huar kokën e anulimit të agjentit të përdoruesit."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Faqet që kanë dhënë qasje për regjistrimin e videos ose audios nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Faqet që përdorin WebDatabase nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Faqet që përdorin WebHID nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Faqet që përdorin WebLocks nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Faqet që përdorin WebNfc nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Faqet që përdorin WebOTPService nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Faqet me WebRTC nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Memoria specifike e faqes së plotë është çaktivizuar sepse është përdorur WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Faqet që përdorin WebShare nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Faqet me WebSocket nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Memoria specifike e faqes së plotë është çaktivizuar sepse është përdorur WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Faqet me WebTransport nuk mund të vendosen në memorien specifike të faqes së plotë."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Memoria specifike e faqes së plotë është çaktivizuar sepse është përdorur WebTransport ."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Faqet që përdorin WebXR nuk janë aktualisht të përshtatshme për memorien specifike të faqes së plotë."}}