{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Pri avtorizaciji ne bo mogoče uporabiti nadomestnega znaka (*) pri obdelavi CORS Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Atribut »disableRemotePlayback« uporabite za onemogočanje privzete integracije za predvajanje namesto izbirnika »-internal-media-controls-overlay-cast-button«."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Vrednost prikaza CSS-ja slider-vertical ni standardizirana in bo odstranjena."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Zahteve za sredstva, katerih URL-ji so vsebovali odstranjene znake za presledke »\\(n|r|t)« in znake »manj kot« (»<«), so blokirane. Odstranite prelome vrstic in kodirajte znake »manj kot« z mest, kot so vrednosti atributov elementov, da bi lahko naložili ta sredstva."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Možnost »chrome.loadTimes()« je zastarela, zato namesto nje uporabite standardizirani API: Čas krmarjenja 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Možnost »chrome.loadTimes()« je zastarela, zato namesto nje uporabite standardizirani API: Čas upodabljanja."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Možnost »chrome.loadTimes()« je zastarela, zato namesto nje uporabite standardizirani API: »nextHopProtocol« v Času krmarjenja 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON>, ki vsebujejo znak »\\(0|r|n)«, bodo zavrn<PERSON> in ne prirezani."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Opustitev pravilnika, ki predpisuje isti izvor, z nastavitvijo možnosti »document.domain« je zastarela in bo privzeto onemogočena. To opozorilo o zastarelosti je za dostop iz več izvorov, ki je bil omogočen z nastavitvijo možnosti »document.domain«."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Sprožanje »window.alert« iz elementov iframe iz več izvorov je zastarelo in bo v prihodnosti odstranjeno."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Sprožanje »window.confirm« iz elementov iframe iz več izvorov je zastarelo in bo v prihodnosti odstranjeno."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Podpora za podatke: URL-ji v elementu SVGUseElement so zastareli in bodo v prihodnosti odstranjeni."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "»getCurrentPosition()« in »watchPosition()« ne delujeta več z izvori, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Možnosti »getCurrentPosition()« in »watchPosition()« sta označeni kot zastareli za izvore, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "»getUserMedia()« ne deluje več z izvori, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "V elementu <article>, <aside>, <nav> ali <section> je bila najdena oznaka <h1>, ki nima navedene velikosti pisave. Velikost besedila tega naslova se bo v tem brskalniku v bližnji prihodnosti spremenila. Več informacij je na voljo na ttps://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Možnost »RTCPeerConnectionIceErrorEvent.hostCandidate« je zastarela. Namesto nje uporabite »RTCPeerConnectionIceErrorEvent.address« ali »RTCPeerConnectionIceErrorEvent.port«."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ta oblika za zahtevo navigator.credentials.get() za digitalne poverilnice je zastarela. Posodobite klic, da uporabite novo obliko."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "<PERSON><PERSON><PERSON> in arbitrarni podatki iz dogodka procesa storitve canmakepayment so zast<PERSON>i in bodo odstranjeni: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Spletno mesto je zahtevalo podsredstvo iz omrežja, do katerega je lahko dostopalo samo zaradi uporabnikovega prednostnega omrežnega položaja. Te zahteve razkrivajo naprave in strežnike, ki niso javni, v internetu, s čimer se poveča nevarnost za napad s poneverbo zahteve med spletnimi mesti (CSRF) in/ali puščanje podatkov. Za zmanjšanje teh nevarnosti Chrome označi zahteve podsredstvom, ki niso javna, kot zastarele, pri sprožanju iz konte<PERSON>, ki niso varni, in jih bo začel blokirati."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Polje dailyUpdateUrl v strukturi InterestGroups, posredovano sem: joinAdInterestGroup(), je bilo preimenovano v updateUrl, da natančneje odraža svoje delovanje."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Možnost »Intl.v8BreakIterator« je zastarela. Namesto tega uporabite »Intl.Segmenter«."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Slogov CSS ni mogoče naložiti iz URL-jev za »file:«, razen če se končajo s datotečno pripono .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Uporaba možnosti »SourceBuffer.abort()« za prekinitev asinhrone odstranitve obsega za »remove()« je zastarela zaradi spremembe specifikacije. Podpora bo v prihodnosti odstranjena. Namesto tega raje poslušajte dogodek updateend. Možnost »abort()« je namenjena samo za prekinitev asinhronega dodajanja predstavnosti ali ponastavitev stanja razčlenjevalnika."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Nastavitev možnosti »MediaSource.duration« pod najvišji časovni žig predstavitve katerih koli medpomnjenih kodiranih okvirov je zastarela zaradi spremembe specifikacije. Podpora za implicitno odstranitev prirezanih medpomnjenih predstavnosti bo v prihodnosti odstranjena. Namesto tega izvedite eksplicitno možnost »remove(newDuration, oldDuration)« na vseh »sourceBuffers«, kjer je newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Spletni MIDI bo zahteval dovoljenje za uporabo, tudi če sysex ni določen v »MIDIOptions«."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Obvestilnega API-ja ni več dovoljeno uporabljati iz izvorov, ki niso varni. Razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Dovoljenja za obvestilni API ni več mogoče zahtevati iz elementa iframe iz več izvorov. Razmislite o tem, da bi namesto tega zahtevali dovoljenje od okvira na visoki ravni ali odprli novo okno."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Možnost »imageOrientation: 'none'« v elementu »createImageBitmap« je zastarela. Namesto tega uporabite element »createImageBitmap« z možnostjo »{imageOrientation: 'from-image'}«."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Vaš partner p<PERSON><PERSON><PERSON> pri pogajanju uporabiti zastarelo različico (D)TLS. Obrnite se na partnerja, da to popravi."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Če določite lastnost overflow: visible za oznake slik, videooznake in oznake platna, se lahko zaradi tega zgodi, da bodo vizualno vs<PERSON><PERSON> ustvarjale zunaj mej elementa. Oglejte si https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "API paymentManager.instruments je zastarel. Za rutine za obravnavo plačil raje uporabite namestitve »just-in-time«."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Klic API-ja PaymentRequest je zaobšel direktivo connect-src pravilnika o varnosti vsebine. To zaobidenje je zastarelo. Dodajte identifikator plačilnega sredstva iz API-ja PaymentRequest (v polju supportedMethods) v direktivo connect-src pravilnika o varnosti vsebine."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "Možnost »StorageType.persistent« je zastarela. Namesto tega uporabite standardizirano navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Element »<source src>« z nadrejenim elementom »<picture>« je neveljaven. Namesto tega uporabite »<source srcset>«."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Metoda webkitCancelAnimationFrame je samo za določenega ponudnika. Uporabite standardno metodo cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame je samo za določenega ponudnika. Uporabite standardno metodo requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "API HTMLVideoElement.webkitDisplayingFullscreen je zastarel. Uporabite »Document.fullscreenElement«."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "API HTMLVideoElement.webkitEnterFullScreen() je zastarel. Uporabite »Element.requestFullscreen()«."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "API HTMLVideoElement.webkitEnterFullscreen() je zastarel. Uporabite »Element.requestFullscreen()«."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "API HTMLVideoElement.webkitExitFullScreen() je zastarel. Uporabite »Document.exitFullscreen()«."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "API HTMLVideoElement.webkitExitFullscreen() je zastarel. Uporabite »Document.exitFullscreen()«."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "API HTMLVideoElement.webkitSupportsFullscreen je zastarel. Uporabite »Document.fullscreenEnabled«."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled bo označen kot zastarel, vendar bo do izdaje različice M113 aktiven zaradi povratne združljivosti. Namesto tega API-ja uporabite chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled in chrome.privacy.websites.adMeasurementEnabled. Glejte https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Omejitev »DtlsSrtpKeyAgreement« je odstranjena. <PERSON><PERSON><PERSON> ste vrednost »false« za to <PERSON><PERSON><PERSON><PERSON>, kar se razlaga kot poskus uporabe odstranjene metode »SDES key negotiation«. Ta funkcija je odstranjena. Namesto nje uporabite storitev, ki podpira »DTLS key negotiation«."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Omejitev »DtlsSrtpKeyAgreement« je odstranjena. Za to omejitev ste navedli vrednost »true«, kar ni imelo nobenega vpliva, lahko pa to omejitev odstranite zaradi jasnosti."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Metoda getStats(), ki temelji na povratnih klicih, je zastarel<PERSON> in bo odstranjena. Uporabite metodo getStats(), ki je skladna s tehničnimi podatki."}, "generated/Deprecation.ts | RangeExpand": {"message": "Obseg Range.expand() je zastarel. Uporabite »Selection.modify()«."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON> za <PERSON>s<PERSON>st<PERSON>, katerih URL-ji vsebujejo vdelane poverilnice (npr. »**********************/«), so blo<PERSON><PERSON>."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Možnost »rtcpMuxPolicy« je zastarela in bo odstranjena."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "Za »SharedArrayBuffer« bo potrebno izoliranje od drugih izvorov. Več podrobnosti: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Možnost »speechSynthesis.speak()« brez aktiviranja uporabnika je zastarela in bo odstranjena."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Poslušalci dogodkov za odstranjevanje so zastareli in bodo odstranjeni."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki želijo še naprej uporabljati »SharedArrayBuffer«, morajo omogočiti izoliranje od drugih izvorov. Glejte: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atribut isFallbackAdapter za GPUAdapter je zastarel, namesto tega uporabite atribut isFallbackAdapter za GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON odgovora v »XMLHttpRequest« ne podpira standarda UTF-16."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhrona možnost »XMLHttpRequest« na glavni niti je zastarela, ker slabo vpliva na izkušnjo končnega uporabnika. Dodatna pomoč: https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacija"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Spremembe postavitve se pojavijo, ko se elementi premikajo brez interakcije uporabnika. [Raziščite vzroke sprememb postavitve](https://web.dev/articles/optimize-cls), kot so dodajanje ali odstranjevanje elementov ali spreminjanje pisav med nalaganjem strani."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Zahteva za pisavo"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Vstavljen okvir iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Gruča sprememb postavitve @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Ni bilo mogoče zaznati nobenega razloga za težave pri spremembi postavitve"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Ni sprememb postavitve"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Razlogi za težave pri spremembi postavitve"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Najpogostejši razlogi za težave pri spremembi postavitve"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "<PERSON><PERSON><PERSON><PERSON>ša gruča sprememb postavitve"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL predpomnjenja"}, "models/trace/insights/Cache.ts | description": {"message": "Dolgotrajno predpomnjenje lahko pospeši vnovične obiske strani. [Več o tem](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Ni zahtev s pravilniki o neučinkovitem predpomnjenju"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON> {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Uporaba učinkovitega dolgotrajnega predpomnjenja"}, "models/trace/insights/DOMSize.ts | description": {"message": "Velik DOM lahko podaljša trajanje slogovnih izračunov in prilagoditev postavitve, kar vpliva na odzivnost strani. Velik DOM prav tako povzroči povečano uporabo pomnilnika. [<PERSON>ber<PERSON>, kako se izognete prekomerni velikosti DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Največje število neposredno podrejenih elementov"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Globina DOM-a"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistični podatek"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizirajte velikost DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON><PERSON><PERSON> število elementov"}, "models/trace/insights/DOMSize.ts | value": {"message": "Vrednost"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Vaša prva omrežna zahteva je najpomembnejša.  Zmanjšajte zakasnitev tako, da se izognete preusmeritvam, s <PERSON><PERSON>r poskrbite za hiter odziv strežnika in omogočite stiskanje besedila."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON><PERSON> je preusmeritve (št. preusmeritev: {PH1}, več kot {PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Odziv strežnika je bil počasen (zaznano: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Stiskanje ni bilo uporabljeno"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Izogiba se preusmeritvam"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON><PERSON><PERSON> strežnika je hiter (zaznano: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Uporablja stiskanje besedila"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Preusmeritve"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Čas odziva strežnika"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Zakasnitev zahteve za dokument"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "<PERSON>nos brez stiskanja"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Podvojeni baj<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Vir"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Odstranite velike, podvojene module JavaScript iz svežnjev in tako zmanjšajte nepotrebne bajte, uporabljene v omrežni dejavnosti."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Podvojen JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Razmislite o nastavitvi funkcije [font-display](https://developer.chrome.com/blog/font-display) na swap ali optional, da z<PERSON><PERSON><PERSON>, da je besedilo ves čas vidno. swap lahko dodatno optimizirate, da ublažite spremembe s [preglasitvami meritve pisav](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Prikaz pisave"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Izgubljen čas"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonimno)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Številni API-ji, ki obi<PERSON><PERSON>no berejo geometrijo postavitve, prisilijo <PERSON>m upodabljan<PERSON>, da za<PERSON><PERSON>no zaustavi izvajan<PERSON> sk<PERSON>, da izračuna slog in postavitev. Preberite več o [prisilni prilagoditvi](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) in njenem zmanjšanju."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Sled sklada"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Najpogostejši klic funkcije"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Skupni čas prilagoditve"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Če zmanjšate čas prenosa slik, lahko izboljšate zaznano nalaganje strani in največji izris vsebine (LCP). [Preberite več o optimiziranju velikosti slike](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (pribl. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON> slik, ki jih je mogoče optimizirati"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimizacija velikosti datoteke"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON> {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Izboljšajte prikazovanje slik"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Če povečate faktor stiskanja slike, lahko izboljšate velikost prenosa te slike."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Če uporabite sodobno obliko zapisa slike (WebP, AVIF) ali povečate stiskanje slike, lahko izboljšate velikost prenosa te slike."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ta slikovna datoteka je prevelika ({PH1}) za prikazane dimenzije ({PH2}). Uporabite odzivne slike, da zmanjšate velikost prenosa slike."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Če namesto GIF-ov uporabite videoposnetke, lahko izboljšate velikost prenosa animirane vsebine."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Začnite preiskavo z najdaljšo fazo. [<PERSON><PERSON><PERSON> se lahko <PERSON>](https://web.dev/articles/optimize-inp#optimize_interactions). Če želite zmanjšati trajanje obdelave, [optimizirajte stroške glavne niti](https://web.dev/articles/optimize-long-tasks) (to je pogosto JavaScript)."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Zakasnitev vnosa"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Ni zaznanih interakcij"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Faza"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Zakasnitev predstavitve"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP po fazah"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizirajte LCP tako, da sliko LCP nastavite tako, da bo takoj [vidna](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) v kodi HTML, in se [izognite odloženemu nalaganju](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "vrednost fetchpriority=high je uporabljena"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Uporabiti je treba fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "odloženo nalaganje ni uporabljeno"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Slika LCP-ja je bila naložena {PH1} po najzgodnejši začetni točki."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ni zaznanega LCP-ja"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Zaznan ni bil noben vir za LCP, ker LCP ni slika"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Zahtevo je mogoče odkriti v začetnem dokumentu"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Odkrivanje zahteve za LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Vsaka [faza ima določene strategije za izboljšanje](https://web.dev/articles/optimize-lcp#lcp-breakdown). V idealnem primeru bi morala biti večina časa pri meritvi LCP porabljena za nalaganje sredstev, ne za zamude."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Zakasnitev upodabljanja elementa"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Polje p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ni zaznanega LCP-ja"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Faza"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Zakasnitev nalaganja sredstva"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "T<PERSON><PERSON>je nalaganja sredstva"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Čas do prvega bajta"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP po fazah"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "<PERSON>zgu<PERSON><PERSON><PERSON><PERSON> baj<PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Elementi »polyfill« in »transform« starejšim brskalnikom omogočajo uporabo novih funkcij JavaScripta. Pri sodobnih brskalnikih pa številni niso potrebni. Razmislite o spremembi postopka gradnje JavaScripta, da ne pride do prevajanja med programskimi jeziki za funkcije [Baseline](https://web.dev/articles/baseline-and-polyfills), razen če veste, da potrebujete podporo za starejše brskalnike. [Preberite, zakaj je mogoče na večini spletnih mest uvesti kodo ES6+ brez prevajanja med programskimi jeziki](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)."}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Starejši JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 in HTTP/3 imata več prednosti pred HTTP/1.1, vključno z multipleksiranjem. [Preberite več o uporabi sodobnega protokola HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Nobena zahteva ni uporabljala protokola HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Sodobni protokol HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Vir"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Čas"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Ocenjeni prihranki za LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Neuporabljeno vnaprejšnje povezovanje. Preverite, ali je atribut crossorigin pravilno uporabljen."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Izogibajte se veriženju kritičnih zahtev](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) tako, da skraj<PERSON>te verige, zmanjšate velikost sredstev ali odložite prenos nepotrebnih sredstev zaradi izboljšanja nalaganja strani."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Dodajte predloge za [vnaprejš<PERSON><PERSON> povezovanje](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) za najpomembnejše izvore, vendar poskusite uporabiti največ 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidati za vnaprejšnje povezovanje"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Največja zakasnitev kritične poti:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Omrežne odvisnosti ne vplivajo na nobeno opravilo upodabljanja"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Noben dodatni izvor ni primeren za vnaprejšnje povezovanje"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "noben izvor ni bil vnaprej povezan"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Predlogi za [vnaprejšn<PERSON> povezovan<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pomagajo brskalniku vzpostaviti povezavo v zgodnejši fazi nalaganja strani, kar prihrani čas pri prvi zahtevi za ta izvor. To so izvori, s katerimi je stran povezala vnaprej."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Vnaprej povezani izvori"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Drevo omrežne odvisnosti"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Našli smo več kot 4 povezave preconnect. Te je treba uporabljati gospodarno in samo za najpomembnejše izvore."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Neuporabljeno vnaprejšnje povezovanje. Atribute preconnect uporabljajte samo za izvore, ki jih bo stran verjetno zahtevala."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Izogibajte se veriženju kritičnih zahtev tako, da skrajšate verige, zmanjšate velikost sredstev ali odložite prenos nepotrebnih sredstev zaradi izboljšanja nalaganja strani."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Zahteve blokirajo začetno upodabljanje strani, kar lahko zakasni LCP. [Odlaganje ali vstavljanje v vrstico](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) lahko te omrežne zahteve premakne zunaj kritične poti."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Za to premikanje ni zahtev za blokiranje upodabljanja"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Zahteve z blokiranjem upodabljanja"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Če so stroški za vnovični izračun sloga še vedno visoki, jih lahko z optimizacijo izbirnika zmanjšate. [Optimizirajte izbirnike](https://developer.chrome.com/docs/devtools/performance/selector-stats) z visokim pretečenim časom in visokim odstotkom počasnih poti. Preprostejši izbirniki, manj<PERSON><PERSON><PERSON>, manj<PERSON>i model DOM in plitvejša struktura modela DOM bodo zmanjšali stroške ujemanja."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Pretekli č<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Ni izbirnikov CSS. Statistični podatki o izbirniku CSS morajo biti omogočeni v nastavitvah podokna za učinkovitost delovanja."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Poskusi ujemanja"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Število u<PERSON>j"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Stroški izbirnika CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Najboljši izbirniki"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Čas glav<PERSON> niti"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Velikost prenosa"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Koda drugega ponudnika lahko znatno vpliva na učinkovitost nalaganja. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in odložite nalaganja kode drugega ponudnika](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), da boste dali prednost vsebini strani."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON> <PERSON>ih pon<PERSON>nikov"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "Interakcije z dotikom imajo la<PERSON>ko [zakasnitev do 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/), če vidno območje ni optimizirano za mobilne naprave."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Zakasnitev dotika v mobilni napravi"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimiziranje vidnega območja za mobilne naprave"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON> strani, naložene z zahtevo GET, so primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Predpomniti je mogoče samo strani s kodo statusa 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome je zaznal poskus izvajanja JavaScripta v predpomnilniku."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON>, ki so zahtevale AppBanner, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen z zastavicami. Če ga želite lokalno omogočiti v tej napravi, obiščite chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen v ukazni vrstici."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker ni dovolj pomnilnika."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Pooblaščenec ne podpira predpomnilnika za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen za predupodabljalnik."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Strani ni mogoče predpomniti, ker ima primerek BroadcastChannel z registriranimi poslušalci."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Strani z glavo cache-control:no-store ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Predpomnilnik je bil namenoma izbrisan."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Stran je bila izvržena iz predpomnilnika, zato da je bila lahko predpomnjena druga stran."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "<PERSON><PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> v<PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Neopredeljeno"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "<PERSON><PERSON>, ki uporabljajo FileChooser API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "<PERSON><PERSON>, ki uporabljajo File System Access API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON>, ki uporabljajo Media Device Dispatcher, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Predvajalnik predstavnosti je predva<PERSON><PERSON><PERSON>, preden ga je obiskovalec zapustil."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "<PERSON><PERSON>, ki uporabljajo MediaSession API in nastavijo stanje predvajanja, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "<PERSON><PERSON>, ki uporabljajo MediaSession API in nastavijo rutine za obravnavo dejanj, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi bralnika zaslona."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo <PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Strani, ki uporabljajo WebAuthentication API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "<PERSON><PERSON>, ki uporabljajo WebBluetooth API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "<PERSON><PERSON>, ki uporabljajo WebUSB API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker so piškotki onemogočeni na strani, ki uporablja to: Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> name<PERSON>ki proces ali delovni proces, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Nalaganje dokumenta se ni končalo, preden ga je obiskovalec zapustil."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "<PERSON>b zapuščanju strani je bila prisotna pasica za aplikacije."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON>b zapuščanju strani je bil prisoten Chromov upravitelj gesel."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON>b zapuščanju strani je potekalo povzemanje DOM-a."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "<PERSON>b zapuščanju strani je bil prisoten pregledovalnik povzemanja DOM-a."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi razširitev, ki uporabljajo API za sporočanje."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Razširitve z dolgotrajno povezavo morajo pred preklopom v predpomnilnik za hitro obnovitev strani prekiniti povezavo."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Razširitve z dolgotrajno povezavo so poskusile poslati sporočila okvirjem v predpomnilniku za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi razširitev."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Ob zapuščanju strani je bilo prikazano modalno pogovorno okno, na primer za vnovično pošiljanje obrazca ali pogovorno okno http za geslo."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "<PERSON><PERSON> zapuščanju strani je bila prikazana stran brez povezave."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "<PERSON>b zapuščanju strani je bila prisotna vrstica za posredovanje zaradi pomanjkanja pomnilnika."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "<PERSON>b zapuščanju strani so bile prikazane zahteve za dovoljenja."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON>b zapuščanju strani je bil prisoten preprečevalec pojavnih oken."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "<PERSON>b zapuščanju strani so bile prikazane podrobnosti Varnega brskanja."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Varno brskanje je to stran smatralo kot neprimerno in je blokiralo pojavno okno."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Ko je bila stran v predpomnilniku za hitro obnovitev strani, je bil aktiviran proces storitve."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi napake dokumenta."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ni mogoče shraniti v predpomnilniku za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Stran je bila izvržena iz predpomnilnika, zato da je bila lahko predpomnjena druga stran."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON><PERSON>, ki so podelile dostop do toka predstavnosti, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Strani z določenimi vrstami vdelane vsebine (npr. PDF-ji) trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "<PERSON><PERSON>, ki imajo odprto povezavo IndexedDB, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi dogodka »IndexedDB«."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Uporabljeni so bili neprimerni API-ji."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Strani, v katere razširitve vstavijo JavaScript, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Strani, v katere razširitve vstavijo StyleSheet, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker je neka omrežna zahteva JavaScript prejela vir z glavo Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi zahteve za ohranjanje povezave."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON>, ki uporabljajo KeyboardLock trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Nalaganje strani se ni končalo, preden jo je obiskovalec zapustil."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, pri katerih ima glavno sredstvo cache-control:no-cache, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, pri katerih ima glavno sredstvo cache-control:no-store, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Premikanje je bilo prek<PERSON>, preden je bilo stran mogoče obnoviti iz predpomnilnika za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Stran je bila izvržena iz predpomnilnika, ker je dejavna omrežna povezava prejela preveč podatkov. Chrome omejuje koli<PERSON> podatkov, ki jih lahko stran prejme, medtem ko je predpomnjena."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON>, ki imajo fetch() ali <PERSON>, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Stran je bila izvržena iz predpomnilnika za hitro obnovitev strani, ker je bila pri dejavni omrežni povezavi vključena preusmeritev."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Stran je bila izvrž<PERSON> iz predpomnilnika, ker je bila omrežna povezava predolgo odprta. Chrome omejuje časovno obdobje, med katerim lahko stran prejema podatke, medtem ko je predpomnjena."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON>, ki nimajo veljavne glave odziva, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Do premikanje je prišlo v okviru, ki ni glavni okvir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> indeksirane transakcije zbirke podatkov, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Strani z omrežno zahtevo, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Strani z omrežno zahtevo fetch(), ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Strani z omrežno zahtevo, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Strani z omrežno zahtevo XHR, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON>, ki uporabljajo sliko v sliki, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "<PERSON><PERSON>, ki prikazujejo uporabniški vmesnik za tiskanje, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Stran je bila odprta z izrezkom »window.open()« in drug zavihek ima sklic nanjo ali pa se je stran odprla v oknu."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Proces upodabljalnika za stran v predpomnilniku za hitro obnovitev strani se je zrušil."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Proces upodabljalnika za strank v predpomnilniku za hitro obnovitev strani je ustavljen."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za zajem zvoka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za senzorje, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Strani, ki so zahtevale sinhronizacijo v ozadju ali dovoljenja za pridobitev, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za MIDI, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za obvestila, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON><PERSON>, ki so zahtevale dostop do shrambe, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za zajem videa, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Predpomniti je mogoče samo strani s shemo URL-ja HTTP/HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "To stran je zahteval proces storitve, medtem ko je v predpomnilniku za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Proces storitve je poskušal stran v predpomnilniku za hitro obnovitev strani poslati izrezek MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Registracija za ServiceWorker je bila preklicana, medtem ko je bila stran v predpomnilniku za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Stran je bila izvržena iz predpomnilnika za hitro obnovitev strani zaradi aktiviranja procesa storitve."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome je znova zagnal in počistil vnose v predpomnilniku za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SpeechSynthesis, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "<PERSON><PERSON> iframe ban strani je za<PERSON> premikanje, ki se ni dokon<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, pri katerih ima del sredstva cache-control:no-cache, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, pri katerih ima del sredstva cache-control:no-store, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Stran je presegla najdaljši čas v predpomnilniku za hitro obnovitev strani in je potekla."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Časovna omejitev strani je potekla med vstopom v predpomnilnik za hitro obnovitev strani (verjetno zaradi dolgotrajno izvajajočih se rutin za obravnavo pagehide)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Stran ima rutino za obravnavo za odstranjevanje v glavnem okviru."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Stran ima rutino za obravnavo za odstranjevanje v podokviru."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Brskalnik je spremenil glavo za preglasitev uporabnikovega posrednika."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON><PERSON>, ki so podelile dostop za snemanje videa ali zvoka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "<PERSON>rani, ki uporabljajo WebDatabase, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "<PERSON><PERSON>, ki uporabl<PERSON>jo WebHID, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo WebLocks, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> WebOTPService, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Strani s tehnologijo WebRTC ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker se uporablja tehnologija WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "<PERSON><PERSON>, ki uporab<PERSON><PERSON>jo WebShare, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Strani z vtičnico WebSocket ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker se uporablja vtičnica WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Strani z razlogom WebTransport ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker se uporablja API WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo WebXR, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}}