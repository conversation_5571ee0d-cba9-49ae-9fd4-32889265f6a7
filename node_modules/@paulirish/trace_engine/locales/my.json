{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers ဆောင်ရွက်ရာတွင် ခွင့်ပြုချက်ကို အစားထိုး အထူးအက္ခရာ (*) ဖြင့် ပြသမည်မဟုတ်ပါ။"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "မူရင်း ‘ကာစ်’ ပေါင်းစည်းမှုကို ပိတ်ရန်အတွက် -internal-media-controls-overlay-cast-button ရွေးချယ်စနစ်ကို အသုံးပြုမည့်အစား disableRemotePlayback ရည်ညွှန်းချက်ကို အသုံးပြုသင့်သည်။"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS အသွင်အပြင်တန်ဖိုး slider-vertical သည် သတ်မှတ်စံနှုန်းနှင့် မကိုက်ညီ၍ ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ရင်းမြစ်တောင်းဆိုမှုများ၏ URL များတွင် ပါဝင်သော ဖယ်ရှားထားသည့် နေရာလွတ် \\(n|r|t) အက္ခရာများနှင့် ‘ပိုနည်းသော အက္ခရာ’ (<) များကို ပိတ်ထားသည်။ ဤရင်းမြစ်များဖွင့်ရန်အတွက် လိုင်းအသစ်များ ဖယ်ရှားပြီး အစိတ်အပိုင်း ရည်ညွှန်းချက်တန်ဖိုးများကဲ့သို့ နေရာများမှ ‘ပိုနည်းသော အက္ခရာ’ များကို ဒေတာ အသွင်ပြောင်းပါ။"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() ကို ရပ်ဆိုင်းထားပြီး ၎င်းအစား စံသတ်မှတ်ထားသည့် ဤ API ကို အသုံးပါ- Navigation Timing 2။"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() ကို ရပ်ဆိုင်းထားပြီး ၎င်းအစား စံသတ်မှတ်ထားသည့် ဤ API ကို အသုံးပြုပါ- Paint Timing။"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() ကို ရပ်ဆိုင်းထားပြီး ၎င်းအစား စံသတ်မှတ်ထားသည့် ဤ API ကို အသုံးပြုပါ- Navigation Timing 2 ရှိ nextHopProtocol။"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) အက္ခရာပါဝင်သော ကွတ်ကီးများကို ဖြတ်တောက်မည့်အစား ငြင်းပယ်ပါမည်။"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain သတ်မှတ်ခြင်းဖြင့် မူရင်းတူညီသည့် မူဝါဒ ဖယ်ရှားခြင်းကို ရပ်ဆိုင်းထားပြီး မူရင်းသတ်မှတ်ချက်အနေဖြင့် ပိတ်ပါမည်။ ဤရပ်ဆိုင်းမှု သတိပေးချက်သည် ဇာစ်မြစ်များကြား အသုံးပြုခွင့်အတွက်ဖြစ်ပြီး ၎င်းကို document.domain သတ်မှတ်ခြင်းဖြင့် ဖွင့်ထားသည်။"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "iframe ဇာစ်မြစ်အမျိုးမျိုးမှ window.alert စတင်ခြင်းကို ရပ်ဆိုင်းထားပြီး နောက်ပိုင်းတွင်ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "iframe ဇာစ်မြစ်အမျိုးမျိုးမှ window.confirm စတင်ခြင်းကို ရပ်ဆိုင်းထားပြီး နောက်ပိုင်းတွင်ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ဒေတာအတွက် ပံ့ပိုးမှု- SVGUse အစိတ်အပိုင်းရှိ URL များကို ရပ်ဆိုင်းလိုက်သဖြင့် နောင်တွင် ၎င်းကိုဖယ်ရှားလိုက်ပါမည်။"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() နှင့် watchPosition() သည် လုံခြုံမှုမရှိသော ဇာစ်မြစ်များတွင် ဆက်လက်မလုပ်ဆောင်တော့ပါ။ ဤဝန်ဆောင်မှုကို အသုံးပြုရန် သင့်အပလီကေးရှင်းကို HTTPS ကဲ့သို့ လုံခြုံသည့်ဇာစ်မြစ်သို့ ပြောင်းရန် စဉ်းစားသင့်သည်။ နောက်ထပ်အသေးစိတ်အတွက် https://goo.gle/chrome-insecure-origins တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() နှင့် watchPosition() တို့ကို လုံခြုံမှုမရှိသော ဇာစ်မြစ်များတွင် ရပ်ဆိုင်းထားသည်။ ဤဝန်ဆောင်မှုကို အသုံးပြုရန် သင့်အပလီကေးရှင်းကို HTTPS ကဲ့သို့ လုံခြုံသည့်ဇာစ်မြစ်သို့ ပြောင်းရန် စဉ်းစားသင့်သည်။ နောက်ထပ်အသေးစိတ်အတွက် https://goo.gle/chrome-insecure-origins တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() သည် လုံခြုံမှုမရှိသော ဇာစ်မြစ်များတွင် ဆက်လက်မလုပ်ဆောင်တော့ပါ။ ဤဝန်ဆောင်မှုကို အသုံးပြုရန် သင့်အပလီကေးရှင်းကို HTTPS ကဲ့သို့ လုံခြုံသည့်ဇာစ်မြစ်သို့ ပြောင်းရန် စဉ်းစားသင့်သည်။ နောက်ထပ်အသေးစိတ်အတွက် https://goo.gle/chrome-insecure-origins တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>၊ <aside>၊ <nav> (သို့) <section> အတွင်း <h1> တဂ်ကို တွေ့ထားပြီး ၎င်းတွင် သတ်မှတ်ထားသော ဖောင့်အရွယ်အစား မရှိပါ။ ယင်းခေါင်းစဉ်စာသား အရွယ်အစားသည် ဤဘရောင်ဇာ၌ နောင်သိပ်မကြာမီ ပြောင်းသွားမည်။ ပိုသိရှိရန် https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 ကို ကြည့်ပါ။"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate ကို ရပ်ဆိုင်းထားသည်။ ၎င်းအစား RTCPeerConnectionIceErrorEvent.address (သို့) RTCPeerConnectionIceErrorEvent.port ကို အသုံးပြုနိုင်သည်။"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ဒစ်ဂျစ်တယ် အထောက်အထားများဆိုင်ရာ navigator.credentials.get() တောင်းဆိုချက်အတွက် ဤဖော်မက်ကို ရပ်ဆိုင်းထားသည်။ ဖော်မက်အသစ်သုံးရန် သင့်ခေါ်ဆိုမှုကို အပ်ဒိတ်လုပ်ပါ။"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment ဝန်ဆောင်မှုဆောင်ရွက်စနစ် အစီအစဉ်မှ ရောင်းသူဇာစ်မြစ်နှင့် မည်သည့်ဒေတာကိုမဆို ရပ်ဆိုင်းထားပြီး ဖယ်ရှားပါမည်- topOrigin၊ paymentRequestOrigin၊ methodData၊ modifiers။"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "အသုံးပြုသူများ၏ အထူးအခွင့်အရေးရှိသည့် ကွန်ရက်အနေအထားဖြစ်သောကြောင့် ၎င်းကိုသုံးခွင့်ရှိသည့် ကွန်ရက်တစ်ခုမှ ရင်းမြစ်ခွဲတစ်ခုကို ဝဘ်ဆိုက်က တောင်းဆိုထားသည်။ ဤတောင်းဆိုမှုများက အများသုံးမဟုတ်သည့် စက်ပစ္စည်းနှင့် ဆာဗာများကို အင်တာနက်တွင် ဖော်ထုတ်သဖြင့် အခြားဝဘ်ဆိုက်မှ တောင်းဆိုကာ လိမ်လည်သော (CSRF) တိုက်ခိုက်မှု နှင့်/သို့မဟုတ် အချက်အလက် ပေါက်ကြားခြင်း အန္တရာယ်များကို ပိုတိုးစေသည်။ ထိုအန္တရာယ်များကို လျော့ပါးစေရန် အများသုံးမဟုတ်သည့် ရင်းမြစ်ခွဲများကို မလုံခြုံသော အခြေအနေများမှ တောင်းဆိုသောအခါ Chrome က ရပ်ဆိုင်းပြီး ၎င်းတို့ကို စတင်ပိတ်ပင်ပါမည်။"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "၎င်း၏လုပ်ဆောင်ပုံကို ပိုမိုတိကျမှန်ကန်စွာ ပြသနိုင်ရန် joinAdInterestGroup() သို့ ပို့သော InterestGroups ၏ dailyUpdateUrl ရည်ညွှန်းချက်ကို updateUrl ဟု အမည်ပြောင်းလိုက်သည်။"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator ကို ရပ်ဆိုင်းထားသည်။ ၎င်းအစား Intl.Segmenter ကို အသုံးပြုပါ။"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css ဖိုင်နောက်ဆက်တွဲဖြင့် အဆုံးမသတ်ပါက CSS ကို file: URL များမှ ဖွင့်၍မရပါ။"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove() ၏ ပြတ်တောင်းသည့် အပိုင်းအခြား ဖယ်ရှားခြင်းကို ဖျက်သိမ်းရန် SourceBuffer.abort() အသုံးပြုခြင်းအား သတ်မှတ်ချက် ပြောင်းလဲမှုကြောင့် ရပ်ဆိုင်းထားသည်။ ပံ့ပိုးမှုကို နောက်ပိုင်းတွင် ဖယ်ရှားပါမည်။ ၎င်းအစား updateend အစီအစဉ်ကို နားထောင်သင့်သည်။ abort() ကို ပြတ်တောင်းသည့် မီဒီယာ ပူးတွဲမှု (သို့) ပါဆာ အခြေအနေ ပြင်ဆင်သတ်မှတ်မှုကို ဖျက်သိမ်းရန်သာ ရည်ရွယ်သည်။"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "ယာယီကြားခံထားသည့် ကုဒ်ထည့်သွင်းထားသော ဖရိမ်တိုင်း၏ MediaSource.duration အောက် အမြင့်ဆုံးတင်ပြမှု အချိန်ဖော်ပြချက်ကို သတ်မှတ်ချက် ပြောင်းလဲမှုကြောင့် ရပ်ဆိုင်းထားသည်။ ဖြတ်တောက်ထားသည့် မီဒီယာ၏ သွယ်ဝိုက်ဖယ်ရှားမှုအတွက် ပံ့ပိုးမှုကို အနာဂတ်တွင် ဖယ်ရှားပါမည်။ ၎င်းအစား newDuration < oldDuration ရှိ sourceBuffers အားလုံးတွင် ရှင်းလင်းသည့်remove(newDuration, oldDuration) ကို လုပ်ဆောင်သင့်သည်။"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Sysex ကို MIDIOptions တွင် သတ်မှတ်မထားလျှင်ပင် ဝဘ် MIDI သည် အသုံးပြုရန် ခွင့်ပြုချက်ကို တောင်းဆိုပါမည်။"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "‘အကြောင်းကြားချက် API’ ကို လုံခြုံမှုမရှိသည့် ဇာစ်မြစ်များမှ အသုံးပြုနိုင်တော့မည် မဟုတ်ပါ။ သင့်အပလီကေးရှင်းကို HTTPS ကဲ့သို့ လုံခြုံသည့်ဇာစ်မြစ်သို့ ပြောင်းရန် စဉ်းစားသင့်သည်။ နောက်ထပ်အသေးစိတ်အတွက် https://goo.gle/chrome-insecure-origins တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "‘အကြောင်းကြားချက် API’ အတွက် ခွင့်ပြုချက်ကို iframe ဇာစ်မြစ်များကြားတွင် တောင်းဆိုမည် မဟုတ်တော့ပါ။ ထိပ်ဆုံးအဆင့် ဖရိမ်တစ်ခုမှ ခွင့်ပြုချက်တောင်းရန် (သို့) ဝင်းဒိုးသစ်တစ်ခုဖွင့်ရန် စဉ်းစားသင့်သည်။"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap ရှိ imageOrientation: 'none' ရွေးချယ်စရာကို ရပ်ဆိုင်းထားသည်။ ၎င်းအစား '{imageOrientation: ‘from-image’}' ရွေးချယ်စရာ ပါဝင်သည့် createImageBitmap ကို သုံးပါ။"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "သင့်ပါတနာသည် (D)TLS ဗားရှင်းအဟောင်းကို ဆက်သုံးနေသည်။ ၎င်းကိုဖြေရှင်းရန် သင့်ပါတနာနှင့် ဆက်သွယ်ပါ။"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "ပုံ၊ ဗီဒီယိုနှင့် ကင်းဗတ်စ်တဂ်များတွင် overflow: visible သတ်မှတ်ခြင်းကြောင့် အစိတ်အပိုင်းနယ်သတ်များ ပြင်ပတွင် အမြင်ပိုင်းအချက်အလက်များ ပေါ်ပေါက်နိုင်သည်။ https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments ကို ရပ်ဆိုင်းထားသည်။ ၎င်းအစား ပေးချေမှုစီမံသူအတွက် အချိန်မီထည့်သွင်းမှုကို သုံးပေးပါ။"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "သင့် PaymentRequest ခေါ်ဆိုမှုသည် အချက်အလက်-လုံခြုံမှု-မူဝါဒ (CSP) ဆိုင်ရာ connect-src လမ်းညွှန်ချက်ကို ရှောင်ကွင်းသွားသည်။ ဤရှောင်ကွင်းမှုကို ရပ်ဆိုင်းထားသည်။ PaymentRequest API (supportedMethods အကွက်ရှိ) မှ ငွေပေးချေနည်းလမ်း သတ်မှတ်မှုစနစ်ကို သင့် CSP connect-src လမ်းညွှန်ချက်တွင် ထည့်ပါ။"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent ကို ရပ်ဆိုင်းထားသည်။ ၎င်းအစား စံသတ်မှတ်ထားသည့် navigator.storage ကို အသုံးပြုပါ။"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> ပင်မ ရှိသည့် <source src> သည် မမှန်ကန်သဖြင့် လစ်လျူရှုလိုက်သည်။ ၎င်းအစား <source srcset> ကို အသုံးပြုပါ။"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame သည် ရောင်းချသူ သတ်မှတ်ချက်ဖြစ်သည်။ ၎င်းအစား စံသတ်မှတ်ထားသော cancelAnimationFrame ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame သည် ရောင်းချသူ သတ်မှတ်ချက်ဖြစ်သည်။ ၎င်းအစား စံသတ်မှတ်ထားသော requestAnimationFrame ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ကို ဆက်မသုံးတော့ပါ။ ၎င်းအစား Document.fullscreenElement သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Element.requestFullscreen() ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Element.requestFullscreen() ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Document.exitFullscreen() ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Document.exitFullscreen() ကို သုံးပါ။"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Document.fullscreenEnabled ကို သုံးပါ။"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled ကို ရပ်ဆိုင်းတော့မည် ဖြစ်သော်လည်း M113 ဖြန့်ချိမှု မတိုင်မီအထိ ယခင်စနစ်နှင့် တွဲဖက်သုံးနိုင်ရန်အတွက် ဆက်လက်ထားရှိပါမည်။ ၎င်းအစား chrome.privacy.websites.topicsEnabled၊ chrome.privacy.websites.fledgeEnabled နှင့် chrome.privacy.websites.adMeasurementEnabled တို့ကို အသုံးပြုပါ။ https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled တွင်ကြည့်ပါ။"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement ကန့်သတ်ချက်ကို ဖယ်ရှားထားသည်။ ဖယ်ရှားထားသည့် SDES key negotiation နည်းလမ်း အသုံးပြုရန် ကြိုးပမ်းမှုတစ်ခုအနေဖြင့် နားလည်နိုင်သော ဤကန့်သတ်ချက်အတွက် false တန်ဖိုးကို သင် သတ်မှတ်ထားသည်။ ဤလုပ်ဆောင်ချက်ကို ဖယ်ရှားထားသည်။ ၎င်းအစား DTLS key negotiation ပံ့ပိုးသည့် ဝန်ဆောင်မှုတစ်ခု အသုံးပြုပါ။"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement ကန့်သတ်ချက်ကို ဖယ်ရှားထားသည်။ ဤကန့်သတ်ချက်အတွက် true တန်ဖိုးကို သင် သတ်မှတ်ထားပြီး ၎င်းသည်သက်ရောက်မှုမရှိသော်လည်း ရှင်းလင်းမှုအတွက် ဤကန့်သတ်ချက်ကို ဖယ်ရှားနိုင်သည်။"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "ပြန်ခေါ်ခြင်းအခြေခံ getStats() ကို ရပ်ဆိုင်းပြီး ဖယ်ရှားလိုက်ပါမည်။ ၎င်းအစား သတ်မှတ်ချက်လိုက်နာသော getStats() ကို သုံးပါ။"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ကို ရပ်ဆိုင်းလိုက်သည်။ ၎င်းအစား Selection.modify() ကို သုံးပါ။"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ရင်းမြစ်ခွဲ တောင်းဆိုမှုများ၏ URL များတွင် ပါဝင်သော မြှုပ်သွင်းထားသည့် အထောက်အထားများ (ဥပမာ- **********************/) ကို ပိတ်ထားသည်။"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy ရွေးချယ်မှုကို ရပ်ဆိုင်းထားပြီး ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer က ဇာတ်မြစ်များကြားမှ ခွဲခြားမှုကို လိုအပ်ပါမည်။ နောက်ထပ်အသေးစိတ်အတွက် https://developer.chrome.com/blog/enabling-shared-array-buffer/ တွင် ကြည့်ပါ။"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "အသုံးပြုသူက စတင်မှုမရှိသော speechSynthesis.speak() ကို ရပ်ဆိုင်းထားပြီး ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | UnloadHandler": {"message": "ဖြစ်ရပ်ဖယ်ရှားခြင်း နားထောင်စနစ်များကို ဆက်မသုံးတော့ဘဲ ဖယ်ရှားပါမည်။"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer ကို ဆက်လက်အသုံးပြုရန် နောက်ဆက်တွဲများသည် ဇာစ်မြစ်များကြားမှ ခွဲခြားမှုတွင် အပါအဝင် ဖြစ်သင့်သည်။ https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ ကို ကြည့်ပါ။"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ရည်ညွှန်းချက်ကို ရပ်ဆိုင်းထားပြီး ၎င်းအစား GPUAdapterInfo isFallbackAdapter ရည်ညွှန်းချက်ကို သုံးပါ။"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest တွင် json အလိုက်သင့်တုံ့ပြန်မှုက UTF-16 ကို ပံ့ပိုးမထားပါ"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "အဓိက Thread ရှိ စင့်ခ်လုပ်ထားသော XMLHttpRequest သည် အသုံးပြုသူ၏ အတွေ့အကြုံအပေါ် ထိခိုက်စေနိုင်သည့်အတွက် ရပ်ဆိုင်းထားသည်။ နောက်ထပ်အကူအညီအတွက် https://xhr.spec.whatwg.org/ တွင် ကြည့်ပါ။"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "လှုပ်ရှားသက်ဝင်ပုံ"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "အစိတ်အပိုင်းလှုပ်ရှားမှုတွင် အသုံးပြုသူ၏ ပြန်လှန်တုံ့ပြန်မှုများ မပါဝင်သည့်အခါ အပြင်အဆင်ပြောင်းလဲမှုများ ဖြစ်ပေါ်သည်။ စာမျက်နှာဖွင့်သည့်အခါ အစိတ်အပိုင်းထည့်ခြင်း၊ ဖယ်ရှားခြင်း (သို့) ၎င်းတို့၏ဖောင့်များ ပြောင်းလဲခြင်းကဲ့သို့ [အပြင်အဆင်ပြောင်းလဲမှုများ၏ အကြောင်းရင်းများကို စစ်ဆေးနိုင်သည်](https://web.dev/articles/optimize-cls)။"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ဖောင့်တောင်းဆိုချက်"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "ထည့်သွင်းထားသော iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "အပြင်အဆင်ပြောင်းလဲမှုအစု @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "မည်သည့်အပြင်အဆင်ပြောင်းလဲမှု ပြဿနာကိုမျှ မတွေ့ပါ"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "အပြင်အဆင်ပြောင်းလဲမှု မရှိပါ"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "အပြင်အဆင်ပြောင်းလဲမှု ပြဿနာများ"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "အကောင်းဆုံး အပြင်အဆင်ပြောင်းလဲမှု ပြဿနာများ"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "အရွယ်တိုင်းမထားသော ပုံအစိတ်အပိုင်း"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "အဆိုးဆုံးအစု"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "အဆိုးဆုံး အပြင်အဆင်ပြောင်းလဲမှု အစု"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "ကက်ရှ် TTL"}, "models/trace/insights/Cache.ts | description": {"message": "ကက်ရှ်သက်တမ်း ကြာရှည်မှုသည် သင့်စာမျက်နှာသို့ ထပ်မံဝင်ကြည့်ခြင်းများကို ပိုမြန်စေနိုင်သည်။ [ပိုလေ့လာပါ](https://web.dev/uses-long-cache-ttl/)။"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "မလုံလောက်သော ကက်ရှ်ဆိုင်ရာ မူဝါဒများအတွက် တောင်းဆိုချက်မရှိပါ"}, "models/trace/insights/Cache.ts | others": {"message": "အခြား {PH1} ခု"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "တောင်းဆိုချက်"}, "models/trace/insights/Cache.ts | title": {"message": "လုံလောက်သော ကက်ရှ်သက်တမ်းကို သုံးပါ"}, "models/trace/insights/DOMSize.ts | description": {"message": "ကြီးမားသည့် DOM သည် ပုံစံတွက်ချက်မှု၊ အပြင်အဆင် ပြန်လည်တွက်ချက်မှုများ၏ ကြာချိန်၊ စာမျက်နှာ အလိုက်သင့်တုံ့ပြန်မှု၏ သက်ရောက်မှုတို့ကို တိုးစေနိုင်သည်။ ကြီးမားသည့် DOM သည် မှတ်ဉာဏ်အသုံးပြုမှုကိုလည်း တိုးစေနိုင်သည်။ [DOM အရွယ်အစား လွန်ကဲမှု မရှိစေသည့်နည်းလမ်းကို လေ့လာရန်](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)။"}, "models/trace/insights/DOMSize.ts | element": {"message": "အစိတ်အပိုင်း"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "အခွဲ အများစု"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM အနက်"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "ကိန်းဂဏန်း စာရင်း"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM အရွယ်အစားကို အကောင်းဆုံးချိန်ညှိခြင်း"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "စုစုပေါင်း အစိတ်အပိုင်း"}, "models/trace/insights/DOMSize.ts | value": {"message": "တန်ဖိုး"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "သင်၏ ပထမဆုံး ကွန်ရက်တောင်းဆိုချက်သည် အရေးအကြီးဆုံးဖြစ်သည်။  တစ်ဆင့်ပြန်ညွှန်ပြမှုများ မဖြစ်စေခြင်း၊ ဆာဗာတုံ့ပြန်မှု မြန်စေခြင်းနှင့် စာသားချုံ့ခြင်းကို ဖွင့်ပေးခြင်းတို့ဖြင့် ၎င်း၏တုံ့ပြန်ချိန်ကို လျှော့ချပါ။"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "တစ်ဆင့်ပြန်ညွှန်ပြခြင်းများ ရှိသည် (တစ်ဆင့်ပြန်ညွှန်ပြမှု {PH1} ခါ၊ +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "ဆာဗာက နှေးကွေးစွာ တုံ့ပြန်သည် ({PH1} ဟု တွေ့ရှိသည်)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "ချုံ့ခြင်း သုံးမထားပါ"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "တစ်ဆင့်ပြန်ညွှန်ပြခြင်းများကို ရှောင်ရှားသည်"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "ဆာဗာက လျင်မြန်စွာ တုံ့ပြန်သည် ({PH1} ဟု တွေ့ရှိသည်)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "စာသားချုံ့ခြင်း သုံးထားသည်"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "တစ်ဆင့်ပြန်ညွှန်ပြမှုများ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "ဆာဗာတုံ့ပြန်ချိန်"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "မှတ်တမ်းတောင်းဆိုချက် တုံ့ပြန်ချိန်"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "ချုံ့မထားသော ဒေါင်းလုဒ်"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ထပ်နေသော ဘိုက်များ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ရင်းမြစ်"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "ကွန်ရက်လုပ်ဆောင်ချက်၏ မလိုအပ်သော ဘိုက်အသုံးပြုမှုကို လျှော့ချရန်အတွက် ကြီးမားပြီး ထပ်နေသော JavaScript မော်ဂျူးများကို အတွဲများမှ ဖယ်ရှားနိုင်သည်။"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ထပ်နေသော JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "စာသားကို အပြောင်းအလဲမရှိ မြင်နိုင်ရကြောင်း သေချာစေရန်အတွက် [font-display](https://developer.chrome.com/blog/font-display) ကို swap သို့မဟုတ် optional သို့ သတ်မှတ်ရန် စဉ်းစားပါ။ [ဖောင့်မက်ထရစ် အစားထိုးမှုများ](https://developer.chrome.com/blog/font-fallbacks) ဖြင့် အပြင်အဆင်ပြောင်းလဲမှုများ လျော့ပါးစေရန်အတွက် swap ကို ဆက်လက်၍ အကောင်းဆုံးချိန်ညှိနိုင်သည်။"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ဖောင့်"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ဖောင့်ပြကွက်"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "လေလွင့်သွားသော အချိန်"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(အမည်မသိ)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "API အများအပြားက အပြင်အဆင် ပုံသဏ္ဍန်ကို ထုံးစံအတိုင်းဖတ်ရှုသည်။ ပုံစံနှင့် အပြင်အဆင်ကို တွက်ချက်ရန်အတွက် Script စီမံဆောင်ရွက်ခြင်းကို ခဏရပ်ရန် အင်ဂျင် ပုံဖော်ဆောင်မှုကို မဖြစ်မနေဆောင်ရွက်သည်။ [မဖြစ်မနေ ပြန်လည်တွက်ချက်ခြင်း](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) နှင့် ၎င်း၏ လျှော့ပေါ့ပေးမှုများအကြောင်း ပိုမိုလေ့လာနိုင်သည်။"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "သုံးနည်းမှတ်တမ်း"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "မဖြစ်မနေ ပြန်လည်တွက်ချက်ခြင်း"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "ထိပ်တန်းလုပ်ဆောင်ချက် ခေါ်ဆိုမှု"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "ပြန်လည်တွက်ချက်သည့် အချိန်စုစုပေါင်း"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ရည်ညွှန်းမထား]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "ပုံများ၏ ဒေါင်းလုဒ်လုပ်ချိန် လျှော့ချခြင်းဖြင့် ကြုံတွေ့နိုင်သော စာမျက်နှာဖွင့်ချိန်နှင့် LCP ကို ပိုကောင်းစေနိုင်သည်။ [ပုံအရွယ်အစား အကောင်းဆုံးချိန်ညှိခြင်းအကြောင်း ပိုမိုလေ့လာရန်](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ခန့်မှန်း {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "အကောင်းဆုံးချိန်ညှိနိုင်သောပုံ မရှိပါ"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ဖိုင်အရွယ်အစားကို အကောင်းဆုံးချိန်ညှိခြင်း"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "အခြား {PH1} ခု"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ပုံ ပေးပို့မှု ပိုကောင်းအောင်လုပ်ခြင်း"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "ပုံချုံ့သည့်ဆခွဲကိန်း တိုးခြင်းသည် ဤပုံ၏ ဒေါင်းလုဒ်အရွယ်အစားကို ပိုကောင်းစေနိုင်သည်။"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "ခေတ်မီ ပုံဖိုင်အမျိုးအစား (WebP၊ AVIF) သုံးခြင်း (သို့) ပုံချုံ့ခြင်းကို တိုးမြှင့်ခြင်းသည် ဤပုံ၏ ဒေါင်းလုဒ်အရွယ်အစားကို ပိုကောင်းစေနိုင်သည်။"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ဤပုံပါဝင်သော ဖိုင်သည် ၎င်း၏အတိုင်းအတာများ ({PH2}) အတွက် လိုအပ်သည့် ({PH1}) ထက် ပိုကြီးနေသည်။ ပုံ ဒေါင်းလုဒ်အရွယ်အစားကို လျှော့ချရန် အလိုက်သင့်တုံ့ပြန်သော ပုံများ သုံးပါ။"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF များအစား ဗီဒီယိုဖော်မက်များ အသုံးပြုခြင်းသည် လှုပ်ရှားသက်ဝင် အကြောင်းအရာ၏ ဒေါင်းလုဒ်အရွယ်အစားကို ပိုကောင်းအောင်လုပ်နိုင်သည်။"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "အရှည်ဆုံးအဆင့်ဖြင့် စတင်စစ်ဆေးပါ။ [နှောင့်နှေးမှုများကို လျှော့ချနိုင်သည်](https://web.dev/articles/optimize-inp#optimize_interactions)။ စီမံဆောင်ရွက်မှု ကြာချိန်ကို လျှော့ချရန်အတွက် မကြာခဏ JS ၏ [အဓိကစာတွဲ ကုန်ကျစရိတ်များကို အကောင်းဆုံးချိန်ညှိပါ](https://web.dev/articles/optimize-long-tasks)။"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "ကြာချိန်"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ဖိတ်ခေါ်မှု နှောင့်နှေးခြင်း"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ပြန်လှန်တုံ့ပြန်မှု မတွေ့ပါ"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "အဆင့်"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "တင်ပြမှု နှောင့်နှေးခြင်း"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "စီမံဆောင်ရွက်မှု ကြာချိန်"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "အဆင့်အလိုက် INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP ရုပ်ပုံကို HTML တွင် ချက်ချင်း [ရှာဖွေတွေ့နိုင်အောင်](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) ပြုလုပ်ခြင်းနှင့် [နေရာကွက်၍ ဖွင့်ခြင်း မဖြစ်စေခြင်း](https://web.dev/articles/lcp-lazy-loading) တို့ဖြင့် LCP ကို အကောင်းဆုံးချိန်ညှိနိုင်သည်"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high သုံးထားသည်"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "‘ဦးစားပေးရယူမှု=မြင့်’ ကို သုံးသင့်သည်"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "နေရာကွက်၍ ဖွင့်ခြင်းကို သုံးမထားပါ"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "အစောဆုံး စတင်သည့်အချိန်မှ {PH1} ကြာပြီးနောက် LCP ပုံ ပွင့်သည်။"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP မတွေ့ပါ"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP သည် ပုံမဟုတ်သောကြောင့် LCP ရင်းမြစ်ကို မတွေ့ပါ"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "တောင်းဆိုချက်ကို မှတ်တမ်း၏ အစပိုင်းတွင် ရှာဖွေတွေ့နိုင်သည်"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP တောင်းဆိုချက် စူးစမ်းရှာဖွေခြင်း"}, "models/trace/insights/LCPPhases.ts | description": {"message": "[အဆင့်တစ်ခုစီတွင် သတ်မှတ်ထားသော ပိုကောင်းအောင်လုပ်သည့် နည်းဗျူဟာများရှိသည်](https://web.dev/articles/optimize-lcp#lcp-breakdown)။ LCP အချိန်အများစုကို နှောင့်နှေးမှုများအတွင်း မဟုတ်ဘဲ ရင်းမြစ်များဖွင့်ရာတွင် အသုံးပြုပါက အကောင်းဆုံးဖြစ်သည်။"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "ကြာချိန်"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "အစိတ်အပိုင်းပုံဖော်ခြင်း နှောင့်နှေးမှု"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "အကွက် p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP မတွေ့ပါ"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "အဆင့်"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ရင်းမြစ်ဖွင့်ခြင်း နှောင့်နှေးမှု"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "ရင်းမြစ်ဖွင့်သည့် ကြာချိန်"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "ပထမဆုံးဘိုက် အသုံးပြုချိန်"}, "models/trace/insights/LCPPhases.ts | title": {"message": "အဆင့်အလိုက် LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "လေလွင့်သွားသော ဘိုက်များ"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill နှင့် ကုဒ်ပြောင်းလဲမှုများသည် ဘရောင်ဇာအဟောင်းများအား JavaScript တူးလ်အသစ်များကို ပံ့ပိုးသည်။ သို့သော် ဘရောင်ဇာသစ်များအတွက် အများအပြား မလိုအပ်ပါ။ ဘရောင်ဇာအဟောင်းများကို ပံ့ပိုးရန်လိုအပ်ကြောင်း မသိပါက [Baseline](https://web.dev/articles/baseline-and-polyfills) တူးလ်များ ကုဒ်မပြောင်းစေရန် JavaScript တည်ဆောက်မှု လုပ်ငန်းစဉ်ကို မွမ်းမံကြည့်ပါ။ [ဝဘ်ဆိုက်အများစုက ES6+ ကုဒ်ကို ပြောင်းရန်မလိုဘဲ ထည့်သွင်းနိုင်သည့် အကြောင်းရင်းကို လေ့လာရန်](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript ဗားရှင်းဟောင်း"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 နှင့် HTTP/3 တို့သည် မာလ်တီပလက်ဆင်းကဲ့သို့ HTTP/1.1 ထက် အကျိုးခံစားခွင့်များပေးသည်။ [ခေတ်မီ HTTP သုံးခြင်းအကြောင်း ပိုလေ့လာပါ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)။"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 သုံးထားသော တောင်းဆိုချက် မရှိပါ"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "ပရိုတိုကော"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "တောင်းဆိုချက်"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "ခေတ်မီ HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ဇာစ်မြစ်"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "တောင်းဆိုချက်"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ရင်းမြစ်"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "အချိန်"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Est LCP အချိန်ချွေတာမှုများ"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "သုံးမထားသည့် ကြိုတင်ချိတ်ဆက်မှု။ crossorigin ရည်ညွှန်းချက်ကို မှန်ကန်စွာ သုံးထားခြင်းရှိမရှိ စစ်ပါ။"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "စာမျက်နှာ ပိုကောင်းမွန်စွာ ဖွင့်နိုင်ရန်အတွက် တွဲဆက်မှုများ၏ အရှည်ကို လျှော့ချခြင်း၊ ရင်းမြစ်များ၏ ဒေါင်းလုဒ်အရွယ်အစားကို လျှော့ချခြင်း (သို့) မလိုအပ်သော ရင်းမြစ်များ၏ ဒေါင်းလုဒ်ကို ရွှေ့ဆိုင်းခြင်းတို့ဖြင့် [အရေးပါသော တောင်းဆိုချက်များ တွဲဆက်ထားခြင်းကို ရှောင်ရှားပါ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)။"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "သင်၏ အရေးပါဆုံးဇာစ်မြစ်များတွင် [ကြိုတင်ချိတ်ဆက်ခြင်း](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) အကြံပြုချက်များ ထည့်နိုင်သော်လည်း ၄ ခုထက် ပိုမသုံးသင့်ပါ။"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ကြိုတင်ချိတ်ဆက်မှု ရွေးစရာများ"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "အလွန်အရေးကြီးသည့် လမ်းကြောင်း အများဆုံး တုံ့ပြန်ချိန်-"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ကွန်ရက်မှီခိုမှုများအလိုက် သက်ရောက်သော ပုံဖော်ခြင်းလုပ်ဆောင်စရာ မရှိပါ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "ကြိုတင်ချိတ်ဆက်ခြင်းအတွက် ရွေးစရာ ထပ်ဆောင်းဇာစ်မြစ်ကောင်းများ မရှိပါ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ကြိုတင်ချိတ်ဆက်ထားသော ဇာစ်မြစ်မရှိပါ"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[ကြိုတင်ချိတ်ဆက်ခြင်း](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) အကြံပြုချက်များသည် စာမျက်နှာဖွင့်ရာတွင် ချိတ်ဆက်မှုတည်ဆောက်ပေးရန်နှင့် ၎င်းဇာစ်မြစ်အတွက် ပထမဆုံး တောင်းဆိုချက် ပြုလုပ်သောအခါ အချိန်ချွေတာနိုင်ရန် ဘရောင်ဇာကို ကူညီပေးသည်။ အောက်ပါတို့သည် စာမျက်နှာက ကြိုတင်ချိတ်ဆက်ထားသော ဇာစ်မြစ်များဖြစ်သည်။"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "ကြိုတင်ချိတ်ဆက်ထားသော ဇာစ်မြစ်များ"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ကွန်ရက်မှီခိုမှု ဆက်နွယ်စနစ်"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "preconnect ချိတ်ဆက်မှု ၄ ခုအထက်ကို တွေ့ရှိသည်။ ၎င်းတို့ကို ချွေတာသုံးစွဲသင့်ပြီး အရေးအပါဆုံး ဇာစ်မြစ်များတွင်သာ သုံးသင့်သည်။"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "သုံးမထားသည့် ကြိုတင်ချိတ်ဆက်မှု။ စာမျက်နှာက တောင်းဆိုနိုင်ဖွယ်ရှိသော ဇာစ်မြစ်များအတွက်သာ preconnect ကို သုံးပါ။"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "စာမျက်နှာ ပိုကောင်းမွန်စွာ ဖွင့်နိုင်ရန်အတွက် တွဲဆက်မှုများ၏ အရှည်ကို လျှော့ချခြင်း၊ ရင်းမြစ်များ၏ ဒေါင်းလုဒ်အရွယ်အစားကို လျှော့ချခြင်း (သို့) မလိုအပ်သော ရင်းမြစ်များ၏ ဒေါင်းလုဒ်ကို ရွှေ့ဆိုင်းခြင်းတို့ဖြင့် အရေးပါသော တောင်းဆိုချက်များ တွဲဆက်ထားခြင်းကို ရှောင်ရှားပါ။"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "တောင်းဆိုချက်များသည် စာမျက်နှာ၏ ကနဦးပုံဖော်ခြင်းကို ပိတ်ထားပြီး LCP ကို နှောင့်နှေးစေနိုင်သည်။ [ရွှေ့ဆိုင်းခြင်း (သို့) အင်လိုင်းလုပ်ခြင်း](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) သည် ဤကွန်ရက်တောင်းဆိုချက်များကို အရေးပေါ်လမ်းကြောင်းပြင်ပသို့ ရွှေ့နိုင်သည်။"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "ကြာချိန်"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ဤလမ်းညွှန်မှုအတွက် ပုံဖော်ခြင်းကို တားဆီးသည့် တောင်းဆိုချက် မရှိပါ"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "တောင်းဆိုချက်"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "ပုံဖော်ခြင်းကို တားဆီးသည့် တောင်းဆိုချက်များ"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "‘ပုံစံ ပြန်လည်တွက်ချက်ခြင်း’ ကုန်ကျစရိတ်များ ဆက်လက်မြင့်မားနေပါက ရွေးချယ်စနစ် အကောင်းဆုံးချိန်ညှိခြင်းသည် ၎င်းတို့ကို လျှော့ချနိုင်သည်။ ကြာချိန်များခြင်းနှင့် လမ်းကြောင်းပိုနှေးသည့် % နှစ်ခုစလုံးသုံး၍ [ရွေးချယ်စနစ်များကို အကောင်းဆုံးချိန်ညှိပါ](https://developer.chrome.com/docs/devtools/performance/selector-stats)။ ရိုးရှင်းသော/နည်းသော ရွေးချယ်စနစ်များ၊ သေးသော/ အပေါ်ယံဖြစ်သော DOM အားလုံးတို့သည် ကိုက်ညီသည့် ကုန်ကျစရိတ်များကို လျှော့ချပေးမည်။"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "ကုန်လွန်သွားချိန်"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS ရွေးချယ်စနစ်ဒေတာကို မတွေ့ပါ CSS ရွေးချယ်စနစ် အချက်အလက်ကို စွမ်းဆောင်ရည်အကန့် ဆက်တင်များတွင် ဖွင့်ထားရမည်။"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ကိုက်ညီသော ကြိုးပမ်းမှုများ"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "တူညီသောအရေအတွက်"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ရွေးချယ်စနစ် ကုန်ကျစရိတ်များ"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ထိပ်တန်း ရွေးချယ်စနစ်များ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "စုစုပေါင်း"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "အဓိက စာတွဲအချိန်"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "ပြင်ပကုမ္ပဏီ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "လွှဲပြောင်းရန် အရွယ်အစား"}, "models/trace/insights/ThirdParties.ts | description": {"message": "ပြင်ပကုမ္ပဏီကုဒ်သည် ဖွင့်သည့်စွမ်းဆောင်ရည်အပေါ် သိသာထင်ရှားစွာ သက်ရောက်နိုင်သည်။ သင့်စာမျက်နှာ၏ အကြောင်းအရာကို ဦးစားပေးရန်အတွက် [ပြင်ပကုမ္ပဏီကုဒ်ဖွင့်ခြင်းကို လျှော့ချပြီး ရွှေ့ဆိုင်းပါ](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)။"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "ပြင်ပကုမ္ပဏီ မတွေ့ပါ"}, "models/trace/insights/ThirdParties.ts | title": {"message": "ပြင်ပကုမ္ပဏီ"}, "models/trace/insights/Viewport.ts | description": {"message": "မြင်နိုင်သော အစိတ်အပိုင်းကို မိုဘိုင်းအတွက် အကောင်းဆုံးချိန်ညှိမထားပါက တို့သည့်ပြန်လှန်တုံ့ပြန်မှုများသည် [၃၀၀ ms အထိ နှောင့်နှေး](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) နိုင်သည်။"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "မိုဘိုင်းတို့ထိမှု ကြန့်ကြာခြင်း"}, "models/trace/insights/Viewport.ts | title": {"message": "မြင်နိုင်သော အစိတ်အပိုင်းကို မိုဘိုင်းအတွက် အကောင်းဆုံးချိန်ညှိခြင်း"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET တောင်းဆိုချက်မှတစ်ဆင့် ဖွင့်ထားသောစာမျက်နှာများသာ ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးဝင်ပါသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "အခြေအနေပြကုတ် 2XX ပါသော စာမျက်နှာများကိုသာ ကက်ရှ်သိမ်းနိုင်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "ကက်ရှ်သိမ်းနေစဉ် Chrome သည် JavaScript စီမံဆောင်ရွက်ရန် ကြိုးပမ်းမှုတစ်ခု တွေ့ရှိသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ရှေ့-နောက် ကက်ရှ်ကို အလံပြခြင်းများက ပိတ်လိုက်သည်။ Chrome://flags/#back-forward-cache သို့ ဝင်ကြည့်ပြီး ဤစက်ပေါ်တွင် ဖွင့်လိုက်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "ရှေ့-နောက် ကက်ရှ်ကို ကွန်မန်းလိုင်းက ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "မှတ်ဉာဏ်မလုံလောက်သဖြင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "ရှေ့-နောက် ကက်ရှ်ကို မြှုပ်သွင်းခြင်းအတွက် မပံ့ပိုးပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "အကြိုပုံဖော်ခြင်းအတွက် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "မှတ်ပုံတင်ထားသော နားထောင်သူများပါသည့် BroadcastChannel ဖြစ်ရပ် ရှိသဖြင့် စာမျက်နှာကို ကက်ရှ်သိမ်း၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "ခေါင်းစီး cache-control:no-store ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "ဤကက်ရှ်ကို ရည်ရွယ်ချက်ရှိရှိ ရှင်းထုတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "စာမျက်နှာနောက်တစ်ခု ကက်ရှ်သိမ်းခွင့်ပြုရန် ဤစာမျက်နှာကို ကက်ရှ်မှထုတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "ပလတ်အင်များပါဝင်သော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "သတ်မှတ်မထားပါ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "‘မီဒီယာ စက်ပစ္စည်း ဆက်သွယ်စနစ်’ သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "ထွက်သွားချိန်တွင် မီဒီယာ ပလေယာကို ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API သုံးပြီး ဖွင့်ရန်အခြေအနေ သတ်မှတ်သော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API သုံးပြီး လုပ်ဆောင်ချက်စီမံသူများ သတ်မှတ်ထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "ဖန်သားပြင်ဖတ် အက်ပ်ကြောင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store သုံးသော စာမျက်နှာတွင် ကွတ်ကီးများ ပိတ်ထားသောကြောင့် ရှေ့-နောက် ကက်ရှ် ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "သတ်မှတ်ထားသော လုပ်သား (သို့) လုပ်ဆောင်စရာနေရာ သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "မှတ်တမ်းသည် ထွက်သွားချိန်အထိ ဖွင့်၍မပြီးသေးပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "ထွက်သွားချိန်တွင် ‘အက်ပ်နဖူးစည်း’ ကို ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "ထွက်သွားချိန်တွင် ‘Chrome စကားဝှက်မန်နေဂျာ’ ကို ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "ထွက်သွားချိန်တွင် DOM ရှင်းလင်းခြင်းကို ဆောင်ရွက်နေဆဲဖြစ်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "ထွက်သွားချိန်တွင် ‘DOM ရှင်းလင်းစွာ ကြည့်ရှုရေးစနစ်’ ကို ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "မက်ဆေ့ဂျ်ပို့ခြင်း API သုံးသော နောက်ဆက်တွဲများကြောင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "ရှေ့-နောက် ကက်ရှ်သို့ မဝင်မီ ကြာရှည်ဖွင့်ထားသော ချိတ်ဆက်မှုရှိသည့် နောက်ဆက်တွဲများသည် ချိတ်ဆက်မှုကို ပိတ်သင့်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "ကြာရှည်ဖွင့်ထားသော ချိတ်ဆက်မှုရှိသည့် နောက်ဆက်တွဲများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဖရိမ်များသို့ မက်ဆေ့ဂျ်ပို့ရန် ကြိုးပမ်းထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "နောက်ဆက်တွဲများကြောင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ထွက်သွားချိန်တွင် ဖောင်ပြန်လည်တင်ခြင်းကဲ့သို့ မိုဒယ်ဒိုင်ယာလော့ခ် (သို့) http စကားဝှက် ဒိုင်ယာလော့ခ်ကို ဝဘ်စာမျက်နှာအတွက် ပြထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "ထွက်သွားချိန်တွင် အော့ဖ်လိုင်းစာမျက်နှာကို ပြထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "ထွက်သွားချိန်တွင် ‘မှတ်ဉာဏ်ကုန်ဆုံးကြောင်း အသိပေးဘား’ ကို ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "ထွက်သွားချိန်တွင် ခွင့်ပြုချက်တောင်းဆိုချက်များ ရှိသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "ထွက်သွားချိန်တွင် ပေါ့ပ်အပ် ပိတ်စနစ် ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "ထွက်သွားချိန်တွင် ‘လုံခြုံစွာ ကြည့်ရှုခြင်း’ အသေးစိတ်ကို ပြထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "‘လုံခြုံစွာ ကြည့်ရှုခြင်း’ က ဤစာမျက်နှာကို အလွဲသုံးစားပြုသည်ဟု မှတ်ယူပြီး ပေါ့ပ်အပ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "စာမျက်နှာသည် ရှေ့-နောက် ကက်ရှ်တွင်ရှိစဉ် ServiceWorker ကို စတင်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "မှတ်တမ်းအမှားတစ်ခုကြောင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames ကို သုံးသည့် စာမျက်နှာများကို bfcache တွင် သိမ်း၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "စာမျက်နှာနောက်တစ်ခု ကက်ရှ်သိမ်းခွင့်ပြုရန် ဤစာမျက်နှာကို ကက်ရှ်မှထုတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "မီဒီယာထုတ်လွှင့်ခွင့် ပေးထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "မြှုပ်သွင်းထားသော အကြောင်းအရာအမျိုးအစားအချို့ (ဥပမာ PDF များ) ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ် အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "ဖွင့်ထားသော IndexedDB ချိတ်ဆက်မှုရှိသည့် စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "ရှေ့-နောက် ကက်ရှ်ကို IndexedDB ဖြစ်ရပ်အတွက် ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "အကျုံးမဝင်သော API များ သုံးထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "နောက်ဆက်တွဲများက JavaScript ထည့်သွင်းထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "နောက်ဆက်တွဲများက StyleSheet ထည့်သွင်းထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "အတွင်းပိုင်း အမှား။"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "JavaScript ကွန်ရက်တောင်းဆိုချက်အချို့သည် Cache-Control: no-store ခေါင်းစီးနှင့် ရင်းမြစ်ကို ရရှိထားသောကြောင့် ရှေ့-နောက် ကက်ရှ် ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "ဆက်ချိတ်ထားသည့် တောင်းဆိုချက်ကြောင့် ရှေ့-နောက် ကက်ရှ်ကို ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "ကီးဘုတ်လော့ခ်သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "စာမျက်နှာသည် ထွက်သွားချိန်အထိ ဖွင့်၍မပြီးသေးပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ပင်မရင်းမြစ်တွင် cache-control:no-cache ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ပင်မရင်းမြစ်တွင် cache-control:no-store ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ရှေ့-နောက် ကက်ရှ်မှ စာမျက်နှာကို ပြန်မယူနိုင်မီ ထွက်သွားခြင်းကို ရပ်ဆိုင်းလိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "ဖွင့်ထားသော ကွန်ရက်ချိတ်ဆက်မှုသည် ဒေတာအများအပြား လက်ခံနေသောကြောင့် ဤစာမျက်နှာကို ကက်ရှ်မှထုတ်လိုက်သည်။ ကက်ရှ်သိမ်းစဉ် စာမျက်နှာတစ်ခု လက်ခံနိုင်သော ဒေတာပမာဏကို Chrome က ကန့်သတ်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "လုပ်ဆောင်ဆဲ fetch() (သို့) XHR ရှိသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "လက်ရှိ ကွန်ရက်တောင်းဆိုချက်တွင် တစ်ဆင့်ပြန်ညွှန်ပြခြင်း ပါဝင်နေသောကြောင့် ဤစာမျက်နှာကို ရှေ့-နောက် ကက်ရှ်မှထုတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ကွန်ရက်ချိတ်ဆက်မှု အလွန်ကြာရှည်စွာ ဖွင့်ထားသောကြောင့် ဤစာမျက်နှာကို ကက်ရှ်မှထုတ်လိုက်သည်။ ကက်ရှ်သိမ်းစဉ် စာမျက်နှာတစ်ခု လက်ခံနိုင်သော အချိန်ပမာဏကို Chrome က ကန့်သတ်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "မှန်ကန်သောတုံ့ပြန်မှု ခေါင်းစီးမရှိသည့် စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "လမ်းညွှန်ခြင်းသည် ပင်မဖရိမ်ထက် ဖရိမ်တစ်ခုတွင် ဖြစ်ပေါ်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "လက်ရှိအက္ခရာစဉ် DB လွှဲပြောင်းမှုများပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "လုပ်ဆောင်ဆဲ ကွန်ရက်တောင်းဆိုချက်ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "လုပ်ဆောင်ဆဲ fetch ကွန်ရက်တောင်းဆိုချက် ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "လုပ်ဆောင်ဆဲ ကွန်ရက်တောင်းဆိုချက်ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "လုပ်ဆောင်ဆဲ XHR ကွန်ရက်တောင်းဆိုချက် ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "‘နှစ်ခုထပ်၍ ကြည့်ခြင်း’ သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "‘ပုံနှိပ်ထုတ်ယူမှု UI’ ပြသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "စာမျက်နှာကို 'window.open()' သုံး၍ ဖွင့်ထားပြီး တဘ်နောက်တစ်ခုတွင် ၎င်းသို့ ရည်ညွှန်းချက်ပါရှိသည် (သို့) စာမျက်နှာက ဝင်းဒိုးတစ်ခု ဖွင့်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "ရှေ့-နောက် ကက်ရှ်ရှိ စာမျက်နှာအတွက် ပုံဖော်ခြင်းလုပ်ငန်းစဉ် ရပ်တန့်သွားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "ရှေ့-နောက် ကက်ရှ်ရှိ စာမျက်နှာအတွက် ပုံဖော်ခြင်းလုပ်ငန်းစဉ်ကို ပိတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "အသံဖမ်းယူမှု ခွင့်ပြုချက်များ တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "အာရုံခံကိရိယာခွင့်ပြုချက်များ တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "နောက်ခံစင့်ခ်လုပ်ခြင်း (သို့) fetch ခွင့်ပြုချက် တောင်းဆိုထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI ခွင့်ပြုချက်များ တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "အကြောင်းကြားချက် ခွင့်ပြုချက်များ တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "သိုလှောင်ခန်းသုံးခွင့် တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "ဗီဒီယိုဖမ်းယူမှု ခွင့်ပြုချက်များ တောင်းဆိုသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "URL စနစ်သည် HTTP / HTTPS ဖြစ်သော စာမျက်နှာများကိုသာ ကက်ရှ်သိမ်းနိုင်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "စာမျက်နှာသည် ရှေ့-နောက် ကက်ရှ်တွင်ရှိစဉ် ServiceWorker က တောင်းဆိုထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "ရှေ့-နောက် ကက်ရှ်တွင် ServiceWorker သည် စာမျက်နှာအား MessageEvent ပို့ရန် ကြိုးပမ်းထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "စာမျက်နှာသည် ရှေ့-နောက် ကက်ရှ်တွင် ရှိနေချိန်၌ ServiceWorker ကို စာရင်းသွင်းမထားပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "ServiceWorker စတင်မှုကြောင့် ဤစာမျက်နှာကို ရှေ့-နောက် ကက်ရှ်မှထုတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome ပြန်စသွားပြီး ရှေ့-နောက် ကက်ရှ်ဝင်ရောက်မှုများကို ရှင်းထုတ်လိုက်သည်။"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "စာမျက်နှာပေါ်ရှိ iframe သည် အပြီးမသတ်သော လမ်းညွှန်မှုစတင်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "ရင်းမြစ်ခွဲတွင် cache-control:no-cache ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "ရင်းမြစ်ခွဲတွင် cache-control:no-store ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "စာမျက်နှာ ရှေ့-နောက် ကက်ရှ်ရှိ အများဆုံးအချိန်ထက် ကျော်သွားပြီး သက်တမ်းကုန်သွားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "စာမျက်နှာသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်ရန် အချိန်ကုန်သွားသည် (အချိန်ကြာလုပ်ဆောင်နေသော စာမျက်နှာဖျောက်သည့် စီမံသူများကြောင့် ဖြစ်နိုင်သည်)။"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "စာမျက်နှာတွင် ပင်မဖရိမ်အတွင်း၌ ဖယ်ရှားမှုစီမံသူရှိသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "စာမျက်နှာတွင် ဖရိမ်ခွဲအတွင်း၌ ဖယ်ရှားမှုစီမံသူ ရှိသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ဘရောင်ဇာသည် အသုံးပြုသူ အေးဂျင့် အစားထိုးခေါင်းစီးကို ပြောင်းထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "ဗီဒီယို (သို့) အသံဖမ်းယူရန် ခွင့်ပြုထားသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC သုံးထားသောကြောင့် ရှေ့-နောက် ကက်ရှ် ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket သုံးထားသောကြောင့် ရှေ့-နောက် ကက်ရှ် ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport ပါသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်တွင် ဝင်၍မရပါ။"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport သုံးထားသောကြောင့် ရှေ့-နောက် ကက်ရှ် ပိတ်ထားသည်။"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR သုံးသော စာမျက်နှာများသည် ရှေ့-နောက် ကက်ရှ်အတွက် လောလောဆယ်အကျုံးမဝင်ပါ။"}}