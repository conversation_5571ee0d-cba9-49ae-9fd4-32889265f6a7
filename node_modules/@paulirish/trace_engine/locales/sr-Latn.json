{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Ovlašćenje neće biti pokriveno simbolom džokerskog znaka (*) u okviru CORS upravljanja atributom Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Atribut disableRemotePlayback treba da se koristi da bi se onemogućila podrazumevana integracija za prebacivanje umesto korišćenja birača -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Vrednost izgleda CSS-a slider-vertical nije standardizovana i biće uklonjena."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Zahtevi za resurse čiji URL-ovi su sadržali uklonjene znakove za razmak (\\(n|r|t)) i znakove „manje-od“ (<) su blokirani. Uklonite nove redove i kodirajte znakove „manje-od“ iz izvora kao što su vrednosti atributa elemenata da bi se učitali ti resursi."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Atribut chrome.loadTimes() je zastareo, pa koristite standardizovani API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Atribut chrome.loadTimes() je zastareo, pa koristite standardizovani API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Atribut chrome.loadTimes() je zastareo, pa koristite standardizovani API: nextHopProtocol za Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Kolačići koji sadrže \\(0|r|n) znak će biti odbijeni, a ne skraćeni."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Popuštanje smernica za isto poreklo podešavanjem atributa document.domain je zastarelo i biće podrazumevano onemogućeno. Ovo upozorenje o zastarevanju je za pristup različitog porekla koji je omogućen podešavanjem document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Pokretanje window.alert iz iframe-ova različitog porekla je zastarelo i ukloniće se u budućnosti."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Pokretanje window.confirm iz iframe-ova različitog porekla je zastarelo i ukloniće se u budućnosti."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Podrška za podatke: URL-ovi u SVGUseElement-u su zastareli i biće uklonjeni u budućnosti."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() iwatchPosition() više ne rade sa nebezbednim poreklom. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() iwatchPosition() su zastareli ili nebezbedni izvori. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() više ne radi sa nebezbednim poreklom. Da biste koristili ovu funkciju, razmislite o prebacivanju aplikacije na bezbedno poreklo kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Pronađena je oznaka <h1> u okviru člank<PERSON> <article>, <aside>, <nav> ili o<PERSON> <section> koji nema navedenu veličinu fonta. Veličina teksta ovog naslova će se uskoro menjati u ovom pregledaču. Više informacija potražite na https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "API RTCPeerConnectionIceErrorEvent.hostCandidate je zastareo. Umesto njega koristite RTCPeerConnectionIceErrorEvent.address ili RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ovaj format za navigator.credentials.get() zahtev za digitalne akreditive je zastareo. Ažurirajte poziv da biste koristili novi format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Poreklo prodavca i proizvoljni podaci iz događaja servisera canmakepayment su zastareli i biće uklonjeni: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Veb-sajt je zatražio podizvor sa mreže kojoj je mogao da pristupi samo zbog privilegovane mrežne pozicije svojih korisnika. Ovi zahtevi izlažu uređaje i servere koji nisu javni internetu, čime se povećava rizik od napada falsifikovanjem zahteva sa drugih sajtova (CSRF) i/ili curenja informacija. Da bi ublažio ove rizike, Chrome zastareva zahteve ka podizvorima koji nisu javni kada se pokrenu iz nebezbednog konteksta i počeće da ih blokira."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Polje dailyUpdateUrl iz strukture InterestGroups koje je preneto u strukturu joinAdInterestGroup() preimenovano je u updateUrl, što preciznije odražava njegovo ponašanje."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator je zastareli tip. Umesto njega koristite Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS ne može da se učita sa file: URL-ova ako se ne završavaju ekstenzijom fajla .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Korišćenje atributa SourceBuffer.abort() da bi se otkazalo uklanjanje asinhronog opsega za remove() je zastarelo zbog promene specifikacije. Podrška će se ukloniti u budućnosti. Umesto njega treba da slušate događaj updateend. abort() ima za cilj samo da otkaže dodavanje asinhronih medija ili da resetuje stanje raščlanjivača."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Podešavanje atributa MediaSource.duration ispod najviše vremenske oznake prezentacije bilo kojih baferovanih kodiranih okvira je zastarelo zbog promene specifikacije. Podrška za implicitno uklanjanje skraćenog baferovanog medijskog sadržaja će se ukloniti u budućnosti. Umesto toga treba da izvršite eksplicitni remove(newDuration, oldDuration) na sve sourceBuffers, gde je newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI će zatražiti dozvolu za korišćenje čak i ako SysEx nije naveden u atributu MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "API za obaveštenja više ne sme da se koristi iz nezbezbednog porekla. Razmislite o prebacivanju aplikacije na bezbedno poreklo, kao što je HTTPS. Pogledajte https://goo.gle/chrome-insecure-origins za više detalja."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Dozvola za API za obaveštenja više ne može da se traži od iframe-a različitog porekla. Razmislite o tome da zatražite dozvolu od okvira najvišeg nivoa ili da otvorite novi prozor."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opcija imageOrientation: 'none' u stavci createImageBitmap je zastarela. Koristite createImageBitmap sa opcijom {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Vaš partner <PERSON><PERSON><PERSON> (D)TLS verziju. Proverite sa partnerom da biste ispravili ovo."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "<PERSON>ko navedete overflow: visible na oznakama img, video i canvas, one mogu da dovedu do pravljenja vizuelnog sadržaja van granica elementa. Pogledajte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments je zastareli API. Bolje da koristite jednokratno instaliranje za obrađivače plaćanja."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest poziv je zaobišao direktivu connect-src za smernice za bezbednost sadržaja (CSP). Ovo zaobilaženje je zastarelo. Dodajte identifikator načina plaćanja iz API-ja PaymentRequest (u polju supportedMethods) u direktivu connect-src za CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "API StorageType.persistent je zastareo. Koristite standardizovani navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Atribut <source src> sa nadređenim elementom <picture> je ne<PERSON>, pa se ignoriše. Umesto njega koristite <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame zavisi od prodavca. Umesto njega koristite standardni cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame zavisi od prodavca. Umesto njega koristite standardni requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen je zastareo. Umesto toga koristite Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() je zastareo. Umesto toga koristite Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() je zastareo. Umesto toga koristite Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() je zastareo. Umesto toga koristite Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() je zastareo. Umesto toga koristite Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen je zastareo. Umesto toga koristite Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Obustavljamo API chrome.privacy.websites.privacySandboxEnabled, ali c<PERSON>e ostati aktivan za kompatibilnost unazad do izdanja M113. <PERSON>esto toga, koristite chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled i chrome.privacy.websites.adMeasurementEnabled. Pogledajte https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ograničenje DtlsSrtpKeyAgreement je uklonjeno. <PERSON><PERSON>i ste vrednost false za ovo ograni<PERSON>, što se tumači kao pokušaj korišćenja uklonjenog metoda SDES key negotiation. <PERSON><PERSON> funkcija je uklonjena, pa koristite uslugu koja podržava DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ograničenje DtlsSrtpKeyAgreement je uklonjeno. Naveli ste vrednost true za ovo ograničenje, što nije imalo efekta, ali možete da uklonite ovo ograničenje radi preglednosti."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() zasnovan na povratnom pozivu je zastareo i biće uklonjen. Umesto toga koristite getStats() koji je u skladu sa specifikacijama."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() je zastareo. Umesto njega koristite Select.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Zahtevi za podresurse čiji URL-ovi sadrže ugrađene akreditive (npr. **********************/) su blokirani."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opcija rtcpMuxPolicy je zastarela i biće uklonjena."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer će zahtevati izolaciju od pristupa iz drugih izvora. Pogledajte https://developer.chrome.com/blog/enabling-shared-array-buffer/ za više detalja."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Atribut speechSynthesis.speak() bez aktivacije korisnika je zastareo i ukloniće se."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Osluškivači događaja uklanjanja su zastareli i biće uklonjeni."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Dodaci treba da omoguće izolaciju od pristupa iz drugih izvora da biste i dalje koristili SharedArrayBuffer. Pogledajte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atribut GPUAdapter isFallbackAdapter je zastareo, pa koristite atribut GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON za odgovor ne podržava UTF-16 u atributu XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhroni XMLHttpRequest u glavnoj niti je zastareo zbog negativnog uticaja na doživljaj krajnjeg korisnika. Dodatnu pomoć potražite na https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacija"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Do promena rasporeda dolazi kada se elementi pomeraju bez ikakve interakcije korisnika. [Istražite uzroke promena rasporeda](https://web.dev/articles/optimize-cls), kao što su dodavanje i uklanjanje elemenata ili promena njihovih fontova dok se stranica učitava."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Zahtev za font"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Umet<PERSON><PERSON> iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Skup promena rasporeda: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "<PERSON><PERSON> otkriven nijedan uzrok promene rasporeda"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Nema promena rasporeda"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "<PERSON><PERSON><PERSON>ci promene rasporeda"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Najčešći uzroci promene rasporeda"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Element slike bez veličine"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON> skup"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Skup sa najlošijim promenama rasporeda"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Vreme preživljavanja keša"}, "models/trace/insights/Cache.ts | description": {"message": "Dugo trajanje keša može da ubrza ponovne posete stranici. [Saznajte više](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nema zahteva sa neefikasnim smernicama za keširanje"}, "models/trace/insights/Cache.ts | others": {"message": "jo<PERSON> {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Korist<PERSON> e<PERSON><PERSON> trajanja keš<PERSON>"}, "models/trace/insights/DOMSize.ts | description": {"message": "Veliki DOM može da poveća trajanje izračunavanja stilova i preoblikovanja izgleda, što utiče na prilagodljivost stranice. Veliki DOM će takođe povećati korišćenje memorije. [Saznajte kako da izbegnete prekomernu veličinu DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Najviše podređenih elemenata"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM dubina"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizujte veličinu DOM-a"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON><PERSON> broj elemena<PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "Vrednost"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Prvi zahtev za mrežu je najvažniji.  Smanjite kašnjenje izbegavanjem preusmeravanja, obezbeđivanjem brzog odgovora servera i omogućavanjem kompresije teksta."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Bilo je preusmeravanja ({PH1} preusmeravanja, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Server je odgovorio sporo (uočeno: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nije primenjeno komprimovanje"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Izbegava preusmeravanja"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Server odgo<PERSON>a br<PERSON> (uočeno {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Primenjuje se kompresija teksta"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Preusmeravan<PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Vreme odziva servera"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Kašnjenje zahteva za dokument"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Nekomprimovano <PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Uklonite velike, duplirane JavaScript module iz paketa da biste smanjili nepotrebnu potrošnju podataka tokom mrežnih aktivnosti."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplikat JavaScript-a"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Razmislite o tome da podesite [font-display](https://developer.chrome.com/blog/font-display) na swap ili optional kako biste se uverili da je tekst uvek vidljiv. swap može da se dodatno optimizuje kako bi se ublažile promene izgleda pomoću [zamena pokazatelja fonta](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Font"}, "models/trace/insights/FontDisplay.ts | title": {"message": "P<PERSON>z fonta"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Protraćeno vreme"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonimno)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Mnogi API-ji, koji obično čitaju geometriju rasporeda, primoravaju mehanizam za renderovanje da pauzira izvršavanje skripte da bi izračunali stil i raspored. Saznajte više o [prinudnom preoblikovanju](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) i njegovom ublažavanju."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON> s<PERSON>"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Prinudno preoblikovanje"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Najčešći poziv funkcije"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Ukupno vreme preoblikovanja"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[nep<PERSON><PERSON>o]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Smanjivanje vremena preuzimanja slika može da poboljša vidljivu brzinu učitavanja stranice i LCP. [Saznajte više o optimizovanju veličine slike](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (proc. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nema slika koje mogu da se optimizuju"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimizujte veličinu fajla"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "jo<PERSON> {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Poboljšajte isporuku slika"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Povećanje faktora kompresije slike može da poboljša veličinu ove preuzete slike."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Korišćenje modernog formata slike (WebP, AVIF) ili povećanje kompresije slike može da poboljša veličinu ove preuzete slike."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON><PERSON><PERSON> fajl slike je veći nego što treba da bude ({PH1}) za prikazane dimenzije ({PH2}). Koristite prilagodljive slike da biste smanjili veličinu slike za preuzimanje."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "<PERSON><PERSON> k<PERSON> video formate umesto GIF-ova, možete da poboljšate veličinu preuzimanja animiranog sadržaja."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Počnite da istražujete sa najdužom fazom. [Kašnjenja mogu da se umanje](https://web.dev/articles/optimize-inp#optimize_interactions). Da biste smanjili trajanje obrade, [optimizujte troškove glavne niti](https://web.dev/articles/optimize-long-tasks), često JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Kašnjenje unosa"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nije otkrivena nijedna interakcija"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Faza"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Kašnjenje pre<PERSON>cije"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> obra<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP po fazi"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizujte LCP tako što ćete [omogućiti otkrivanje](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) LCP slike iz HTML-a odmah i [izbegavati spora učitavanja](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Primenjeno je fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Treba da se primeni fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "sporo učitavanje nije primenjeno"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Slika LCP-a je učitana {PH1} posle najranije početne tačke."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nije otkriven LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP resurs nije otkriven jer LCP nije slika"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Zahtev je vidljiv u početnom dokumentu"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Otkrivanje LCP zahteva"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Svaka [faza ima specifične strategije poboljšanja](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idealno bi bilo da se većina vremena LCP-a potroši na učitavanje resursa, a ne na kašnjenja."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Kašnjenje prikazivanja elementa"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Polje p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nije otkriven LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Faza"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Kašnjenje učitavanja resursa"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Trajanje učitavanja resursa"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP prema fazi"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Skripta"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Neiskorišc<PERSON><PERSON> b<PERSON>i"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polifili i transformacije omogućavaju da starije verzije pregledača koriste nove funkcije JavaScript-a. <PERSON>, mnogi nisu neophodni za moderne pregledače. Razmislite o tome da modifikujete proces pravljenja JavaScript-a tako da ne transpajlira [referentni](https://web.dev/articles/baseline-and-polyfills) skup funkcija, osim ako znate da mora da podržava starije pregledače. [Saznajte zašto većina sajtova može da koristi ES6+ kôd bez transpajliranja](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Stara verzija JavaScript-a"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 i HTTP/3 imaju brojne prednosti u odnosu na HTTP/1.1, kao što je multipleksovanje. [Saznajte više o korišćenju modernog HTTP-a](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "<PERSON>ema z<PERSON>va koji koriste HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderan HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Vreme"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Procenjena ušteda LCP-a"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Nekorišćeno povezivanje unapred. Proverite da li je atribut crossorigin pravilno upotrebljen."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Izbegavajte pravljenje lanaca kritičnih zahteva](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) tako što ćete smanjiti dužinu lanaca, smanjiti veličinu preuzimanja za resurse ili odložiti preuzimanje resursa koji nisu neophodni radi bržeg učitavanja stranice."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Dodajte savete za [povezivanje unapred](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) sa najvažnijim izvorima, ali probajte da koristite najviše 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidati za povezivanje unapred"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Maksimalno kašnjenje kritične putanje:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Zavisnosti mreže nisu uticale na zadatke renderovanja"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nijedan dodatni izvor nije dobar kandidat za povezivanje unapred"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "Nijedan izvor nije unapred povezan"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "<PERSON><PERSON> za [povezivanje unapred](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pomažu pregledaču da uspostavi vezu ranije tokom učitavanja stranice, što štedi vreme kada se uputi prvi zahtev za taj izvor. U nastavku su navedeni izvori sa kojima je stranica povezana unapred."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON> povezani izvori"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Pronađeno je više od 4 povezivanja preconnect. Ona treba da se koriste retko i samo do najvažnijih izvora."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Nekorišćeno povezivanje unapred. Koristite preconnect samo za izvore koje će stranica verovatno zahtevati."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Izbegavajte pravljenje lanaca kritičnih zahteva tako što ćete smanjiti dužinu lanaca, smanjiti veličinu preuzimanja za resurse ili odložiti preuzimanje resursa koji nisu neophodni radi bržeg učitavanja stranice."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Zahtevi blokiraju prvi prikaz stra<PERSON>, što može da odloži LCP. [Odlaganje ili ugrađivanje](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) može da premesti ove mrežne zahteve sa kritične putanje."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nema zahteva za blokiranje renderovanja za ovu navigaciju"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Zahtevi za b<PERSON>ki<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ako troškovi ponovnog izračunavanja stila ostanu visoki, optimizacija birača može da ih smanji. [Optimizujte birače](https://developer.chrome.com/docs/devtools/performance/selector-stats) sa velikim proteklim vremenom i velikim % spore putanje. Jednostavni birači, manje birača, manji, kao i manje dubok DOM sadržaj; sve to će smanjiti troškove podudaranja."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Proteklo vreme"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nije pronađen nijedan podatak CSS birača. Statistika CSS birača mora da bude omogućena u podešavanjima okna za učinak."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Pokušaji podudaranja"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Troškovi CSS birača"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Najvažniji birači"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Ukupno"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Vreme glavne niti"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Treća strana"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Velič<PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Kôd nezavisnog dobavljača može značajno da utiče na učinak učitavanja. [Smanjite i odložite učitavanje koda nezavisnog dobavljača](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) da biste utvrdili prioritete sadržaja stranice."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON>je pronađena nijedna treća strana"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Trec<PERSON>e strane"}, "models/trace/insights/Viewport.ts | description": {"message": "Interakcije dodirom mogu da budu [odložene za najviše 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ako oblast prikaza nije optimizovana za mobilne uređaje."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Kašnjenje pri dodiru na mobilnom uređaju"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimizujte oblast prikaza za mobilne uređaje"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Samo stranice učitane preko GET zahteva ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Samo stranice sa kodom statusa 2XX mogu da se keširaju."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome je otkrio pokušaj izvršavanja JavaScript-a dok je bio u kešu."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Stranice koje su zahtevale AppBanner trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Oznake su onemogućile keširanje cele stranice. Posetite chrome://flags/#back-forward-cache da biste ga omogućili lokalno na uređaju."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Komandna linija je onemogućila keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Keširanje cele stranice je onemogućeno zbog nedovoljno memorije."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Delegat ne podržava keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Funkcija za prikazivanje unapred je onemogućila keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Ova stranica ne može da se kešira jer ima instancu BroadcastChannel sa registrovanim slušaocima."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Stranice sa zaglavljem cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON> je <PERSON><PERSON> obrisan."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Stranica je uklonjena iz keša da bi se dozvolilo keširanje druge stranice."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Stranice koje imaju dodatne komponente trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON>ini<PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Stranice koje koriste FileChooser API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Stranice koje koriste API za pristup sistemu fajlova trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Stranice koje koriste dispečer za medijski uređaj ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Reprodukcija iz medija plejera je bila u toku pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Stranice koje koriste MediaSession API i podešavaju status reprodukcije ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Stranice koje koriste MediaSession API i podešavaju obrađivače radnji ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Čitač ekrana je onemogućio keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Stranice koje koriste SecurityHandler trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Stranice koje koriste Serial API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Stranice koje koriste WebAuthetication API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Stranice koje koriste WebBluetooth API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Stranice koje koriste WebUSB API trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Keširanje cele stranice je onemogućeno zato što su kolačići onemogućeni na stranici koja koristi Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Stranice koje koriste predviđeni obrađivač ili radni zadatak trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokument nije dovršio učitavanje pre napuštanja dokumenta."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "App Banner je bio aktivan pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome menadžer lozinki je bio aktivan pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM destilacija je bila u toku pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer je bio aktivan pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Keširanje cele stranice je onemogućeno jer su dodaci koristili API za razmenu poruka."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Dodaci sa trajnom vezom treba da zatvore vezu pre keširanja cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Dodaci sa trajnom vezom su pokušali da šalju poruke okvirima u keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Keširanje cele stranice je onemogućeno zbog dodataka."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Modalni dijalog kao što je ponovno slanje obrasca ili dijalog za HTTP lozinku je prikazan pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Oflajn stranica je prikazana pri napuštanju."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Traka za intervenciju u vezi sa nedostatkom memorije je bila prisutna pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Došlo je do slanja zahteva za dozvole pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Otkriven je blokator iskačućih prozora pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Prikazani su detalji o Bezbednom pregledanju pri napuštanju stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezbedno pregledanje je oktrilo da ova stranica sadrži zloupotrebu i blokiralo je iskačući prozor."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Serviser je aktiviran dok je stranica bila u procesu keširanja cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Keširanje cele stranice je onemogućeno zbog greške u dokumentu"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Stranice koje koriste FencedFrames ne mogu da se skladište u kešu cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Stranica je uklonjena iz keša da bi se dozvolilo keširanje druge stranice."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Stranice koje su odobrile pristup za strimovanje medija trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Stranice koje imaju određene vrste ugrađenog sad<PERSON> (npr. PDF-ovi) trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Stranice koje koriste IdleManager trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Stranice koje imaju otvorenu IndexedDB vezu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Keširanje cele stranice je onemogućeno zbog IndexedDB događaja."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "<PERSON><PERSON><PERSON> se <PERSON>-ji koji ne ispunja<PERSON>ju uslove."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Stranice na kojima se JavaScript umeće pomoću dodataka trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Stranice na kojima se StyleSheet umeće pomoću dodataka trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Interna greška."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Keširanje cele stranice je onemogućeno jer je neki zahtev za JavaScript mrežu primio resurs sa zaglavljem Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Keširanje cele stranice je onemogućeno zbog zahteva za održavanje linka."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Stranice koje koriste zaključavanje tastature trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Stranica nije dovršila učitavanje pre napuštanja stranice."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> č<PERSON> glavni resurs ima cache-control:no-cache ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> č<PERSON> glavni resurs ima cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Kretanje je otkazano pre nego što je stranica mogla da bude vraćena iz keša cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Stranica je uklonjena iz keša jer je aktivna mrežna veza primila previše podataka. Chrome ograničava količinu podataka koju stranica može da primi dok je keširana."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stranice koje imaju preuzimanje() ili XHR u toku ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Stranica je uklonjena iz keširanja cele stranice jer je aktivan mrežni zahtev obuhvatao preusmeravanje."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Stranica je uklonjena iz keša jer je mrežna veza bila predugo otvorena. Chrome ograničava vreme koje stranica ima za primanje podataka dok je keširana."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Stranice koje nemaju ispravno zaglavlje odgovora ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Kretanje se desilo u okviru koji nije glavni okvir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Stranice sa aktivnim indeksiranim DB transakcijama trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Stranice sa aktivnim zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Stranice sa aktivnim zahtevom za preuzimanje mreže trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Stranice sa aktivnim zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Stranice sa aktivnim XHR zahtevom za mrežu trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Stranice koje koriste PaymentManager trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Stranice koje koriste funkciju Slika u slici trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Stranice koje prikazuju korisnički interfejs za štampanje trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Stranica je otvorena pomoću metoda window.open(), a druga kartica sadrži referencu na nju ili je stranica otvorila prozor."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Otkazao je proces renderovanja za stranicu u kešu cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Proces renderovanja za stranicu u kešu cele stranice je prekinut."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Stranice koje zahtevaju dozvole za snimanje audio sadržaja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Stranice koje zahtevaju dozvole za senzore trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Stranice koje zahtevaju sinhronizaciju u pozadini ili dozvole za preuzimanje trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Stranice koje zahtevaju dozvole za MIDI trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Stranice koje zahtevaju dozvole za obaveštenja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Stranice koje zahtevaju pristup memorijskom prostoru trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Stranice koje zahtevaju dozvole za snimanje videa trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Samo stranice čija šemu URL-a je HTTP ili HTTPS mogu da se keširaju."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON> je preuzeo serviser dok je keširanje cele stranice u toku."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Serviser je pokušao da pošalje stranicu koja je u procesu keširanja cele stranice atributu MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Registracija za ServiceWorker je opozvana dok je bilo u toku keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Stranica je uklonjena iz keširanja cele stranice zbog aktivacije servisera."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome je restartovao i obrisao unose keširanja cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Stranice koje koriste SharedWorker trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Stranice koje koriste SpeechRecognizer trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Stranice koje koriste SpeechSynthesis trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe na stranice je započeo kretanje koje se nije završilo."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-cache ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-store ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Stranica je premašila maksimalno vreme za keširanje cele stranice i istekla je."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Isteklo je vreme da stranica pristupi keširanju cele stranice (verovatno zbog obrađivača sakrivanja stranice koji su dugo bili pokrenuti)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Stranica ima unload obrađivač u glavnom okviru."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Stranica ima unload obrađivač u podokviru."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Pregledač je promenio zaglavlje zamene korisničkog agenta."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Stranice koje su odobrile pristup za snimanje video ili audio sadržaja trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Stranice koje koriste WebDatabase trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Stranice koje koriste WebHID trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Stranice koje koriste WebLocks trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Stranice koje koriste WebNfc trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Stranice koje koriste WebOTPService trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Stranice sa WebRTC-om ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Keširanje cele stranice je onemogućeno zato što je korišćen WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Stranice koje koriste WebShare trenutno ne ispunjavaju uslove za keširanje cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Keširanje celih stranica sa WebSocket-om nije moguće."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Keširanje cele stranice je onemogućeno jer je korišćen WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Stranice sa WebTransport-om ne mogu da pristupe keširanju cele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Keširanje cele stranice je onemogućeno jer je korišćen WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Stranice koje koriste WebXR trenutno ne ispunjavaju uslove za keširanje cele stranice."}}