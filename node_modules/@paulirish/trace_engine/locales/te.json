{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers హ్యాండ్లింగ్‌లో ప్రామాణీకరణ వైల్డ్ కార్డ్ చిహ్నం (*) ద్వారా కవర్ కాదు."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "కాస్ట్ ఇంటిగ్రేషన్ ఆటోమేటిక్ సెట్టింగ్‌ను డిజేబుల్ చేయడానికి -internal-media-controls-overlay-cast-button ఎంపిక సాధనానికి బదులుగా disableRemotePlayback ఫీచర్‌ను ఉపయోగించండి."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS డిస్‌ప్లే విలువ slider-vertical ప్రామాణీకరించబడలేదు, ఇంకా అది తీసివేయబడుతుంది."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "రిసోర్స్ రిక్వెస్ట్‌లు వేటి URLలు అయితే, తీసివేయబడిన వైట్‌స్పేస్ \\(n|r|t) అక్షరాలను, (<) అక్షరాలు కంటే తక్కువ ఉన్న వాటి రెండింటి కలయికను కలిగి ఉన్నాయో, అవి బ్లాక్ చేయబడ్డాయి. ఈ రిసోర్స్‌లను లోడ్ చేయడానికి, దయచేసి ఎలిమెంట్ లక్షణం విలువలు వంటి స్థలాల నుండి కొత్తలైన్‌లను తీసివేయండి, అంత కంటే తక్కువ అక్షరాలను ఎన్‌కోడ్ చేయండి."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() విస్మరించబడింది, బదులుగా ప్రామాణిక APIని ఉపయోగించండి: నావిగేషన్ సమయం 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() విస్మరించబడింది, బదులుగా ప్రామాణిక APIని ఉపయోగించండి: పెయింట్ సమయం."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() విస్మరించబడింది, బదులుగా ప్రామాణిక APIని ఉపయోగించండి: నావిగేషన్ సమయం 2లో nextHopProtocol."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) అక్షరాన్ని కలిగిన కుక్కీలు కుదించబడటానికి బదులుగా తిరస్కరించబడతాయి."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain‌ను సెట్ చేసి, ఒకే ఆరిజిన్ పాలసీని సడలించడం విస్మరించబడింది, అలాగే ఆటోమేటిక్‌గా డిజేబుల్ చేయబడుతుంది. document.domain‌ను సెట్ చేసి ఎనేబుల్ చేసిన క్రాస్-ఆరిజిన్ యాక్సెస్ కోసం ఈ విస్మరణ హెచ్చరిక."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "క్రాస్ ఆరిజిన్ iframeల నుండి విండో అలర్ట్‌ను ట్రిగ్గర్ చేయడం విస్మరించబడింది, భవిష్యత్తులో ఈ ఆప్షన్ తీసివేయబడుతుంది."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "క్రాస్ ఆరిజిన్ iframeల నుండి window.confirmను ట్రిగ్గర్ చేయడం విస్మరించబడింది, భవిష్యత్తులో ఈ ఆప్షన్ తీసివేయబడుతుంది."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "డేటాకు సపోర్ట్: SVGUseElementలోని URLలు విస్మరించబడ్డాయి, భవిష్యత్తులో అవి తీసివేయబడతాయి."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition(), watchPosition() ఇకపై సురక్షితం కాని ఆరిజిన్‌లలో పని చేయవు. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు స్విచ్ చేయడాన్ని పరిగణించండి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌ను చూడండి."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "సురక్షితం కాని ఆరిజిన్‌లలో getCurrentPosition(), watchPosition() విస్మరించబడ్డాయి. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు స్విచ్ చేయడాన్ని పరిగణించండి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌ను చూడండి."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ఇకపై సురక్షితం కాని ఆరిజిన్‌లలో పని చేయదు. ఈ ఫీచర్‌ను ఉపయోగించడానికి, మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు స్విచ్ చేయడాన్ని పరిగణించండి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌ను చూడండి."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav>, లేదా <section> లోపల ఒక <h1> ట్యాగ్‌ను కనుగొన్నారు, దీనికి నిర్దిష్ట ఫాంట్-సైజ్ పేర్కొనబడలేదు. ఈ బ్రౌజర్‌లో ఈ హెడ్డింగ్ టెక్స్ట్ సైజ్ త్వరలో మారనుంది. మరింత సమాచారం కోసం https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 ను చూడండి."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate విస్మరించబడింది. బదులుగా దయచేసి RTCPeerConnectionIceErrorEvent.address లేదా RTCPeerConnectionIceErrorEvent.port ఉపయోగించండి."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "డిజిటల్ ఆధారాల navigator.credentials.get() రిక్వెస్ట్ కోసం ఈ ఫార్మాట్ విస్మరించబడింది, దయచేసి కొత్త ఫార్మాట్‌ను ఉపయోగించడానికి మీ కాల్‌ను అప్‌డేట్ చేయండి."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment సర్వీస్ వర్కర్ ఈవెంట్‌లోని వ్యాపారి ఆరిజిన్, ఏకపక్ష డేటా విస్మరించబడ్డాయి, తీసివేయబడతాయి: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "వెబ్‌సైట్ దాని యూజర్ యోక్క ప్రత్యేక నెట్‌వర్క్ స్థానం కారణంగా మాత్రమే యాక్సెస్ చేయగల నెట్‌వర్క్ నుండి సబ్‌రిసోర్స్‌లను రిక్వెస్ట్ చేసింది. ఈ రిక్వెస్ట్‌లు ప్రైవేట్ పరికరాలను, సర్వర్‌లను ఇంటర్నెట్‌కు బహిర్గతం చేస్తాయి, దీని వలన క్రాస్-సైట్ రిక్వెస్ట్ ఫోర్జరీ (CSRF) దాడి, మరియు/లేదా సమాచారాన్ని బహిర్గతం చేసే ప్రమాదం పెరుగుతుంది. ఈ ప్రమాదాలను తగ్గించడానికి, సురక్షితం కాని ఆరిజిన్‌ల నుండి ప్రారంభించి, ప్రైవేట్ సబ్‌రిసోర్స్‌ల కోసం చేసిన రిక్వెస్ట్‌లను Chrome విస్మరిస్తుంది, వాటిని బ్లాక్ చేయడం ప్రారంభిస్తుంది."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups‌కు సంబంధించిన dailyUpdateUrl ఫీల్డ్, joinAdInterestGroup()‌కు పాస్ చేయబడింది, దాని పని తీరును మరింత ఖచ్చితంగా సూచించడానికి దాని పేరు updateUrl‌కు మార్చబడింది."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator విస్మరించబడింది. అందుకు బదులుగా దయచేసి Intl.Segmenter‌ను ఉపయోగించండి."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "file: URLల చివర .css ఫైల్ ఎక్స్‌టెన్షన్ ఉంటే మినహా, వాటి నుండి CSSను లోడ్ చేయలేము."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove() యొక్క సింక్రనస్ కాని పరిధి తీసివేతను SourceBuffer.abort() ఉపయోగించి రద్దు చేయడం, నిర్దేశించిన మార్పు కారణంగా విస్మరించబడింది. భవిష్యత్తులో సపోర్ట్ తీసివేయబడుతుంది. బదులుగా మీరు updateend ఈవెంట్‌ను వినాలి. జతపరిచిన సింక్రనస్ కాని మీడియాను రద్దు చేయడానికి లేదా పార్సర్ స్టేట్‌ను రీసెట్ చేయడానికి మాత్రమే abort() ఉద్దేశించి ఉంది."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "బఫర్ అయిన ఏ కోడెడ్ ఫ్రేమ్‌ల యొక్క ప్రదర్శించిన అత్యధిక టైమ్ స్టాంప్ కంటే MediaSource.duration‌ను తక్కువగా సెట్ చేయడం, నిర్దేశించిన మార్పు కారణంగా విస్మరించబడింది. బఫర్ అయిన మీడియా కుదింపును ఏమీ పేర్కొనకుండా తీసివేయడానికి మద్దతు, భవిష్యత్తులో తీసివేయబడుతుంది. బదులుగా మీరు newDuration < oldDuration దగ్గర అన్ని sourceBuffers‌లలో స్పష్టంగా remove(newDuration, oldDuration)‌ను అమలు చేయాలి."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions‌లో sysex పేర్కొనబడనప్పటికీ, ఉపయోగించడానికి వెబ్ MIDI అనుమతిని అడుగుతుంది."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "నోటిఫికేషన్ API ఇకపై సురక్షితం కాని ఆరిజిన్‌ల నుండి ఉపయోగించబడకపోవచ్చు. మీ అప్లికేషన్‌ను HTTPS వంటి సురక్షితమైన ఆరిజిన్‌కు స్విచ్ చేయడాన్ని పరిగణించండి. మరిన్ని వివరాల కోసం https://goo.gle/chrome-insecure-origins లింక్‌ను చూడండి."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "నోటిఫికేషన్ APIకి అనుమతిని ఇకపై క్రాస్-ఆరిజిన్ iframe నుండి రిక్వెస్ట్ చేయలేకపోవచ్చు. టాప్ లెవెల్ ఫ్రేమ్ నుండి అనుమతిని రిక్వెస్ట్ చేయడాన్ని, లేదా బదులుగా కొత్త విండోను తెరవడాన్ని పరిగణించండి."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmapలో ఆప్షన్ imageOrientation: 'none' విస్మరించబడింది. దయచేసి బదులుగా '{imageOrientation: 'from-image'}' ఆప్షన్‌తో createImageBitmap‌ను ఉపయోగించండి."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "మీ పార్ట్‌నర్ వాడుకలో లేని (D)TLS వెర్షన్‌ను ఉపయోగిస్తున్నారు. దీనిని పరిష్కరించడం గురించి దయచేసి మీ పార్ట్‌నర్‌తో సంప్రదించండి."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "overflow: visibleను img, వీడియో, కాన్వాస్ ట్యాగ్‌లపై పేర్కొనడం వలన అవి ఎలిమెంట్ పరిధికి వెలుపల విజువల్ కంటెంట్‌ను ఉత్పత్తి చేయగలవు. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md. ని చూడండి."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments విస్మరించబడింది. బదులుగా పేమెంట్ హ్యాండ్లర్ల కోసం దయచేసి జస్ట్ ఇన్-టైమ్ ఇన్‌స్టాల్‌ని ఉపయోగించండి."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "మీ PaymentRequest కాల్, కంటెంట్-సెక్యూరిటీ-పాలసీ (CSP) connect-src డైరెక్టివ్‌ని బైపాస్ చేసింది. ఈ బైపాస్ విస్మరించబడింది. దయచేసి API PaymentRequest (supportedMethods ఫీల్డ్‌లో) నుండి CSP connect-src డైరెక్టివ్‌కి పేమెంట్ ఆప్షన్ ఐడెంటిఫైయర్‌ని జోడించండి."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent విస్మరించబడింది. బదులుగా దయచేసి ప్రామాణిక navigator.storage ఉపయోగించండి."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> పేరెంట్ కలిగిన <source src> చెల్లదు, కాబట్టి విస్మరించబడింది. బదులుగా దయచేసి <source srcset> ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame అనేది వెండార్ ప్రత్యేకమైనది. దయచేసి బదులుగా ప్రామాణిక cancelAnimationFrameని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame అనేది వెండార్ ప్రత్యేకమైనది. దయచేసి బదులుగా ప్రామాణిక requestAnimationFrameని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen అనేది నిలిపివేయబడింది. దానికి బదులుగా దయచేసి Document.fullscreenElementని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() విస్మరించబడింది. దయచేసి బదులుగా Element.requestFullscreen()ని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() విస్మరించబడింది. దయచేసి బదులుగా Element.requestFullscreen()ని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() విస్మరించబడింది. దయచేసి బదులుగా Document.exitFullscreen()ని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() విస్మరించబడింది. దయచేసి బదులుగా Document.exitFullscreen()ని ఉపయోగించండి."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen నిలిపివేయబడింది. బదులుగా, దయచేసి Document.fullscreenEnabledని ఉపయోగించండి."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "మేము API chrome.privacy.websites.privacySandboxEnabled‌ను విస్మరిస్తున్నాము, అయినప్పటికీ M113 రిలీజ్ అయ్యే వరకు పాత బ్రౌజర్‌లతో అనుకూలంగా ఉండటం కోసం ఇది యాక్టివ్‌గానే ఉంటుంది. బదులుగా, దయచేసి chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, chrome.privacy.websites.adMeasurementEnabled‌ను ఉపయోగించండి. ఈ లింక్‌ను చూడండి https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "పరిమితి DtlsSrtpKeyAgreement తీసివేయబడింది. మీరు ఈ పరిమితికి false విలువను పేర్కొన్నారు, అనగా తీసివేసిన SDES key negotiation విధానాన్ని మీరు ఉపయోగించడానికి ప్రయత్నించారని అర్ఠం. ఈ ఫంక్షనాలిటీ తీసివేయబడింది; బదులుగా DTLS key negotiation‌ను సపోర్ట్ చేసే సర్వీస్‌ను ఉపయోగించండి."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "పరిమితి DtlsSrtpKeyAgreement తీసివేయబడింది. మీరు ఈ పరిమితికి true విలువను పేర్కొన్నారు, దీని వలన ఎటువంటి ప్రభావం ఉండదు, కానీ స్పష్టత కోసం మీరు దీనిని తీసివేయవచ్చు."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "కాల్‌బ్యాక్ ఆధారిత getStats() విస్మరించబడింది, భవిష్యత్తులో ఈ ఆప్షన్ తీసివేయబడుతుంది. బదులుగా నిర్దేశ అనుకూల getStats()ని ఉపయోగించండి."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() విస్మరించబడింది. దయచేసి బదులుగా Selection.modify()ని ఉపయోగించండి."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "సబ్‌రిసోర్స్ రిక్వెస్ట్‌లు వేటి URLలు అయితే పొందుపరిచిన ఆధారాలు (ఉదా. **********************/) కలిగి ఉన్నాయో, అవి బ్లాక్ చేయబడ్డాయి."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy ఆప్షన్ విస్మరించబడింది, అలాగే తీసివేయబడుతుంది."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer‌కు క్రాస్-ఆరిజిన్ ఐసోలేషన్ అవసరం. మరిన్ని వివరాల కోసం https://developer.chrome.com/blog/enabling-shared-array-buffer/ లింక్‌ను చూడండి."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "యూజర్ యాక్టివేషన్ లేని speechSynthesis.speak() విస్మరించబడింది, అలాగే తీసివేయబడుతుంది."}, "generated/Deprecation.ts | UnloadHandler": {"message": "అన్‌లోడ్ ఈవెంట్ లిజనర్లు విస్మరించబడ్డాయి, తీసివేయబడతాయి."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer‌ను ఉపయోగించడం కొనసాగించడానికి, ఎక్స్‌టెన్షన్‌లు క్రాస్-ఆరిజిన్ ఐసోలేషన్‌ను ఎంపిక చేసుకోవాలి. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ లింక్‌ను చూడండి."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter అట్రిబ్యూట్ వినియోగాన్ని ఆపివేశారు, బదులుగా GPUAdapterInfo isFallbackAdapter అట్రిబ్యూట్‌ను ఉపయోగించండి."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest‌లోని JSON ప్రతిస్పందన ద్వారా UTF-16 సపోర్ట్ చేయబడదు"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "ప్రధాన థ్రెడ్‌లో సింక్రనస్ XMLHttpRequest విస్మరించబడింది, ఎందుకంటే ఇది ఎండ్ యూజర్ అనుభవంపై ప్రతికూల ప్రభావం చూపుతుంది. మరింత సహాయం కోసం, https://xhr.spec.whatwg.org/ లింక్‌లోని కంటెంట్‌ను రెఫర్ చేయండి."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "యానిమేషన్"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "ఎలిమెంట్‌లు ఏవైనా యూజర్ ఇంటరాక్షన్‌కు దూరంగా ఉన్నప్పుడు లేఅవుట్ షిఫ్ట్‌లు సంభవిస్తాయి. [లేఅవుట్ షిఫ్ట్‌ల కారణాలను పరిశోధించండి](https://web.dev/articles/optimize-cls), ఉదాహరణకు ఎలిమెంట్స్ జోడించబడటం, తీసివేయబడటం లేదా పేజీ లోడ్ అవుతున్న కొద్దీ వాటి ఫాంట్‌లు మారడం వంటివి."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ఫాంట్ రిక్వెస్ట్"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "iframe ఇంజెక్ట్ చేయబడింది"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "లేఅవుట్ షిఫ్ట్ క్లస్టర్ @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "లేఅవుట్ షిఫ్ట్ కారణాలు ఏవీ కనుగొనబడలేదు"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "లేఅవుట్ షిఫ్ట్‌లు ఏవీ లేవు"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "లేఅవుట్ షిఫ్ట్‌కు కారణాలు"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "టాప్ లేఅవుట్ షిఫ్ట్ కారణాలను చూపండి"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "కొలతలు లేని ఇమేజ్ ఎలిమెంట్"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "పనికి రాని క్లస్టర్"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "పనికి రాని లేఅవుట్ షిఫ్ట్ క్లస్టర్"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "కాష్ TTL"}, "models/trace/insights/Cache.ts | description": {"message": "కాష్ జీవిత కాలం ఎక్కువ ఉంటే, మీ పేజీకి రిపీట్‌గా వచ్చే సందర్శనల సంఖ్యలో వేగం పుంజుకుంటుంది. [మరింత తెలుసుకోండి](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "అసమర్థమైన కాష్ పాలసీలతో రిక్వెస్ట్‌లు ఏవీ లేవు"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} ఇతరులు"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "రిక్వెస్ట్"}, "models/trace/insights/Cache.ts | title": {"message": "సమర్థవంతమైన కాష్ జీవితకాలాలను ఉపయోగించండి"}, "models/trace/insights/DOMSize.ts | description": {"message": "పెద్ద DOM వలన, స్టయిల్ కాలిక్యులేషన్‌ల వ్యవధి, లేఅవుట్ రీఫ్లోలను పెంచుతుంది, ఇది పేజీ ప్రతిస్పందనను ప్రభావితం చేస్తుంది. పెద్ద DOM మెమరీ వినియోగాన్ని కూడా పెంచుతుంది. [అధిక DOM సైజ్‌ను ఎలా నివారించాలో తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "ఎలిమెంట్"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "చాలా చైల్డ్ డైరెక్టరీలు"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM డెప్త్"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "గణాంకాలు"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM సైజ్‌ను ఆప్టిమైజ్ చేయండి"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "మొత్తం ఎలిమెంట్‌లు"}, "models/trace/insights/DOMSize.ts | value": {"message": "వాల్యూ"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "మీ మొదటి నెట్‌వర్క్ రిక్వెస్ట్ చాలా ముఖ్యమైనది.  మళ్లింపు‌లను నివారించడం, సర్వర్ ప్రతిస్పందన వేగంగా ఉండేలా చూసుకోవడం, అలాగే టెక్స్ట్ కంప్రెషన్‌ను ఎనేబుల్ చేయడం ద్వారా దాని ల్యాగ్‌ను తగ్గించండి."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "మళ్లింపులు ఉన్నాయి ({PH1} మళ్లింపులు, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "సర్వర్ నెమ్మదిగా సమాధానం ఇచ్చింది ({PH1} సమయంలో)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "కంప్రెషన్ ఏదీ వర్తించబడలేదు"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "మళ్లింపులను నివారిస్తుంది"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "సర్వర్ వేగంగా సమాధానం ఇస్తోంది ({PH1} సమయంలో)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "టెక్స్ట్ కంప్రెషన్‌ను వర్తింపజేస్తుంది"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "మళ్లింపులు"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "సర్వర్ ప్రతిస్పందన సమయం"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "డాక్యుమెంట్ రిక్వెస్ట్ ల్యాగ్"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "అన్‌కంప్రెస్డ్ డౌన్‌లోడ్"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "డూప్లికేట్ అవ్వని బైట్‌లు"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "సోర్స్"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "నెట్‌వర్క్ యాక్టివిటీలో అనవసరమైన బైట్‌ల వినియోగం తగ్గించడానికి బండిల్స్ నుండి పెద్దగా ఉండే, డూప్లికేట్ JavaScript మాడ్యూల్స్‌ను తీసివేయండి."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "డూప్లికేట్ చేయబడిన JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "టెక్స్ట్ స్థిరంగా కనిపించేలా చూడడానికి [font-display](https://developer.chrome.com/blog/font-display)ని swap లేదా optional సెట్ చేసి చూడండి. [ఫాంట్ మెట్రిక్ ఓవర్‌రైడ్‌ల](https://developer.chrome.com/blog/font-fallbacks)తో లేఅవుట్ షిఫ్ట్‌లను తగ్గించడానికి swapను మరింత ఆప్టిమైజ్ చేయవచ్చు."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ఫాంట్"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ఫాంట్ డిస్‌ప్లే"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "వృధా అయిన సమయం"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(అజ్ఞాతం)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "సాధారణంగా లేఅవుట్ జామెట్రీని చదివే చాలా APIలు, స్టయిల్, లేఅవుట్‌ను కాలిక్యులేట్ చేయడానికి స్క్రిప్ట్ ఎగ్జిక్యూషన్‌ను పాజ్ చేయమని రెండరింగ్ ఇంజిన్‌ను ఫోర్స్ చేస్తాయి. [ఫోర్స్ చేయబడిన రీఫ్లో](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)‌ను, దాని ఉపశమనాల గురించి మరింత తెలుసుకోండి."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "స్టాక్ ట్రేస్"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "ఫోర్స్ చేయబడిన రీఫ్లో"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "టాప్ ఫంక్షన్ కాల్"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "మొత్తం రీఫ్లోకు పట్టిన సమయం"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[unattributed]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "ఇమేజ్‌ల డౌన్‌లోడ్ సమయాన్ని తగ్గించడం ద్వారా పేజీ, LCP లోడ్ సమయాన్ని మెరుగుపరచవచ్చు. [ఇమేజ్ సైజ్‌ను ఆప్టిమైజ్ చేయడం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (అంచనా {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ఆప్టిమైజ్ చేయగల ఇమేజ్‌లు ఏవీ లేవు"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ఫైల్ సైజ్‌ను ఆప్టిమైజ్ చేయండి"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} ఇతరులు"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ఇమేజ్ డెలివరీని మెరుగుపరచండి"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "ఇమేజ్ కంప్రెషన్ ఫ్యాక్టర్‌ను పెంచడం వల్ల ఈ ఇమేజ్ డౌన్‌లోడ్ సైజ్‌ను మెరుగుపరచవచ్చు."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "ఆధునిక ఇమేజ్ ఫార్మాట్‌ను (WebP, AVIF) ఉపయోగించడం లేదా ఇమేజ్ కంప్రెషన్‌ను పెంచడం ద్వారా ఈ ఇమేజ్ డౌన్‌లోడ్ సైజ్‌ను మెరుగుపరచవచ్చు."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ఈ ఇమేజ్ ఫైల్, దాన్ని డిస్‌ప్లే చేయడానికి ఉండాల్సిన డైమెన్షన్‌ల ({PH2}) కంటే పెద్దదిగా ({PH1}) ఉంది. ఇమేజ్ డౌన్‌లోడ్ సైజ్‌ను తగ్గించడానికి ఫ్లెక్సిబుల్ ఇమేజ్‌లను ఉపయోగించండి."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFలకు బదులుగా వీడియో ఫార్మాట్‌లను ఉపయోగించడం ద్వారా యానిమేటెడ్ కంటెంట్ డౌన్‌లోడ్ సైజ్‌ను మెరుగుపరచవచ్చు."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "సుదీర్ఘమైన దశతో పరిశోధన ప్రారంభించండి. [ఆలస్యాలను తగ్గించుకోవచ్చు](https://web.dev/articles/optimize-inp#optimize_interactions). ప్రాసెసింగ్ వ్యవధిని తగ్గించడానికి, [మెయిన్ థ్రెడ్ ఖర్చులను ఆప్టిమైజ్ చేయండి](https://web.dev/articles/optimize-long-tasks), ఇవి సాధారణంగా JSకి చెందినవి అయి ఉంటాయి."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "వ్యవధి"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ఇన్‌పుట్ ఆలస్యం"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ఇంటరాక్షన్‌లు ఏవీ లేవు"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ఫేజ్"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "ప్రెజెంటేషన్‌లో ఆలస్యం"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "ప్రాసెసింగ్ వ్యవధి"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "దశల వారీగా INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTML నుంచి LCP ఇమేజ్‌ను తక్షణమే [కనిపించగలిగేలా](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) చేయడం, అలాగే [ప్రాధాన్యతను బట్టి లోడింగ్‌ పద్దతిని నివారించడం ](https://web.dev/articles/lcp-lazy-loading) ద్వారా LCPని ఆప్టిమైజ్ చేయండి"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high applied"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high వర్తింపజేయాలి"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ప్రాధాన్యతను బట్టి లోడింగ్ వర్తించబడదు"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "మొట్టమొదటి ప్రారంభ స్థానం తర్వాత LCP ఇమేజ్ {PH1} లోడ్ చేసింది."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP ఏదీ కనుగొనబడలేదు"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP ఇమేజ్ కానందున LCP రిసోర్స్ కనుగొనబడలేదు"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "ఇనిషియల్ డాక్యుమెంట్‌లో రిక్వెస్ట్ కనిపించగలదు"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP రిక్వెస్ట్ డిస్కవరీ"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ప్రతి [దశలో నిర్దిష్ట మెరుగుదల వ్యూహాలు ఉన్నాయి](https://web.dev/articles/optimize-lcp#lcp-breakdown). ముఖ్యంగా, LCP సమయాన్ని ఎక్కువ భాగం రిసోర్స్‌లను లోడ్ చేయడంలో వెచ్చించాలి, ఆలస్యంలో కాదు."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "వ్యవధి"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "ఎలిమెంట్ రెండర్ ల్యాగ్"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ఫీల్డ్ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP ఏదీ కనుగొనబడలేదు"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ఫేజ్"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "రిసోర్స్ లోడ్ ల్యాగ్"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "రిసోర్స్ లోడ్ వ్యవధి"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "తొలి బైట్ రెస్పాన్స్ సమయం"}, "models/trace/insights/LCPPhases.ts | title": {"message": "ఫేజ్ ఆధారంగా LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "స్క్రిప్ట్"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "వృధా బైట్‌లు"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "పాలీఫిల్స్, ఇంకా ట్రాన్స్‌ఫామ్‌లు, కొత్త JavaScript ఫీచర్‌లను ఉపయోగించగలిగేలా పాత బ్రౌజర్‌లను ఎనేబుల్ చేస్తాయి. అయితే, ఆధునిక బ్రౌజర్‌లకు వాటిలో చాలా వరకు అవసరం ఉండదు. మీరు మరింత పాత బ్రౌజర్‌లకు సపోర్ట్ అందించాలని మీకు ఖచ్చితంగా తెలిస్తే తప్ప, [బేస్‌లైన్](https://web.dev/articles/baseline-and-polyfills) ఫీచర్‌లను ట్రాన్స్‌పైల్ చేయకుండా మీ JavaScript బిల్డ్ ప్రాసెస్‌ను సవరించేలా పరిగణించండి. [చాలా సైట్‌లు ట్రాన్స్‌పైలింగ్ లేకుండా ES6+ కోడ్‌ను ఎందుకు అమలు చేయగలవో తెలుసుకోండి](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "లెగసీ JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/1.1 కంటే HTTP/2, HTTP/3లు, మల్టీప్లెక్సింగ్ వంటి అనేక ప్రయోజనాలను అందిస్తాయి. [ఆధునిక HTTP వినియోగం గురించి మరింత తెలుసుకోండి](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 ను రిక్వెస్ట్‌లు ఏవీ ఉపయోగించలేదు"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "ప్రోటోకాల్"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "రిక్వెస్ట్"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "ఆధునిక HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ఆరిజిన్"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "రిక్వెస్ట్"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "సోర్స్"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "సమయం"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Est LCP సేవింగ్స్"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "వినియోగించని ప్రీకనెక్ట్. crossorigin అట్రిబ్యూట్‌ను సరిగ్గా ఉపయోగించారో లేదో చెక్ చేయండి."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "పేజీ లోడ్‌ను మెరుగుపరచడానికి చెయిన్‌ల పొడవును తగ్గించడం, రిసోర్స్‌ల డౌన్‌లోడ్ సైజును తగ్గించడం, లేదా అనవసరమైన రిసోర్స్‌లను డౌన్‌లోడ్ చేయడాన్ని వాయిదా వేయడం వంటివి చేయడం ద్వారా [అతి ముఖ్యమైన రిక్వెస్ట్‌ల చైనింగ్‌ను నివారించండి](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "అత్యంత ముఖ్యమైన మీ ఆరిజిన్‌లకు [ప్రీకనెక్ట్](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) సూచనలను జోడించండి, కానీ 4 కంటే ఎక్కువ ఉపయోగించకుండా ట్రై చేయండి."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ప్రీకనెక్ట్ కావలసిన ఆరిజిన్‌లు"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "గరిష్ఠ క్లిష్టమైన పాత్ ల్యాగ్:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "నెట్‌వర్క్ డిపెండెన్సీ‌ల ద్వారా రెండరింగ్ టాస్క్‌లు ఏవీ ప్రభావితం కాలేదు"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "ప్రీకనెక్ట్ చేయడానికి అదనపు ఆరిజిన్‌లు ఏవీ సరైనవి కావు"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ఆరిజిన్‌లు ఏవీ ప్రీకనెక్ట్ అయి లేవు"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "పేజీ లోడ్ అవ్వడానికి ముందే కనెక్షన్‌ను ఏర్పాటు చేసేలా [ప్రీకనెక్ట్](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) సూచనలు బ్రౌజర్‌కు సహాయపడతాయి, దాని వలన ఆ ఆరిజిన్ కోసం మొదటి రిక్వెస్ట్ చేసినప్పుడు సమయం ఆదా అవుతుంది. ఈ కింది ఆరిజిన్‌లకు పేజీ ప్రీకనెక్ట్ అయి ఉంది."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "ముందుగా కనెక్ట్ చేసిన ఆరిజిన్‌లు"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "నెట్‌వర్క్ డిపెండెన్సీ ట్రీ"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4 కంటే ఎక్కువ preconnect కనెక్షన్‌లను కనుగొన్నాము. వీటిని పరిమితంగా, కేవలం అత్యంత ముఖ్యమైన ఆరిజిన్‌లకు మాత్రమే ఉపయోగించాలి."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "వినియోగించని ప్రీకనెక్ట్ ఉంది. పేజీ రిక్వెస్ట్ చేసే అవకాశం ఉన్న ఆరిజిన్‌లకు మాత్రమే preconnect‌ను ఉపయోగించండి."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "పేజీ లోడ్‌ను మెరుగుపరచడానికి చెయిన్‌ల పొడవును తగ్గించడం, రిసోర్స్‌ల డౌన్‌లోడ్ సైజును తగ్గించడం, లేదా అనవసరమైన రిసోర్స్‌లను డౌన్‌లోడ్ చేయడాన్ని వాయిదా వేయడం వంటివి చేయడం ద్వారా అతి ముఖ్యమైన రిక్వెస్ట్‌ల చైనింగ్‌ను నివారించండి."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "రిక్వెస్ట్‌లు పేజీకి సంబంధించిన ప్రారంభ రెండర్‌ను బ్లాక్ చేస్తున్నాయి, దీని వలన LCP ఆలస్యం కావచ్చు. [వాయిదా వేయడం లేదా ఇన్‌లైన్ చేయడం](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ఈ నెట్‌వర్క్ రిక్వెస్ట్‌లను క్లిష్టమైన మార్గం నుండి తరలించగలదు."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "వ్యవధి"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ఈ నావిగేషన్ విషయంలో రెండర్ బ్లాకింగ్ రిక్వెస్ట్‌లు ఏవీ లేవు"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "రిక్వెస్ట్"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "బ్లాకింగ్ రిక్వెస్ట్‌లను రెండర్ చేయండి"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "స్టయిల్‌ను తిరిగి లెక్కించడానికి అయ్యే ఖర్చు ఎక్కువగా ఉంటే, ఎంపిక సాధనం ఆప్టిమైజేషన్ దాన్ని తగ్గిస్తుంది. అధిక గడిచిన సమయం, అలాగే స్లో-పాత్‌ % రెండింటితో [ఎంపిక సాధనాలను ఆప్టిమైజ్ చేయండి](https://developer.chrome.com/docs/devtools/performance/selector-stats). సులభమైన ఎంపిక సాధనాలు, అతి తక్కువ ఎంపిక సాధనాలు, చిన్న DOM, అలాగే షాలో సైజ్ DOM ఇవన్నీ మ్యాచింగ్ ఖర్చులను తగ్గిస్తాయి."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "గడిచిన సమయం"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS ఎంపిక సాధనం డేటా ఏదీ లేదు. పనితీరు ప్యానెల్ సెట్టింగ్‌లలో CSS ఎంపిక సాధనం గణాంకాలను ఎనేబుల్ చేయాలి."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "మ్యాచ్ ప్రయత్నాలు"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "మ్యాచ్ సంఖ్య"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ఎంపిక సాధనం ఖర్చులు"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "టాప్ ఎంపిక సాధనాలు"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "మొత్తం"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "ప్రధాన థ్రెడ్ సమయం"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "3వ పార్టీ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "బదిలీ సైజ్"}, "models/trace/insights/ThirdParties.ts | description": {"message": "3వ-పార్టీ కోడ్ గణనీయమైన స్థాయిలో లోడ్ పనితీరుపై ప్రభావం చూపవచ్చు. మీ పేజీకి సంబంధించిన కంటెంట్‌కు ప్రాధాన్యత ఇవ్వడానికి[3వ-పార్టీ కోడ్‌ను తగ్గించండి, అలాగే లోడ్ చేయడం మినహాయించండి](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "థర్డ్-పార్టీ‌లు ఏవీ లేవు"}, "models/trace/insights/ThirdParties.ts | title": {"message": "3వ పార్టీలు"}, "models/trace/insights/Viewport.ts | description": {"message": "మొబైల్ కోసం వీక్షణ పోర్ట్‌ను ఆప్టిమైజ్ చేయనట్లయితే ట్యాప్ ఇంటరాక్షన్‌లు [గరిష్ఠంగా 300 మి.సె. వరకు ఆలస్యం కావచ్చు](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "మొబైల్ ట్యాప్‌లో ఆలస్యం"}, "models/trace/insights/Viewport.ts | title": {"message": "మొబైల్ కోసం వీక్షణ పోర్ట్‌ను ఆప్టిమైజ్ చేయండి"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET రిక్వెస్ట్ ద్వారా లోడ్ చేయబడిన పేజీలకు మాత్రమే వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి అర్హత ఉంది."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX స్టేటస్ కోడ్‌ను కలిగి ఉన్న పేజీలు మాత్రమే కాష్ చేయబడతాయి."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "కాష్‌లో ఉన్నప్పుడు JavaScriptను ఎగ్జిక్యూట్ చేసే ప్రయత్నాన్ని Chrome గుర్తించింది."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "వెనుకకు/ముందుకు కాష్ ఫ్లాగ్‌ల ద్వారా డిజేబుల్ చేయబడింది. chrome://flags/#back-forward-cacheకు వెళ్లి, దాన్ని ఈ పరికరంలోనే ఎనేబుల్ చేయండి."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "కమాండ్ లైన్ ద్వారా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "తగినంత మెమరీ లేనందున వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "వెనుకకు/ముందుకు కాష్‌కు డెలిగేట్ సపోర్ట్ లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "ప్రీరెండరర్ కోసం వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "రిజిస్టర్ అయిన లిజనర్‌లతో ఈ పేజీ BroadcastChannel పర్యాయాన్ని కలిగి ఉంది కనుక, ఈ పేజీని కాష్ చేయడం కుదరదు."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store హెడర్‌ను కలిగి ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "కాష్ ఉద్దేశపూర్వకంగా క్లియర్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "మరొక పేజీని కాష్ చేయడానికి ఈ పేజీ కాష్ నుండి తీసివేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "ప్లగ్ఇన్‌లను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "నిర్వచించబడనిది"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcherను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు మీడియా ప్లేయర్ ప్లే అవుతోంది."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession APIను ఉపయోగించే, ఇంకా ప్లేబ్యాక్ స్థితిని సెట్ చేసే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession APIను ఉపయోగించే, యాక్షన్ హ్యాండ్లర్‌లను సెట్ చేసే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "స్క్రీన్ రీడర్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandlerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB APIను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store‌ను ఉపయోగించే పేజీలో కుక్కీలు డిజేబుల్ చేసి ఉన్నందున వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "డెడికేటెడ్ వర్కర్ లేదా వర్క్‌లెట్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "డాక్యుమెంట్ లోడ్ అవ్వడం పూర్తి కాకుండానే పేజీ నుండి నిష్క్రమించారు."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు యాప్ బ్యానర్ రన్ అవుతూ ఉండింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు Chrome పాస్‌వర్డ్ మేనేజర్ రన్ అవుతూ ఉండింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు DOM డిస్టిలేషన్ ప్రోగ్రెస్‌లో ఉంది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు DOM Distiller వ్యూయర్ రన్ అవుతూ ఉండింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "మెసేజింగ్ APIను ఉపయోగించే ఎక్స్‌టెన్షన్‌ల కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "దీర్ఘకాలిక కనెక్షన్‌ను కలిగి ఉన్న ఎక్స్‌టెన్షన్‌లు వెనుకకు/ముందుకు కాష్‌లో స్టోర్ అవ్వకంటే ముందు అవి ఆ కనెక్షన్‌ను నిలిపివేయాలి."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "దీర్ఘకాలిక కనెక్షన్‌ను కలిగి ఉన్న ఎక్స్‌టెన్షన్‌లు వెనుకకు/ముందుకు కాష్‌లో ఫ్రేమ్‌లకు మెసేజ్‌లను పంపడానికి ట్రై చేశాయి."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "ఎక్స్‌టెన్షన్‌ల కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు ఫారమ్ తిరిగి సమర్పించడానికి సంబంధించిన సమాచారం లేదా HTTP పాస్‌వర్డ్ డైలాగ్ వంటి మోడల్ డైలాగ్ కనిపించింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు ఆఫ్‌లైన్ పేజీ చూపబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు మెమరీ నిండింది అనే చూపే ఇంటర్వెన్షన్ బార్ రన్ అవుతూ ఉండింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు అనుమతికి సంబంధించిన రిక్వెస్ట్‌లు ఎదురయ్యాయి."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు పాప్‌అప్ బ్లాకర్ రన్ అవుతూ ఉండింది."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "పేజీ నుండి నిష్క్రమిస్తున్నప్పుడు సురక్షిత బ్రౌజింగ్ వివరాలు ప్రదర్శించబడ్డాయి."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "సురక్షిత బ్రౌజింగ్, ఈ పేజీ దుర్వినియోగమైనదిగా గుర్తించి, పాప్‌‌అప్‌ను బ్లాక్ చేసింది."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌లో ఉన్నప్పుడు ఒక సర్వీస్ వర్కర్ యాక్టివేట్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "డాక్యుమెంట్‌లో ఉన్న ఎర్రర్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFramesను ఉపయోగిస్తున్న పేజీలను వెనుకకు/ముందుకు కాష్‌లో స్టోర్ చేయలేము."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "మరొక పేజీని కాష్ చేయడానికి ఈ పేజీ కాష్ నుండి తీసివేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "మీడియా స్ట్రీమ్ యాక్సెస్ కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "పొందుపరచబడిన కంటెంట్ తాలూకు నిర్దిష్ట రకాలను కలిగి ఉన్న పేజీలు (ఉదా. PDFలు) బ్యాక్/ఫార్వర్డ్ కాష్‌కు ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManagerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "ఓపెన్ IndexedDB కనెక్షన్ ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB ఈవెంట్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "అర్హత లేని APIలు ఉపయోగించబడ్డాయి."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "ఎక్స్‌టెన్షన్‌ల ద్వారా JavaScript ఇంజెక్ట్ చేయబడిన పేజీలకు వెనుకకు-ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "ఎక్స్‌టెన్షన్‌ల ద్వారా StyleSheet ఇంజెక్ట్ చేసిన పేజీల కోసం వెనుకకు-ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "అంతర్గత ఎర్రర్."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది, ఎందుకంటే కొంత JavaScript నెట్‌వర్క్ రిక్వెస్ట్ Cache-Control: no-store హెడర్‌తో కూడిన రిసోర్స్‌ను అందుకుంది."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "'యాక్టివ్‌గా ఉంచ'మని పంపిన రిక్వెస్ట్ కారణంగా వెనుకకు/ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "కీబోర్డ్ లాక్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "పేజీ లోడ్ అవ్వడం పూర్తి కాకుండానే పేజీ నుండి నిష్క్రమించారు."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "cache-control:no-cache, ప్రధాన రిసోర్స్‌గా ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "cache-control:no-store, ప్రధాన రిసోర్స్‌గా ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "వెనుకకు/ముందుకు కాష్ నుండి పేజీని రీస్టోర్ చేయడానికి ముందే నావిగేషన్ రద్దు చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "కాష్ నుండి ఈ పేజీ తీసివేయబడింది, ఎందుకంటే ప్రస్తుతం ఉన్న నెట్‌వర్క్ కనెక్షన్ చాలా ఎక్కువ డేటాను అందుకుంది. ఏదైనా పేజీ కాష్ చేయబడుతున్నప్పుడు ఆ పేజీ అందుకునే డేటా మొత్తాన్ని Chrome పరిమితం చేస్తుంది."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ఇన్‌ఫ్లయిట్ ఫెచ్() లేదా XHR ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "యాక్టీవ్ నెట్‌వర్క్ రిక్వెస్ట్‌పై మళ్లింపు ఏర్పడింది, దీని వలన పేజీ వెనుకకు/ముందుకు కాష్ నుండి తీసివేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "కాష్ నుండి ఈ పేజీ తీసివేయబడింది, ఎందుకంటే నెట్‌వర్క్ కనెక్షన్ చాలా సేపు తెరిచి ఉంది. పేజీ కాష్ అయినప్పుడు డేటాను తిరిగి పొందేందుకు పట్టే సమయాన్ని Chrome పరిమితం చేస్తుంది."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "చెల్లుబాటు అయ్యే ప్రతిస్పందన హెడర్ లేని పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "నావిగేషన్ ప్రధాన ఫ్రేమ్‌లో కాకుండా వేరొక ఫ్రేమ్‌లో జరిగింది."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "ఇండెక్స్ చేయబడిన ఆన్‌గోయింగ్ DB లావాదేవీలను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "ఇన్-ఫ్లయిట్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "ఇన్-ఫ్లయిట్ ఫెచ్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "ఇన్-ఫ్లయిట్ నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "ఇన్-ఫ్లయిట్ XHR నెట్‌వర్క్ రిక్వెస్ట్‌ను కలిగి ఉన్న పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManagerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "పిక్చర్-ఇన్-పిక్చర్‌ను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "ప్రింటింగ్ UIను చూపించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "ఈ పేజీ 'window.open()'ను ఉపయోగించి తెరవబడింది, అలాగే ఇది వేరొక ట్యాబ్ రెఫరెన్స్‌ను కలిగి ఉంది, లేదా ఆ పేజీ విండోను తెరిచింది."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "వెనుకకు/ముందుకు కాష్‌లో పేజీ కోసం రెండరర్ ప్రాసెస్ క్రాష్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "వెనుకకు/ముందుకు కాష్‌లోని పేజీ కోసం రెండరర్ ప్రాసెస్ ఆపివేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ఆడియో క్యాప్చర్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "సెన్సార్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "బ్యాక్‌గ్రౌండ్ సింక్ కోసం లేదా బ్యాక్‌గ్రౌండ్‌లో అనుమతులను పొందడానికి రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "నోటిఫికేషన్‌ల అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "స్టోరేజ్ యాక్సెస్ కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "వీడియో క్యాప్చర్ అనుమతుల కోసం రిక్వెస్ట్ చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPS URL స్కీమ్ ఉన్న పేజీలు మాత్రమే కాష్ చేయబడతాయి."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "ఈ పేజీని వెనుకకు/ముందుకు కాష్ చేస్తున్నప్పుడు సర్వీస్ వర్కర్ ద్వారా పేజీ వినియోగించబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "సర్వీస్ వర్కర్, వెనుకకు/ముందుకు కాష్‌లో ఉన్న పేజీకి MessageEventను పంపడానికి ట్రై చేసింది."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌లో ఉన్నప్పుడు ServiceWorker రిజిస్టర్ చేయబడలేదు."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "సర్వీస్ వర్కర్‌కు సంబంధించిన యాక్టివేషన్ కారణంగా పేజీ వెనుక/ముందుకు కాష్ నుండి తీసివేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome రీస్టార్ట్ చేయబడింది, ఇంకా అది వెనుకకు/ముందుకు కాష్ ఎంట్రీలను క్లియర్ చేసింది."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorkerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizerను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesisను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "పేజీలోని iframe ఒక నావిగేషన్‌ను ప్రారంభించింది, కానీ అది పూర్తి కాలేదు."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "cache-control:no-cache, సబ్‌రిసోర్స్‌గా ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "cache-control:no-store సబ్‌రిసోర్స్‌గా ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "ఈ పేజీ వెనుకకు/ముందుకు కాష్‌లో గరిష్ఠ సమయాన్ని దాటింది, ఇంకా దీని గడువు ముగిసింది."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "పేజీ వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయడానికి సమయం ముగిసింది (pagehide హ్యాండ్లర్‌లు ఎక్కువ సేపు రన్ అయిన కారణంగా ఇలా జరిగి ఉండవచ్చు)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "ఈ పేజీ, ప్రధాన ఫ్రేమ్‌లో అన్‌లోడ్ హ్యాండ్లర్‌ను కలిగి ఉంది."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "ఈ పేజీ సబ్ ఫ్రేమ్‌లో అన్‌లోడ్ హ్యాండ్లర్ ఉంది."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "బ్రౌజర్, వెబ్‌సైట్ యాక్సెస్ సాధనం ఓవర్‌రైడ్ హెడర్‌ను మార్చింది."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "వీడియో లేదా ఆడియోను రికార్డ్ చేయడానికి యాక్సెస్‌ను మంజూరు చేసిన పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabaseను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHIDను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocksను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfcని ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPServiceను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTCని ఉపయోగించే పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTCని ఉపయోగించారు కనుక వెనుకకు-ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShareను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSockets ఉన్న పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocketను ఉపయోగించారు కనుక వెనుకకు-ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransportను ఉపయోగించే పేజీలు వెనుకకు/ముందుకు కాష్‌ను యాక్సెస్ చేయలేవు."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransportను ఉపయోగించారు కనుక వెనుకకు-ముందుకు కాష్ డిజేబుల్ చేయబడింది."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXRను ఉపయోగించే పేజీలకు వెనుకకు/ముందుకు కాష్‌ను ఉపయోగించడానికి ప్రస్తుతం అర్హత లేదు."}}