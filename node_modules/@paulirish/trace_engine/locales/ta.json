{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headersஸைக் கையாளும்போது வைல்டுகார்டு குறியீட்டில் (*) அங்கீகரிப்பு சேர்க்கப்படாது."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "இயல்பு அலைபரப்பு ஒருங்கிணைப்பை முடக்க -internal-media-controls-overlay-cast-button தேர்விக்குப் பதிலாக disableRemotePlayback பண்புக்கூறைப் பயன்படுத்த வேண்டும்."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "slider-vertical என்ற CSS தோற்ற மதிப்பு நிலையானது அல்ல மற்றும் இது அகற்றப்படும்."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "அகற்றப்பட்ட இடைவெளி \\(n|r|t) எழுத்துகள், ‘குறைவைக்’ குறிக்கும் எழுத்துகள் (<) ஆகிய இரண்டும் இருக்கும் URLகளின் ஆதாரக் கோரிக்கைகள் தடுக்கப்பட்டுள்ளன. இந்த ஆதாரங்களை ஏற்ற, நியூலைன்களை அகற்றி ‘குறைவைக்’ குறிக்கும் எழுத்துகளை எலிமெண்ட் ஆட்ரிபியூட் மதிப்புகள் போன்ற இடங்களில் இருந்து என்கோடிங் செய்யவும்."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() செயல்பாடு நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() நிறுத்தப்பட்டது, இதற்குப் பதிலாகப் பின்வரும் நிலையான APIயைப் பயன்படுத்தவும்: Navigation Timing 2ல் nextHopProtocol."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) எழுத்து இருக்கும் குக்கீகள் துண்டிக்கப்படுவதற்குப் பதிலாக நிராகரிக்கப்படும்."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain ஐ அமைத்து அதே ஆரிஜின் கொள்கையைத் தளர்த்துவது நிறுத்தப்பட்டது. இயல்பாகவே இது முடக்கப்படும். இந்த நிறுத்த எச்சரிக்கை கிராஸ் ஆரிஜின் அணுகலுக்கானது. இது document.domain அமைப்பின் மூலம் இயக்கப்பட்டது."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "கிராஸ் ஆரிஜின் iframesஸில் இருந்து window.alertடை டிரிகர் செய்வது நிறுத்தப்பட்டது, எதிர்காலத்தில் அது அகற்றப்படும்."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "கிராஸ் ஆரிஜின் iframesஸில் இருந்து window.confirmமை டிரிகர் செய்வது நிறுத்தப்பட்டது, எதிர்காலத்தில் அது அகற்றப்படும்."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "தரவுக்கான ஆதரவு: SVGUseElementடில் இருக்கும் URLகள் நிறுத்தப்பட்டன மற்றும் எதிர்காலத்தில் அது அகற்றப்படும்."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் getCurrentPosition() மற்றும் watchPosition() இனி செயல்படாது. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் getCurrentPosition(), watchPosition() ஆகியவை நிறுத்தப்பட்டன. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் getUserMedia() இனி செயல்படாது. இந்த அம்சத்தைப் பயன்படுத்த உங்கள் ஆப்ஸை HTTPS போன்ற பாதுகாப்பான ஆரிஜின்களுக்கு மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> அல்லது <section> இல் குறிப்பிட்ட எழுத்து வடிவ அளவு இல்லாத <h1> குறிச்சொல் கண்டறியப்பட்டுள்ளது. இந்தத் தலைப்பு வார்த்தைகளின் அளவு இந்த பிரவுசரில் வெகுவிரைவில் மாற்றப்படும். மேலும் தகவலுக்கு https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 பக்கத்தைப் பாருங்கள்."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate நிறுத்தப்பட்டது. இதற்குப் பதிலாக RTCPeerConnectionIceErrorEvent.address அல்லது RTCPeerConnectionIceErrorEvent.port ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "டிஜிட்டல் அனுமதிச் சான்றிதழ்களுக்கான navigator.credentials.get() கோரிக்கைக்கான இந்த வடிவமைப்பு நிறுத்தப்பட்டது, புதிய வடிவமைப்பைப் பயன்படுத்தி உங்கள் கோரிக்கையை அனுப்பவும்."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment service worker நிகழ்வில் இருந்து பெறப்பட்ட வணிகரின் ஆரிஜின் மற்றும் தன்னிச்சையான தரவு நிறுத்தப்பட்டு அகற்றப்படும்: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "பயனர்களின் சிறப்பு நெட்வொர்க் நிலை காரணமாக மட்டுமே இந்த இணையதளம் அணுகக்கூடிய நெட்வொர்க்கில் இருந்து துணை ஆதாரத்தைக் கோருகிறது. இந்தக் கோரிக்கைகள் பொதுவில் இல்லாத சாதனங்களையும் சேவையகங்களையும் இணையத்தில் வெளிப்படுத்தும். இதன் மூலம் வேற்று தளக் கோரிக்கை மோசடி (CSRF) தாக்குதல் மற்றும்/அல்லது தகவல் கசிவு போன்ற அபாயத்தை அதிகரிக்கும். இதுபோன்ற அபாயங்களைக் குறைக்க பொதுவில் இல்லாத துணை ஆதாரங்களுக்குப் பாதுகாப்பற்ற சூழல்களில் இருந்து வரக்கூடிய கோரிக்கைகளை Chrome நிறுத்துவதோடு அவற்றைத் தடுக்கவும் செய்கிறது."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup()க்கு அனுப்பப்பட்ட InterestGroupsன் dailyUpdateUrl என்ற புலம் அதன் செயல்பாடுகளை மிகவும் துல்லியமாகக் காட்ட updateUrl என்று பெயர் மாற்றப்பட்டுள்ளது."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator நிறுத்தப்பட்டது. இதற்குப் பதிலாக Intl.Segmenter ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css ஃபைல் நீட்டிப்பில் முடிவடைந்தால் மட்டுமே file: URLகளில் இருந்து CSSஸை ஏற்ற முடியும்."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove() இன் ஒத்திசையாத வரம்பு அகற்றுதலை ரத்துசெய்ய SourceBuffer.abort() ஐப் பயன்படுத்துவது, விவரக்குறிப்பு மாற்றம் காரணமாக நிறுத்தப்பட்டது. இதற்கான ஆதரவும் எதிர்காலத்தில் அகற்றப்படும். அதற்குப் பதிலாக updateend நிகழ்வைக் கவனிக்க வேண்டும். ஒத்திசையாத மீடியா இணைப்பை ரத்துசெய்ய அல்லது பாகுபடுத்தும் நிலையை மீட்டமைக்க மட்டும் abort() பயன்படுத்தப்படும்."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "இடையகக் குறியீடு ஃபிரேம்களின் அதிகபட்சக் காட்சி நேரமுத்திரைக்குக் குறைவாக MediaSource.duration ஐ அமைப்பது விவரக்குறிப்பு மாற்றம் காரணமாக நிறுத்தப்பட்டது. துண்டிக்கப்பட்ட இடையக மீடியாவை மறைமுகமாக அகற்றுவதற்கான ஆதரவு எதிர்காலத்தில் அகற்றப்படும். newDuration < oldDuration எனும் நிலை உள்ள எல்லா இடங்களிலும் sourceBuffers அனைத்திலும் வெளிப்படையான remove(newDuration, oldDuration) ஐ மேற்கொள்ள வேண்டும்."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions இல் sysex குறிப்பிடப்படாமல் இருந்தாலும் பயன்படுத்துவதற்கான அனுமதியை இணைய MIDI கோரும்."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "பாதுகாப்பற்ற ஆரிஜின்களில் இருந்து Notification APIயை இனி பயன்படுத்த முடியாது. பாதுகாப்பான ஆரிஜினுக்கு (எ.கா. HTTPS) உங்கள் ஆப்ஸை மாற்றவும். கூடுதல் தகவல்களுக்கு https://goo.gle/chrome-insecure-origins தளத்தைப் பார்க்கவும்."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Notification APIக்கான அனுமதியை கிராஸ் ஆரிஜின் iframeமில் இருந்து இனி கோர முடியாது. உயர்நிலை ஃபிரேமிடம் அனுமதி கோரவும் அல்லது புதிய சாளரத்தைத் திறக்கவும்."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmapபில் imageOrientation: 'none' விருப்பம் நிறுத்தப்பட்டது. இதற்குப் பதிலாக '{imageOrientation: 'from-image'}' விருப்பத்துடன் createImageBitmapபைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "காலாவதியான (D)TLS பதிப்பை உங்கள் கூட்டாளர் பரிமாற்றம் செய்கிறார். இதைச் சரிசெய்ய உங்கள் கூட்டாளரைத் தொடர்புகொள்ளவும்."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "படம், வீடியோ மற்றும் கேன்வாஸ் குறிச்சொற்களில் overflow: visible ஐக் குறிப்பிடுவது, உறுப்பு வரம்புகளைத் தாண்டிய காட்சி உள்ளடக்கத்திற்கு வழிவகுக்கலாம். https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md என்ற இணைப்பைப் பார்க்கவும்."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments நிறுத்தப்பட்டது. அதற்குப் பதிலாகப் பேமெண்ட் ஹேண்ட்லர்களுக்கான just-in-time நிறுவலைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "உங்கள் PaymentRequest அழைப்பு உள்ளடக்கப் பாதுகாப்புக் கொள்கை (CSP) connect-src டைரெக்ட்டிவைப் புறக்கணித்துவிட்டது. இந்தப் புறக்கணிப்பு நிறுத்தப்பட்டது. PaymentRequest APIயில் (supportedMethods புலத்தில்) உள்ள பேமெண்ட் முறை அடையாளங்காட்டியை உங்கள் CSP connect-src டைரெக்டிவில் சேர்க்கவும்."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent நிறுத்தப்பட்டது. இதற்குப் பதிலாக நிலையான navigator.storage ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "முதல்நிலை <picture> உடன் கூடிய <source src> தவறானது, எனவே தவிர்க்கப்பட்டது. இதற்குப் பதிலாக <source srcset> ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame என்பது வெண்டாருக்கு மட்டுமானது. இதற்குப் பதிலாக நிலையான cancelAnimationFrameமைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame என்பது வெண்டாருக்கு மட்டுமானது. இதற்குப் பதிலாக நிலையான requestAnimationFrameமைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen நிறுத்தப்பட்டது. இதற்குப் பதிலாக Document.fullscreenElementடைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() நிறுத்தப்பட்டது. இதற்குப் பதிலாக Element.requestFullscreen() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() நிறுத்தப்பட்டது. இதற்குப் பதிலாக Element.requestFullscreen() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullscreen() நிறுத்தப்பட்டது. இதற்குப் பதிலாக Document.exitFullscreen() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() நிறுத்தப்பட்டது. இதற்குப் பதிலாக Document.exitFullscreen() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen நிறுத்தப்பட்டது. இதற்குப் பதிலாக Document.fullscreenEnabled ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "நாங்கள் chrome.privacy.websites.privacySandboxEnabled APIயை நிறுத்துகிறோம், இருப்பினும் M113 வெளியீட்டின் வரை பின்தங்கிய இணக்கத்தன்மையின் பொருட்டு தொடர்ந்து இயக்கத்தில் இருக்கும். அதற்குப் பதிலாக chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, chrome.privacy.websites.adMeasurementEnabled ஆகிய APIகளைப் பயன்படுத்தவும். https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled என்ற தளத்தைப் பார்க்கவும்."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement கட்டுப்பாடு அகற்றப்பட்டது. இந்தக் கட்டுப்பாட்டுக்கு false மதிப்பைக் குறிப்பிட்டுள்ளீர்கள். இதன் மூலம் அகற்றப்பட்ட SDES key negotiation முறையைப் பயன்படுத்த முயற்சித்ததாகப் புரிந்துகொள்ளப்படுகிறது. இந்தச் செயல்பாடு அகற்றப்பட்டது, இதற்கு பதிலாக DTLS key negotiation ஐ ஆதரிக்கும் சேவையைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement கட்டுப்பாடு அகற்றப்பட்டது. இந்தக் கட்டுப்பாட்டுக்கு true மதிப்பைக் குறிப்பிட்டுள்ளீர்கள். இது விளைவுகள் எதையும் ஏற்படுத்தாது, ஆனால் தோற்றத்தின் காரணமாக இந்தக் கட்டுப்பாட்டை நீங்கள் அகற்றலாம்."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "கால்பேக் அடிப்படையிலான getStats() நிறுத்தப்பட்டது, அகற்றவும்படும். இதற்குப் பதிலாக விவரக்குறிப்புக்கு இணக்கமான getStats() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() நிறுத்தப்பட்டது. இதற்குப் பதிலாக Selection.modify() ஐப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "உட்பொதிந்த அனுமதிச் சான்றுகள் இருக்கும் URLகளின் துணை ஆதாரக் கோரிக்கைகள் (எ.கா. **********************/) தடுக்கப்பட்டன."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy விருப்பம் நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும்."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBufferக்குக் கிராஸ் ஆரிஜின் ஐசொலேஷன் தேவை. கூடுதல் தகவல்களுக்கு https://developer.chrome.com/blog/enabling-shared-array-buffer/ தளத்திற்குச் செல்லவும்."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "பயனரைச் செயல்படுத்தாமல் speechSynthesis.speak() செயல்பாட்டை அழைக்கும் வசதி நிறுத்தப்பட்டது, அது விரைவில் அகற்றப்படும்."}, "generated/Deprecation.ts | UnloadHandler": {"message": "அன்லோடு ஈவண்ட் லிசனர்கள் நிறுத்தப்பட்டன, அவை விரைவில் அகற்றப்படும்."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer ஐத் தொடர்ந்து பயன்படுத்த நீட்டிப்புகள் கிராஸ் ஆரிஜின் ஐசொலேஷனைப் பயன்படுத்த வேண்டும். https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ தளத்தைப் பார்வையிடவும்."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter பண்புக்கூறு நிறுத்தப்பட்டது, அதற்குப் பதிலாக GPUAdapterInfo isFallbackAdapter பண்புக்கூறைப் பயன்படுத்தவும்."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest இல் உள்ள json பதிலில் UTF-16 ஆதரிக்கப்படுவதில்லை"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "இறுதிப் பயனர் அனுபவத்தில் பாதிப்பை ஏற்படுத்தும் என்பதால் முதன்மைத் தொடரிழையில் ஒத்திசையும் XMLHttpRequest நிறுத்தப்பட்டது. கூடுதல் உதவிக்கு https://xhr.spec.whatwg.org/ தளத்தைப் பார்க்கவும்."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "அனிமேஷன்"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "பயனரின் எந்தவொரு செயலும் இல்லாமல் எலிமெண்ட்டுகள் நகரும்போது லே-அவுட் ஷிஃப்ட்கள் நிகழும். பக்கம் ஏற்றப்படும்போது எலிமெண்ட்களின் எழுத்து வடிவங்கள் மாறுவது, எலிமெண்ட்டுகள் சேர்க்கப்படுவது, அகற்றப்படுவது போன்ற [லே-அவுட் ஷிஃப்ட்டிற்கான காரணங்களை ஆய்வு செய்யவும்](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "எழுத்து வடிவத்திற்கான கோரிக்கை"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "சேர்க்கப்பட்டுள்ள iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "{PH1} இல் லே-அவுட் ஷிஃப்ட் கிளஸ்டர்"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "லே-அவுட் ஷிஃப்ட்டுக்கான எந்தக் காரணத்தையும் கண்டறிய முடியவில்லை"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "லே-அவுட் ஷிஃப்ட்கள் இல்லை"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "லே-அவுட் ஷிஃப்ட்டுக்கான காரணங்கள்"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "லே-அவுட் ஷிஃப்ட்டுக்கான முக்கியமான காரணங்கள்"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "மோசமான கிளஸ்டர்"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "மோசமான லே-அவுட் ஷிஃப்ட் கிளஸ்டர்"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "தற்காலிகச் சேமிப்பு TTL"}, "models/trace/insights/Cache.ts | description": {"message": "தற்காலிகச் சேமிப்பின் ஆயுட்காலம் நீண்டதாக இருந்தால், மீண்டும் மீண்டும் திறக்கப்படும் இணையப் பக்கங்கள் விரைவாகக் காட்டப்படலாம். [மேலும் அறிக](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "மேம்படுத்தப்படாத தற்காலிகச் சேமிப்புக் கொள்கைகளில் கோரிக்கைகள் எதுவும் இல்லை"}, "models/trace/insights/Cache.ts | others": {"message": "மேலும் {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "கோரிக்கை"}, "models/trace/insights/Cache.ts | title": {"message": "மேம்படுத்தப்பட்ட தற்காலிகச் சேமிப்பு ஆயுட்காலங்களைப் பயன்படுத்துதல்"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM அளவு பெரிதாக இருப்பதால் ஸ்டைல் கணக்கீடுகள் மற்றும் தளவமைப்பு மறுசீராக்கங்களின் கால அளவு அதிகரிக்கலாம், இதனால் பக்கத்தின் பதிலளிக்கும் தன்மையும் பாதிக்கப்படலாம். ஒரு பெரிய DOM நினைவக உபயோகத்தையும் அதிகரிக்கும். [அதிகப்படியான DOM அளவைத் தவிர்ப்பது எப்படி என்று தெரிந்துகொள்ளுங்கள்](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "எலிமெண்ட்"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "அதிகபட்ச உபநிலைகள்"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM ஆழ அளவு"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "புள்ளிவிவரம்"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM அளவு மேம்பாடு"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "மொத்த எலிமெண்ட்டுகள்"}, "models/trace/insights/DOMSize.ts | value": {"message": "மதிப்பு"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "உங்களின் முதல் நெட்வொர்க் கோரிக்கை மிகவும் முக்கியமானது.  திசைதிருப்புதல்களைத் தவிர்த்தல், விரைவான சேவையகப் பதிலை உறுதிசெய்தல், வார்த்தையைச் சுருக்குதல் ஆகியவற்றின் மூலம் அதன் தாமதத்தைக் குறைக்கவும்."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "திசைதிருப்புதல்கள் இருந்தன ({PH1} திசைதிருப்புதல்கள், +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "சேவையகம் மெதுவாகப் பதிலளித்தது (கண்டறியப்பட்டது {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "சுருக்குதல் பயன்படுத்தப்படவில்லை"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "திசைதிருப்புதல்களைத் தவிர்க்கும்"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "சேவையகம் வேகமாகப் பதிலளித்தது (கண்டறியப்பட்டது {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "வார்த்தைச் சுருக்குதலைப் பயன்படுத்துகிறது"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "திசைதிருப்புதல்கள்"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "சேவையக பதிலளிப்பு நேரம்"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ஆவணக் கோரிக்கைக்கான தாமதம்"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "சுருக்கப்படாத பதிவிறக்கம்"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "நகல் பைட்டுகள்"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ஆதாரம்"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "நெட்வொர்க் செயல்பாடு தேவையில்லாமல் பைட்களை பயன்படுத்துவதைக் குறைக்க, தொகுப்புகளிலில் இருந்து பெரிய, நகலெடுக்கப்பட்ட JavaScript மாடியூல்களை அகற்றவும்."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "நகல் JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "வார்த்தைகள் சீராகத் தெரிவதை உறுதிசெய்துகொள்ள, [font-display](https://developer.chrome.com/blog/font-display) ஐ swap அல்லது optional என்று அமைக்கவும். [எழுத்து வடிவ அளவீட்டை மீறிச் செயல்படுதல்](https://developer.chrome.com/blog/font-fallbacks) அம்சத்தின் மூலம் லே-அவுட் ஷிஃப்ட்டுகளைக் குறைக்க, swap ஐ மேலும் மேம்படுத்தலாம்."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "எழுத்து வடிவம்"}, "models/trace/insights/FontDisplay.ts | title": {"message": "எழுத்து வடிவக் காட்சி"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "வீண் செய்யப்பட்ட நேரம்"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(அறியப்படாதது)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "பல APIகள் (பொதுவாக, லே-அவுட் வடிவியலை ஸ்கேன் செய்பவை) ஸ்டைல் மற்றும் லே-அவுட்டைக் கணக்கிடுவதற்காக ஸ்கிரிப்ட் செயல்படுத்தலை இடைநிறுத்தும்படி ரென்டரிங் இன்ஜினைக் கட்டாயப்படுத்துகின்றன. [கட்டாய ரீஃப்ளோ](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) மற்றும் அதன் மிட்டிகேஷன்கள் குறித்து மேலும் தெரிந்துகொள்ளுங்கள்."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "ஸ்டாக் டிரேஸ்"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "கட்டாய ரீஃப்ளோ"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "பிரதான ஃபங்ஷன் கால்"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "மொத்த மறுசீராக்க நேரம்"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[குறிப்பிடாதவை]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "படங்களின் பதிவிறக்க நேரத்தைக் குறைப்பதால் பக்கம் மற்றும் LCP ஏற்றப்படும் நேரம் மேம்படலாம். [பட அளவை மேம்படுத்துவது குறித்து மேலும் தெரிந்துகொள்ளுங்கள்](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (மதிப்பிடப்பட்ட அளவு: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "மேம்படுத்தக்கூடிய படங்கள் இல்லை"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ஃபைல் அளவை மேம்படுத்து"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "மேலும் {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "படம் காட்டப்படும் விதத்தை மேம்படுத்துதல்"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "படச் சுருக்கக் காரணியை அதிகரிப்பது இந்தப் படத்திற்கான பதிவிறக்க அளவை மேம்படுத்தக்கூடும்."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "நவீனப் பட வடிவமைப்பை (WebP, AVIF) பயன்படுத்துவது அல்லது படச் சுருக்குதலை அதிகரிப்பது படத்தின் பதிவிறக்க அளவை மேம்படுத்தக்கூடும்."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "காட்டப்படும் பரிமாணங்களை ({PH2}) விடவும் இந்தப் பட ஃபைல் ({PH1}) பெரிதாக உள்ளது. படத்தின் பதிவிறக்க அளவைக் குறைக்க, தானாகப் பொருந்தும் தன்மையுள்ள படங்களைப் பயன்படுத்தவும்."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFகளுக்குப் பதிலாக வீடியோ வடிவமைப்புகளைப் பயன்படுத்துவது அனிமேஷன் செய்யப்படும் உள்ளடக்கத்தின் பதிவிறக்க அளவை மேம்படுத்தும்."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "நீளமான நிலையில் இருந்து ஆய்வைத் தொடங்கவும். [தாமதங்களைக் குறைக்கலாம்](https://web.dev/articles/optimize-inp#optimize_interactions). செயலாக்கத்திற்கான கால அளவைக் குறைக்க, [முக்கியத் தொடரிழையின் (பெரும்பாலும் JS செயலாக்கங்கள்) செயல்திறன் பாதிப்புகளை](https://web.dev/articles/optimize-long-tasks) மேம்படுத்தவும்."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "கால அளவு"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "உள்ளீட்டுத் தாமதம்"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "இன்டராக்ஷன்கள் எதுவும் கண்டறியப்படவில்லை"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "நிலை"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "காட்சிப்படுத்தல் தாமதம்"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "செயலாக்க நேரம்"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "நிலையின்படி INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTMLலில் உள்ள LCP படத்தை உடனடியாகக் [கண்டறியப்படும்படி](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) அமைப்பது, ['தேவையுள்ளபோது காண்பித்தல்' அம்சத்தைத் தவிர்ப்பது](https://web.dev/articles/lcp-lazy-loading) ஆகியவற்றின் மூலம் LCPயை மேம்படுத்தலாம்"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high பயன்படுத்தப்பட்டுள்ளது"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high பயன்படுத்தப்பட வேண்டும்"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "தேவையுள்ளபோது காட்டுதல் பயன்படுத்தப்படவில்லை"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "ஆரம்பக்கட்டத் துவக்கப் புள்ளிக்குப் பிறகு {PH1} கழித்து LCP படம் காட்டப்பட்டது."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP எதுவும் கண்டறியப்படவில்லை"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP படம் இல்லை என்பதால் LCP ரிசோர்ஸ் எதுவும் கண்டறியப்படவில்லை"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "முதல் ஆவணத்தில் கோரிக்கையைக் கண்டறியலாம்"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP கோரிக்கையைக் கண்டறிதல்"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ஒவ்வொரு [நிலைக்கும் குறிப்பிட்ட மேம்பாட்டு உத்திகள் உள்ளன](https://web.dev/articles/optimize-lcp#lcp-breakdown). பொதுவாக, LCP நேரம் பெரும்பாலும் ரிசோர்ஸ்களை ஏற்றுவதற்காகச் செலவிடப்படவேண்டுமே தவிர தாமதங்களில் அல்ல."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "கால அளவு"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "எலிமெண்ட் ரென்டரிங் தாமதம்"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "புலத்தில் 75வது சதமானம்"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP எதுவும் கண்டறியப்படவில்லை"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "நிலை"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ரிசோர்ஸை ஏற்றுவதில் தாமதம்"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "ரிசோர்ஸ் ஏற்றப்படும் கால அளவு"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "முதல் பைட்டுக்கான நேரம்"}, "models/trace/insights/LCPPhases.ts | title": {"message": "நிலையின்படி LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "ஸ்கிரிப்ட்"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "வீண் செய்யப்பட்ட பைட்டுகள்"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "பழைய பிரவுசர்கள் புதிய JavaScript அம்சங்களைப் பயன்படுத்த பாலிஃபில்களும் ட்ரான்ஸ்ஃபார்ம்களும் உதவுகின்றன. நவீன பிரவுசர்களுக்கு இவற்றுள் பல தேவைப்படுவதில்லை. பழைய பிரவுசர்களைப் பயன்படுத்த வேண்டிய கட்டாயம் இல்லாதபோது, [பேஸ்லைன்](https://web.dev/articles/baseline-and-polyfills) அம்சங்களை டிரான்ஸ்பைல் செய்யாமல் இருக்கும்படி உங்கள் JavaScript பதிப்புச் செயல்முறையை மாற்றுவது நல்லது. [பெரும்பாலான தளங்கள் ES6+ குறியீட்டை ஏன் டிரான்ஸ்பைல் செய்யாமல் செயல்படுத்த முடியும் என்று தெரிந்துகொள்ளுங்கள்](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "பழைய JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2, HTTP/3 ஆகியவை HTTP/1.1ஐ விடக் கூடுதல் பலன்களை வழங்குகின்றன. உதாரணம்: மல்டிப்ளக்ஸிங். [நவீன HTTPயைப் பயன்படுத்துவது குறித்து மேலும் தெரிந்துகொள்ளுங்கள்](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "எந்தவொரு கோரிக்கையும் HTTP/1.1 பயன்படுத்தவில்லை"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "நெறிமுறை"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "கோரிக்கை"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "நவீன HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ஆரிஜின்"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "கோரிக்கை"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ஆரிஜின்"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "நேரம்"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "கணக்கிடப்பட்ட LCP சேமிப்புகள்"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "பயன்படுத்தப்படாத முன்னிணைப்பு. crossorigin பண்புக்கூறு சரியாகப் பயன்படுத்தப்பட்டிருப்பதை உறுதிசெய்துகொள்ளவும்."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "செயின்களின் நீளத்தைக் குறைத்தல், ஆதாரங்களின் பதிவிறக்க அளவைக் குறைத்தல், பக்கம் ஏற்றப்படுவதன் வேகத்தை அதிகரிக்க தேவையற்ற ஆதாரங்களைப் பதிவிறக்குவதைத் தவிர்த்தல் போன்றவற்றின் மூலம் [முக்கியக் கோரிக்கைகளின் செயினிங்கைத் தவிர்க்கலாம்](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "உங்களின் மிகவும் முக்கியமான ஆரிஜின்களுடன் [முன்னிணைப்புக்](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) குறிப்புகளைச் சேர்க்கவும், ஆனால் 4க்கும் அதிகமாகப் பயன்படுத்தவும்."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "முன்னிணைப்புக்கான ஆரிஜின்கள்"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "முக்கியக் கோரிக்கைத் தடத்தின் அதிகபட்சத் தாமதம்:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "நெட்வொர்க் சார்புகளால் ரென்டரிங் பணிகளில் எந்த மாற்றமும் இல்லை"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "கூடுதல் ஆரிஜின்கள் எதுவும் முன்கூட்டியே இணைப்பதற்குத் தகுதிபெறவில்லை"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ஆரிஜின்கள் எதுவும் முன்கூட்டியே இணைக்கப்படவில்லை"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "இணையப் பக்கம் ஏற்றப்படும்போதே பிரவுசர் இணைப்பை ஏற்படுத்த [முன்னிணைப்புக் குறிப்புகள்](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) உதவுகின்றன. ஆரிஜினுக்கான முதல் கோரிக்கை செயலாக்கப்படும்போது இது நேரத்தைச் சேமிக்கும். இணையப் பக்கம் இந்த ஆரிஜின்களுடன் முன்கூட்டியே இணைத்துள்ளது."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "முன்னிணைக்கப்பட்ட ஆரிஜின்கள்"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "நெட்வொர்க் சார்பு ட்ரீ"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4க்கும் மேற்பட்ட preconnect இணைப்புகள் உள்ளன. இவற்றைக் குறைவாகவும் மிகவும் முக்கியமான ஆரிஜின்களை இணைப்பதற்கும் மட்டுமே பயன்படுத்த வேண்டும்."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "பயன்படுத்தப்படாத முன்னிணைப்பு. இணையப் பக்கம் கேட்க வாய்ப்புள்ள ஆரிஜின்களுக்கு மட்டுமே preconnect ஐப் பயன்படுத்தவும்."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "செயின்களின் நீளத்தைக் குறைத்தல், ஆதாரங்களின் பதிவிறக்க அளவைக் குறைத்தல், பக்கம் ஏற்றப்படுவதன் வேகத்தை அதிகரிக்க தேவையற்ற ஆதாரங்களைப் பதிவிறக்குவதைத் தவிர்த்தல் போன்றவற்றின் மூலம் முக்கியக் கோரிக்கைகளின் செயினிங்கைத் தவிர்க்கலாம்."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "பக்கத்தின் துவக்க ரென்டரைத் தடுக்கும் கோரிக்கைகள் LCPயைத் தாமதப்படுத்தக்கூடும். [தவிர்ப்பது அல்லது இன்லைனில்](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) வழங்குவதன் மூலம் இந்த நெட்வொர்க் கோரிக்கைகளை முக்கியக் கோரிக்கைத் தடத்தில் இருந்து வெளியேற்றலாம்."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "கால அளவு"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "இந்த வழிசெலுத்தலுக்கு ரென்டரிங்கைத் தடுக்கும் கோரிக்கை எதுவும் இல்லை"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "கோரிக்கை"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "தடுப்புக் கோரிக்கைகளை ரென்டரிங் செய்தல்"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "ஸ்டைலை மீண்டும் கணக்கிடுதலின் செயல்திறன் பாதிப்பு அதிகமாக இருந்தால், தேர்விகளை மேம்படுத்தி அதைக் குறைக்கலாம். அதிக நேரம் எடுத்துக்கொள்ளும் தேர்விகளையும் மெதுவான தடத்தின் பொருத்தச் சதவீதம் அதிகமாக உள்ள [தேர்விகளையும் மேம்படுத்தலாம்](https://developer.chrome.com/docs/devtools/performance/selector-stats). எளிமையான தேர்விகள், குறைவான தேர்விகள், சிறிய DOM, அடுக்குகள் குறைவான DOM ஆகியவை பொருத்தச் செயல்திறனின் பாதிப்புகளைக் குறைக்கும்."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "எடுத்துக்கொண்ட நேரம்"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS தேர்வி தரவு எதுவும் கண்டறியப்படவில்லை. செயல்திறன் பேனல் அமைப்புகளில் CSS தேர்வி புள்ளிவிவரங்கள் இயக்கப்பட வேண்டும்."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "பொருந்தும் முயற்சிகள்"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "பொருந்தும் எண்ணிக்கை"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS தேர்விக்கான செயல்திறன் பாதிப்பு"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "முதன்மைத் தேர்விகள்"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "மொத்தம்"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "முக்கிய த்ரெடுக்கான நேரம்"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "மூன்றாம் தரப்பு"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "டிரான்ஸ்ஃபர் அளவு"}, "models/trace/insights/ThirdParties.ts | description": {"message": "மூன்றாம் தரப்புக் குறியீட்டால் ஏற்றுதல் செயல்திறனில் குறிப்பிடத்தக்க மாற்றம் ஏற்படலாம். உங்கள் பக்கத்தின் உள்ளடக்கத்திற்கு முன்னுரிமை வழங்க, [மூன்றாம் தரப்புக் குறியீடு ஏற்றப்படுவதைக் குறைக்கலாம் தாமதப்படுத்தலாம்](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "எந்தவொரு மூன்றாம் தரப்பும் கண்டறியப்படவில்லை"}, "models/trace/insights/ThirdParties.ts | title": {"message": "மூன்றாம் தரப்புகள்"}, "models/trace/insights/Viewport.ts | description": {"message": "காட்சிப் பகுதி மொபைலுக்கு ஏற்றவாறு மேம்படுத்தப்படவில்லை என்றால் தட்டுதல் செயல்பாடுகள் [300 மிவி வரை தாமதம் ஆகக்கூடும்](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "மொபைல் தட்டல் தாமதம்"}, "models/trace/insights/Viewport.ts | title": {"message": "காட்சிப் பகுதியை மொபைலுக்கு ஏற்றதாக மேம்படுத்துதல்"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET கோரிக்கை மூலம் ஏற்றப்பட்ட பக்கங்களை மட்டுமே ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியும்."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX நிலைக் குறியீட்டுடன் இருக்கும் பக்கங்களை மட்டுமே தற்காலிகமாகச் சேமிக்க முடியும்."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "பக்கம் தற்காலிகச் சேமிப்பில் சேமிக்கப்பட்டிருக்கும்போது JavaScriptடைச் செயல்படுத்துவதற்கு முயற்சி செய்யப்பட்டதாக Chrome கண்டறிந்துள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBannerரைக் கோரிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் கொடிகள் மூலம் முடக்கப்பட்டுள்ளது. சாதனத்தில் இதை இயக்க, chrome://flags/#back-forward-cache என்ற பக்கத்திற்குச் செல்லவும்."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "கட்டளை மூலம் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "போதுமான நினைவகம் இல்லாததால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தை உட்பொதிவில் பயன்படுத்த முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "முன்கூட்டியே ரென்டர் செய்யப்படுவதற்காக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "பதிவுசெய்யப்பட்ட லிசனர்களுடன் கூடிய BroadcastChannel நேர்வைப் பக்கம் கொண்டுள்ளதால் அதைத் தற்காலிகமாகச் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "‘cache-control:no-store’ என்ற தலைப்புடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "தற்காலிகச் சேமிப்பு வேண்டுமென்றே அழிக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "மற்றொரு பக்கத்தைத் தற்காலிகமாகச் சேமிப்பதற்காக இந்தப் பக்கம் தற்காலிகச் சேமிப்பில் இருந்து அகற்றப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "செருகுநிரல்களைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "வரையறுக்கப்படவில்லை"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcherரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "வெளியேறும்போது மீடியா பிளேயர் பிளேயாகிக் கொண்டிருந்தது."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession APIயைப் பயன்படுத்தி பிளேபேக் நிலையை அமைக்கும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession APIயைப் பயன்படுத்தி செயல்பாட்டு ஹேண்ட்லர்களை அமைக்கும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "ஸ்கிரீன் ரீடர் காரணமான 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandlerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthentication APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB APIயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store ஐப் பயன்படுத்தும் பக்கத்தில் குக்கீகள் முடக்கப்பட்டுள்ளதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "பிரத்தியேக worker/workletடைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "வெளியேறுவதற்கு முன் ஆவணம் ஏற்றப்படவில்லை."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "வெளியேறும்போது ஆப்ஸ் பேனர் காட்டப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "வெளியேறும்போது Chrome கடவுச்சொல் நிர்வாகி திறந்திருந்தது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "வெளியேறும்போது DOM டிஸ்டிலேஷன் நடைபெற்றுக் கொண்டிருந்தன."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "வெளியேறும்போது DOM டிஸ்டில்லர் வியூவர் திறந்திருந்தது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "மெசேஜிங் APIஐ நீட்டிப்புகள் பயன்படுத்தியதால் 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சத்தைப் பயன்படுத்துவதற்கு முன்பு, நீண்ட கால இணைப்பைக் கொண்டிருக்கும் நீட்டிப்புகள் அவற்றின் இணைப்பைத் துண்டிக்க வேண்டும்."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "நீண்ட கால இணைப்பைக் கொண்டிருக்கும் நீட்டிப்புகள், 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சத்தில் ஃபிரேம்களுக்கு மெசேஜ் அனுப்ப முயன்றன."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "நீட்டிப்புகள் காரணமாக, 'முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்' அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "வெளியேறும்போது பக்கத்திற்கான படிவ மறுசமர்ப்பிப்பு, HTTP கடவுச்சொல் உரையாடல் போன்ற மோடல் உரையாடல் காட்டப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "வெளியேறும்போது ஆஃப்லைன் பக்கம் காட்டப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "வெளியேறும்போது நினைவகப் பற்றாக்குறை இடையீட்டுப் பட்டி திறந்திருந்தது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "வெளியேறும்போது அனுமதிக் கோரிக்கைகள் இருந்தன."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "வெளியேறும்போது பாப்-அப் தடுப்பான் இயங்கிக் கொண்டிருந்தது."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "வெளியேறும்போது பாதுகாப்பு உலாவல் விவரங்கள் காட்டப்பட்டன."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "இந்தப் பக்கம் தவறான பயன்பாட்டில் ஈடுபடக்கூடியது என்பதைப் ‘பாதுகாப்பு உலாவல்’ அறிந்து, பாப்-அப்பைத் தடுத்துள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் சேமிக்கப்பட்டிருந்தபோது service worker இயக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ஆவணப் பிழையின் காரணமாக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFramesஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "மற்றொரு பக்கத்தைத் தற்காலிகமாகச் சேமிப்பதற்காக இந்தப் பக்கம் தற்காலிகச் சேமிப்பில் இருந்து அகற்றப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "மீடியா ஸ்ட்ரீம் அணுகலை வழங்கியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "குறிப்பிட்ட வகையிலான உள்ளமைக்கப்பட்ட உள்ளடக்கம் (எ.கா. PDFகள்) இருக்கும் பக்கங்கள், முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல் அம்சத்திற்குத் தற்சமயம் தகுதிபெறாது."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManagerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "பொது IndexedDB இணைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB நிகழ்வின் காரணமாக ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "பொருத்தமற்ற APIகள் பயன்படுத்தப்பட்டுள்ளன."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "நீட்டிப்புகள் மூலம் JavaScript உள்ளிடப்பட்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "நீட்டிப்புகள் மூலம் StyleSheet உள்ளிடப்பட்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "அகப் பிழை."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "சில JavaScript நெட்வொர்க் கோரிக்கைகள் Cache-Control: no-store தலைப்புடன் ஆதாரங்களைப் பெற்றதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "கீப்-அலைவ் கோரிக்கையின் காரணமாக முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல் அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "கீபோர்டு லாக்கைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "வெளியேறுவதற்கு முன் பக்கம் ஏற்றப்படவில்லை."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "முதன்மை ஆதாரத்தில் ‘cache-control:no-cache’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "முதன்மை ஆதாரத்தில் ‘cache-control:no-store’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தின் மூலம் பக்கம் மீட்டெடுக்கப்படுவதற்கு முன் வழிசெலுத்துதல் ரத்துசெய்யப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "செயலில் உள்ள நெட்வொர்க் இணைப்பு அதிகளவு டேட்டாவைப் பெற்றதால் தற்காலிகச் சேமிப்பில் இருந்து பக்கம் அகற்றப்பட்டது. பக்கம் தற்காலிகமாகச் சேமிக்கப்படும்போது அது எவ்வளவு நேரம் டேட்டாவைப் பெறலாம் என்பதை Chrome கட்டுப்படுத்தும்."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() அல்லது XHR நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "செயலில் உள்ள நெட்வொர்க் கோரிக்கை திசைதிருப்பப்பட்டதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து பக்கம் அகற்றப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "நெட்வொர்க் இணைப்பு நீண்ட நேரம் திறந்திருந்ததால் தற்காலிகச் சேமிப்பில் இருந்து பக்கம் அகற்றப்பட்டது. பக்கம் தற்காலிகமாகச் சேமிக்கப்படும்போது அது எவ்வளவு நேரம் டேட்டாவைப் பெறலாம் என்பதை Chrome கட்டுப்படுத்தும்."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "சரியான ‘பதிலளிப்புத் தலைப்பு’ இல்லாத பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "முதன்மை ஃபிரேமில் அல்லாமல் வேறு ஃபிரேமில் வழிசெலுத்துதல் மேற்கொள்ளப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "செயலில் உள்ள indexed DB பரிமாற்றங்களைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "fetch() நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR நெட்வொர்க் கோரிக்கை நிலுவையில் உள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManagerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "பிக்ச்சர்-இன்-பிக்ச்சர் அம்சத்தைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Printing UIயைக் காட்டும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "‘window.open()’ செயல்பாட்டைப் பயன்படுத்தி பக்கம் திறக்கப்பட்டதுடன் அதற்கான குறிப்பை மற்றொரு உலாவிப் பக்கம் கொண்டுள்ளது அல்லது சாளரத்தைப் பக்கம் திறந்துள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் உள்ள பக்கத்திற்கான ரென்டரிங் செயலாக்கம் சிதைந்துவிட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் உள்ள பக்கத்திற்கான ரென்டரிங் செயலாக்கம் நிறுத்தப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ஆடியோவை ரெக்கார்டு செய்வதற்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "சென்சார் அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "பின்னணி ஒத்திசைவு அல்லது பெறுதல் அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "அறிவிப்புகளுக்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "சேமிப்பக அணுகலைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "வீடியோவை ரெக்கார்டு செய்வதற்கான அனுமதிகளைக் கோரியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPS என்ற URL ஸ்கீமைக் கொண்ட பக்கங்களை மட்டுமே தற்காலிகமாகச் சேமிக்க முடியும்."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் இருக்கும்போது service worker மூலம் அது கோரப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்கப்பட்டுள்ள பக்கத்திற்கு MessageEvent பண்பை அனுப்ப service worker முயற்சி செய்துள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கம் இருந்தபோது ServiceWorker பதிவுநீக்கம் செய்யப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "service worker இயக்கப்பட்டதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் இருந்து பக்கம் அகற்றப்பட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome மீண்டும் தொடங்கப்பட்டு ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தின் உள்ளீடுகள் அழிக்கப்பட்டன."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorkerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizerரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesisஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "பக்கத்தில் உள்ள ஒரு iframe தொடங்கிய வழிசெலுத்துதல் நிறைவடையவில்லை."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "துணை ஆதாரத்தில் ‘cache-control:no-cache’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "துணை ஆதாரத்தில் ‘cache-control:no-store’ என்ற தலைப்பைக் கொண்ட பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமித்து வைப்பதற்கான அதிகபட்ச நேரத்தை இந்தப் பக்கம் தாண்டிவிட்டதால் அது காலாவதியாகிவிட்டது."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் பக்கத்தைச் சேர்ப்பதற்கான நேரம் முடிந்துவிட்டது (நீண்ட நேரம் செயல்படும் pagehide ஹேண்ட்லர்கள் காரணமாக இவ்வாறு நடந்திருக்கலாம்)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "பக்கத்தின் முதன்மை ஃபிரேமில் அகற்றுதல் ஹேண்ட்லர் உள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "பக்கத்தின் துணை ஃபிரேமில் அகற்றுதல் ஹேண்ட்லர் உள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "யூசர் ஏஜெண்ட் ஓவர்-ரைடு தலைப்பை உலாவி மாற்றியுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "வீடியோ/ஆடியோவை ரெக்கார்டு செய்வதற்கான அணுகலை வழங்கியுள்ள பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabaseஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHIDயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocksஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfcயைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPServiceஸைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTCயுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC பயன்படுத்தப்பட்டுள்ளதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShareரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocketடுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேர்க்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket பயன்படுத்தப்பட்டுள்ளதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransportடுடன் கூடிய பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் சேமிக்க முடியாது."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport பயன்படுத்தப்பட்டுள்ளதால் ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சம் முடக்கப்பட்டுள்ளது."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXRரைப் பயன்படுத்தும் பக்கங்களை ‘முன் பின் பக்கங்களைத் தற்காலிகமாகச் சேமித்தல்’ அம்சத்தில் தற்போது சேமிக்க முடியாது."}}