{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers işlemlerinde kimlik doğ<PERSON>lam<PERSON>, joker karakter (*) simgesinin kapsamına girmeyecektir."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Varsayılan Cast entegrasyonunu devre dışı bırakmak için -internal-media-controls-overlay-cast-button yerine disableRemotePlayback özelliği kullanılmalıdır."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS görünüm değeri slider-vertical standart hale getirilmemiştir ve kaldırılacaktır."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "URL'lerinde hem kaldırılan boşluk \\(n|r|t) karakterleri hem küçüktür karakterleri (<) bulunan kaynak istekleri engellenir. Bu kaynakları yüklemek için lütfen yeni satırları kaldırıp öğe özellik değeri gibi yerlerdeki küçüktür karakterlerini kodlayın."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() kullanımdan kaldırıldı. Onun yerine standart API: Navigation Timing 2'yi kullanın."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() kullanımdan kaldırıldı. Lütfen standart API: Paint Timing'i kullanın."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() kullanımdan kaldırıldı. Onun yerine Navigation Timing 2'de standart API: nextHopProtocol kullanın."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) karakteri içeren çerezler kısaltılmayıp reddedilecektir."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain ayarlayarak aynı kaynak politikasını esnetme işlevi kullanımdan kaldırılmış olup varsayılan olarak devre dışı olacaktır. Bu kullanımdan kaldırma uyarısı, document.domain ayarlamak suretiyle izin verilen bir çapraz kaynak erişimine yöneliktir."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Çapraz kaynak iframe'lerden window.alert tetikleme işlevinin desteği sonlandırılmış olup ileride kaldırılacaktır."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Çapraz kaynak iframe'lerden window.confirm tetikleme desteği sonlandırılmış olup ileride kaldırılacaktır."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Veri desteği: SVGUseElement öğesindeki URL'lerin desteği sonlandırılmıştır ve gelecekte kaldırılacaktır."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() ve watchPosition() güvenilir olmayan kaynaklarda artık çalışmıyor. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() ve watchPosition() güvenli olmayan kaynaklarda kullanımdan kaldırıldı. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() güvenilir olmayan kaynaklarda artık çalışmıyor. Bu özelliği kullanmak için uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Bir <article>, <aside>, <nav> veya <section> içinde yer alan ve yazı tipi boyutu belirtilmeyen <h1> etiketi bulundu. Bu başlık metninin boyutu yakında bu tarayıcıda değişecek. Daha fazla bilgi için https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 adresine göz atın."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate kullanımdan kaldırıldı. Yerine lütfen RTCPeerConnectionIceErrorEvent.address veya RTCPeerConnectionIceErrorEvent.port kullanın."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Dijital kimlik bilgileri için navigator.credentials.get() isteğinin bu biçimi için destek sonlandırıldı. Lütfen isteğinizi güncelleyerek yeni biçime geçirin."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment hizmet çalışanı etkinliğindeki satıcı kaynağı ve rastgele veriler kullanımdan kaldırılacak ve mevcut olmayacak: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Web sitesi, kullanıcılarının ayrıcalıklı ağ konumu sayesinde erişebildiği bir ağdan alt kaynak istedi. Bu istekler herkese açık olmayan cihazların ve sunucuların internette açığa çıkmasına neden olarak, siteler arası istek sahtekarlığı (CSRF) saldırısı ve/veya bilgi sızıntısı riskini artırır. Bu riskleri azaltmak amacıyla Chrome, gü<PERSON>li olmayan bağlamlardan herkese açık olmayan alt kaynaklara gönderilen istekleri kullanımdan kaldıracak ve engellemeye başlayacaktır."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups adlı yapıdan joinAdInterestGroup() adlı yapıya iletilen dailyUpdateUrl alanı, davranışını daha doğru şekilde yansıtmak için updateUrl olarak yeniden adlandırıldı."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator desteği sonlandırıldı. Lütfen bunun yerine Intl.Segmenter seçeneğini kullanın."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css dosya uzantısıyla bitmeyen file: URL'lerinden CSS yüklenemiyor."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Spesifikasyon değişimi nedeniyle artık remove() eşzamansız aralık kaldırma işlemini iptal etmek için SourceBuffer.abort() kullanılmamaktadır. Bu işlevle ilgili destek ileride kaldırılacaktır. Bunun yerine updateend etkinliğini dinlemeniz gerekir. abort() yalnızca eşzamansız medya eklerini iptal etmek veya ayrıştırıcı durumunu sıfırlamak için kullanılmalıdır."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Spesifikasyon değişikliği neden<PERSON>, arabellekte kodlanan çerçevelerdeki en yüksek sunum zaman damgasından düşük bir MediaSource.duration değeri ayarlama özelliği kullanımdan kaldırılmıştır. Arabellekteki kırpılmış medya öğelerinin dolaylı yoldan kaldırılması ileride desteklenmeyecektir. Onun yerine, newDuration < oldDuration olan durumlarda her sourceBuffers üzerinde açık remove(newDuration, oldDuration) uygulamanız gerekir."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Sysex, MIDIOptions içinde belirtilmiş olmasa bile Web MIDI, sysex kullanmak için izin ister."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Artık Notification API güvenli olmayan kaynaklardan kullanılamaz. Uygulamanızı HTTPS gibi güvenli bir kaynağa geçirmenizi öneririz. Daha fazla bilgi için https://goo.gle/chrome-insecure-origins adresine göz atın."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Artık çapraz kaynak iframe'lerden Notification API izni istenemez. Bunun yerine izni üst düzey bir çerçeveden istemeniz veya yeni bir pencere açmanız gerekir."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap içindeki imageOrientation: 'none' seçeneğinin desteği sonlandırıldı. <PERSON><PERSON><PERSON> yer<PERSON>, createImageBitmap'i lütfen \\{imageOrientation: \"from-image\"\\} seçeneği ile kullanın."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "İş ortağınızın anlaşma yaptığı (D)TLS sürümü artık kullanılmıyor. Lütfen iş ortağınızla görüşüp bu durumu dü<PERSON>."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video ve canvas etiketlerinde overflow: visible be<PERSON><PERSON><PERSON><PERSON><PERSON>, ö<PERSON><PERSON> sınırlarının dışında görsel içerik oluşturulmasına neden olabilir. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md sayfasını inceleyin."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments kullanımdan kaldırıldı. <PERSON><PERSON><PERSON> yerine, ödeme işleyiciler için lütfen tam zamanında yükleme özelliğini kullanın."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest çağrınız, İçerik Güvenliği Politikası (İGP) connect-src yönergesini atladı. Bu atlama işleminin desteği sonlandırılmıştır. Lütfen PaymentRequest API'sindeki (supportedMethods alanındaki) ödeme yöntemi tanımlayıcısını İGP connect-src yönergenize ekleyin."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent kull<PERSON><PERSON><PERSON><PERSON> kaldırıldı. Lütfen bunun yerine standart navigator.storage kullanın."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Üst öğesi <picture> olan <source src> öğeleri geçersiz olduğundan yoksayılır. Lütfen bunun yerine <source srcset> politikasını kullanın."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame, satıcıya özgü bir işlevdir. Lütfen bunun yerine standart cancelAnimationFrame işlevini kullanın."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame, satıcıya özgü bir işlevdir. Lütfen bunun yerine standart requestAnimationFrame işlevini kullanın."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen desteği sonlandırıldı. Lütfen bunun yerine Document.fullscreenElement kullanın."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() desteği sonlandırıldı. Lütfen bunun yerine Element.requestFullscreen() kullanın."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() desteği sonlandırıldı. Lütfen bunun yerine Element.requestFullscreen() kullanın."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() desteği sonlandırıldı. Lütfen bunun yerine Document.exitFullscreen() kullanın."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() desteği sonlandırıldı. Lütfen bunun yerine Document.exitFullscreen() kullanın."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen desteği sonlandırıldı. Lütfen bunun yerine Document.fullscreenEnabled kullanın."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "chrome.privacy.websites.privacySandboxEnabled API'sinin desteğini sonlandırı<PERSON>z, ancak geriye dönük uyumluluk için M113 sürümüne kadar etkin kalacaktır. Bunun yerine lütfen chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled ve chrome.privacy.websites.adMeasurementEnabled kullanın. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled adresini ziyaret edin."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement kısıtlaması kaldırıldı. Bu kısıtlama için belirtti<PERSON> false değeri, kaldırılan SDES key negotiation yöntemini kullanmaya yönelik bir deneme olarak yorumlandı. Bu işlev kaldırılmıştır; onun yerine DTLS key negotiation destekleyen bir hizmet kullanın."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement kısıtlaması kaldırıldı. Bu kısıtlama için belirttiğiniz true değeri etkisiz olmuştur ancak düzenli olmak adına bu kısıtlamayı kaldırabilirsiniz."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Geri çağırmaya dayalı getStats() desteği sonlandırılmış olup ileride kaldırılacaktır. Bunun yerine spesifikasyonla uyumlu getStats() kullanın."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() desteği sonlandırıldı. Lütfen bunun yerine Selection.modify() kullanın."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL'lerinde <PERSON>rilmiş kimlik bilgileri (ör. **********************/) olan alt kaynak istekleri engellenir."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy seçeneği artık kullanılmıyor olup kaldırılacaktır."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> a<PERSON>ı eri<PERSON><PERSON> ka<PERSON>ılma<PERSON>ını gerektiriyor. Daha fazla bilgi için https://developer.chrome.com/blog/enabling-shared-array-buffer/ adresini ziyaret edin."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Kullanıcı etkinliği olmadan speechSynthesis.speak() çağırma işlevi artık kullanılmıyor olup kaldırılacaktır."}, "generated/Deprecation.ts | UnloadHandler": {"message": "<PERSON><PERSON><PERSON>rma etkinliği işleyicileri desteği sonlandırılmıştır ve kaldırılacaktır."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Uzantıların SharedArrayBuffer kullanmayı sürdürmek için kökler arası erişimi kapatmaları gerekir. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ adresini ziyaret edin."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter özelliğinin desteği sonlandırıldı. Bunun yerine GPUAdapterInfo isFallbackAdapter özelliğini kullanın."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest içindeki yanıt JSON dosyası UTF-16'yı desteklemiyor."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Son kullanıcı deneyimini o<PERSON>uz etkilem<PERSON> neden<PERSON>, ana iş par<PERSON>ığında eşzamanlı XMLHttpRequest işleviyle ilgili destek sonlandırıldı. Daha fazla yardım için https://xhr.spec.whatwg.org/ adresini ziyaret edin."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animasyon"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "<PERSON><PERSON><PERSON> kaymaları, <PERSON><PERSON><PERSON><PERSON> herhangi bir kullanıcı etkileşimi olmadan hareket ettiğinde gerçekleşir. [<PERSON><PERSON><PERSON><PERSON> e<PERSON>, kaldırılması veya sayfa yüklenirken yazı tiplerinin değişmesi gibi düzen kaymalarının nedenlerini araştırın](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Yazı tipi isteği"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Yerleştirilen iFrame"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Düzen kayması kümesi @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Düzen kayması sorununun nedenleri tespit edilemedi"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Düzen kayması yok"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Düzen kayması sorununun nedenleri"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Düzen kayması sorununun en önemli nedenleri"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "En kötü küme"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "En kötü düzen kayması kümesi"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL'yi Ö<PERSON>belleğe Al"}, "models/trace/insights/Cache.ts | description": {"message": "Uzun önbellek ömrü, say<PERSON><PERSON><PERSON><PERSON>n tekrar ziyaret edilmesi sürecini hızlandırabilir. [Daha fazla bilgi edinin](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Verimsiz önbellek politikalarını gerektiren istek yok"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} tane daha"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "İstek"}, "models/trace/insights/Cache.ts | title": {"message": "Verimli önbellek sürelerini kullanın"}, "models/trace/insights/DOMSize.ts | description": {"message": "Büyük bir DOM, stil hesaplamalarının ve yeniden düzenlemelerin daha uzun sürmesine neden olarak sayfanın yanıt verme hızını etkileyebilir. Ayrıca bellek kullanımını da artırır. [DOM'un aşırı büyümesini nasıl önleyeceğinizi öğrenin](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Maks<PERSON>um alt öğe sayısı"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM derinliği"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "İstatistik"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM boyutunu optimize edin"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Toplam öğe sayısı"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "İlk ağ isteğiniz en önemli isteğinizdir.  Yönlendirmelerden kaçınarak, hızlı sunucu yanıtı sağlayarak ve metin sıkıştırmayı etkinleştirerek gecikmeyi azaltın."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Yönlendirmeler vardı ({PH1} yönlendirme, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON><PERSON> yavaş yanıt verdi ({PH1} gözlemlendi)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Sıkıştırma uygulanmadı"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Yönlendirme içermiyor"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON>u hızlı yanıt veriyor ({PH1} gözlemlendi)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON><PERSON> yanıt s<PERSON>"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Doküman isteğiyle il<PERSON> gec<PERSON>"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Sıkıştırılmamış indirme işlemi"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON><PERSON>ğ<PERSON>n kullandığı bayt sayısını azaltmak için paketlerden büyük, yinelenen JavaScript modüllerini kaldırın."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Yinelenen JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "<PERSON><PERSON> her zaman gör<PERSON>n<PERSON>ini sağlamak için [font-display](https://developer.chrome.com/blog/font-display) özelliğini swap veya optional olarak ayarlayabilirsiniz. swap, [yazı tipi metriği geçersiz kılmalarıyla](https://developer.chrome.com/blog/font-fallbacks) düzen kaymalarını azaltmak için daha fazla optimize edilebilir."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Yazı tipi"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Yazı tipi görüntüleme"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON><PERSON> ha<PERSON> s<PERSON>re"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonim)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Genellikle düzen geometrisini okuyan pek çok API, stil ve düzeni hesaplamak için oluşturma motorunu komut dosyasının yürütülmesini duraklatmaya zorlar. [Zorunlu yeniden düzenleme](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) ve çözümleri hakkında daha fazla bilgi edinin."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (stack trace)"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Zorunlu yeniden düzenleme"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "En çok zaman alan i<PERSON> ç<PERSON>ğrısı"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Toplam yeniden düzenleme sü<PERSON>i"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[iliş<PERSON><PERSON>dirilmemiş]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Resimlerin indirme süresini kı<PERSON>tmak, say<PERSON><PERSON><PERSON> al<PERSON>lanan yükleme süresini ve LCP'yi daha iyi hale getirebilir. [Resim boyutunu optimize etme hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON><PERSON> {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Optimize edilebilir resim yok"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON><PERSON> optimize edin"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} tane daha"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Resim yayınlamayı kolaylaştırın"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "<PERSON>si<PERSON> sı<PERSON>ış<PERSON><PERSON>rma faktörünü artırarak bu resmin indirme boyutunu küçültebilirsiniz."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Modern bir resim biçimi (WebP, AVIF) kullanmak veya resim sıkıştırmasını artırmak bu resmin indirme boyutunu küçültebilir."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Bu resim dos<PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boyutlarına ({PH2}) göre olması gerekenden ({PH1}) daha büyük. Resim indirme boyutunu küçültmek için duyarlı resimler kullanın."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF yerine video biçimleri kullanmak, animasyonlu içeriğin indirme boyutunu küçültebilir."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "En uzun aşamayla incelemeye başlayın. [Gecikmeler en aza indirilebilir](https://web.dev/articles/optimize-inp#optimize_interactions). İşleme süresini kısaltmak için genellikle JS'den oluşan [ana i<PERSON>ığı maliyetlerini optimize edin](https://web.dev/articles/optimize-long-tasks)."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Aşama"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON> g<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "İşleme <PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Aşama bazında INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP resmini HTML'den anında [bulunabilir](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) hale getirerek ve [geç yüklemeden](https://web.dev/articles/lcp-lazy-loading) kaçınarak LCP'yi optimize edin"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high uygulandı"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high uygulanmalıdır"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "geç yü<PERSON>me uygulanmadı"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP resmi, en erken başlangıç noktasından {PH1} sonra yüklendi."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP algılanmadı"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP bir resim olmadığı için LCP kaynağı algılanmadı"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "İstek, ilk dokümanda bulunabilir"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP istek keşfi"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Her [a<PERSON><PERSON> i<PERSON>in belirli iyileştirme stratejileri vardır](https://web.dev/articles/optimize-lcp#lcp-breakdown). İdeal olarak LCP süresinin çoğu gecikmeler iç<PERSON>, kaynakları yüklemek için harcanmalıdır."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Öğe oluşturma gecikmesi"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75. <PERSON><PERSON><PERSON><PERSON><PERSON> di<PERSON> al<PERSON>ı"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP algılanmadı"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Aşama"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Aşama bazında LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Boşa harcanan bayt miktarı"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill'ler ve <PERSON><PERSON><PERSON><PERSON>, daha eski tarayıcıların yeni JavaScript özelliklerini kullanmasını sağlar. Ancak, çoğu modern tarayıcılar için gerekli değildir. Daha eski tarayıcıları desteklemeniz gerekmediğinden eminseniz JavaScript derleme işleminizi [Baseline](https://web.dev/articles/baseline-and-polyfills) özelliklerini çevirecek şekilde değiştirmeyi düşünün. [Çoğu sitenin neden ES6+ kodunu çevirmeden dağıtabildiğini öğrenin](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Eski JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 ve HTTP/3, HTTP/1.1'e kıyasla multiplex gibi birçok avantaj sunar. [Modern HTTP'yi kullanma hakkında daha fazla bilgi edinin](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Hiçbir istekte HTTP/1.1 kullanılmadı"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "İstek"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Modern HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "İstek"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Zaman"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Ta<PERSON>ini LCP tasarrufu"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Kullanılmayan ön bağlantı. crossorigin özelliğinin doğru şekilde kullanıldığını kontrol edin."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "Sayfaların daha kolay yüklenmesi için zincir uzunluğunu azaltarak, kaynakların indirme boyutunu küçülterek veya gereksiz kaynakların indirilmesini erteleyerek [kritik istek zinciri oluşturmaktan kaçının](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "En önemli kaynaklarınıza [ö<PERSON><PERSON> bağlanma](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ipuçları ekleyin ancak 4'ten az ipucu kullanmaya çalışın."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Önceden bağlanma ad<PERSON>ı"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON><PERSON><PERSON> kritik yol gecikmesi:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "<PERSON><PERSON> bağımlılıklarından etkilenen oluşturma görevi yok"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Ön bağlantı için uygun ek kaynak bulunmamaktadır"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "hiçbir kaynak önceden bağlanmadı"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[Önceden bağlanma](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ipuçları, sayfa yüklenirken tarayıcının gerekli bağlantıları önceden kurmasına yardımcı olarak ilgili kaynağa yapılacak ilk istekte zaman kazandırır. Sayfanın önceden bağlantı kurduğu kaynaklar aşağıda verilmiştir."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Önceden bağlanmış kaynaklar"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON> bağımlılık ağacı"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4'ten fazla preconnect bağlantısı bulundu. Bunlar az kullanılıp sadece en önemli kaynaklar için tercih edilmelidir."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Kullanılmayan ön bağlantı. Yalnızca sayfanın isteyebileceği kaynaklar için preconnect öğesini kullanın."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Sayfaların daha kolay yüklenmesi için zincir uzunluğunu azaltarak, kaynakların indirme boyutunu küçülterek veya gereksiz kaynakların indirilmesini erteleyerek kritik istek zinciri oluşturmaktan kaçının."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "İstekler sayfanın ilk oluşturma işlemini engelliyor. Bu durum, LCP'yi geciktirebilir. [Erteleme veya satır içine yerleştirme](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources), bu ağ isteklerini kritik yoldan uzaklaştırabilir."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "<PERSON>u gezinme için o<PERSON>turma engelleme isteği yok"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "İstek"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Oluşturma engelleme is<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "\"Stili yeniden hesapla\" maliyetleri yüksekse seçicinin optimize edilmesi bu maliyetleri azaltabilir. Hem geçen süresi hem de yavaş yol yüzdesi yüksek olan [seçicileri optimize edin](https://developer.chrome.com/docs/devtools/performance/selector-stats). <PERSON><PERSON> basit seçici, daha az sayıda se<PERSON>, daha küçük DOM ve daha sığ DOM, eşleşme maliyetlerini azaltır."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Geçen süre"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS seçici verisi bulunamadı. Performans paneli ayarlarında CSS seçici istatistiklerinin etkinleştirilmesi gerekir."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Eşleşme sayısı"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS seçici maliyetleri"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "En iyi seçiciler"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Toplam"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Ana ileti dizesi süresi"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "3. <PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> boy<PERSON>u"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Üçüncü taraf kodu, yü<PERSON>me performansını önemli ölçüde etkileyebilir. Sayfanızdaki içeriklere öncelik vermek için [üçüncü taraf kodlarının yüklenmesini azaltın ve geciktirin](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Üçüncü taraf içeriği bulunamadı"}, "models/trace/insights/ThirdParties.ts | title": {"message": "3. <PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için optimize edilmezse dokunma etkileşimleri [300 ms.ye kadar gecikebilir](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Mobil dokunma g<PERSON>ik<PERSON>i"}, "models/trace/insights/Viewport.ts | title": {"message": "Görüntü alanını mobil cihazlar için optimize edin"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Yalnızca GET isteği aracılığıyla yüklenen sayfalar geri-ileri önbelleğe alınmaya uygundur."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Yalnızca 2XX durum koduna sahip sayfalar önbelleğe alınabilir."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome, önbellekteyken JavaScript'i yürütme girişimi tespit etti."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner isteğinde bulunan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, flag'lerle devre dışı bırakılmış. Özelliği yerel olarak bu cihazda etkinleştirmek için chrome://flags/#back-forward-cache adresine gidin."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, komut satırıyla devre dışı bırakılmış."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, yetersiz bellek nedeniyle devre dışı bırakılmış."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> verilmiş kullanıcı tarafından desteklenmiyor."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, önceden oluşturucu için devre dışı bırakılmış."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON><PERSON>, kayıtlı dinleyicileri olan bir BroadcastChannel örneği içerdiğinden önbelleğe alınamıyor."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store başlığı içeren sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Önbellek kasıtlı olarak temizlenmiş."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON><PERSON>, başka bir sayfanın önbelleğe alınabilmesi için önbellekten çıkarılmış."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Eklenti içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Tanımsız"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Sayfadan <PERSON>ıkılırken medya içeriği oynatılıyormuş."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API kullanan ve oynatma durumu tanımlamış olan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API kullanan ve işlem işleyicileri tanımlamış olan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucu nedeniyle devre dışı bırakılmış."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler kullanan say<PERSON>lar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API kullanan sayfalar geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store kullanan bir sayfada çerezler devre dışı bırakıldığı için geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Özel bir çalışan veya iş akışı kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Yüklenmesi tamamlanmadan dokümandan çıkılmış."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Sayfadan çıktıktan sonra uygulama banner'ı görüntülendi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Sayfadan <PERSON>ı<PERSON>ıktan sonra Chrome Şifre Yöneticisi görüntülendi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Sayfadan çıktıktan sonra DOM ayrıştırma süreci devam etti."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Sayfadan çıktıktan sonra DOM Ayrıştırıcı Görünümü görüntülendi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Messaging API'yi kullanan uzantılar nedeniyle geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Uzun süreli bağlantısı olan uzantılar geri-ileri önbelleğe girmeden önce bağlantıyı kapatmalıdır."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Uzun süreli bağlantısı olan uzantılar geri-ileri önbellekteki çerçevelere mesaj göndermeyi denedi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Geri-ileri önbellek uzantılar nedeniyle devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Sayfadan çıktıktan sonra yeniden form gönderme veya http şifre iletişimi gibi bir modal iletişim gösterildi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Sayfadan çıktıktan sonra çevrimdışı sayfa gösterildi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Sayfadan çıktıktan sonra Yetersiz Bellek Müdahalesi çubuğu görüntülendi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Sayfadan <PERSON>ı<PERSON>ıktan sonra izin istekleri alındı."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON>an sonra pop-up engel<PERSON><PERSON> görüntülendi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Sayfadan çıktıktan sonra Güvenli Tarama ayrıntıları gösterildi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Güvenli Tarama bu sayfayı kötüye kullanım amaçlı olarak değerlendirdi ve pop-up'ı engelledi."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Sayfa geri-ileri önbellekteyken bir hizmet çalışanı etkinleştirilmiş."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Doküman hatası nedeniyle geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames kullanan sayfalar bfcache'te saklanamaz."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON><PERSON>, başka bir sayfanın önbelleğe alınabilmesi için önbellekten çıkarılmış."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Me<PERSON>a yayınına erişim izni veren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Belirli yerleştirilmiş içerik türleri (ör. PDF'ler) barındıran sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager kullanan sayfalar şu an için geri-ileri <PERSON>bell<PERSON>ğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Açık bir IndexedDB bağlantısına sahip olan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, IndexedDB etkinliği nedeniyle devre dışı bırakılmış."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Uygun olmayan API'ler kullanılmış."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Uzantılar aracılığıyla JavaScript yerleştirilen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Uzantılar aracılığıyla StyleSheet yerleştirilen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON> hata."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Bazı JavaScript ağ istekleri Cache-Control: no-store başlığına sahip kaynak aldığı için geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, keepalive isteği nedeniyle devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Klavye kilidi kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Yüklenmesi tamamlanmadan sayfadan çı<PERSON>ılmış."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON> kaynağında cache-control:no-cache bulunan sayfalar geri-il<PERSON>e g<PERSON>z."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON> kaynağında cache-control:no-store bulunan sayfalar geri-il<PERSON>e gire<PERSON>z."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON> geri-ileri <PERSON> geri yüklenemeden önce gezinme iptal edilmiş."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Etkin bir ağ bağlantısı çok fazla veri aldığından sayfa önbellekten çıkarılmış. Chrome, önbellekteki bir sayfanın alabileceği veri miktarını sınırlar."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "İletim aşamasında fetch() veya XHR içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "<PERSON><PERSON>, yönlendirmeyle ilgili etkin bir ağ isteği nedeniyle geri-ileri önbellekten çıkarılmış."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "<PERSON><PERSON>, çok uzun süre açık kalan bir ağ bağlantısı nedeniyle önbellekten çıkarılmış. Chrome, önbellekteki bir sayfanın veri alabileceği süreyi sınırlar."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Geçerli yanıt başlığı içermeyen sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON>, ana çerçeve dışında bir çerçevede gerçekleşmiş."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Devam etmekte olan dizine eklenmiş DB işlemleri içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "İletim aşamasında bir ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "İletim aşamasında bir getirme ağı isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "İletim aşamasında bir ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "İletim aşamasında bir XHR ağ isteği içeren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Pencere içinde pencere özelliğini kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Yazdırma kullanıcı arayüzü görüntüleyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "<PERSON><PERSON>, \"window.open()\" kullanılarak açılmış ve başka bir sekmede buna atıfta bulunuluyor ya da sayfa bir pencere açmış."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Geri-ileri önbellekteki sayfa için oluşturucu süreci kilitlenmiş."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Geri-ileri önbellekteki sayfa için oluşturucu süreci sonlandırılmış."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Ses kaydetme izni isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Sensör izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Arka plan senkronizasyonu veya getirme izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Bildirim izinleri isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Depolama alanına eri<PERSON><PERSON> isteğinde bulunan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Video kaydetme izni isteyen sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Yalnızca URL şeması HTTP/HTTPS olan sayfalar önbelleğe alınabilir."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Bir hizmet çalışanı, geri-ileri önbellekteyken sayfa üzerinde hak talebinde bulunmuş."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Bir hizmet çalışanı, geri-ileri önbellekteki sayfaya MessageEvent gönderme girişiminde bulunmuş."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Bir sayfa geri-ileri önbellekteyken ServiceWorker kaydı iptal edilmiş."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON>, bir hizmet çalışanının etkinleştirilmesi nedeniyle geri-ileri önbellekten çıkarılmış."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome yeniden başlatılmış ve geri-ileri önbellek girişlerini temizlemiş."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer kullanan sayfalar şu an için geri-ileri <PERSON>belleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Sayfadaki bir iframe'in başlattığı gezinme işlemi tamamlanmamış."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Alt kaynağında cache-control:no-cache bulunan sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Alt kaynağında cache-control:no-store bulunan sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON>, geri-ileri önbellekte azami süreyi aşmış ve geçerliliğini kaybetmiş."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "<PERSON><PERSON>, muhtemelen uzun süre çalışan sayfa gizleme işleyicileri nedeniyle, geri-ileri <PERSON>nbelleğe girerken zaman aşımına uğramış."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Sayfanın ana çerçevesinde kaldırma işleyicisi var."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Sayfanın alt çerçevelerinden birinde kaldırma işleyicisi var."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Tarayıcı, kullanıcı aracısını geçersiz kılma başlığını değiştirmiş."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Video veya ses kaydı erişimine izin veren sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService kullanan sayfalar şu an için geri-ileri <PERSON>belleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC içeren sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC kullanıldığı için geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket içeren sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket kullanıldığı için geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport içeren sayfalar geri-ileri önbelleğe giremez."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport kullanıldığı için geri-ileri önbellek devre dışı bırakıldı."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR kullanan sayfalar şu an için geri-ileri önbelleğe alınmaya uygun değildir."}}