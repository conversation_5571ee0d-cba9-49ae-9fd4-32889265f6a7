{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS एपियरेन्स भ्यालू slider-vertical स्तरीकृत नभएको हुनाले यो भ्यालू हटाइने छ।"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain सेट गरेर उही ओरिजिनसम्बन्धी नीतिमा लगाइएको प्रतिबन्ध हटाउने सुविधा चल्तीबाट हटाइएको छ र यो सुविधा स्वतः अफ गरिने छ। सुविधा चल्तीबाट हटाइएको कुरासम्बन्धी यो चेतावनी document.domain सेट गरेर अन गरिएको क्रस ओरिजिन एक्सेसका हकमा लागू हुन्छ।"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "क्रस ओरिजिन iframe बाट window.alert ट्रिगर गर्ने सुविधा चल्तीबाट हटाइएको छ र भविष्यमा यो सुविधा सदाका लागि हटाइने छ।"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "क्रस ओरिजिन iframe बाट window.confirm ट्रिगर गर्ने सुविधा चल्तीबाट हटाइएको छ र भविष्यमा यो सुविधा सदाका लागि हटाइने छ।"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "डेटासम्बन्धी सहायता: SVGUseElement मा भएका URL चल्तीबाट हटाइँदै छन् र तिनलाई चाँडै नै सदाका लागि हटाइने छन्।"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() र watchPosition() ले अब उप्रान्त असुरक्षित ओरिजिनहरूमा काम गर्ने छैनन्। तपाईं यो सुविधा प्रयोग गर्न चाहनुहुन्छ भने तपाईंले आफ्नो एपको ओरिजिन परिवर्तन गरी HTTPS जस्ता सुरक्षित ओरिजिन प्रयोग गर्नु पर्ने हुन्छ। तपाईं यस सम्बन्धमा थप जानकारी प्राप्त गर्न चाहनुहुन्छ भने https://goo.gle/chrome-insecure-origins हेर्नुहोस्।"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "असुरक्षित ओरिजिनहरूमा getCurrentPosition() र watchPosition() चल्तीबाट हटाइएका छन्। तपाईं यो सुविधा प्रयोग गर्न चाहनुहुन्छ भने तपाईंले आफ्नो एपको ओरिजिन परिवर्तन गरी HTTPS जस्ता सुरक्षित ओरिजिन प्रयोग गर्नु पर्ने हुन्छ। तपाईं यस सम्बन्धमा थप जानकारी प्राप्त गर्न चाहनुहुन्छ भने https://goo.gle/chrome-insecure-origins हेर्नुहोस्।"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "article>, <aside>, <nav> वा <section> भित्र रहेको फन्टको आकार सेट नगरिएको <h1> ट्याग भेटिएको छ। निकट भविष्यमा यो ब्राउजर अपडेट गरिने क्रममा यो शीर्षकको टेक्स्टको आकार पनि बदलिने छ। यस सम्बन्धमा थप जानकारी हेर्न https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 मा जानुहोस्।"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate चल्तीबाट हटाइएको छ। कृपया यसको साटो RTCPeerConnectionIceErrorEvent.address वा RTCPeerConnectionIceErrorEvent.port प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "डिजिटल क्रिडेन्सियलहरू माग्नका निम्ति प्रयोग गरिएको navigator.credentials.get() अनुरोधको यो फर्म्याट चल्तीबाट हटाइएको छ, कृपया नयाँ फर्म्याट प्रयोग गर्न आफ्नो कल अपडेट गर्नुहोस्।"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "मर्चन्टको ओरिजिन र canmakepayment सर्भिस वर्कर इभेन्टबाट प्राप्त गरिएको आर्बिट्रेरी डेटा चल्तीबाट हटाइएको छ र भविष्यमा पूर्ण रूपमा हटाइने छ: topOrigin, paymentRequestOrigin, methodData, modifiers।"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup() मा पठाइएको InterestGroups को dailyUpdateUrl फिल्डको नाम बदलेर updateUrl बनाइएको छ। उक्त फिल्डले काम गर्ने तरिका अझ राम्रोसँग देखाउन यसो गरिएको हो।"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator चल्तीबाट हटाइएको छ। कृपया यसको साटो Intl.Segmenter प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Using SourceBuffer.abort() to abort remove()'s asynchronous range removal is deprecated due to specification change. Support will be removed in the future. You should listen to the updateend event instead. abort() is intended to only abort an asynchronous media append or reset parser state."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "अब उप्रान्त असुरक्षित ओरिजिनबाट Notification API प्रयोग गर्न सकिँदैन। तपाईंलाई आफ्नो एपको ओरिजिन परिवर्तन गरी HTTPS जस्ता सुरक्षित ओरिजिन प्रयोग गर्न सिफारिस गरिन्छ। तपाईं यस सम्बन्धमा थप जानकारी प्राप्त गर्न चाहनुहुन्छ भने https://goo.gle/chrome-insecure-origins हेर्नुहोस्।"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "अब उप्रान्त क्रस ओरिजिन iframe बाट Notification API प्रयोग गर्ने अनुमति माग्न सकिँदैन। तपाईंलाई शीर्ष स्तरको फ्रेमबाट अनुमति माग्न वा यसको साटो नयाँ विन्डो खोल्न सिफारिस गरिन्छ।"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap मा imageOrientation: 'none' विकल्प चल्तीबाट हटाइएको छ। कृपया यसको साटो '{imageOrientation: 'from-image'}' विकल्प भएको createImageBitmap प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "तपाईंले img, video तथा canvas ट्यागमा overflow: visible उल्लेख गर्नुभयो भने ती ट्यागले एलिमेन्टका सीमाबाहिर भिजुअल सामग्री बनाउन सक्छन्। https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md हेर्नुहोस्।"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments चल्तीबाट हटाइएको छ। यसको साटो भुक्तानी ह्यान्ड्लरहरूका हकमा just-in-time इन्स्टल गर्ने सुविधा प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "तपाईंको PaymentRequest कलले सामग्रीको सुरक्षासम्बन्धी नीति (CSP) connect-src डिरेक्टिभ बाइपास गरेको छ। यो बाइपास चल्तीबाट हटाइएको छ। कृपया PaymentRequest API (supportedMethods फिल्डमा) बाट आफ्नो CSP connect-src डिरेक्टिभमा भुक्तानी विधिको आइडेन्टिफायर हाल्नुहोस्।"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent चल्तीबाट हटाइएको छ। कृपया यसको साटो navigator.storage प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> प्यारेन्ट भएको <source src> अवैध छ र यो एलिमेन्टलाई बेवास्ता गरिएको छ। कृपया यसको साटो <source srcset> प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame विक्रेतामा आधारित हुन्छ। कृपया यसको साटो डिफल्ट cancelAnimationFrame प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame विक्रेतामा आधारित हुन्छ। कृपया यसको साटो requestAnimationFrame प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen चल्तीबाट हटाइएको छ। कृपया यसको साटो Document.fullscreenElement प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() चल्तीबाट हटाइएको छ। कृपया यसको साटो Element.requestFullscreen() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() चल्तीबाट हटाइएको छ। कृपया यसको साटो Element.requestFullscreen() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() चल्तीबाट हटाइएको छ। कृपया यसको साटो Document.exitFullscreen() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() चल्तीबाट हटाइएको छ। कृपया यसको साटो Document.exitFullscreen() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen चल्तीबाट हटाइएको छ। कृपया यसको साटो Document.fullscreenEnabled प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "हामी chrome.privacy.websites.privacySandboxEnabled API चल्तीबाट हटाउँदै छौँ तर संस्करण M113 रिलिज नहुँदासम्म यो API ब्याकवार्ड कम्प्याटिबिलिटीका लागि सक्रिय रहिरहने छ। कृपया यसको साटो chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled र chrome.privacy.websites.adMeasurementEnabled प्रयोग गर्नुहोस्। https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled मा जानुहोस्।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement कन्स्ट्रेन्ट हटाइएको छ। तपाईंले यो कन्स्ट्रेन्टका हकमा false मान तोक्नुभएको हुनाले तपाईंले हटाइएको SDES key negotiation विधि प्रयोग गर्ने प्रयास गरेको मानिन्छ। यो सेवा हटाइएको छ। यसको साटो DTLS key negotiation प्रयोग गर्न मिल्ने सेवा प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement कन्स्ट्रेन्ट हटाइएको छ। तपाईंले यो कन्स्ट्रेन्टका हकमा true मान तोक्नुभएको छ तर उक्त मानले कुनै पनि प्रभाव पारेन। तपाईं व्यवस्थित राख्नका निम्ति यो कन्स्ट्रेन्ट हटाउन सक्नुहुन्छ।"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "कलब्याकमा आधारित getStats() चल्तीबाट हटाइएको छ र भविष्यमा यसलाई सदाका लागि हटाइने छ। यसको साटो विशेष विवरण अनुरूप रहेको getStats() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() चल्तीबाट हटाइएको छ। कृपया यसको साटो Selection.modify() प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy विकल्प चल्तीबाट हटाइएको छ र यसलाई सदाका लागि हटाइने छ।"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "प्रयोगकर्ताले एक्टिभेट नगरेको speechSynthesis.speak() चल्तीबाट हटाइएको छ र यो API सदाका लागि हटाइने छ।"}, "generated/Deprecation.ts | UnloadHandler": {"message": "अनलोड इभेन्ट लिस्नरहरू चल्तीबाट हटाइएका छन् र भविष्यमा यिनलाई सदाका लागि हटाइने छ।"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter एट्रिब्युट चल्तीबाट हटाइएको छ। यसको साटो GPUAdapterInfo isFallbackAdapter एट्रिब्युट प्रयोग गर्नुहोस्।"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "मुख्य थ्रेडमा XMLHttpRequest सिंक्रोनाइज गर्ने सुविधाले प्रयोगकर्ताको अनुभवमा नराम्रो प्रभाव पारेको हुनाले सो सुविधा चल्तीबाट हटाइएको छ। थप मद्दत प्राप्त गर्न https://xhr.spec.whatwg.org/ मा जानुहोस्।"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "एनिमेसन"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "प्रयोगकर्ताले कुनै अन्तर्क्रिया नगरिकनै एलिमेन्टहरू सर्दा लेआउट सिफ्ट हुन्छ। पेज लोड हुने क्रममा एलिमेन्टहरू हालिएको, हटाइएको वा तिनको फन्ट परिवर्तन गरिएको जस्ता [लेआउट सिफ्ट हुनुका कारणहरूका बारेमा अनुसन्धान गर्नुहोस्](https://web.dev/articles/optimize-cls)।"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "फन्टसम्बन्धी अनुरोध"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "इन्जेक्ट गरिएको iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "लेआउट सिफ्ट क्लस्टर @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "लेआउटको सिफ्टको कुनै मूल कारण भेटाउन सकिएन"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "लेआउटको सिफ्ट भेटिएन"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "लेआउट सिफ्ट गराउने कारणहरू"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "लेआउट सिफ्ट गराउने सबैभन्दा ठुला कारणहरू"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "सबैभन्दा खराब क्लस्टर"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "सबैभन्दा खराब लेआउट सिफ्ट क्लस्टर"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "क्यास TTL"}, "models/trace/insights/Cache.ts | description": {"message": "लामो समयसम्म क्यास गरिराख्ने सुविधाले मान्छेहरूले तपाईंको पेज घरिघरि हेर्दा उक्त पेज छिटो लोड गर्छ। [थप जान्नुहोस्](https://web.dev/uses-long-cache-ttl/)।"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "क्यास गर्नु पर्ने कुनै पनि अनुरोध भेटिएन"}, "models/trace/insights/Cache.ts | others": {"message": "अन्य {PH1} वटा"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "अनुरोध गर्नुहोस्"}, "models/trace/insights/Cache.ts | title": {"message": "सामग्री लामो समयसम्म क्यास गरिराख्ने सुविधा प्रयोग गर्नुहोस्"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM को आकार ठुलो भयो भने शैलीको गणना गर्न र लेआउटको रिफ्लो गर्न अझ बढी समय लाग्न सक्छ र यिनका कारणले पेजको रेस्पोन्सिभनेस प्रभावित हुन्छ। DOM को आकार ठुलो भयो भने मेमोरी पनि बढी प्रयोग हुने छ। [DOM को आकार अत्यधिक ठुलो हुन नदिने तरिका सिक्नुहोस्](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)।"}, "models/trace/insights/DOMSize.ts | element": {"message": "एलिमेन्ट"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "अधिकांश चाइल्ड एलिमेन्टहरू"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM डेप्थ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "तथ्याङ्क"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM को आकार अप्टिमाइज गर्नुहोस्"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "एलिमेन्टहरूको कुल सङ्ख्या"}, "models/trace/insights/DOMSize.ts | value": {"message": "मान"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "नेटवर्कसम्बन्धी तपाईंको पहिलो अनुरोध सबैभन्दा महत्त्वपूर्ण हुन्छ।  रिडिरेक्ट गर्न रोक लगाएर, सर्भरबाट छिटो जवाफ प्राप्त हुने कुरा सुनिश्चित गरेर र टेक्स्ट कम्प्रेस गर्ने सुविधा अन गरेर यसको विलम्बता घटाउनुहोस्।"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "रिडिरेक्टहरू ({PH1} वटा रिडिरेक्ट, +{PH2}) छन्"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "सर्भरले ढिलो जवाफ दियो ({PH1} लाग्यो)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "कुनै कम्प्रेसन लागू गरिएको छैन"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "रिडिरेक्ट गर्दैन"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "सर्भरले तुरुन्तै जवाफ दियो ({PH1} लाग्यो)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "टेक्स्ट कम्प्रेसन लागू गर्छ"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "रिडिरेक्टहरू"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "सर्भरबाट रेस्पोन्स प्राप्त हुन लागेको समय"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "डकुमेन्टका लागि अनुरोध गर्न भएको ढिलाइ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "डाउनलोड कम्प्रेस गरिएको छैन"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "दोहोरिएका बाइट"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "स्रोत"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "नेटवर्कसम्बन्धी गतिविधिले प्रयोग गर्ने अनावश्यक बाइटहरूको सङ्ख्या कम गर्न बन्डलहरूबाट ठुला डुप्लिकेट JavaScript मोड्युलहरू हटाउनुहोस्।"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "दोहोरिएको JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "टेक्स्ट एकनासको देखिन्छ भन्ने कुरा सुनिश्चित गर्न [font-display](https://developer.chrome.com/blog/font-display) सेट गरी swap वा optional बनाउनुहोस्। [फन्ट मेट्रिक ओभरराइडहरू](https://developer.chrome.com/blog/font-fallbacks) प्रयोग गरी लेआउट सिफ्टहरू न्यूनीकरण गर्न swap लाई थप अप्टिमाइज गर्न सकिन्छ।"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "फन्ट"}, "models/trace/insights/FontDisplay.ts | title": {"message": "फन्ट डिस्प्ले"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "खेर गएको समय"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(बेनामी)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "सामान्यतया लेआउट जियोमेट्री रिड गर्ने कयौँ API हरूले रेन्डर गर्ने इन्जिनलाई शैली र लेआउटको गणना गर्ने प्रयोजनका लागि स्क्रिप्ट एक्जिक्युसन जबरजस्ती पज गर्न लगाउँछन्। [फोर्स्ड रिफ्लो](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) र यसका मिटिगेसनका बारेमा थप जान्नुहोस्।"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "स्ट्याक ट्रेस"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "फोर्स्ड रिफ्लो"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "सबैभन्दा बढी समय लाग्ने फङ्सन कल"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "रिफ्लो गर्न लाग्ने कुल समय"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[unattributed]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "तपाईंले फोटोहरू डाउनलोड गर्न लाग्ने समय घटाउनुभयो भने पेज र LCP लोड गर्न लाग्ने समय घट्न सक्छ। [फोटोको आकार अप्टिमाइज गर्ने बारेमा थप जान्नुहोस्](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (अनुमानित: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "अप्टिमाइज गर्न मिल्ने कुनै फोटो छैन"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "फाइलको आकार अप्टिमाइज गर्नुहोस्"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "अन्य {PH1} वटा"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "फोटो डेलिभरी गर्ने सुविधाको गुणस्तर सुधार्नुहोस्"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "तपाईंले फोटो कम्प्रेस गर्ने फ्याक्टर बढाउनुभयो भने यसले डाउनलोड गरिने फोटोको आकार घटाउन सक्छ।"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "तपाईंले फोटोको आधुनिक फर्म्याट (WebP, AVIF) प्रयोग गर्नुभयो भने वा फोटोको कम्प्रेसन बढाउनुभयो भने डाउनलोड गरिने फोटोको आकार घट्न सक्छ।"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "यो फोटो फाइलको आकार देखाइएको डाइमेन्सन ({PH2}) मा प्रयोग गर्न मिल्ने ({PH1}) भन्दा अझ ठुलो छ। डाउनलोड गरिने फोटोको आकार घटाउन रेस्पोन्सिभ फोटोहरू प्रयोग गर्नुहोस्।"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "तपाईंले GIF का साटो भिडियो फर्म्याटहरू प्रयोग गर्नुभयो भने डाउनलोड गरिने एनिमेसन गरिएका सामग्रीको आकार घट्न सक्छ।"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "सबैभन्दा लामो चरण प्रयोग गरी अनुसन्धान गर्न थाल्नुहोस्। [ढिलाइ न्यूनीकरण गर्न सकिन्छ](https://web.dev/articles/optimize-inp#optimize_interactions)। प्रोसेस गर्ने अवधि घटाउन प्रायः JS सँग सम्बन्धित [मुख्य थ्रेडको लागत अप्टिमाइज गर्नुहोस्](https://web.dev/articles/optimize-long-tasks)।"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "अवधि"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "इनपुट गर्दा हुने विलम्बता"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "कुनै अन्तर्क्रिया गरिएको छैन"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "चरण"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "प्रस्तुतिमा विलम्ब"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "प्रोसेस गर्न लाग्ने अवधि"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "चरणअनुसार INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTML बाट LCP को फोटो तुरुन्तै [भेट्टाउन सकिने](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) बनाएर र [लेजी लोडिङ प्रयोग गर्न रोक लगाएर](https://web.dev/articles/lcp-lazy-loading) LCP अप्टिमाइज गर्नुहोस्"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high लागू गरियो"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high लागू गर्नु पर्छ"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "लेजी लोड लागू गरिएको छैन"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP इमेज सबैभन्दा सुरुवाती विन्दुभन्दा {PH1} पछि लोड भयो।"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "कुनै LCP भेटिएको छैन"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP फोटो नभएको हुनाले LCP को कुनै स्रोत भेटिएन"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "यो अनुरोध सुरुको डकुमेन्टमा भेट्टाउन सकिन्छ"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP सम्बन्धी अनुरोधहरू पत्ता लगाउने प्रक्रिया"}, "models/trace/insights/LCPPhases.ts | description": {"message": "हरेक [चरणमा सुधारका निश्चित रणनीतिहरू](https://web.dev/articles/optimize-lcp#lcp-breakdown) छन्। सामान्यतया LCP को अधिकांश समय ढिलाइमा नभई स्रोतहरू लोड गर्ने प्रक्रियामा खर्च गरिनु पर्छ।"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "अवधि"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "एलिमेन्ट रेन्डर गर्दा भएको ढिलाइ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "फिल्ड p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "कुनै LCP भेटिएको छैन"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "चरण"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "स्रोत लोड गर्दा भएको ढिलाइ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "रिसोर्स लोड हुन लागेको समय"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "पहिलो बाइट प्राप्त गर्दा लागेको समय"}, "models/trace/insights/LCPPhases.ts | title": {"message": "चरणअनुसारको LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "स्क्रिप्ट"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "खेर गएका बाइट"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "पोलिफिल र ट्रान्सफर्मले पुराना ब्राउजरहरूलाई JavaScript का नयाँ सुविधाहरू प्रयोग गर्न दिन्छन्। तर आधुनिक ब्राउजरहरूलाई यीमध्ये धेरै सुविधाहरू चाहिँदैन। तपाईंलाई आफूले पुराना ब्राउजरहरू प्रयोग गर्नु पर्छ भन्ने कुरा थाहा नभएसम्म [Baseline](https://web.dev/articles/baseline-and-polyfills) का सुविधाहरू ट्रान्स्पाइल नगर्नका निम्ति आफ्नो JavaScript बिल्ड प्रोसेस परिमार्जन गर्नुहोस्। [अधिकांश साइटहरूले ट्रान्स्पाइल नगरिकनै ES6+ कोड डिप्लोई गर्न सक्नुको कारणका बारेमा जान्नुहोस्](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "लिगेसी JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 र HTTP/3 ले मल्टिप्लेक्सिङ जस्ता HTTP/1.1 भन्दा धेरै लाभहरू प्रदान गर्छ। [आधुनिक HTTP प्रयोग गर्ने तरिकाका बारेमा थप जान्नुहोस्](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)।"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "कुनै पनि अनुरोधमा HTTP/1.1 प्रयोग गरिएको छैन"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "प्रोटोकल"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "अनुरोध गर्नुहोस्"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "आधुनिक HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "डेटाको स्रोत"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "अनुरोध"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "स्रोत"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "समय"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "LCP सम्बन्धी अनुमानित बचत"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "प्रयोग नगरिएको प्रिकनेक्ट। crossorigin विशेषता सही तरिकाले प्रयोग गरिएको छ कि छैन भन्ने कुरा जाँच्नुहोस्।"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "पेज लोडको गुणस्तर सुधार गर्ने प्रयोजनका लागि चेनहरूको लम्बाइ वा डाउनलोड गरिने स्रोतहरूको आकार घटाएर वा अनावश्यक स्रोतहरू डाउनलोड गर्ने कार्य स्थगित गरेर [महत्त्वपूर्ण अनुरोधहरूको चेन बनाउन नदिनुहोस्](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "आफ्ना डेटाका सबैभन्दा महत्त्वपूर्ण स्रोतमा [अग्रिम रूपमा कनेक्ट हुने डेटाका स्रोत](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)सम्बन्धी सङ्केतहरू हाल्नुहोस् तर ४ वटाभन्दा बढी सङ्केत प्रयोग गर्ने प्रयास नगर्नुहोस्।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "डेटाका स्रोतहरू अग्रिम रूपमा कनेक्ट गर्नुहोस्"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "अधिकतम क्रिटिकल पाथ ल्याटेन्सी:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "नेटवर्क डिपेन्डेन्सीहरूले रेन्डर गर्ने कुनै पनि कार्यमा प्रभाव पारेका छैनन्"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "डेटाका अन्य कुनै पनि स्रोत अग्रिम रूपमा कनेक्ट गर्नका निम्नि उपयुक्त छैनन्"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "डेटाका कुनै पनि स्रोत अग्रिम रूपमा कनेक्ट गरिएका छैनन्"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[अग्रिम रूपमा कनेक्ट हुने डेटाका स्रोत](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)सम्बन्धी सङ्केतहरूले ब्राउजरलाई यो पेज लोड हुनुअघि नै कनेक्ट गर्न मद्दत गर्छन् जसले गर्दा डेटाको उक्त स्रोतसम्बन्धी पहिलो अनुरोध गर्दा समय बचत हुन्छ। यो पेज अग्रिम रूपमा कनेक्ट हुने डेटाका स्रोतहरू यस प्रकार छन्।"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "प्रिकनेक्ट गरिएका ओरिजिनहरू"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "नेटवर्क डिपेन्डेन्सी ट्री"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "४ वटाभन्दा बढी preconnect कनेक्सनहरू भेटिएका छन्। यिनलाई संयमतापूर्वक डेटाका सबैभन्दा महत्त्वपूर्ण स्रोतहरूमा मात्र प्रयोग गर्नु पर्छ।"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "प्रयोग नगरिएका अग्रिम रूपमा कनेक्ट गरिएका डेटाका स्रोतहरू। यो पेजले माग्न सक्ने डेटाका स्रोतहरूका हकमा मात्र preconnect प्रयोग गर्नुहोस्।"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "पेज अझ छिटो लोड गर्ने प्रयोजनका लागि चेनहरूको लम्बाइ वा डाउनलोड गरिने स्रोतहरूको आकार घटाएर वा अनावश्यक स्रोतहरू डाउनलोड गर्ने कार्य स्थगित गरेर महत्त्वपूर्ण अनुरोधहरूको चेन बनाउन नदिनुहोस्।"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "अनुरोधहरूले पेजको सुरुवाती रेन्डर ब्लक गरिरहेका छन् र यसले गर्दा LCP मा ढिलाइ हुन सक्छ। [केही समयका लागि स्थगित वा इनलाइनिङ गरेर](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) नेटवर्कसम्बन्धी यी अनुरोधहरूलाई महत्त्वपूर्ण पथबाट सार्न सकिन्छ।"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "अवधि"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "यो नेभिगेसनको रेन्डर गर्ने प्रक्रिया ब्लक गर्न कुनै अनुरोध गरिएको छैन"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "अनुरोध"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "ब्लक गर्ने अनुरोधहरू ब्लक गर्नुहोस्"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "स्टाइल रिक्यालकुलेट गर्दा निकै समय लाग्ने भएका खण्डमा सेलेक्टर अप्टिमाइज गर्दा उक्त समय घट्न सक्छ। लागेको सबैभन्दा बढी समय र ढिलो पथको सबैभन्दा बढी % दुवै कुरा प्रयोग गरेर [सेलेक्टरहरू अप्टिमाइज गर्नुहोस्](https://developer.chrome.com/docs/devtools/performance/selector-stats)। अझ सरल सेलेक्टर, अझ थोरै सेलेक्टर, अझ सानो DOM र अझ थोरै काम गर्ने DOM जस्ता सबै कुराले गर्दा मिलाउने समय घट्ने छ।"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "लागेको समय"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS सेलेक्टरसँग सम्बन्धित कुनै जानकारी भेटिएको छैन। पर्फर्मेन्स प्यानलसम्बन्धी सेटिङमा गई CSS सेलेक्टरको तथ्याङ्क देखाउने सुविधा अन गर्नुहोस्।"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ठ्याक्कै मिलाउने प्रयासहरू"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "ठ्याक्कै मिल्ने नियमको सङ्ख्या"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS सेलेक्टरको लागत"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "शीर्ष सेलेक्टरहरू"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "कुल"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "मुख्य थ्रेडमा बिताइएको समय"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "तेस्रो पक्ष"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ट्रान्स्फर गरिने डेटाको आकार"}, "models/trace/insights/ThirdParties.ts | description": {"message": "तेसो पक्षीय कोडले लोड पर्फर्मेन्समा उल्लेख्य रूपमा प्रभाव पार्न सक्छ। आफ्नो पेजमा भएका सामग्रीलाई प्राथमिकतामा राख्न [तेस्रो पक्षीय कोड लोड हुने समय घटाउनुहोस् र डिफर गर्नुहोस्](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)।"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "कुनै पनि तेस्रो पक्षीय सामग्री भेटिएन"}, "models/trace/insights/ThirdParties.ts | title": {"message": "तेस्रो पक्षहरू"}, "models/trace/insights/Viewport.ts | description": {"message": "मोबाइलमा भ्युपोर्ट अप्टिमाइज गरिएको छैन भने ट्याप गरी गरिने अन्तर्क्रिया [बढीमा ३०० मिलिसेकेन्ड](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ढिलो हुन सक्छ।"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "मोबाइलमा ट्याप गरी कारबाही गर्न ढिलो भयो"}, "models/trace/insights/Viewport.ts | title": {"message": "मोबाइलमा भ्युपोर्ट अप्टिमाइज गर्नुहोस्"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET अनुरोधमार्फत लोड गरिएका पेजहरू मात्र ब्याक/फर्वार्ड क्यासमा राख्न मिल्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "वस्तुस्थिति कोड 2XX भएका पेजहरू मात्र क्यास गर्न मिल्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome ले यो पेज क्यासमा भएका बेला JavaScript कार्यान्वयन गर्ने प्रयास भएको कुरा पत्ता लगायो।"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner सम्बन्धी अनुरोध गरेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Chrome को फ्ल्यागमा ब्याक/फर्वार्ड क्यास अफ गरिएको छ। यो डिभाइसमा ब्याक/फर्वार्ड क्यास अन गर्न chrome://flags/#back-forward-cache मा जानुहोस्।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "कमान्ड लाइनअनुसार ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "पर्याप्त मेमोरी नभएका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "यो डेलिगेटका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "प्रिरेन्डररका लागि ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "यो पेजमा दर्ता गरिएका लिस्नर भएका BroadcastChannel इन्स्टन्स भएकाले यो पेज क्यास गर्न सकिँदैन।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store हेडर भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "यो क्यास जानाजान मेटाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "कुनै अर्को पेज क्यास गर्नका निम्ति यो पेज क्यासबाट हटाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "प्लगइन भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "अपरिभाषित"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "मिडिया डिभाइस डिस्प्याचर प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "तपाईं यो पेजबाट निस्कँदा मिडिया प्लेयर चलिरहेको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API प्रयोग गर्ने तथा प्लेब्याकको स्थिति सेट गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API प्रयोग गर्ने तथा कारबाहीका ह्यान्ड्लरहरू सेट गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "स्क्रिन रिडरका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store प्रयोग गरिने पेजमा कुकीहरू अफ गरिएकाले ब्याक-फर्वार्ड क्यास पनि अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "समर्पित वर्कर वा वर्कलेट प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "तपाईं यो डकुमेन्ट पूर्ण रूपमा लोड नहुँदै यसबाट बाहिरिनुभयो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "तपाईं यो पेजबाट निस्कँदा एप ब्यानर देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "तपाईं यो पेजबाट निस्कँदा Chrome पासवर्ड म्यानेजर देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "तपाईं यो पेजबाट निस्कँदा DOM डिस्टिलेसनको प्रक्रिया पूरा भइसकेको थिएन।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "तपाईं यो पेजबाट निस्कँदा DOM Distiller Viewer देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "एक्स्टेन्सनहरूले म्यासेजिङ API प्रयोग गरिरहेकाले ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "लामो समयदेखि कनेक्ट भएका एक्स्टेन्सनहरू ब्याक/फर्वार्ड क्यासमा सेभ गर्नुअघि कनेक्सन बन्द गर्नु पर्ने हुन्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "लामो समयदेखि कनेक्ट भएका एक्स्टेन्सनहरूले ब्याक/फर्वार्ड क्यासमा भएका फ्रेमहरूमा म्यासेज पठाउने प्रयास गरे।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "एक्स्टेन्सनहरूका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "तपाईं यो पेजबाट निस्कँदा फाराम पुनः पेस गर्ने डायलग वा http पासवर्ड हाल्ने डायलग जस्ता मोडल डायलग देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "तपाईं यो पेजबाट निस्कँदा अफलाइन पेज देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "तपाईं यो पेजबाट निस्कँदा मेमोरी सकिएका खण्डमा देखाइने इन्टरभेन्सन बार देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "तपाईं यो पेजबाट निस्कँदा अनुमति मागिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "तपाईं यो पेजबाट निस्कँदा पपअप ब्लकर देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "तपाईं यो पेजबाट निस्कँदा Safe Browsing सम्बन्धी विवरणहरू देखाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing ले यो पेज दुर्व्यवहारपूर्ण भएको ठहर गरेको छ र पपअप ब्लक गरेको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "यो पेज ब्याक/फर्वार्ड क्यासमा भएका बेला कुनै सर्भिस वर्कर एक्टिभेट गरिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "डकुमेन्टमा त्रुटि भएका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames प्रयोग गरिएका पेजहरू bfcache मा भण्डारण गर्न सकिँदैन।"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "कुनै अर्को पेज क्यास गर्नका निम्ति यो पेज क्यासबाट हटाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "मिडिया स्ट्रिम गर्न सकिने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "इम्बेड गरिएका निश्चित प्रकारका सामग्री (जस्तै, PDF हरू) भएका पेजहरूमा हाल ब्याक-फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON>dleMana<PERSON> प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "IndexedDB कनेक्सन खोलिएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB इभेन्टका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "अनुचित API हरू प्रयोग गरिएका थिए।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "एक्स्टेन्सनहरूमार्फत JavaScript समावेश गरिएका पेजहरूमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "एक्स्टेन्सनहरूमार्फत StyleSheet समावेश गरिएका पेजहरूमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "आन्तरिक त्रुटि।"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Cache-Control: no-store हेडर भएका स्रोतसहित केही JavaScript नेटवर्कसम्बन्धी अनुरोध प्राप्त भएकाले ब्याक-फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "किपअलाइभ अनुरोधका कारण ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "किबोर्ड लक प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "तपाईं यो पेज पूर्ण रूपमा लोड नहुँदै यसबाट बाहिरिनुभयो।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "मुख्य स्रोतमा cache-control:no-cache भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "मुख्य स्रोतमा cache-control:no-store भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ब्याक/फर्वार्ड क्यासबाट यो पेज रिस्टोर गरिनुअघि नै नेभिगेसन प्रक्रिया रद्द गरिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "कुनै सक्रिय नेटवर्क कनेक्सनमा अत्यन्तै धेरै डेटा प्राप्त भएकाले यो पेज क्यासबाट हटाइएको थियो। क्यास गरिएको पेजमा कति डेटा प्राप्त गर्न सकिन्छ भन्ने सम्बन्धमा Chrome ले सीमा तोकेको हुन्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() वा XHR सम्बन्धी विचाराधीन अनुरोध भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "नेटवर्कसम्बन्धी कुनै तात्कालिक अनुरोध पूरा गर्नका निम्ति रिडिरेक्ट गर्नु पर्ने भएकाले यो पेज ब्याक/फर्वार्ड क्यासबाट हटाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "कुनै नेटवर्क कनेक्सन धेरै लामो समयसम्म खुला रहेका कारण यो पेज क्यासबाट हटाइएको थियो। क्यास गरिएको पेजमा कति समयसम्म डेटा प्राप्त गर्न सकिन्छ भन्ने सम्बन्धमा Chrome ले सीमा तोकेको हुन्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "वैध रिस्पन्स हेडर नभएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "मुख्य फ्रेम नभई अन्य कुनै फ्रेममा नेभिगेसन गरिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "इन्डेक्स गरिएका DB ट्रान्ज्याक्सनहरू भइरहेको पेजका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "नेटवर्कसम्बन्धी विचाराधीन अनुरोध भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "फेच नेटवर्कसम्बन्धी विचाराधीन अनुरोध भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "नेटवर्कसम्बन्धी विचाराधीन अनुरोध भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR नेटवर्कसम्बन्धी विचाराधीन अनुरोध भएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Picture-in-Picture सुविधा प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "प्रिन्टिङ UI देखाउने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "यो पेज 'window.open()' प्रयोग गरी खोलिएको थियो र अर्को ट्याबमा त्यसको सन्दर्भ छ वा यो पेजले कुनै विन्डो खोलेको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "ब्याक/फर्वार्ड क्यासमा भएको यो पेजको रेन्डरर प्रक्रिया क्र्यास भएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "ब्याक/फर्वार्ड क्यासमा भएको यो पेजको रेन्डरर प्रक्रिया रोकिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "अडियो रेकर्ड गर्ने अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "सेन्सर प्रयोग गर्ने अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "ब्याकग्राउन्डमा सिंक गर्ने वा फेचसम्बन्धी अनुमतिहरू मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI सम्बन्धी अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "सूचना पठाउने अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "भण्डारण प्रयोग गर्ने अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "भिडियो खिच्ने अनुमति मागेका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "URL स्किमका रूपमा HTTP / HTTPS भएका पेजहरू मात्र क्यास गर्न मिल्छ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "सर्भिस वर्करले यो पेज ब्याक/फर्वार्ड क्यासमा भएका बेला यो पेज दाबी गरेका थिए।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "कुनै सर्भिस वर्करले ब्याक/फर्वार्ड क्यासमा भएको यो पेजमा MessageEvent पठाउने प्रयास गरेको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "पेज ब्याक/फर्वार्ड क्यासमै भएका बेला ServiceWorker को दर्ता रद्द गरिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "कुनै सर्भिस वर्कर एक्टिभेट भएका कारण यो पेज क्यासबाट हटाइएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome रिस्टार्ट भयो र ब्याक/फर्वार्ड क्यासमा भएका सबै डेटा मेटियो।"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Shared<PERSON><PERSON><PERSON> प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "यो पेजमा कुनै iframe ले नेभिगेसन प्रक्रिया सुरु गरेको थियो तर सो प्रक्रिया पूरा भएन।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "सहायक स्रोतमा cache-control:no-cache भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "सहायक स्रोतमा cache-control:no-store भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "ब्याक/फर्वार्ड क्यासमा यो पेज अधिकतम समयसम्म राखिएको हुनाले यो पेजको म्याद सकिएको थियो।"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "यो पेज ब्याक/फर्वार्ड क्यासमा राख्ने क्रममा यो पेजको टाइम आउट भयो (सम्भवतः pagehide ह्यान्ड्लरहरू धेरै लामो समयसम्म चलेका कारण)।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "यो पेजको मुख्य फ्रेममा अनलोड ह्यान्ड्लर छ।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "यो पेजको कुनै सहायक फ्रेममा अनलोड ह्यान्ड्लर छ।"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ब्राउजरले प्रयोगकर्ताको एजेन्टको ओभरराइड हेडर परिवर्तन गरेको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "भिडियो वा अडियो रेकर्ड गर्ने अनुमति दिइएका पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService प्रयोग गर्ने पेजहरूका हकमा ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC प्रयोग गरिएकाले ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket प्रयोग गरिएकाले ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport भएका पेजहरू ब्याक/फर्वार्ड क्यासमा राख्न मिल्दैन।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport प्रयोग गरिएकाले ब्याक/फर्वार्ड क्यास अफ गरिएको छ।"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR प्रयोग गर्ने पेजहरूका हकमा हाल ब्याक/फर्वार्ड क्यास प्रयोग गर्न मिल्दैन।"}}