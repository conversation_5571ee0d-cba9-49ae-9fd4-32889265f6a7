{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "सीएसएस के दिखने की slider-vertical वैल्यू स्टैंडर्ड के मुताबिक नहीं है और इसे हटा दिया जाएगा."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain को सेट करके, एक ही ऑरिजिन से जुड़ी नीति में पाबंदियों को हटाने की सुविधा पर रोक लगा दी गई है और इसे डिफ़ॉल्ट रूप से बंद कर दिया जाएगा. इस्तेमाल रोकने की यह चेतावनी, उस क्रॉस-ऑरिजिन ऐक्सेस के लिए है जिसे document.domain सेटिंग की मदद से चालू किया गया था."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "क्रॉस ओरिजिन iframe से window.alert को ट्रिगर करने की सुविधा अब काम नहीं करती और इसे आने वाले समय में हटा दिया जाएगा."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "क्रॉस ऑरिजिन iframe से window.confirm को ट्रिगर करने की सुविधा अब काम नहीं करती और इसे आने वाले समय में हटा दिया जाएगा."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "डेटा के लिए सहायता: SVGUseElement में मौजूद यूआरएल अब काम नहीं करते. इन्हें आने वाले समय में हटा दिया जाएगा."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() और watchPosition() अब असुरक्षित ऑरिजिन पर काम नहीं करते. इस सुविधा का इस्तेमाल करने के लिए, आपको अपना ऐप्लिकेशन किसी सुरक्षित ऑरिजिन पर स्विच करना होगा, जैसे कि एचटीटीपीएस. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "असुरक्षित ऑरिजिन पर, getCurrentPosition() और watchPosition() के इस्तेमाल पर रोक लगा दी गई है. इस सुविधा का इस्तेमाल करने के लिए, आपको अपना ऐप्लिकेशन किसी सुरक्षित ऑरिजिन पर स्विच करना होगा, जैसे कि एचटीटीपीएस. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> या <section> में एक <h1> टैग मिला है जिसका फ़ॉन्ट साइज़ सेट नहीं किया गया है. आने वाले समय में इस ब्राउज़र के अपडेट होने पर, इस हेडिंग के टेक्स्ट का साइज़ बदल जाएगा. ज़्यादा जानकारी के लिए, https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 देखें."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate के इस्तेमाल पर रोक लगा दी गई है. इसकी जगह RTCPeerConnectionIceErrorEvent.address या RTCPeerConnectionIceErrorEvent.port इस्तेमाल करें."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "डिजिटल क्रेडेंशियल का अनुरोध करने के लिए इस्तेमाल किया गया यह फ़ॉर्मैट navigator.credentials.get() अब काम नहीं करता. कृपया नए फ़ॉर्मैट का इस्तेमाल करने के लिए, कॉल अपडेट करें."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment सर्विस वर्कर इवेंट से लिए गए, व्यापारी या कंपनी के ऑरिजिन और उनके आर्बिट्रेरी डेटा के इस्तेमाल पर रोक लगा दी गई है और उन्हें हटा दिया जाएगा: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups के dailyUpdateUrl फ़ील्ड को joinAdInterestGroup() के पास भेजा गया है. इसके काम करने के तरीके को ज़्यादा सटीक रूप से दिखाने के लिए, updateUrl का नाम बदल दिया गया."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, कृपया Intl.Segmenter का इस्तेमाल करें."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "स्पेसिफ़िकेशन में हुए बदलाव की वजह से, SourceBuffer.abort() का इस्तेमाल करके remove() की एसिंक्रोनस रेंज को हटाने की प्रक्रिया रोक दी गई है. आने वाले समय में, सपोर्ट हटा दिया जाएगा. इसकी जगह, आपको updateend इवेंट सुनना चाहिए. abort() का इस्तेमाल करके, एसिंक्रोनस मीडिया एपेंड या रीसेट पार्सर स्टेट को सिर्फ़ रद्द किया जाता है."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "अब असुरक्षित ऑरिजिन से, Notification API का इस्तेमाल नहीं किया जा सकेगा. कृपया अपने ऐप्लिकेशन को किसी सुरक्षित ऑरिजिन पर ले जाएं, जैसे कि एचटीटीपीएस. ज़्यादा जानकारी के लिए, https://goo.gle/chrome-insecure-origins पर जाएं."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "क्रॉस-ऑरिजिन iframe से, Notification API को इस्तेमाल करने का अनुरोध नहीं किया जा सकता. इसके बजाय, आप किसी टॉप लेवल फ़्रेम से अनुरोध करें या नई विंडो का इस्तेमाल करें."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap में imageOrientation: 'none' विकल्प के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, कृपया '{imageOrientation: 'from-image'}' विकल्प के साथ, createImageBitmap का इस्तेमाल करें."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video, और canvas टैग पर overflow: visible की जानकारी देने से, एलिमेंट की सीमाओं के बाहर, टैग विज़ुअल कॉन्टेंट बना सकते हैं. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md देखें."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, पेमेंट हैंडलर के लिए just-in-time इंस्टॉल करने की सुविधा इस्तेमाल करें."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "आपके PaymentRequest कॉल ने कॉन्टेंट की सुरक्षा के बारे में नीति (सीएसपी) connect-src डायरेक्टिव को बायपास किया है. हालांकि, यह बायपास अब काम नहीं करता है. कृपया PaymentRequest API (supportedMethods फ़ील्ड में) से अपने सीएसपी connect-src डायरेक्टिव में, पैसे चुकाने के तरीके के आइडेंटिफ़ायर को जोड़ें."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent के इस्तेमाल पर रोक लगा दी गई है. इसके बजाय, स्टैंडर्ड navigator.storage का इस्तेमाल करें."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> पैरंट वाला <source src> गलत है. इसलिए, इसे अनदेखा किया गया है. इसके बजाय, कृपया <source srcset> का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "हर वेंडर का webkitCancelAnimationFrame अलग होता है. इसके बजाय, कृपया स्टैंडर्ड cancelAnimationFrame का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "हर वेंडर का webkitRequestAnimationFrame अलग होता है. इसके बजाय, कृपया स्टैंडर्ड requestAnimationFrame का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen अब काम नहीं करता. इसके बजाय, कृपया Document.fullscreenElement का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() अब काम नहीं करता है. इसके बजाय, कृपया Element.requestFullscreen() का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() अब काम नहीं करता है. इसके बजाय, कृपया Element.requestFullscreen() का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() अब काम नहीं करता है. इसके बजाय, कृपया Document.exitFullscreen() का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() अब काम नहीं करता है. इसके बजाय, कृपया Document.exitFullscreen() का इस्तेमाल करें."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen अब काम नहीं करता है. इसके बजाय, कृपया Document.fullscreenEnabled का इस्तेमाल करें."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "हम chrome.privacy.websites.privacySandboxEnabled API का इस्तेमाल रोक रहे हैं. हालांकि, वर्शन M113 के रिलीज़ होने तक, यह पुराने सिस्टम के साथ काम करता रहेगा. इसके बजाय, chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, और chrome.privacy.websites.adMeasurementEnabled का इस्तेमाल करें. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled पर जाएं."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement कंस्ट्रेंट को हटा दिया गया है. आपने इस कंस्ट्रेंट के लिए, false वैल्यू दी है जिससे ऐसा लगता है कि आपने हटाए गए SDES key negotiation तरीके को इस्तेमाल करने की कोशिश की है. इस सुविधा को हटा दिया गया है. इसके बजाय, ऐसी सेवा का इस्तेमाल करें जो DTLS key negotiation के साथ काम करती हो."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement कंस्ट्रेंट को हटा दिया गया है. आपने इस कंस्ट्रेंट के लिए true वैल्यू दी है, जिससे कोई फ़र्क़ नहीं पड़ेगा. हालांकि, व्यवस्थित रखने के लिए, इस कंस्ट्रेंट को हटाया जा सकता है."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "कॉलबैक-आधारित getStats() अब काम नहीं करता है और इसे हटा दिया जाएगा. इसके बजाय, निर्देशों का पालन करने वाले getStats() का इस्तेमाल करें."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() अब काम नहीं करता है. इसके बजाय, कृपया Selection.modify() का इस्तेमाल करें."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy विकल्प के इस्तेमाल पर रोक लगा दी गई है और इसे हटा दिया जाएगा."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "यूज़र ऐक्टिवेशन किए बिना, speechSynthesis.speak() के इस्तेमाल पर रोक लगा दी गई है और इसे हटा दिया जाएगा."}, "generated/Deprecation.ts | UnloadHandler": {"message": "अनलोड इवेंट लिसनर के इस्तेमाल पर रोक लगा दी गई है और उन्हें हटा दिया जाएगा."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter एट्रिब्यूट अब काम नहीं करता. इसके बजाय, GPUAdapterInfo isFallbackAdapter एट्रिब्यूट इस्तेमाल करें."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "मुख्य थ्रेड पर सिंक किया गया XMLHttpRequest अब काम नहीं करता. इसकी वजह यह है कि असली उपयोगकर्ता के अनुभव पर इसका बुरा असर पड़ रहा था. इस बारे में किसी भी तरह की मदद के लिए, https://xhr.spec.whatwg.org/ पर जाएं."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ऐनिमेशन"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "लेआउट शिफ़्ट तब होते हैं, जब उपयोगकर्ता के इंटरैक्शन के बिना एलिमेंट मूव होते हैं. [लेआउट शिफ़्ट की वजहों की जांच करें](https://web.dev/articles/optimize-cls). जैसे, पेज लोड होने के दौरान एलिमेंट जोड़े या हटाए जाते हैं या उनके फ़ॉन्ट बदले जाते हैं."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "फ़ॉन्ट का अनुरोध"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "इंजेक्ट किया गया iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "लेआउट शिफ़्ट का क्लस्टर @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "लेआउट शिफ़्ट की कोई वजह नहीं मिली"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "लेआउट में कोई बदलाव नहीं हुआ"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "लेआउट शिफ़्ट की वजहें"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "लेआउट शिफ़्ट की मुख्य वजहें"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "सबसे खराब क्लस्टर"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "लेआउट शिफ़्ट का सबसे खराब क्लस्टर"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "कैश TTL"}, "models/trace/insights/Cache.ts | description": {"message": "डेटा लंबे समय तक कैश मेमोरी में सेव रहने से, लोगों के आपके पेज पर बार-बार वापस आने की प्रोसेस में तेज़ी आ सकती है. [ज़्यादा जानें](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "ऐसा कोई अनुरोध नहीं मिला जिससे जुड़े संसाधन को कैश मेमोरी में सेव करने की ज़रूरत हो"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} अन्य"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "अनुरोध"}, "models/trace/insights/Cache.ts | title": {"message": "कैश मेमोरी में कॉन्टेंट को लंबे समय तक सेव रखने वाले संसाधनों का इस्तेमाल करें"}, "models/trace/insights/DOMSize.ts | description": {"message": "डीओएम के बड़े साइज़ की वजह से, स्टाइल कैलकुलेशन और लेआउट रीफ़्लो की प्रोसेस में ज़्यादा समय लग सकता है. इसकी वजह से, पेज के रिस्पॉन्स देने की प्रोसेस पर असर पड़ता है. बड़े डीओएम से मेमोरी का इस्तेमाल भी बढ़ जाएगा. [बड़े साइज़ के डीओएम से बचने का तरीका जानें](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "एलिमेंट"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "ज़्यादा से ज़्यादा चाइल्ड एलिमेंट"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "डीओएम ट्री की लेंथ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "आंकड़े"}, "models/trace/insights/DOMSize.ts | title": {"message": "डीओएम साइज़ को ऑप्टिमाइज़ करें"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "कुल एलिमेंट"}, "models/trace/insights/DOMSize.ts | value": {"message": "वैल्यू"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "नेटवर्क पर किया जाने वाला पहला अनुरोध सबसे ज़्यादा अहम है.  इसके इंतज़ार का समय कम करने के लिए, रीडायरेक्ट से बचें और पक्का करें कि सर्वर तेज़ी से जवाब दे. साथ ही, टेक्स्ट कंप्रेस करने की सुविधा चालू करें."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "डॉक्यूमेंट के अनुरोध में रीडायरेक्ट करने वाले लिंक मौजूद थे ({PH1} रीडायरेक्ट, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "सर्वर धीरे काम कर रहा है ({PH1} तक इंतज़ार किया)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "कोई कंप्रेस नहीं किया गया"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "डॉक्यूमेंट के अनुरोध में रीडायरेक्ट करने वाला कोई लिंक मौजूद नहीं था"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "सर्वर तेज़ी से काम कर रहा है ({PH1} तक इंतज़ार किया)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "टेक्स्ट कंप्रेस कर दिया गया है"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "रीडायरेक्ट"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "सर्वर से जवाब मिलने में लगने वाला समय"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "दस्तावेज़ को डाउनलोड करने में लगने वाला समय"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "बिना कंप्रेस किए डाउनलोड हो रहा है"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "डुप्लीकेट बाइट"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "सोर्स"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "बंडल से बड़े और डुप्लीकेट JavaScript मॉड्यूल हटाएं. ऐसा करके, नेटवर्क गतिविधि में खर्च होने वाले गैर-ज़रूरी बाइट कम किए जा सकते हैं."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "डुप्लीकेट JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "[font-display](https://developer.chrome.com/blog/font-display) को swap या optional पर सेट करें, ताकि यह पक्का किया जा सके कि टेक्स्ट हमेशा दिखता रहे. [फ़ॉन्ट मेट्रिक ओवरराइड](https://developer.chrome.com/blog/font-fallbacks) की मदद से, लेआउट शिफ़्ट कम करने के लिए, swap को और ऑप्टिमाइज़ किया जा सकता है."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "फ़ॉन्ट"}, "models/trace/insights/FontDisplay.ts | title": {"message": "फ़ॉन्ट डिसप्ले"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "इंतज़ार में लगा समय"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(बिना नाम के)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "कई एपीआई, जो आम तौर पर लेआउट की जियॉमेट्री देखते हैं वे रेंडरिंग इंजन को स्क्रिप्ट चलाने से रोकते हैं, ताकि स्टाइल और लेआउट को कैलकुलेट किया जा सके. [हर हाल में होने वाले रीफ़्लो](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) और इसे कम करने के बारे में ज़्यादा जानें."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "स्टैक ट्रेस"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "हर हाल में होने वाला रीफ़्लो"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "सबसे ज़्यादा समय लेने वाला फ़ंक्शन कॉल"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "रीफ़्लो में लगने वाला कुल समय"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[unattributed]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "इमेज डाउनलोड होने में लगने वाले समय को कम करने से, पेज और एलसीपी के लोड होने में लगने वाले समय को कम किया जा सकता है. [इमेज के साइज़ को ऑप्टिमाइज़ करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (अनुमानित {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "कोई इमेज ऑप्टिमाइज़ नहीं की जा सकती"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "फ़ाइल का साइज़ ऑप्टिमाइज़ करें"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} अन्य"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "इमेज की डिलीवरी बेहतर बनाएं"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "इमेज के कंप्रेसन फ़ैक्टर को बढ़ाकर, इस इमेज का डाउनलोड साइज़ कम किया जा सकता है."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "इमेज के डाउनलोड साइज़ को कम करने के लिए, WebP या AVIF जैसे आधुनिक इमेज फ़ॉर्मैट का इस्तेमाल करें या इमेज को ज़्यादा कंप्रेस करें."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "इस इमेज फ़ाइल का साइज ज़रूरत से ज़्यादा बड़ा है. डिसप्ले किए गए डाइमेंशन {PH2} के लिए, इसका साइज़ ज़्यादा से ज़्यादा {PH1} होना चाहिए. डाउनलोड की जाने वाली इमेज का साइज़ कम करने के लिए, रिस्पॉन्सिव इमेज इस्तेमाल करें."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF के बजाय, वीडियो फ़ॉर्मैट का इस्तेमाल करने से, ऐनिमेटेड कॉन्टेंट के डाउनलोड साइज़ को कम किया जा सकता है."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "सबसे लंबे फ़ेज़ से जांच करना शुरू करें. [देरी को कम किया जा सकता है](https://web.dev/articles/optimize-inp#optimize_interactions). प्रोसेस करने में लगने वाला समय कम करने के लिए, [मुख्य थ्रेड की लागत को ऑप्टिमाइज़ करें](https://web.dev/articles/optimize-long-tasks). आम तौर पर, यह JS होती है."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "कुल समय"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "इनपुट में देरी"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "कोई इंटरैक्शन नहीं हुआ"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "फ़ेज़"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "प्रज़ेंटेशन में देरी"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "प्रोसेस होने की अवधि"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "फ़ेज़ के हिसाब से आईएनपी"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "एलसीपी इमेज को एचटीएमएल से तुरंत [खोजे जाने लायक](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) बनाकर और [लेज़ी-लोडिंग से बचकर](https://web.dev/articles/lcp-lazy-loading), एलसीपी को ऑप्टिमाइज़ करें"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority प्रॉपर्टी की वैल्यू high लागू की गई"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority की वैल्यू high लागू की गई"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "लेज़ी लोड वाली प्रॉपर्टी लागू नहीं की गई"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "एलसीपी इमेज, खोजे जाने के शुरुआती समय से {PH1} बाद लोड हुई."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "कोई एलसीपी नहीं मिला"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "कोई एलसीपी संसाधन नहीं मिला, क्योंकि एलसीपी कोई इमेज नहीं है"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "अनुरोध को शुरुआती दस्तावेज़ में खोजा जा सकता है"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "एलसीपी के लिए रिक्वेस्ट डिस्कवरी"}, "models/trace/insights/LCPPhases.ts | description": {"message": "हर [फ़ेज़ के लिए, बेहतर बनाने की खास रणनीतियां होती हैं](https://web.dev/articles/optimize-lcp#lcp-breakdown). एलसीपी का ज़्यादातर समय संसाधनों को लोड करने में लगना चाहिए, न कि देरी में."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "कुल समय"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "एलिमेंट के रेंडर होने में देरी"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "फ़ील्ड p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "कोई एलसीपी नहीं मिला"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "फ़ेज़"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "संसाधन लोड होने में देरी हुई"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "संसाधन लोड होने में लगा समय"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "टाइम टू फ़र्स्ट बाइट"}, "models/trace/insights/LCPPhases.ts | title": {"message": "फ़ेज़ के हिसाब से एलसीपी"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "स्क्रिप्ट"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "सेव की गई बाइट"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfills और Transforms की मदद से, पुराने ब्राउज़र पर JavaScript की नई सुविधाएं इस्तेमाल की जा सकती हैं. हालांकि, मॉडर्न ब्राउज़र को इनमें से कई सुविधाओं की ज़रूरत नहीं पड़ती. [बेसलाइन](https://web.dev/articles/baseline-and-polyfills) सुविधाओं को ट्रांसपाइल न करने के लिए, JavaScript की बिल्ड प्रोसेस में बदलाव किया जा सकता है. यह बदलाव सिर्फ़ तब न करें, जब आपके लिए पुराने ब्राउज़र का इस्तेमाल करना ज़रूरी हो. [जानें कि क्यों ज़्यादातर साइटें ट्रांसपाइल किए बिना ES6+ कोड को डिप्लॉय कर सकती हैं](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "लेगसी JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "एचटीटीपी/1.1 की तुलना में एचटीटीपी/2 और एचटीटीपी/3, कई फ़ायदे देते हैं. जैसे, मल्टीप्लेक्सिंग. [मॉर्डन एचटीटीपी का इस्तेमाल करने के बारे में ज़्यादा जानें](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "एचटीटीपी/1.1 के लिए कोई अनुरोध नहीं है"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "प्रोटोकॉल"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "अनुरोध"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "मॉर्डन एचटीटीपी"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ऑरिजिन"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "अनुरोध"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "सोर्स"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "समय"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "एलसीपी से जुड़ी अनुमानित बचत"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "इस्तेमाल नहीं किया गया प्रीकनेक्ट. देखें कि crossorigin एट्रिब्यूट का इस्तेमाल सही तरीके से किया गया है या नहीं."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "पेज लोड होने की प्रोसेस को बेहतर बनाने के लिए, [एक-दूसरे पर निर्भर रहने वाले ज़रूरी अनुरोधों की चेन न बनाएं](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains). इसके लिए चेन की लेंथ कम रखें और कम साइज़ वाले रिसॉर्स डाउनलोड करें. इसके अलावा, अनचाहे रिसॉर्स डाउनलोड न करें."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "सबसे अहम वेबसाइटों में, [प्रीकनेक्ट](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) (पहले से लोड करने का निर्देश) करने के लिए संकेत जोड़ें. हालांकि, चार से ज़्यादा संकेत नहीं जोड़ें जा सकते."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ऑरिजिन को पहले से कनेक्ट करें"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "क्रिटिकल पाथ की ज़्यादा से ज़्यादा लेटेंसी:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "नेटवर्क से जुड़े एक-दूसरे पर निर्भर करने वाले ज़रूरी अनुरोधों से, रेंडरिंग की प्रोसेस पर कोई असर नहीं पड़ा है"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "पहले से कनेक्ट करने के लिए, कोई और ऑरिजिन उपलब्ध नहीं है"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "कोई भी ऑरिजिन पहले से कनेक्ट नहीं किया गया था"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[पहले से कनेक्ट](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) होने के संकेत, ब्राउज़र को पेज लोड होने से पहले ही कनेक्शन बनाने में मदद करते हैं. इससे, उस ऑरिजिन के लिए पहला अनुरोध करने में लगने वाले समय की बचत होती है. ये वे ऑरिजिन हैं जिनसे पेज पहले से कनेक्ट है."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "पहले से कनेक्ट किए गए ऑरिजिन"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "नेटवर्क डिपेंडेंसी ट्री"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "चार से ज़्यादा preconnect लिंक पाए गए. इनका इस्तेमाल सिर्फ़ अहम कनेक्शन के लिए सावधानी से किया जाना चाहिए."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "इस्तेमाल नहीं किया गया प्रीकनेक्ट. preconnect का इस्तेमाल सिर्फ़ उन ऑरिजिन के लिए करें जिनसे आपका पेज, डेटा लेने का अनुरोध कर सकता है."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "पेज लोड होने की प्रोसेस को बेहतर बनाने के लिए, एक-दूसरे पर निर्भर रहने वाले ज़रूरी अनुरोधों की चेन न बनाएं. इसके लिए चेन की लंबाई कम रखें और कम साइज़ वाले रिसॉर्स डाउनलोड करें. इसके अलावा, अनचाहे रिसॉर्स डाउनलोड न करें."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "अनुरोधों की वजह से, पेज की शुरुआती रेंडरिंग ब्लॉक की जा रही है. इस वजह से, पेज की एलसीपी वैल्यू खराब हो सकती है. [कुछ समय के लिए टालकर या इनलाइन करके](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources), नेटवर्क के इन अनुरोधों को क्रिटिकल पाथ से बाहर ले जाया जा सकता है."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "कुल समय"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "इस नेविगेशन की रेंडरिंग को रोकने का कोई अनुरोध नहीं किया गया है"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "अनुरोध"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "रेंडर होने से रोकने का अनुरोध"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "अगर स्टाइल को फिर से कैलकुलेट करने में ज़्यादा समय लग रहा है, तो सेलेक्टर ऑप्टिमाइज़ेशन से इसे कम किया जा सकता है. [सेलेक्टर ऑप्टिमाइज़ करें](https://developer.chrome.com/docs/devtools/performance/selector-stats) ऐसे सेलेक्टर ऑप्टिमाइज़ करें जिनका स्लो-पाथ प्रतिशत के साथ-साथ प्रोसेस शुरू करने और उसे पूरा करने में लगने वाले समय का प्रतिशत ज़्यादा है. सेलेक्टर मैच करने में लगने वाले समय को कम करने के लिए, आसान सेलेक्टर, कम सेलेक्टर, कम साइज़ वाले डीओएम, और कम एलिमेंट वाले डीओएम इस्तेमाल किए जाते हैं."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "प्रोसेस शुरू करने और उसे पूरा होने के बीच का समय"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "सीएसएस सिलेक्टर से जुड़ा कोई डेटा नहीं मिला. परफ़ॉर्मेंस पैनल की सेटिंग में जाकर, सीएसएस सिलेक्टर के आंकड़े दिखाने की सुविधा चालू करें."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "मैच करने की कोशिशें"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "मिलते-जुलते एलिमेंट की संख्या"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "सीएसएस सिलेक्टर की लागत"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "सबसे ज़्यादा समय लेने वाले सिलेक्टर"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "कुल"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "थ्रेड में लगने वाला मुख्य समय"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "तीसरा पक्ष"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ट्रांसफ़र साइज़"}, "models/trace/insights/ThirdParties.ts | description": {"message": "तीसरे पक्ष के कोड, आपके पेज के लोड होने की परफ़ॉर्मेंस पर गहरा असर डाल सकते हैं. पेज के कॉन्टेंट को प्राथमिकता देने के लिए, [तीसरे पक्ष के कोड वाले कॉन्टेंट को कम करें और उसे धीरे लोड करें](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "तीसरे पक्ष का कोई कॉन्टेंट नहीं मिला"}, "models/trace/insights/ThirdParties.ts | title": {"message": "तीसरे पक्ष"}, "models/trace/insights/Viewport.ts | description": {"message": "अगर व्यूपोर्ट को मोबाइल के लिए ऑप्टिमाइज़ नहीं किया गया है, तो टैप इंटरैक्शन में [300 मि॰से॰ तक की देरी](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) हो सकती है."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "मोबाइल की स्क्रीन पर टैप करके कार्रवाई करने में देरी हुई"}, "models/trace/insights/Viewport.ts | title": {"message": "मोबाइल के लिए व्यूपोर्ट को ऑप्टिमाइज़ करें"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "जीईटी अनुरोध की मदद से लोड किए गए पेज ही बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल कर सकते हैं."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "सिर्फ़ 2XX स्टेटस कोड वाले पेजों को कैश मेमोरी में सेव किया जा सकता है."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome ने कैश मेमोरी में सेव पेज के लिए JavaScript चलाने की कोशिश का पता लगाया."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "जिन पेजों के लिए AppBanner का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "फ़्लैग की मदद से बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद की गई. इस डिवाइस पर यह सुविधा चालू करने के लिए, chrome://flags/#back-forward-cache पर जाएं."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को कमांड लाइन की मदद से बंद किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "कम मेमोरी की वजह से बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "डेलिगेट के लिए, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "प्रीरेंडरर के लिए, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "पेज को कैश मेमोरी में सेव नहीं किया जा सकता, क्योंकि इसमें रजिस्टर किए गए लिसनर के साथ BroadcastChannel इंस्टेंस मौजूद है."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "जिन पेजों के हेडर में cache-control:no-store मौजूद होता है उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "कैश मेमोरी जान-बूझकर मिटाई गई."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "पेज को कैश मेमोरी से हटा दिया गया था, ताकि दूसरे पेज को कैश मेमोरी में सेव करने की अनुमति दी जा सके."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "जिन पेजों पर प्लग इन मौजूद हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "तय नहीं किया है"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "पेज से जाने के बाद भी मीडिया प्लेयर पर मीडिया चल रहा था."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API और सेट ऐक्शन हैंडलर का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "स्क्रीन रीडर वजह से, बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "बैक या फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है, क्योंकि Cache-Control: no-store का इस्तेमाल करने वाले पेज पर कुकी बंद होती हैं."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "खास तौर पर काम करने वाले वर्कर या वर्कलेट का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "इस दस्तावेज़ से जाने से पहले, यह पूरी तरह से लोड नहीं हुआ था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "पेज छोड़कर जाने पर, ऐप्लिकेशन बैनर दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "पेज छोड़कर जाने पर, Chrome पासवर्ड मैनेजर का पेज दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "पेज छोड़कर जाने पर, डीओएम डिस्टलेशन की प्रोसेस जारी थी."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "पेज छोड़कर जाने पर, डीओएम डिस्टलर व्यूअर दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "मैसेज सेवा एपीआई का इस्तेमाल करने वाले एक्सटेंशन की वजह से, बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "अगर कोई एक्सटेंशन लंबे समय से किसी एक कनेक्शन से जुड़ा है, तो उसे बैक/फ़ॉरवर्ड कैश मेमोरी में सेव करने से पहले, कनेक्शन को बंद करना होगा."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "उस एक्सटेंशन ने बैक/फ़ॉरवर्ड कैश मेमोरी में मौजूद फ़्रेम को मैसेज भेजने की कोशिश की जो लंबे समय से किसी एक कनेक्शन से जुड़ा है."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "एक्सटेंशन की वजह से, बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "पेज छोड़कर जाने पर, मोडल डायलॉग दिखाए गए थे, जैसे कि फ़ॉर्म को फिर से सबमिट करने या एचटीटीपी पासवर्ड वाले डायलॉग."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "पेज छोड़कर जाने पर, ऑफ़लाइन पेज दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "पेज छोड़कर जाने पर, Out-Of-Memory Intervention बार दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "पेज छोड़कर जाने पर, अनुमति के अनुरोध दिखाए गए थे."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "पेज छोड़कर जाने पर, पॉप-अप ब्लॉकर दिखाया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "पेज छोड़कर जाने पर, सुरक्षित ब्राउज़िंग की जानकारी दिखाई गई थी."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "सुरक्षित ब्राउज़िंग की मदद से पता चला है कि इस पेज पर नुकसान पहुंचाने वाली जानकारी मौजूद है. इसलिए, पॉप-अप को ब्लॉक कर दिया गया है."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में पेज के मौजूद होने के दौरान एक सर्विस वर्कर को चालू किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "दस्तावेज़ से जुड़ी किसी गड़बड़ी की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद कर दिया गया है."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Fenced<PERSON>rames का इस्तेमाल करने वाले पेजों को बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "पेज को कैश मेमोरी से हटा दिया गया था, ताकि दूसरे पेज को कैश मेमोरी में सेव करने की अनुमति दी जा सके."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "जिन पेजों पर मीडिया स्ट्रीम को ऐक्सेस किया जा सकता है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "जिन पेजों में कोई कॉन्टेंट जोड़ा गया है (जैसे, PDF) वे फ़िलहाल बैक/फ़ॉरवर्ड कैश मेमोरी का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "जिन पेजों के लिए कोई ओपेन IndexedDB कनेक्शन मौजूद है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB इवेंट की वजह से, बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "गलत एपीआई इस्तेमाल किए गए थे."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "जिन पेजों की JavaScript, एक्सटेंशन की मदद से इंजेक्ट की जाती है वे फ़िलहाल बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "जिन पेजों की StyleSheet, एक्सटेंशन की मदद से इंजेक्ट की जाती है वे फ़िलहाल बैक/फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "कोई अंदरूनी गड़बड़ी हुई."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "बैक या फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है, क्योंकि कुछ JavaScript नेटवर्क के अनुरोध को रिसॉर्स,Cache-Control: no-store हेडर के साथ मिला है."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "कीपअलाइव (चालू रखें) के अनुरोध की वजह से, बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा को बंद किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "कीबोर्ड लॉक का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "इस पेज को छोड़ने से पहले, यह पूरी तरह से लोड नहीं हुआ था."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "जिन पेजों के मुख्य संसाधन में cache-control:no-cache मौजूद होता है उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "जिन पेजों के मुख्य संसाधन में cache-control:no-store मौजूद होता है उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "पेज को बैक-फ़ॉरवर्ड कैश मेमोरी से वापस लाने से पहले ही, नेविगेशन को रद्द कर दिया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "पेज को कैश मेमोरी से हटा दिया गया था क्योंकि चालू इंटरनेट कनेक्शन में बहुत ज़्यादा डेटा मिला था. Chrome, कैश मेमोरी में सेव किए गए पेज को मिलने वाले डेटा की मात्रा को सीमित करता है."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "जिन पेजों में इनफ़्लाइट fetch() या XHR मौजूद होता है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "पेज को कैश मेमोरी से हटा दिया गया था, क्योंकि दूसरे वेबलिंक पर भेजने का एक नेटवर्क अनुरोध ऐक्टिव था."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "पेज को कैश मेमोरी से हटा दिया गया था, क्योंकि कोई इंटरनेट कनेक्शन बहुत देर से चालू था. Chrome, कैश मेमोरी में सेव किए गए पेज को मिलने वाले डेटा की अवधि को सीमित करता है."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "जिन पेजों के मान्य रिस्पॉन्स हेड नहीं होते उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "नेविगेशन, मुख्य फ़्रेम की जगह किसी और फ़्रेम में हुआ."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "जिस पेज के लिए इंडेक्स किया गया DB ट्रांज़ैक्शन चल रहा हो वह फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकता."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क को फ़ेच करने का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "जिन पेजों के लिए इन-फ़्लाइट नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "जिन पेजों के लिए इन-फ़्लाइट XHR नेटवर्क का अनुरोध किया गया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Payment<PERSON><PERSON>ger का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "पिक्चर में पिक्चर सुविधा का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "जिन पेजों के लिए प्रिंटिंग यूज़र इंटरफ़ेस (यूआई) दिखता है उनके लिए फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा इस्तेमाल नहीं की जा सकती."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "पेज को 'window.open()' की मदद से खोला गया था और इसका रेफ़रंस दूसरे टैब में मौजूद है या पेज किसी नई विंडो में खुला है."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में पेज रेंडर करने की प्रोसेस बंद हो गई."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में पेज रेंडर करने की प्रोसेस बंद कर दी गई है."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "जिन पेजों के लिए ऑडियो रिकॉर्ड करने की अनुमतियां मांगी गई हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "जिन पेजों ने सेंसर अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "जिन पेजों ने बैकग्राउंड सिंक या अनुमतियां फ़ेच करने का अनुरोध किया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "जिन पेजों ने MIDI अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "जिन पेजों ने सूचनाएं भेजने के लिए अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "जिन पेजों ने डिवाइस के स्टोरेज का ऐक्सेस मांगा है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "जिन पेजों ने वीडियो रिकॉर्ड करने की अनुमतियां मांगी हैं वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "सिर्फ़ उन पेजों को कैश मेमोरी में सेव किया जा सकता है जिनकी यूआरएल स्कीम एचटीटीपी / एचटीटीपीएस है."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "जब यह पेज बैक-फ़ॉरवर्ड कैश मेमोरी में सेव किया गया था, तब उस पर किसी सर्विस वर्कर ने दावा किया था."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "किसी सर्विस वर्कर ने बैक-फ़ॉरवर्ड कैश मेमोरी में मौजूद पेज को MessageEvent भेजने की कोशिश की."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "एक पेज के बैक-फ़ॉरवर्ड कैश मेमोरी में मौजूद होने के दौरान, ServiceWorker का रजिस्ट्रेशन रद्द किया गया."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "सर्विस वर्कर ऐक्टिवेट होने की वजह से, इस पेज को बैक-फ़ॉरवर्ड कैश मेमोरी से हटा दिया गया था."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome रीस्टार्ट हो गया और बैक-फ़ॉरवर्ड कैश मेमोरी में सेव की गई एंट्री मिट गईं."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecog<PERSON><PERSON> का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "पेज पर मौजूद किसी iframe ने नेविगेशन शुरू किया, जो पूरा नहीं हो सका."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "जिन पेजों के सबरिसोर्स में cache-control:no-cache मौजूद होता है उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "जिन पेजों के सबरिसोर्स में cache-control:no-store मौजूद होता है उन्हें बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किया जा सकता."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी में रहने की समय-सीमा पूरी करने की वजह से, पेज एक्सपायर हो गया."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "पेज को बैक-फ़ॉरवर्ड कैश मेमोरी में सेव करने में लगने वाले समय की सीमा खत्म हो चुकी है (इस बात की ज़्यादा संभावना है कि ऐसा pagehide हैंडलर के ज़्यादा समय तक चलते रहने की वजह से हुआ होगा)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "पेज के मुख्य फ़्रेम में अनलोड हैंडलर मौजूद है."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "पेज के किसी सब फ़्रेम में कोई अनलोड हैंडलर मौजूद है."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ब्राउज़र ने उपयोगकर्ता एजेंट को बदलने वाला हेडर बदल दिया है."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "जिन पेजों ने वीडियो या ऑडियो रिकॉर्ड करने का ऐक्सेस दिया है वे फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC की सुविधा वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किए जा सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है, क्योंकि WebRTC का इस्तेमाल किया जा चुका है."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket की सुविधा वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किए जा सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है, क्योंकि WebSocket का इस्तेमाल किया जा चुका है."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport की सुविधा वाले पेज, बैक-फ़ॉरवर्ड कैश मेमोरी में सेव नहीं किए जा सकते."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा बंद है, क्योंकि WebTransport का इस्तेमाल किया जा चुका है."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR का इस्तेमाल करने वाले पेज, फ़िलहाल बैक-फ़ॉरवर्ड कैश मेमोरी की सुविधा का इस्तेमाल नहीं कर सकते."}}