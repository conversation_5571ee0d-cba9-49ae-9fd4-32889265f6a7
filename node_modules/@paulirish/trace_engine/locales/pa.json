{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers ਹੈਂਡਲਿੰਗ ਵਿੱਚ ਇਖਤਿਆਰੀਕਰਨ ਨੂੰ ਵਾਈਲਡ-ਕਾਰਡ ਚਿੰਨ੍ਹ (*) ਰਾਹੀਂ ਕਵਰ ਨਹੀਂ ਕੀਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ਪੂਰਵ-ਨਿਰਧਾਰਿਤ Cast ਏਕੀਕਰਨ ਨੂੰ ਬੰਦ ਕਰਨ ਲਈ -internal-media-controls-overlay-cast-button ਚੋਣਕਾਰ ਦੀ ਵਰਤੋਂ ਕਰਨ ਦੀ ਬਜਾਏ disableRemotePlayback ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਵਰਤਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ।"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS ਦੀ ਦਿੱਖ ਦਾ ਮੁੱਲ slider-vertical ਮਿਆਰੀ ਨਹੀਂ ਹੈ ਅਤੇ ਉਸਨੂੰ ਹਟਾਇਆ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ਉਨ੍ਹਾਂ ਸਰੋਤ ਬੇਨਤੀਆਂ ਨੂੰ ਬਲਾਕ ਕੀਤਾ ਗਿਆ ਹੈ ਜਿਨ੍ਹਾਂ ਦੇ URL ਵਿੱਚ ਹਟਾਈ ਗਈ ਖਾਲੀ ਥਾਂ \\(n|r|t) ਅੱਖਰ-ਚਿੰਨ੍ਹ ਅਤੇ ਮੁਕਾਬਲਤਨ ਘੱਟ ਅੱਖਰ-ਚਿੰਨ੍ਹ (<) ਦੋਵੇਂ ਸ਼ਾਮਲ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਨਵੀਆਂ ਲਾਈਨਾਂ ਨੂੰ ਹਟਾਓ ਅਤੇ ਇਨ੍ਹਾਂ ਸਰੋਤਾਂ ਨੂੰ ਲੋਡ ਕਰਨ ਲਈ ਤੱਤ ਵਿਸ਼ੇਸ਼ਤਾ ਮੁੱਲਾਂ ਵਰਗੀਆਂ ਥਾਵਾਂ ਤੋਂ ਮੁਕਾਬਲਤਨ ਘੱਟ ਅੱਖਰ-ਚਿੰਨ੍ਹਾਂ ਨੂੰ ਇਨਕੋਡ ਕਰੋ।"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ, ਇਸਦੀ ਬਜਾਏ ਮਿਆਰੀ API: Navigation Timing 2 ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ, ਇਸਦੀ ਬਜਾਏ ਮਿਆਰੀ API: Paint Timing ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ, ਇਸਦੀ ਬਜਾਏ ਮਿਆਰੀ API: Navigation Timing 2 ਵਿੱਚ nextHopProtocol ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) ਅੱਖਰ-ਚਿੰਨ੍ਹ ਵਾਲੀਆਂ ਕੁਕੀਜ਼ ਨੂੰ ਕੱਟਣ ਦੀ ਬਜਾਏ ਅਸਵੀਕਾਰ ਕਰ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain ਨੂੰ ਸੈੱਟ ਕਰ ਕੇ ਸਮਾਨ-ਮੂਲ ਨੀਤੀ ਨੂੰ ਢਿੱਲ ਦੇਣਾ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਪੂਰਵ-ਨਿਰਧਾਰਿਤ ਤੌਰ 'ਤੇ ਇਸਨੂੰ ਬੰਦ ਕਰ ਦਿੱਤਾ ਜਾਵੇਗਾ। ਇਹ ਨਾਪਸੰਦਗੀ ਚਿਤਾਵਨੀ document.domain ਨੂੰ ਸੈੱਟ ਕਰ ਕੇ ਚਾਲੂ ਕੀਤੀ ਗਈ ਕ੍ਰਾਸ-ਮੂਲ ਪਹੁੰਚ ਲਈ ਹੈ।"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "ਕ੍ਰਾਸ-ਮੂਲ iframes ਤੋਂ window.alert ਨੂੰ ਟ੍ਰਿਗਰ ਕਰਨ ਦੀ ਸੁਵਿਧਾ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਭਵਿੱਖ ਵਿੱਚ ਇਸਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "ਕ੍ਰਾਸ-ਮੂਲ iframes ਤੋਂ window.confirm ਨੂੰ ਟ੍ਰਿਗਰ ਕਰਨ ਦੀ ਸੁਵਿਧਾ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਭਵਿੱਖ ਵਿੱਚ ਇਸਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ਡਾਟੇ ਦੇ ਲਈ ਸਹਾਇਤਾ: SVGUseElement ਵਿੱਚ ਮੌਜੂਦ URL ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਸ ਨੂੰ ਭਵਿੱਖ ਵਿੱਚ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() ਅਤੇ watchPosition() ਹੁਣ ਅਸੁਰੱਖਿਅਤ ਮੂਲਾਂ 'ਤੇ ਕੰਮ ਨਹੀਂ ਕਰਦੇ। ਇਸ ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਵਰਤਣ ਲਈ, ਤੁਹਾਨੂੰ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ HTTPS ਵਰਗੇ ਕਿਸੇ ਸੁਰੱਖਿਅਤ ਮੂਲ 'ਤੇ ਸਵਿੱਚ ਕਰਨ ਬਾਰੇ ਵਿਚਾਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ। ਹੋਰ ਵੇਰਵਿਆਂ ਲਈ https://goo.gle/chrome-insecure-origins ਦੇਖੋ।"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "ਅਸੁਰੱਖਿਅਤ ਮੂਲਾਂ 'ਤੇ getCurrentPosition() ਅਤੇ watchPosition() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ। ਇਸ ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਵਰਤਣ ਲਈ, ਤੁਹਾਨੂੰ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ HTTPS ਵਰਗੇ ਕਿਸੇ ਸੁਰੱਖਿਅਤ ਮੂਲ 'ਤੇ ਸਵਿੱਚ ਕਰਨ ਬਾਰੇ ਵਿਚਾਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ। ਹੋਰ ਵੇਰਵਿਆਂ ਲਈ https://goo.gle/chrome-insecure-origins ਦੇਖੋ।"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ਹੁਣ ਅਸੁਰੱਖਿਅਤ ਮੂਲਾਂ 'ਤੇ ਕੰਮ ਨਹੀਂ ਕਰਦਾ। ਇਸ ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਵਰਤਣ ਲਈ, ਤੁਹਾਨੂੰ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ HTTPS ਵਰਗੇ ਕਿਸੇ ਸੁਰੱਖਿਅਤ ਮੂਲ 'ਤੇ ਸਵਿੱਚ ਕਰਨ ਬਾਰੇ ਵਿਚਾਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ। ਹੋਰ ਵੇਰਵਿਆਂ ਲਈ https://goo.gle/chrome-insecure-origins ਦੇਖੋ।"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> ਜਾਂ <section> ਵਿੱਚ ਇੱਕ <h1> ਟੈਗ ਮਿਲਿਆ ਹੈ ਜਿਸਦਾ ਕੋਈ ਨਿਰਧਾਰਿਤ ਫ਼ੌਂਟ-ਆਕਾਰ ਨਹੀਂ ਹੈ। ਇਸ ਬ੍ਰਾਊਜ਼ਰ ਵਿੱਚ ਆਉਣ ਵਾਲੇ ਸਮੇਂ ਵਿੱਚ ਇਸ ਸਿਰਲੇਖ ਲਿਖਤ ਦਾ ਆਕਾਰ ਬਦਲ ਜਾਵੇਗਾ। ਹੋਰ ਜਾਣਕਾਰੀ ਲਈ https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 ਦੇਖੋ।"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ RTCPeerConnectionIceErrorEvent.address ਜਾਂ RTCPeerConnectionIceErrorEvent.port ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ਡਿਜੀਟਲ ਕ੍ਰੀਡੈਂਸ਼ੀਅਲ ਦੀ ਬੇਨਤੀ ਕਰਨ ਲਈ ਵਰਤੇ ਗਏ ਇਸ ਫਾਰਮੈਟ navigator.credentials.get() 'ਤੇ ਰੋਕ ਲਗਾ ਦਿੱਤੀ ਗਈ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਨਵੇਂ ਫਾਰਮੈਟ ਦੀ ਵਰਤੋਂ ਕਰਨ ਲਈ ਆਪਣੀ ਕਾਲ ਨੂੰ ਅੱਪਡੇਟ ਕਰੋ।"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment ਸਰਵਿਸ ਵਰਕਰ ਇਵੈਂਟ ਤੋਂ ਪ੍ਰਾਪਤ ਵਪਾਰੀ ਮੂਲ ਅਤੇ ਇਖਤਿਆਰੀ ਡਾਟਾ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਅਤੇ ਇਸਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "ਵੈੱਬਸਾਈਟ ਨੇ ਕਿਸੇ ਨੈੱਟਵਰਕ ਤੋਂ ਇੱਕ ਅਜਿਹੇ ਉਪ-ਸਰੋਤ ਦੀ ਬੇਨਤੀ ਕੀਤੀ ਹੈ ਜਿਸ ਤੱਕ ਇਹ ਸਿਰਫ਼ ਆਪਣੇ ਵਰਤੋਂਕਾਰਾਂ ਦੀ ਵਿਸ਼ੇਸ਼-ਅਧਿਕਾਰ ਪ੍ਰਾਪਤ ਨੈੱਟਵਰਕ ਸਥਿਤੀ ਦੇ ਕਾਰਨ ਪਹੁੰਚ ਕਰ ਸਕਦੀ ਹੈ। ਇਨ੍ਹਾਂ ਬੇਨਤੀਆਂ ਨਾਲ ਗੈਰ-ਜਨਤਕ ਡੀਵਾਈਸ ਅਤੇ ਸਰਵਰ ਇੰਟਰਨੈੱਟ 'ਤੇ ਜਨਤਕ ਹੋ ਜਾਂਦੇ ਹਨ, ਜਿਸ ਕਰਕੇ ਜਾਅਲੀ ਕ੍ਰਾਸ-ਸਾਈਟ ਬੇਨਤੀ (CSRF) ਸੰਬੰਧੀ ਹਮਲੇ ਅਤੇ/ਜਾਂ ਜਾਣਕਾਰੀ ਲੀਕ ਹੋਣ ਦਾ ਜੋਖਮ ਵਧ ਜਾਂਦਾ ਹੈ। ਇਨ੍ਹਾਂ ਜੋਖਮਾਂ ਨੂੰ ਘੱਟ ਕਰਨ ਲਈ, Chrome ਗੈਰ-ਸੁਰੱਖਿਅਤ ਸੰਦਰਭਾਂ ਤੋਂ ਸ਼ੁਰੂ ਕੀਤੇ ਜਾਣ 'ਤੇ ਗੈਰ-ਜਨਤਕ ਉਪ-ਸਰੋਤਾਂ ਲਈ ਬੇਨਤੀਆਂ ਨੂੰ ਨਾਪਸੰਦ ਕਰ ਦਿੰਦਾ ਹੈ ਅਤੇ ਉਨ੍ਹਾਂ ਨੂੰ ਬਲਾਕ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰ ਦੇਵੇਗਾ।"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups ਦੇ dailyUpdateUrl ਖੇਤਰ ਨੂੰ joinAdInterestGroup() ਦੇ ਕੋਲ ਭੇਜਿਆ ਗਿਆ ਹੈ, ਇਸਦੇ ਵਿਵਹਾਰ ਨੂੰ ਵਧੇਰੇ ਸਟੀਕਤਾ ਨਾਲ ਦਰਸਾਉਣ ਲਈ ਇਸਦਾ ਨਾਮ ਬਦਲ ਕੇ updateUrl ਕੀਤਾ ਗਿਆ ਹੈ।"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator 'ਤੇ ਰੋਕ ਲਗਾ ਦਿੱਤੀ ਗਈ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ Intl.Segmenter ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS ਨੂੰ file: URL ਤੋਂ ਉਦੋਂ ਤੱਕ ਲੋਡ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ ਜਦੋਂ ਤੱਕ ਉਹ .css ਫ਼ਾਈਲ ਐਕਸਟੈਂਸ਼ਨ ਨਾਲ ਸਮਾਪਤ ਨਹੀਂ ਹੁੰਦੇ ਹਨ।"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "ਵਿਵਰਨ ਵਿੱਚ ਕੀਤੀ ਗਈ ਤਬਦੀਲੀ ਦੇ ਕਾਰਨ remove() ਦੀ ਬਿਖਮਕਾਲੀ ਰੇਂਜ ਹਟਾਉਣ ਦੀ ਪ੍ਰਕਿਰਿਆ ਰੱਦ ਕਰਨ ਲਈ SourceBuffer.abort() ਦੀ ਵਰਤੋਂ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ। ਭਵਿੱਖ ਵਿੱਚ ਸਹਾਇਤਾ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ। ਤੁਹਾਨੂੰ ਇਸਦੀ ਬਜਾਏ updateend ਇਵੈਂਟ ਨੂੰ ਸੁਣਨਾ ਚਾਹੀਦਾ ਹੈ। abort() ਦਾ ਉਦੇਸ਼ ਸਿਰਫ਼ ਬਿਖਮਕਾਲੀ ਮੀਡੀਆ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਰੱਦ ਕਰਨਾ ਜਾਂ ਪਾਰਸਰ ਸਥਿਤੀ ਨੂੰ ਰੀਸੈੱਟ ਕਰਨਾ ਹੈ।"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "ਵਿਵਰਨ ਵਿੱਚ ਕੀਤੀ ਗਈ ਤਬਦੀਲੀ ਦੇ ਕਾਰਨ MediaSource.duration ਨੂੰ ਬਫ਼ਰ ਕੀਤੇ ਹਰ ਕੋਡੇਡ ਫ੍ਰੇਮ ਦੇ ਸਭ ਤੋਂ ਵੱਧ ਪੇਸ਼ਕਾਰੀ ਟਾਈਮਸਟੈਂਪ ਦੇ ਹੇਠਾਂ ਸੈੱਟ ਕਰਨਾ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ। ਕੱਟੇ ਹੋਏ ਬਫ਼ਰ ਮੀਡੀਆ ਨੂੰ ਅਪ੍ਰਤੱਖ ਤੌਰ 'ਤੇ ਹਟਾਉਣ ਲਈ ਸਹਾਇਤਾ ਨੂੰ ਭਵਿੱਖ ਵਿੱਚ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ। ਇਸਦੀ ਬਜਾਏ ਤੁਹਾਨੂੰ ਸਾਰੇ sourceBuffers 'ਤੇ ਸਪਸ਼ਟ remove(newDuration, oldDuration) ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ, ਜਿੱਥੇ newDuration < oldDuration ਹੈ।"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI ਵਰਤੋਂ ਕਰਨ ਦੀ ਇਜਾਜ਼ਤ ਮੰਗੇਗਾ, ਭਾਵੇਂ MIDIOptions ਵਿੱਚ sysex ਨੂੰ ਨਿਰਧਾਰਿਤ ਨਾ ਕੀਤਾ ਗਿਆ ਹੋਵੇ।"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification API ਨੂੰ ਹੁਣ ਅਸੁਰੱਖਿਅਤ ਮੂਲਾਂ ਤੋਂ ਨਹੀਂ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਤੁਹਾਨੂੰ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ HTTPS ਵਰਗੇ ਕਿਸੇ ਸੁਰੱਖਿਅਤ ਮੂਲ 'ਤੇ ਸਵਿੱਚ ਕਰਨ ਬਾਰੇ ਵਿਚਾਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ। ਹੋਰ ਵੇਰਵਿਆਂ ਲਈ https://goo.gle/chrome-insecure-origins ਦੇਖੋ।"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Notification API ਲਈ ਇਜਾਜ਼ਤ ਹੁਣ ਕ੍ਰਾਸ-ਮੂਲ iframe ਤੋਂ ਨਹੀਂ ਮੰਗੀ ਜਾ ਸਕਦੀ ਹੈ। ਇਸਦੀ ਬਜਾਏ ਤੁਹਾਨੂੰ ਉੱਚ-ਪੱਧਰੀ ਫ੍ਰੇਮ ਤੋਂ ਇਜਾਜ਼ਤ ਦੀ ਬੇਨਤੀ ਕਰਨ ਜਾਂ ਕੋਈ ਨਵੀਂ ਵਿੰਡੋ ਖੋਲ੍ਹਣ ਬਾਰੇ ਵਿਚਾਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ।"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap ਵਿੱਚ imageOrientation: 'none' ਵਿਕਲਪ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ, ਇਸ ਦੀ ਬਜਾਏ {imageOrientation: 'from-image'} ਵਿਕਲਪ ਦੇ ਨਾਲ createImageBitmap ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "ਤੁਹਾਡਾ ਪਾਰਟਨਰ ਕਿਸੇ ਅਪ੍ਰਚਲਿਤ (D)TLS ਵਰਜਨ ਲਈ ਗੱਲਬਾਤ ਕਰ ਰਿਹਾ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਨੂੰ ਠੀਕ ਕਰਨ ਲਈ ਆਪਣੇ ਪਾਰਟਨਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, ਵੀਡੀਓ ਅਤੇ ਕੈਨਵਸ ਟੈਗਾਂ 'ਤੇ overflow: visible ਨੂੰ ਨਿਰਧਾਰਿਤ ਕਰਨ ਨਾਲ ਤੱਤ ਦੀਆਂ ਸੀਮਾਵਾਂ ਤੋਂ ਬਾਹਰ ਦ੍ਰਿਸ਼ ਸਮੱਗਰੀ ਬਣ ਸਕਦੀ ਹੈ। https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md ਦੇਖੋ।"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ ਭੁਗਤਾਨ ਹੈਂਡਲਰਾਂ ਲਈ just-in-time ਸਥਾਪਨਾ ਵਰਤੋ।"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "ਤੁਹਾਡੀ PaymentRequest ਕਾਲ ਨੇ ਸਮੱਗਰੀ-ਸੁਰੱਖਿਆ-ਨੀਤੀ (CSP) connect-src ਡਾਇਰੈਕਟਿਵ ਨੂੰ ਬਾਈਪਾਸ ਕੀਤਾ। ਇਸ ਬਾਈਪਾਸ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ PaymentRequest API (supportedMethods ਖੇਤਰ ਵਿੱਚ) ਤੋਂ CSP connect-src ਡਾਇਰੈਕਟਿਵ ਵਿੱਚ ਭੁਗਤਾਨ ਵਿਧੀ ਪਛਾਣਕਰਤਾ ਸ਼ਾਮਲ ਕਰੋ।"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ ਮਿਆਰੀ navigator.storage ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> ਪੇਰੈਂਟ ਵਾਲਾ <source src> ਅਵੈਧ ਹੈ ਅਤੇ ਇਸ ਲਈ ਇਸਨੂੰ ਅਣਡਿੱਠ ਕੀਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ <source srcset> ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame ਵਿਕਰੇਤਾ-ਵਿਸ਼ੇਸ਼ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ ਮਿਆਰੀ cancelAnimationFrame ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame ਵਿਕਰੇਤਾ-ਵਿਸ਼ੇਸ਼ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ ਮਿਆਰੀ requestAnimationFrame ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ, ਇਸ ਦੀ ਬਜਾਏ Document.fullscreenElement ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ Element.requestFullscreen() ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ Element.requestFullscreen() ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ Document.exitFullscreen() ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ Document.exitFullscreen() ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ Document.fullscreenEnabled ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "ਅਸੀਂ API chrome.privacy.websites.privacySandboxEnabled ਨੂੰ ਬੰਦ ਕਰ ਰਹੇ ਹਾਂ, ਹਾਲਾਂਕਿ ਇਹ M113 ਰਿਲੀਜ਼ ਹੋਣ ਤੱਕ ਪਿਛਲੇ ਵਰਜਨ ਨਾਲ ਅਨੁਰੂਪ ਹੋਣ ਦੇ ਕਾਰਨ ਕਿਰਿਆਸ਼ੀਲ ਰਹੇਗਾ। ਇਸਦੀ ਬਜਾਏ, ਕਿਰਪਾ ਕਰਕੇ chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled ਅਤੇ chrome.privacy.websites.adMeasurementEnabled ਦੀ ਵਰਤੋਂ ਕਰੋ। https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled ਦੇਖੋ।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement ਸੰਬੰਧੀ ਪਾਬੰਦੀ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਤੁਸੀਂ ਇਸ ਪਾਬੰਦੀ ਲਈ false ਮੁੱਲ ਨਿਰਧਾਰਿਤ ਕੀਤਾ ਹੈ, ਜਿਸਦੀ ਵਿਆਖਿਆ 'ਹਟਾਈ ਗਈ SDES key negotiation ਵਿਧੀ ਦੀ ਵਰਤੋਂ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼' ਵਜੋਂ ਕੀਤੀ ਗਈ ਹੈ। ਇਸ ਪ੍ਰਕਾਰਜਾਤਮਕਤਾ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ; ਇਸਦੀ ਬਜਾਏ ਉਸ ਸੇਵਾ ਦੀ ਵਰਤੋਂ ਕਰੋ ਜੋ DTLS key negotiation ਦਾ ਸਮਰਥਨ ਕਰਦੀ ਹੈ।"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement ਸੰਬੰਧੀ ਪਾਬੰਦੀ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਤੁਸੀਂ ਇਸ ਪਾਬੰਦੀ ਲਈ true ਮੁੱਲ ਨਿਰਧਾਰਿਤ ਕੀਤਾ ਹੈ, ਜਿਸਦਾ ਕੋਈ ਪ੍ਰਭਾਵ ਨਹੀਂ ਸੀ, ਪਰ ਤੁਸੀਂ ਸਪਸ਼ਟਤਾ ਲਈ ਇਸ ਪਾਬੰਦੀ ਨੂੰ ਹਟਾ ਸਕਦੇ ਹੋ।"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "ਕਾਲਬੈਕ-ਆਧਾਰਿਤ getStats() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ। ਇਸ ਦੀ ਬਜਾਏ ਵਿਵਰਨ-ਅਨੁਕੂਲ getStats() ਵਰਤੋ।"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਦੀ ਬਜਾਏ Selection.modify() ਨੂੰ ਵਰਤੋ।"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ਉਨ੍ਹਾਂ ਉਪ-ਸਰੋਤ ਬੇਨਤੀਆਂ ਨੂੰ ਬਲਾਕ ਕੀਤਾ ਗਿਆ ਹੈ ਜਿਨ੍ਹਾਂ ਦੇ URL ਵਿੱਚ ਪਰੋਏ ਹੋਏ ਕ੍ਰੀਡੈਂਸ਼ੀਅਲ (ਜਿਵੇਂ ਕਿ **********************/) ਸ਼ਾਮਲ ਹਨ।"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy ਵਿਕਲਪ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਸਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer ਨੂੰ ਕ੍ਰਾਸ-ਮੂਲ ਆਈਸੋਲੇਸ਼ਨ ਦੀ ਲੋੜ ਪਵੇਗੀ। ਹੋਰ ਵੇਰਵਿਆਂ ਲਈ https://developer.chrome.com/blog/enabling-shared-array-buffer/ ਦੇਖੋ।"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "ਵਰਤੋਂਕਾਰ ਕਿਰਿਆਸ਼ੀਲਤਾ ਤੋਂ ਬਿਨਾਂ speechSynthesis.speak() ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਸਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | UnloadHandler": {"message": "ਅਣਲੋਡ ਇਵੈਂਟ ਲਿਸਨਰਾਂ ਨੂੰ ਨਾਪਸੰਦ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਉਨ੍ਹਾਂ ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਜਾਵੇਗਾ।"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer ਦੀ ਵਰਤੋਂ ਜਾਰੀ ਰੱਖਣ ਲਈ ਐਕਸਟੈਂਸ਼ਨਾਂ ਨੂੰ ਕ੍ਰਾਸ-ਮੂਲ ਆਈਸੋਲੇਸ਼ਨ ਦੀ ਚੋਣ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ। https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ ਦੇਖੋ।"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਰੋਕਿਆ ਗਿਆ ਹੈ, ਇਸਦੀ ਬਜਾਏ GPUAdapterInfo isFallbackAdapter ਵਿਸ਼ੇਸ਼ਤਾ ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest ਵਿੱਚ UTF-16 ਜਵਾਬ json ਨਾਲ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "ਮੁੱਖ ਥ੍ਰੈੱਡ 'ਤੇ ਸਮਕਾਲੀ XMLHttpRequest ਨੂੰ ਹਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ ਕਿਉਂਕਿ ਇਸਦਾ ਵਰਤੋਂਕਾਰ ਦੇ ਅਨੁਭਵ 'ਤੇ ਨੁਕਸਾਨਦੇਹ ਪ੍ਰਭਾਵ ਪੈਂਦਾ ਹੈ। ਹੋਰ ਮਦਦ ਲਈ, https://xhr.spec.whatwg.org/ ਦੇਖੋ।"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ਐਨੀਮੇਸ਼ਨ"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਉਦੋਂ ਹੁੰਦੇ ਹਨ ਜਦੋਂ ਵਰਤੋਂਕਾਰ ਦੀ ਅੰਤਰਕਿਰਿਆ ਦੀ ਅਣਹੋਂਦ ਵਿੱਚ ਤੱਤ ਆਪਣਾ ਥਾਂ ਬਦਲਦੇ ਹਨ। [ਖਾਕਾ ਸ਼ਿਫਟਾਂ ਦੇ ਕਾਰਨਾਂ ਦੀ ਜਾਂਚ-ਪੜਤਾਲ ਕਰੋ](https://web.dev/articles/optimize-cls), ਜਿਵੇਂ ਕਿ ਪੰਨਾ ਲੋਡ ਹੋਣ ਦੌਰਾਨ ਤੱਤ ਜੋੜੇ ਜਾਂਦੇ ਹਨ ਜਾਂ ਹਟਾਏ ਜਾਂਦੇ ਹਨ ਜਾਂ ਉਨ੍ਹਾਂ ਦੇ ਫ਼ੌਂਟ ਬਦਲੇ ਜਾਂਦੇ ਹਨ।"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ਫ਼ੌਂਟ ਦੀ ਬੇਨਤੀ"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "ਇੰਜੈਕਟ ਕੀਤਾ ਗਿਆ iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਕਲਸਟਰ @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਦੇ ਕਾਰਨਾਂ ਦਾ ਪਤਾ ਨਹੀਂ ਲਗਾਇਆ ਜਾ ਸਕਿਆ"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "ਕੋਈ ਖਾਕਾ ਸ਼ਿਫਟ ਨਹੀਂ"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਦੇ ਕਾਰਨ"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਦੇ ਮੁੱਖ ਕਾਰਨ"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "ਸਭ ਤੋਂ ਖਰਾਬ ਕਲਸਟਰ"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "ਖਾਕਾ ਸ਼ਿਫਟ ਦਾ ਸਭ ਤੋਂ ਖਰਾਬ ਕਲਸਟਰ"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "ਕੈਸ਼ੇ TTL"}, "models/trace/insights/Cache.ts | description": {"message": "ਲੰਬਾ ਕੈਸ਼ੇ ਲਾਈਫਟਾਈਮ ਤੁਹਾਡੇ ਪੰਨੇ 'ਤੇ ਦੁਹਰਾਈਆਂ ਜਾਣ ਵਾਲੀਆਂ ਫੇਰੀਆਂ ਦੀ ਗਤੀ ਨੂੰ ਤੇਜ਼ ਕਰ ਸਕਦਾ ਹੈ। [ਹੋਰ ਜਾਣੋ](https://web.dev/uses-long-cache-ttl/)।"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "ਅਕੁਸ਼ਲ ਕੈਸ਼ੇ ਨੀਤੀਆਂ ਵਾਲੀ ਕੋਈ ਬੇਨਤੀ ਨਹੀਂ"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} ਹੋਰ"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "ਬੇਨਤੀ ਕਰੋ"}, "models/trace/insights/Cache.ts | title": {"message": "ਕੁਸ਼ਲ ਕੈਸ਼ੇ ਲਾਈਫਟਾਈਮ ਦੀ ਵਰਤੋਂ ਕਰੋ"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM ਦੇ ਵੱਡੇ ਆਕਾਰ ਕਰਕੇ, ਸਟਾਈਲ ਗਣਨਾ ਅਤੇ ਖਾਕੇ ਰੀਫ਼ਲੋ ਦੀ ਪ੍ਰਕਿਰਿਆ ਲੰਬੀ ਹੋ ਸਕਦੀ ਹੈ, ਜਿਸ ਕਰਕੇ ਪੰਨੇ ਦੀ ਪ੍ਰਤਿਕਿਰਿਆ ਦੇਣ ਦਾ ਸਮਾਂ ਪ੍ਰਭਾਵਿਤ ਹੋ ਸਕਦਾ ਹੈ। DOM ਦੇ ਵੱਡੇ ਆਕਾਰ ਕਰਕੇ ਮੈਮੋਰੀ ਦੀ ਵਰਤੋਂ ਵੀ ਵੱਧ ਜਾਵੇਗੀ। [ਬਹੁਤ ਜ਼ਿਆਦਾ ਵੱਡੇ DOM ਆਕਾਰ ਤੋਂ ਬਚਣ ਦਾ ਤਰੀਕਾ ਜਾਣੋ](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)।"}, "models/trace/insights/DOMSize.ts | element": {"message": "ਤੱਤ"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "ਜ਼ਿਆਦਾਤਰ ਸ਼ਾਖਾਵਾਂ"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM ਦੀ ਗਹਿਰਾਈ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "ਅੰਕੜੇ"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM ਦੇ ਆਕਾਰ ਨੂੰ ਸੁਯੋਗ ਬਣਾਓ"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "ਕੁੱਲ ਤੱਤ"}, "models/trace/insights/DOMSize.ts | value": {"message": "ਮੁੱਲ"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "ਤੁਹਾਡੀ ਪਹਿਲੀ ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਬਹੁਤ ਹੀ ਮਹੱਤਵਪੂਰਨ ਹੈ।  ਇਸਦੀ ਵਿਲੰਬਤਾ ਨੂੰ ਘੱਟ ਕਰਨ ਲਈ, ਰੀਡਾਇਰੈਕਟਾਂ ਤੋਂ ਬਚੋ, ਪੱਕਾ ਕਰੋ ਕਿ ਸਰਵਰ ਤੇਜ਼ੀ ਨਾਲ ਜਵਾਬ ਦੇਵੇ ਅਤੇ ਲਿਖਤ ਨੂੰ ਨਪੀੜਣ ਦੀ ਸੁਵਿਧਾ ਚਾਲੂ ਕਰੋ।"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "ਰੀਡਾਇਰੈਕਟ ਕਰਨ ਵਾਲੇ ਲਿੰਕ ਸੀ ({PH1} ਰੀਡਾਇਰੈਕਟ, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "ਸਰਵਰ ਹੌਲੀ ਚੱਲ ਰਿਹਾ ਹੈ ({PH1} ਤੱਕ ਇੰਤਜ਼ਾਰ ਕੀਤਾ)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "ਕੋਈ ਨਪੀੜਨ ਲਾਗੂ ਨਹੀਂ ਕੀਤਾ ਗਿਆ"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "ਰੀਡਾਇਰੈਕਟ ਕਰਨ ਵਾਲੇ ਲਿੰਕ ਨਹੀਂ ਹਨ"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "ਸਰਵਰ ਤੇਜ਼ੀ ਨਾਲ ਜਵਾਬ ਦਿੰਦਾ ਹੈ ({PH1} ਤੱਕ ਇੰਤਜ਼ਾਰ ਕੀਤਾ)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ਲਿਖਤ ਨਪੀੜਨ ਲਾਗੂ ਹੁੰਦਾ ਹੈ"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "ਰੀਡਾਇਰੈਕਟ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "ਸਰਵਰ ਤੋਂ ਜਵਾਬ ਮਿਲਣ ਵਿੱਚ ਲੱਗਣ ਵਾਲਾ ਸਮਾਂ"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ਦਸਤਾਵੇਜ਼ ਦੀ ਬੇਨਤੀ ਵਿੱਚ ਵਿਲੰਬਤਾ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "ਫ਼ਾਈਲ ਅਸਕੁੰਚਿਤ ਡਾਊਨਲੋਡ ਹੋ ਰਹੀ ਹੈ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ਡੁਪਲੀਕੇਟ ਬਾਈਟ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ਸਰੋਤ"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "ਨੈੱਟਵਰਕ ਸਰਗਰਮੀ ਕਰਕੇ ਖਪਤ ਕੀਤੀਆਂ ਬੇਲੋੜੀਆਂ ਬਾਈਟਾਂ ਨੂੰ ਘਟਾਉਣ ਲਈ ਬੰਡਲਾਂ ਤੋਂ ਵੱਡੇ, ਡੁਪਲੀਕੇਟ JavaScript ਮਾਡਿਊਲਾਂ ਨੂੰ ਹਟਾਓ।"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ਡੁਪਲੀਕੇਟ JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "[font-display](https://developer.chrome.com/blog/font-display) ਨੂੰ swap ਜਾਂ optional 'ਤੇ ਸੈੱਟ ਕਰਨ ਬਾਰੇ ਵਿਚਾਰ ਕਰੋ ਤਾਂ ਜੋ ਇਹ ਯਕੀਨੀ ਬਣਾਇਆ ਜਾ ਸਕੇ ਕਿ ਲਿਖਤ ਨਿਰੰਤਰ ਰੂਪ ਵਿੱਚ ਦਿਖਾਈ ਦਿੰਦੀ ਹੈ। [ਫ਼ੌਂਟ ਮਾਪਕ ਓਵਰਰਾਈਡਾਂ](https://developer.chrome.com/blog/font-fallbacks) ਨਾਲ ਖਾਕਾ ਸ਼ਿਫਟਾਂ ਨੂੰ ਘੱਟ ਕਰਨ ਲਈ, swap ਨੂੰ ਹੋਰ ਸੁਯੋਗ ਬਣਾਇਆ ਜਾ ਸਕਦਾ ਹੈ।"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ਫੌਂਟ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ਫ਼ੌਂਟ ਡਿਸਪਲੇ"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "ਖਰਾਬ ਕੀਤਾ ਗਿਆ ਸਮਾਂ"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(ਗੁਮਨਾਮ)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "ਕਈ API, ਆਮ ਤੌਰ 'ਤੇ ਖਾਕੇ ਦੀ ਜਿਆਮਿਤੀ ਨੂੰ ਪੜ੍ਹਦੇ ਹੋਏ, ਸਟਾਈਲ ਅਤੇ ਖਾਕੇ ਦੀ ਗਿਣਤੀ ਕਰਨ ਲਈ ਰੈਂਡਰਿੰਗ ਇੰਜਣ ਨੂੰ ਸਕ੍ਰਿਪਟ ਦੇ ਲਾਗੂ ਹੋਣ ਤੋਂ ਰੋਕਣ ਲਈ ਜ਼ੋਰ ਪਾਉਂਦੇ ਹਨ। [ਜ਼ੋਰ ਪਾਏ ਰੀਫ਼ਲੋ](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) ਅਤੇ ਇਨ੍ਹਾਂ ਦੀਆਂ ਮਿਟੀਗੇਸ਼ਨਾਂ ਬਾਰੇ ਹੋਰ ਜਾਣੋ।"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "ਸਟੈਕ ਟ੍ਰੇਸ"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "ਜ਼ੋਰ ਪਾਇਆ ਗਿਆ ਰੀਫਲੋ"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "ਪ੍ਰਮੁੱਖ ਫੰਕਸ਼ਨ ਕਾਲ"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "ਕੁੱਲ ਰੀਫ਼ਲੋ ਸਮਾਂ"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ਕੋਈ ਵਿਸ਼ੇਸ਼ਤਾ ਨਹੀਂ]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "ਚਿੱਤਰਾਂ ਦੇ ਡਾਊਨਲੋਡ ਸਮੇਂ ਨੂੰ ਘਟਾਉਣ ਨਾਲ ਪੰਨੇ ਅਤੇ LCP ਦੇ ਲੋਡ ਸਮੇਂ ਵਿੱਚ ਸੁਧਾਰ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ। [ਚਿੱਤਰ ਦੇ ਆਕਾਰ ਨੂੰ ਸੁਯੋਗ ਬਣਾਉਣ ਬਾਰੇ ਹੋਰ ਜਾਣੋ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ਅੰਦਾਜ਼ਨ {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ਸੁਯੋਗ ਕਰਨਯੋਗ ਕੋਈ ਚਿੱਤਰ ਨਹੀਂ"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ਫ਼ਾਈਲ ਦੇ ਆਕਾਰ ਨੂੰ ਸੁਯੋਗ ਬਣਾਓ"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} ਹੋਰ"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ਚਿੱਤਰ ਡਿਲੀਵਰੀ ਨੂੰ ਬਿਹਤਰ ਬਣਾਓ"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "ਚਿੱਤਰ ਦੇ ਨਪੀੜਨ ਕਾਰਕ ਨੂੰ ਵਧਾਉਣ ਨਾਲ ਇਸ ਚਿੱਤਰ ਦੇ ਡਾਊਨਲੋਡ ਆਕਾਰ ਵਿੱਚ ਸੁਧਾਰ ਹੋ ਸਕਦਾ ਹੈ।"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "ਆਧੁਨਿਕ ਚਿੱਤਰ ਫਾਰਮੈਟ (WebP, AVIF) ਦੀ ਵਰਤੋਂ ਕਰਨ ਜਾਂ ਚਿੱਤਰ ਨਪੀੜਨ ਨੂੰ ਵਧਾਉਣ ਨਾਲ, ਇਸ ਚਿੱਤਰ ਦੇ ਡਾਊਨਲੋਡ ਆਕਾਰ ਵਿੱਚ ਸੁਧਾਰ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ।"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ਇਹ ਚਿੱਤਰ ਫ਼ਾਈਲ ਇਸਦੇ ਦਿਖਾਏ ਗਏ ਆਯਾਮਾਂ ({PH2}) ਲਈ ({PH1}) ਦੀ ਲੋੜ ਨਾਲੋਂ ਜ਼ਿਆਦਾ ਵੱਡੀ ਹੈ। ਡਾਊਨਲੋਡ ਕੀਤੇ ਜਾਣ ਵਾਲੇ ਚਿੱਤਰ ਦਾ ਆਕਾਰ ਘਟਾਉਣ ਲਈ ਪ੍ਰਤਿਕਿਰਿਆਤਮਕ ਚਿੱਤਰਾਂ ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF ਦੀ ਬਜਾਏ ਵੀਡੀਓ ਫਾਰਮੈਟਾਂ ਦੀ ਵਰਤੋਂ ਕਰਨ ਨਾਲ ਐਨੀਮੇਟਿਡ ਸਮੱਗਰੀ ਦੇ ਡਾਊਨਲੋਡ ਆਕਾਰ ਵਿੱਚ ਸੁਧਾਰ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ।"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "ਲੰਬੇ ਫੇਜ਼ ਦੇ ਨਾਲ ਜਾਂਚ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰੋ। [ਹੋ ਸਕਦਾ ਹੈ ਕਿ ਦੇਰੀਆਂ ਘੱਟ ਜਾਣ](https://web.dev/articles/optimize-inp#optimize_interactions)। ਪ੍ਰਕਿਰਿਆ ਦੀ ਮਿਆਦ ਨੂੰ ਘੱਟ ਕਰਨ ਲਈ, [ਮੁੱਖ-ਥ੍ਰੈੱਡ ਲਾਗਤਾਂ ਨੂੰ ਸੁਯੋਗ ਬਣਾਓ](https://web.dev/articles/optimize-long-tasks), ਆਮ ਤੌਰ 'ਤੇ ਇਹ JS ਹੁੰਦੀ ਹੈ।"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "ਮਿਆਦ"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ਇਨਪੁੱਟ ਵਿੱਚ ਦੇਰੀ"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ਕੋਈ ਅੰਤਰਕਿਰਿਆ ਨਹੀਂ ਮਿਲੀ"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ਫੇਜ਼"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "ਪੇਸ਼ਕਾਰੀ ਵਿੱਚ ਦੇਰੀ"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "ਮਿਆਦ ਪ੍ਰਕਿਰਿਆ-ਅਧੀਨ ਹੈ"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "ਫੇਜ਼ ਮੁਤਾਬਕ INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP ਚਿੱਤਰ ਨੂੰ HTML ਤੋਂ ਤੁਰੰਤ [ਖੋਜਣਯੋਗ](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) ਬਣਾ ਕੇ ਅਤੇ [ਲੇਜ਼ੀ ਲੋਡਿੰਗ ਤੋਂ ਬੱਚ ਕੇ](https://web.dev/articles/lcp-lazy-loading), LCP ਸੁਯੋਗ ਬਣਾਓ"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "ਲਾਗੂ fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high ਨੂੰ ਲਾਗੂ ਕੀਤਾ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ਲੇਜ਼ੀ ਲੋਡ ਲਾਗੂ ਨਹੀਂ ਕੀਤਾ ਗਿਆ"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP ਚਿੱਤਰ ਸਭ ਤੋਂ ਪਹਿਲਾਂ ਸ਼ੁਰੂਆਤੀ ਬਿੰਦੂ ਤੋਂ {PH1} ਬਾਅਦ ਲੋਡ ਕੀਤਾ ਗਿਆ।"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "ਕੋਈ LCP ਨਹੀਂ ਮਿਲਿਆ"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "ਕੋਈ LCP ਸਰੋਤ ਨਹੀਂ ਮਿਲਿਆ ਕਿਉਂਕਿ LCP ਇੱਕ ਚਿੱਤਰ ਨਹੀਂ ਹੈ"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "ਸੁਰੂਆਤੀ ਦਸਤਾਵੇਜ਼ ਵਿੱਚ ਬੇਨਤੀ ਖੋਜਣਯੋਗ ਹੈ"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP ਲਈ ਬੇਨਤੀ ਖੋਜ"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ਹਰ [ਫੇਜ਼ ਵਿੱਚ ਖਾਸ ਸੁਧਾਰ ਰਣਨੀਤੀਆਂ ਹਨ](https://web.dev/articles/optimize-lcp#lcp-breakdown)। ਵੈਸੇ, LCP ਦਾ ਜ਼ਿਆਦਾਤਰ ਸਮਾਂ ਸਰੋਤਾਂ ਨੂੰ ਲੋਡ ਕਰਨ ਵਿੱਚ ਬਿਤਾਇਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ, ਨਾ ਕਿ ਦੇਰੀਆਂ ਵਿੱਚ।"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "ਮਿਆਦ"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "ਤੱਤ ਨੂੰ ਰੈਂਡਰ ਕਰਨ ਵਿੱਚ ਦੇਰੀ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ਫ਼ੀਲਡ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "ਕੋਈ LCP ਨਹੀਂ ਮਿਲਿਆ"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ਫੇਜ਼"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ਸਰੋਤ ਲੋਡ ਕਰਨ ਵਿੱਚ ਦੇਰੀ ਹੋਈ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "ਸਰੋਤ ਲੋਡ ਹੋਣ ਦੀ ਮਿਆਦ"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "ਟਾਈਮ ਟੂ ਫਸਟ ਬਾਈਟ"}, "models/trace/insights/LCPPhases.ts | title": {"message": "ਫੇਜ਼ ਮੁਤਾਬਕ LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "ਸਕ੍ਰਿਪਟ"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "ਰੱਖਿਅਤ ਕੀਤੀਆਂ ਬਾਈਟਾਂ"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "ਪੌਲੀਫ਼ਿਲ ਅਤੇ ਰੂਪਾਂਤਰਨ ਪੁਰਾਣੇ ਬ੍ਰਾਊਜ਼ਰਾਂ ਨੂੰ ਨਵੀਆਂ JavaScript ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਦੀ ਵਰਤੋਂ ਕਰਨ ਦੇ ਯੋਗ ਬਣਾਉਂਦੇ ਹਨ। ਹਾਲਾਂਕਿ, ਇਨ੍ਹਾਂ ਵਿੱਚੋਂ ਕਈ ਆਧੁਨਿਕ ਬ੍ਰਾਊਜ਼ਰਾਂ ਲਈ ਜ਼ਰੂਰੀ ਨਹੀਂ ਹਨ। [ਬੇਸਲਾਈਨ](https://web.dev/articles/baseline-and-polyfills) ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਨੂੰ ਟ੍ਰਾਂਸਪਾਈਲ ਨਾ ਕਰਨ ਲਈ ਆਪਣੀ JavaScript ਬਿਲਡ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਸੋਧਣ 'ਤੇ ਵਿਚਾਰ ਕਰੋ, ਇਹ ਸੋਧ ਉਦੋਂ ਨਾ ਕਰੋ ਜਦੋਂ ਤੁਹਾਡੇ ਲਈ ਪੁਰਾਣੇ ਬ੍ਰਾਊਜ਼ਰਾਂ ਦਾ ਸਮਰਥਨ ਕਰਨਾ ਲਾਜ਼ਮੀ ਹੋਵੇ। [ਜਾਣੋ ਕਿ ਜ਼ਿਆਦਾਤਰ ਸਾਈਟਾਂ ਟ੍ਰਾਂਸਪਾਈਲ ਕੀਤੇ ਬਿਨਾਂ ES6+ ਕੋਡ ਨੂੰ ਕਿਉਂ ਲਾਗੂ ਕਰ ਸਕਦੀਆਂ ਹਨ](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "ਵਿਰਾਸਤੀ JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/1.1 ਦੇ ਮੁਕਾਬਲੇ HTTP/2 ਅਤੇ HTTP/3 ਕਈ ਲਾਭਾਂ ਦੀ ਪੇਸ਼ਕਸ਼ ਕਰਦੇ ਹਨ, ਜਿਵੇਂ ਕਿ ਮਲਟੀਪਲੈਕਸਿੰਗ। [ਆਧੁਨਿਕ HTTP ਦੀ ਵਰਤੋਂ ਕਰਨ ਬਾਰੇ ਹੋਰ ਜਾਣੋ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)।"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 ਦੇ ਲਈ ਕੋਈ ਬੇਨਤੀ ਨਹੀਂ ਹੈ"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "ਪ੍ਰੋਟੋਕੋਲ"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "ਬੇਨਤੀ ਕਰੋ"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "ਆਧੁਨਿਕ HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ਮੂਲ ਥਾਂ"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "ਬੇਨਤੀ ਕਰੋ"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ਸਰੋਤ"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "ਸਮਾਂ"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "LCP ਨਾਲ ਸੰਬੰਧਿਤ ਅੰਦਾਜ਼ਨ ਬਚਤ"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "ਅਣਵਰਤਿਆ ਪ੍ਰੀ-ਕਨੈਕਟ। ਜਾਂਚ ਕਰੋ ਕਿ crossorigin ਵਿਸ਼ੇਸ਼ਤਾ ਨੂੰ ਸਹੀ ਢੰਗ ਨਾਲ ਵਰਤਿਆ ਜਾ ਰਿਹਾ ਹੈ ਜਾਂ ਨਹੀਂ।"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[ਮਹੱਤਵਪੂਰਨ ਬੇਨਤੀਆਂ ਦੀ ਲੜੀ ਬਣਾਉਣ ਤੋਂ ਬਚੋ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) ਤੁਸੀਂ ਅਜਿਹਾ ਲੜੀਆਂ ਦੀ ਲੰਬਾਈ ਨੂੰ ਘਟਾ ਕੇ, ਸਰੋਤਾਂ ਦੇ ਡਾਊਨਲੋਡ ਆਕਾਰ ਨੂੰ ਘਟਾ ਕੇ ਅਜਿਹਾ ਕਰ ਸਕਦੇ ਹੋ ਜਾਂ ਪੰਨੇ ਨੂੰ ਲੋਡ ਕਰਨ ਦੀ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਵਾਸਤੇ ਬੇਲੋੜੇ ਸਰੋਤਾਂ ਦੇ ਡਾਊਨਲੋਡ ਨੂੰ ਮੁਲਤਵੀ ਕਰ ਸਕਦੇ ਹੋ।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "ਆਪਣੀਆਂ ਸਭ ਤੋਂ ਮਹੱਤਵਪੂਰਨ ਮੂਲ ਥਾਵਾਂ ਵਿੱਚ [ਪ੍ਰੀ-ਕਨੈਕਟ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ਸੰਕੇਤ ਸ਼ਾਮਲ ਕਰੋ, ਪਰ 4 ਤੋਂ ਘੱਟ ਸੰਕੇਤ ਵਰਤਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ।"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ਪ੍ਰੀ-ਕਨੈਕਟ ਕੀਤੇ ਉਮੀਦਵਾਰ"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "ਮਹੱਤਵਪੂਰਨ ਪਾਥ ਦੀ ਵੱਧੋ-ਵੱਧ ਵਿਲੰਬਤਾ:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ਨੈੱਟਵਰਕ ਨਿਰਭਰਤਾਵਾਂ ਕਰਕੇ ਕੋਈ ਰੈਂਡਰਿੰਗ ਕਾਰਜ ਪ੍ਰਭਾਵਿਤ ਨਹੀਂ ਹੋਇਆ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "ਪ੍ਰੀ-ਕਨੈਕਟ ਕਰਨ ਲਈ ਕੋਈ ਹੋਰ ਮੂਲ ਥਾਂ ਉਪਲਬਧ ਨਹੀਂ ਹੈ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ਕੋਈ ਵੀ ਮੂਲ ਥਾਂ ਪ੍ਰੀ-ਕਨੈਕਟ ਨਹੀਂ ਸੀ"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[ਪ੍ਰੀ-ਕਨੈਕਟ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ਸੰਕੇਤ ਬ੍ਰਾਊਜ਼ਰ ਨੂੰ ਪੰਨਾ ਲੋਡ ਹੋਣ ਤੋਂ ਪਹਿਲਾਂ ਹੀ ਕਨੈਕਸ਼ਨ ਸਥਾਪਤ ਕਰਨ ਵਿੱਚ ਮਦਦ ਕਰਦੇ ਹਨ, ਜਿਸ ਨਾਲ ਉਸ ਮੂਲ ਥਾਂ ਲਈ ਪਹਿਲੀ ਬੇਨਤੀ ਕਰਨ 'ਤੇ ਸਮਾਂ ਬਚਦਾ ਹੈ। ਅੱਗੇ ਉਹ ਮੂਲ ਥਾਵਾਂ ਹਨ, ਜਿਨ੍ਹਾਂ ਨਾਲ ਪੰਨਾ ਪ੍ਰੀ-ਕਨੈਕਟ ਹੈ।"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "ਪ੍ਰੀ-ਕਨੈਕਟ ਕੀਤੀਆਂ ਮੂਲ ਥਾਵਾਂ"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ਨੈੱਟਵਰਕ ਨਿਰਭਰਤਾ ਟ੍ਰੀ"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4 ਤੋਂ ਜ਼ਿਆਦਾ preconnect ਕਨੈਕਸ਼ਨ ਮਿਲੇ। ਇਨ੍ਹਾਂ ਦੀ ਵਰਤੋਂ ਘੱਟ ਅਤੇ ਸਿਰਫ਼ ਸਭ ਤੋਂ ਮਹੱਤਵਪੂਰਨ ਮੂਲ ਥਾਵਾਂ 'ਤੇ ਹੀ ਕੀਤੀ ਜਾਣੀ ਚਾਹੀਦੀ ਹੈ।"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "ਅਣਵਰਤਿਆ ਪ੍ਰੀ-ਕਨੈਕਟ। ਤੁਹਾਡਾ ਪੰਨਾ ਜਿਨ੍ਹਾਂ ਮੂਲ ਥਾਵਾਂ ਤੋਂ ਡਾਟਾ ਪ੍ਰਾਪਤ ਕਰਨ ਦੀ ਬੇਨਤੀ ਕਰ ਸਕਦਾ ਹੈ ਸਿਰਫ਼ ਉਨ੍ਹਾਂ ਲਈ preconnect ਦੀ ਵਰਤੋਂ ਕਰੋ।"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "ਮਹੱਤਵਪੂਰਨ ਬੇਨਤੀਆਂ ਦੀ ਲੜੀ ਬਣਾਉਣ ਤੋਂ ਬਚੋ ਤੁਸੀਂ ਅਜਿਹਾ ਲੜੀਆਂ ਦੀ ਲੰਬਾਈ ਨੂੰ ਘਟਾ ਕੇ, ਸਰੋਤਾਂ ਦੇ ਡਾਊਨਲੋਡ ਆਕਾਰ ਨੂੰ ਘਟਾ ਕੇ ਅਜਿਹਾ ਕਰ ਸਕਦੇ ਹੋ ਜਾਂ ਪੰਨੇ ਨੂੰ ਲੋਡ ਕਰਨ ਦੀ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਵਾਸਤੇ ਬੇਲੋੜੇ ਸਰੋਤਾਂ ਦੇ ਡਾਊਨਲੋਡ ਨੂੰ ਮੁਲਤਵੀ ਕਰ ਸਕਦੇ ਹੋ।"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "ਬੇਨਤੀਆਂ ਪੰਨੇ ਦੇ ਸ਼ੁਰੂਆਤੀ ਰੈਂਡਰ ਨੂੰ ਬਲਾਕ ਕਰ ਰਹੀਆਂ ਹਨ, ਜਿਸ ਕਾਰਨ LCP ਵਿੱਚ ਦੇਰੀ ਹੋ ਸਕਦੀ ਹੈ। [ਮੁਲਤਵੀ ਜਾਂ ਇਨਲਾਈਨਿੰਗ](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ਕਰਨ ਨਾਲ ਇਹ ਨੈੱਟਵਰਕ ਬੇਨਤੀਆਂ ਮਹੱਤਵਪੂਰਨ ਪਾਥ ਤੋਂ ਬਾਹਰ ਲਿਜਾਈਆਂ ਜਾ ਸਕਦੀਆਂ ਹਨ।"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "ਮਿਆਦ"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ਇਸ ਨੈਵੀਗੇਸ਼ਨ ਲਈ ਕੋਈ ਰੈਂਡਰ ਬਲਾਕਿੰਗ ਬੇਨਤੀ ਨਹੀਂ"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "ਬੇਨਤੀ"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "ਰੈਂਡਰ ਬਲਾਕ ਕਰਨ ਸੰਬੰਧੀ ਬੇਨਤੀਆਂ"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "ਜੇ ਸਟਾਈਲ ਲਾਗਤਾਂ ਦੀ ਮੁੜ-ਗਣਨਾ ਵਿੱਚ ਅਜੇ ਵੀ ਜ਼ਿਆਦਾ ਸਮਾਂ ਲੱਗ ਰਿਹਾ ਹੈ, ਤਾਂ ਚੋਣਕਾਰ ਸੁਯੋਗਤਾ ਇਸ ਨੂੰ ਘਟਾ ਸਕਦੀ ਹੈ। ਉਨ੍ਹਾਂ [ਚੋਣਕਾਰਾਂ ਨੂੰ ਸੁਯੋਗ ਬਣਾਓ](https://developer.chrome.com/docs/devtools/performance/selector-stats) ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਬੀਤਿਆ ਸਮਾਂ ਅਤੇ ਸਲੋ-ਪਾਥ % ਦੋਵੇਂ ਜ਼ਿਆਦਾ ਹਨ। ਸਰਲ ਚੋਣਕਾਰ, ਘੱਟ ਚੋਣਕਾਰ, ਛੋਟੇ DOM ਅਤੇ ਘੱਟ ਵਿਆਪਕ DOM ਨਾਲ ਸਾਰੀਆਂ ਮੇਲ ਖਾਂਦੀਆਂ ਲਾਗਤਾਂ ਘਟ ਸਕਦੀਆਂ ਹਨ।"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "ਬੀਤਿਆ ਸਮਾਂ"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "ਕੋਈ CSS ਚੋਣਕਾਰ ਡਾਟਾ ਨਹੀਂ ਮਿਲਿਆ। ਕਾਰਗੁਜ਼ਾਰੀ ਪੈਨਲ ਸੈਟਿੰਗਾਂ ਵਿੱਚ CSS ਚੋਣਕਾਰ ਸੰਬੰਧੀ ਅੰਕੜਿਆਂ ਨੂੰ ਚਾਲੂ ਕਰਨ ਦੀ ਲੋੜ ਹੈ।"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ਮਿਲਾਨ ਕਰਨ ਦੀਆਂ ਕੋਸ਼ਿਸ਼ਾਂ"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "ਮੇਲ ਖਾਉਣ ਦੀ ਗਿਣਤੀ"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ਚੋਣਕਾਰ ਲਾਗਤਾਂ"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ਪ੍ਰਮੁੱਖ ਚੋਣਕਾਰ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "ਕੁੱਲ"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "ਮੁੱਖ ਥ੍ਰੈੱਡ ਦਾ ਸਮਾਂ"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "ਤੀਜੀ ਧਿਰ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ਟ੍ਰਾਂਸਫ਼ਰ ਆਕਾਰ"}, "models/trace/insights/ThirdParties.ts | description": {"message": "ਤੀਜੀ-ਧਿਰ ਦਾ ਕੋਡ, ਲੋਡ ਹੋਣ ਦੀ ਕਾਰਗੁਜ਼ਾਰੀ 'ਤੇ ਬਹੁਤ ਜ਼ਿਆਦਾ ਪ੍ਰਭਾਵ ਪਾ ਸਕਦਾ ਹੈ। ਆਪਣੇ ਪੰਨੇ ਦੀ ਸਮੱਗਰੀ ਨੂੰ ਤਰਜੀਹ ਦੇਣ ਲਈ [ਤੀਜੀ-ਧਿਰ ਦੇ ਕੋਡ ਦੀ ਲੋਡਿੰਗ ਨੂੰ ਘਟਾਓ ਅਤੇ ਸਥਗਿਤ ਕਰੋ](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)।"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "ਕੋਈ ਤੀਜੀ-ਧਿਰ ਨਹੀਂ ਮਿਲੀ"}, "models/trace/insights/ThirdParties.ts | title": {"message": "ਤੀਜੀਆਂ ਧਿਰਾਂ"}, "models/trace/insights/Viewport.ts | description": {"message": "ਜੇ ਵਿਊਪੋਰਟ ਮੋਬਾਈਲ ਲਈ ਸੁਯੋਗ ਨਹੀਂ ਹੈ, ਤਾਂ ਟੈਪ ਅੰਤਰਕਿਰਿਆਵਾਂ ਵਿੱਚ [300 ਮਿਲੀਸਕਿੰਟ ਤੱਕ ਦੀ ਦੇਰੀ](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ਹੋ ਸਕਦੀ ਹੈ।"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "ਮੋਬਾਈਲ 'ਤੇ ਟੈਪ ਕਰਨ ਸੰਬੰਧੀ ਦੇਰੀ"}, "models/trace/insights/Viewport.ts | title": {"message": "ਮੋਬਾਈਲ ਲਈ ਵਿਊਪੋਰਟ ਨੂੰ ਸੁਯੋਗ ਬਣਾਓ"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "ਸਿਰਫ਼ GET ਬੇਨਤੀ ਰਾਹੀਂ ਲੋਡ ਕੀਤੇ ਪੰਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "ਸਿਰਫ਼ 2XX ਦੇ ਸਥਿਤੀ ਕੋਡ ਵਾਲੇ ਪੰਨਿਆਂ ਨੂੰ ਹੀ ਕੈਸ਼ੇ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "ਕੈਸ਼ੇ ਵਿੱਚ ਹੋਣ ਦੌਰਾਨ Chrome ਨੂੰ JavaScript ਚਲਾਉਣ ਦੀ ਇੱਕ ਕੋਸ਼ਿਸ਼ ਦਾ ਪਤਾ ਲੱਗਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ AppBanner ਦੀ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਫਲੈਗਾਂ ਵੱਲੋਂ ਬੰਦ ਕੀਤਾ ਗਿਆ। ਇਸ ਡੀਵਾਈਸ 'ਤੇ ਇਸ ਨੂੰ ਸਥਾਨਕ ਤੌਰ 'ਤੇ ਚਾਲੂ ਕਰਨ ਲਈ chrome://flags/#back-forward-cache 'ਤੇ ਜਾਓ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਆਦੇਸ਼ ਲਾਈਨ ਵੱਲੋਂ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਨਾਕਾਫ਼ੀ ਮੈਮੋਰੀ ਦੇ ਕਾਰਨ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਪ੍ਰਤਿਨਿਧ ਵੱਲੋਂ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਪ੍ਰੀਰੈਂਡਰਰ ਲਈ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "ਪੰਨੇ ਨੂੰ ਕੈਸ਼ੇ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ ਕਿਉਂਕਿ ਇਸ ਦੇ ਕੋਲ ਰਜਿਸਟਰ ਕੀਤੇ ਸਰੋਤਿਆਂ ਵਾਲੀ BroadcastChannel ਕਿਰਿਆ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store ਸਿਰਲੇਖ ਵਾਲੇ ਪੰਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "ਕੈਸ਼ੇ ਨੂੰ ਜਾਣਬੁੱਝ ਕੇ ਕਲੀਅਰ ਕੀਤਾ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "ਕੈਸ਼ੇ ਤੋਂ ਇੱਕ ਪੰਨਾ ਕੱਢ ਦਿੱਤਾ ਗਿਆ ਸੀ ਤਾਂ ਜੋ ਦੂਜਾ ਪੰਨਾ ਕੈਸ਼ੇ ਕੀਤਾ ਜਾ ਸਕੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਪਲੱਗਇਨ ਸ਼ਾਮਲ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "ਗੈਰ-ਪਰਿਭਾਸ਼ਿਤ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "ਪੰਨੇ ਜੋ FileChooser API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "ਪੰਨੇ ਜੋ File System Access API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "ਪੰਨੇ ਜੋ Media Device Dispatcher ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "ਪੰਨੇ ਤੋਂ ਦੂਰ ਨੈਵੀਗੇਟ ਕਰਨ ਸਮੇਂ ਮੀਡੀਆ ਪਲੇਅਰ ਚੱਲ ਰਿਹਾ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "ਪੰਨੇ ਜੋ MediaSession API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ ਅਤੇ ਪਲੇਬੈਕ ਸਥਿਤੀ ਨੂੰ ਸੈੱਟ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "ਪੰਨੇ ਜੋ MediaSession API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ ਅਤੇ ਕਾਰਵਾਈ ਹੈਂਡਲਰ ਨੂੰ ਸੈੱਟ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਸਕ੍ਰੀਨ ਰੀਡਰ ਦੇ ਕਾਰਨ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "ਪੰਨੇ ਜੋ SecurityHandler ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "ਪੰਨੇ ਜੋ Serial API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "ਪੰਨੇ ਜੋ WebAuthetication API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "ਪੰਨੇ ਜੋ WebBluetooth API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "ਪੰਨੇ ਜੋ WebUSB API ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਬੰਦ ਕੀਤਾ ਗਿਆ ਹੈ, ਕਿਉਂਕਿ Cache-Control: no-store ਨੂੰ ਵਰਤਣ ਵਾਲੇ ਪੰਨੇ 'ਤੇ ਕੁਕੀਜ਼ ਬੰਦ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "ਪੰਨੇ ਜੋ ਸਮਰਪਿਤ ਵਰਕਰ ਜਾਂ ਵਰਕਲੈੱਟ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "ਪੰਨੇ ਤੋਂ ਦੂਰ ਨੈਵੀਗੇਟ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਦਸਤਾਵੇਜ਼ ਪੂਰੀ ਤਰ੍ਹਾਂ ਨਾਲ ਲੋਡ ਨਹੀਂ ਸੀ ਹੋਇਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਐਪ ਬੈਨਰ ਮੌਜੂਦ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ Chrome ਪਾਸਵਰਡ ਪ੍ਰਬੰਧਕ ਮੌਜੂਦ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ DOM ਡਿਸਟਿਲੇਸ਼ਨ ਜਾਰੀ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ DOM Distiller Viewer ਮੌਜੂਦ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "ਸੁਨੇਹਾ ਸੇਵਾ ਸੰਬੰਧੀ API ਦੀ ਵਰਤੋਂ ਕਰ ਕੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਐਕਸਟੈਂਸ਼ਨਾਂ ਦੇ ਕਾਰਨ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "ਲੰਬੇ ਸਮੇਂ ਤੱਕ ਰਹਿਣ ਵਾਲੇ ਕਨੈਕਸ਼ਨ ਦੀਆਂ ਐਕਸਟੈਂਸ਼ਨਾਂ ਨੂੰ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਹੋਣ ਤੋਂ ਪਹਿਲਾਂ ਕਨੈਕਸ਼ਨ ਬੰਦ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "ਲੰਬੇ ਸਮੇਂ ਤੱਕ ਰਹਿਣ ਵਾਲੇ ਕਨੈਕਸ਼ਨ ਦੀਆਂ ਐਕਸਟੈਂਸ਼ਨਾਂ ਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਫ੍ਰੇਮਾਂ ਨੂੰ ਸੁਨੇਹੇ ਭੇਜਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਐਕਸਟੈਂਸ਼ਨਾਂ ਦੇ ਕਾਰਨ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਫ਼ਾਰਮ ਨੂੰ ਮੁੜ ਸਪੁਰਦ ਕਰਨਾ ਜਾਂ http ਪਾਸਵਰਡ ਵਿੰਡੋ ਵਰਗੀ ਇੱਕ ਮਾਡਲ ਵਿੰਡੋ ਦਿਖਾਈ ਗਈ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਆਫ਼ਲਾਈਨ ਪੰਨਾ ਦਿਖਾਇਆ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ Out-Of-Memory Intervention ਬਾਰ ਮੌਜੂਦ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਇਜਾਜ਼ਤ ਸੰਬੰਧੀ ਬੇਨਤੀਆਂ ਦਿਖਈਆਂ ਗਈਆਂ ਸਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਪੌਪਅੱਪ ਬਲਾਕਰ ਮੌਜੂਦ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "ਛੱਡ ਕੇ ਜਾਣ ਵੇਲੇ ਸੁਰੱਖਿਅਤ ਬ੍ਰਾਊਜ਼ਿੰਗ ਵੇਰਵੇ ਦਿਖਾਏ ਗਏ ਸਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "ਸੁਰੱਖਿਅਤ ਬ੍ਰਾਊਜ਼ਿੰਗ ਨੇ ਇਸ ਪੰਨੇ ਨੂੰ ਦੁਰਵਿਹਾਰੀ ਸਮਝਿਆ ਅਤੇ ਪੌਪ-ਅੱਪ ਨੂੰ ਬਲਾਕ ਕਰ ਦਿੱਤਾ।"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "ਪੰਨੇ ਦੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਹੋਣ ਦੌਰਾਨ ਕਿਸੇ ਸਰਵਿਸ ਵਰਕਰ ਨੂੰ ਕਿਰਿਆਸ਼ੀਲ ਕੀਤਾ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ਦਸਤਾਵੇਜ਼ ਦੀ ਗੜਬੜ ਕਰਕੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਬੰਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames ਵਰਤਣ ਵਾਲੇ ਪੰਨਿਆਂ ਨੂੰ bfcache ਵਿੱਚ ਸਟੋਰ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ।"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "ਕੈਸ਼ੇ ਤੋਂ ਇੱਕ ਪੰਨਾ ਕੱਢ ਦਿੱਤਾ ਗਿਆ ਸੀ ਤਾਂ ਜੋ ਦੂਜਾ ਪੰਨਾ ਕੈਸ਼ੇ ਕੀਤਾ ਜਾ ਸਕੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਨੇ ਮੀਡਿਆ ਸਟ੍ਰੀਮ ਤੱਕ ਪਹੁੰਚ ਦੀ ਆਗਿਆ ਦਿੱਤੀ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "ਜਿਨ੍ਹਾਂ ਪੰਨਿਆਂ ਵਿੱਚ ਖਾਸ ਕਿਸਮ ਦੀ ਜੋੜੀ ਗਈ ਸਮੱਗਰੀ ਸ਼ਾਮਲ ਹੁੰਦੀ ਹੈ (ਜਿਵੇਂ ਕਿ PDF), ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "ਪੰਨੇ ਜੋ IdleManager ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਕੋਲ ਇੱਕ ਖੁੱਲ੍ਹਾ IndexedDB ਕਨੈਕਸ਼ਨ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB ਇਵੈਂਟ ਦੇ ਕਾਰਨ ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਬੰਦ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "ਅਯੋਗ API ਵਰਤੇ ਗਏ ਸਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਐਕਸਟੈਂਸ਼ਨਾਂ ਵੱਲੋਂ JavaScript ਨੂੰ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਐਕਸਟੈਂਸ਼ਨਾਂ ਵੱਲੋਂ StyleSheet ਨੂੰ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "ਅੰਦਰੂਨੀ ਗੜਬੜ।"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਨੂੰ ਬੰਦ ਕੀਤਾ ਗਿਆ ਹੈ, ਕਿਉਂਕਿ ਕੁਝ JavaScript ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਨੂੰ Cache-Control: no-store ਸਿਰਲੇਖ ਦੇ ਨਾਲ ਸਰੋਤ ਪ੍ਰਾਪਤ ਹੋਇਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "ਕੀਪ-ਅਲਾਇਵ ਸੰਬੰਧੀ ਬੇਨਤੀ ਕਰਕੇ ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਬੰਦ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "ਪੰਨੇ ਜੋ ਕੀ-ਬੋਰਡ ਲਾਕ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "ਪੰਨੇ ਤੋਂ ਦੂਰ ਨੈਵੀਗੇਟ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਪੰਨੇ ਦੀ ਲੋਡਿੰਗ ਪੂਰੀ ਨਹੀਂ ਹੋਈ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਦੇ ਮੁੱਖ ਸਰੋਤ ਵਿੱਚ cache-control:no-cache ਹੈ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਦੇ ਮੁੱਖ ਸਰੋਤ ਵਿੱਚ cache-control:no-store ਹੈ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਤੋਂ ਪੰਨੇ ਨੂੰ ਮੁੜ-ਬਹਾਲ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਨੈਵੀਗੇਸ਼ਨ ਨੂੰ ਰੱਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "ਪੰਨੇ ਨੂੰ ਕੈਸ਼ੇ ਤੋਂ ਕੱਢ ਦਿੱਤਾ ਗਿਆ ਸੀ ਕਿਉਂਕਿ ਕਿਸੇ ਕਿਰਿਆਸ਼ੀਲ ਨੈੱਟਵਰਕ ਕਨੈਕਸ਼ਨ ਨੇ ਬਹੁਤ ਜ਼ਿਆਦਾ ਡਾਟਾ ਪ੍ਰਾਪਤ ਕੀਤਾ ਸੀ। Chrome, ਕੈਸ਼ੇ ਕੀਤਾ ਹੋਣ ਦੌਰਾਨ ਕਿਸੇ ਪੰਨੇ ਵੱਲੋਂ ਪ੍ਰਾਪਤ ਕੀਤੇ ਜਾ ਸਕਣ ਵਾਲੇ ਡਾਟਾ ਦੀ ਮਾਤਰਾ ਨੂੰ ਸੀਮਤ ਕਰ ਦਿੰਦਾ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਇਨ-ਫਲਾਈਟ ਫ਼ੈੱਚ() ਜਾਂ XHR ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "ਪੰਨੇ ਨੂੰ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਤੋਂ ਕੱਢ ਦਿੱਤਾ ਗਿਆ ਸੀ, ਕਿਉਂਕਿ ਇੱਕ ਕਿਰਿਆਸ਼ੀਲ ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਵਿੱਚ ਰੀਡਾਇਰੈਕਟ ਸ਼ਾਮਲ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ਪੰਨੇ ਨੂੰ ਕੈਸ਼ੇ ਤੋਂ ਕੱਢ ਦਿੱਤਾ ਗਿਆ ਸੀ ਕਿਉਂਕਿ ਇੱਕ ਨੈੱਟਵਰਕ ਕਨੈਕਸ਼ਨ ਬਹੁਤ ਲੰਮਾ ਸਮਾਂ ਖੁੱਲ੍ਹਾ ਰਿਹਾ ਸੀ। Chrome ਉਸ ਸਮੇਂ ਦੀ ਮਾਤਰਾ ਨੂੰ ਸੀਮਤ ਕਰਦਾ ਹੈ ਜਿਸ ਸਮੇਂ ਲਈ ਪੰਨਾ ਕੈਸ਼ੇ ਵਿੱਚ ਹੋਣ ਦੌਰਾਨ ਡਾਟਾ ਪ੍ਰਾਪਤ ਕਰ ਸਕਦਾ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਕੋਲ ਕੋਈ ਵੈਧ ਜਵਾਬ ਸਿਰਲੇਖ ਨਹੀਂ ਹੈ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "ਨੈਵੀਗੇਸ਼ਨ ਮੁੱਖ ਫ੍ਰੇਮ ਤੋਂ ਇਲਾਵਾ ਹੋਰ ਫ੍ਰੇਮ ਵਿੱਚ ਹੋਈ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "ਚਾਲੂ ਇੰਡੈਕਸਡ DB ਲੈਣ-ਦੇਣ ਵਾਲੇ ਪੰਨੇ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "ਇਨ-ਫਲਾਈਟ ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਵਾਲੇ ਪੰਨੇ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "ਇਨ-ਫਲਾਈਟ ਫ਼ੈੱਚ ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਵਾਲੇ ਪੰਨੇ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "ਇਨ-ਫਲਾਈਟ ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਵਾਲੇ ਪੰਨੇ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "ਇਨ-ਫਲਾਈਟ XHR ਨੈੱਟਵਰਕ ਬੇਨਤੀ ਵਾਲੇ ਪੰਨੇ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "ਪੰਨੇ ਜੋ PaymentManager ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "ਪੰਨੇ ਜੋ ਤਸਵੀਰ-ਵਿੱਚ-ਤਸਵੀਰ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "ਪੰਨੇ ਜੋ ਪ੍ਰਿੰਟਿੰਗ UI ਦਿਖਾਉਂਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "ਪੰਨੇ ਨੂੰ 'window.open()' ਦੀ ਵਰਤੋਂ ਨਾਲ ਖੋਲ੍ਹਿਆ ਗਿਆ ਅਤੇ ਕਿਸੇ ਹੋਰ ਟੈਬ ਵਿੱਚ ਇਸਦਾ ਹਵਾਲਾ ਹੈ, ਜਾਂ ਪੰਨੇ ਨੇ ਕਿਸੇ ਵਿੰਡੋ ਨੂੰ ਖੋਲ੍ਹਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿਚਲੇ ਪੰਨੇ ਲਈ ਰੈਂਡਰਰ ਪ੍ਰਕਿਰਿਆ ਕ੍ਰੈਸ਼ ਹੋ ਗਈ।"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿਚਲੇ ਪੰਨੇ ਲਈ ਰੈਂਡਰਰ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਨਸ਼ਟ ਕਰ ਦਿੱਤਾ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਆਡੀਓ ਕੈਪਚਰ ਕਰਨ ਦੀਆਂ ਇਜਾਜ਼ਤਾਂ ਲਈ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਸੈਂਸਰ ਇਜਾਜ਼ਤਾਂ ਲਈ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਬੈਕਗ੍ਰਾਊਂਡ ਸਿੰਕ ਜਾਂ ਇਜਾਜ਼ਤਾਂ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ MIDI ਇਜਾਜ਼ਤਾਂ ਦੀ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਸੂਚਨਾਵਾਂ ਸੰਬੰਧੀ ਇਜਾਜ਼ਤਾਂ ਦੀ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਸਟੋਰੇਜ ਪਹੁੰਚ ਲਈ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਵੱਲੋਂ ਵੀਡੀਓ ਕੈਪਚਰ ਕਰਨ ਦੀਆਂ ਇਜਾਜ਼ਤਾਂ ਲਈ ਬੇਨਤੀ ਕੀਤੀ ਗਈ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "ਸਿਰਫ਼ ਉਨ੍ਹਾਂ ਪੰਨਿਆਂ ਨੂੰ ਕੈਸ਼ੇ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ ਜਿਨ੍ਹਾਂ ਦੀ URL ਸਕੀਮ HTTP / HTTPS ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "ਪੰਨੇ 'ਤੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਹੋਣ ਦੌਰਾਨ ਕਿਸੇ ਸਰਵਿਸ ਵਰਕਰ ਵੱਲੋਂ ਦਾਅਵਾ ਕੀਤਾ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "ਸਰਵਿਸ ਵਰਕਰ ਨੇ MessageEvent ਨੂੰ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਪੰਨਾ ਭੇਜਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ਕਿਸੇ ਪੰਨੇ ਦੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਹੋਣ ਦੌਰਾਨ ServiceWorker ਅਣਰਜਿਸਟਰ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "ਸਰਵਿਸ ਵਰਕਰ ਕਿਰਿਆਸ਼ੀਲਤਾ ਕਰਕੇ ਪੰਨੇ ਨੂੰ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਤੋਂ ਕੱਢਿਆ ਗਿਆ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome ਮੁੜ-ਸ਼ੁਰੂ ਕੀਤਾ ਗਿਆ ਅਤੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਦੀਆਂ ਐਂਟਰੀਆਂ ਨੂੰ ਕਲੀਅਰ ਕੀਤਾ ਗਿਆ।"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "ਪੰਨੇ ਜੋ SharedWorker ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "ਪੰਨੇ ਜੋ SpeechRecognizer ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "ਪੰਨੇ ਜੋ SpeechSynthesis ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "ਪੰਨੇ 'ਤੇ ਇੱਕ iframe ਨੇ ਨੈਵੀਗੇਸ਼ਨ ਸ਼ੁਰੂ ਕੀਤੀ ਜੋ ਪੂਰੀ ਨਹੀਂ ਹੋਈ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਦੇ ਉਪ-ਸਰੋਤ ਵਿੱਚ cache-control:no-cache ਹੈ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਦੇ ਉਪ-ਸਰੋਤ ਵਿੱਚ cache-control:no-store ਹੈ, ਉਹ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "ਪੰਨੇ ਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਵੱਧੋ-ਵੱਧ ਸਮੇਂ ਨੂੰ ਪਾਰ ਕਰ ਲਿਆ ਸੀ ਅਤੇ ਉਸਦੀ ਮਿਆਦ ਮੁੱਕ ਗਈ ਸੀ।"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "ਪੰਨੇ ਦਾ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਹੋਣ ਸਮੇਂ ਟਾਈਮ-ਆਊਟ ਹੋ ਗਿਆ (ਸੰਭਾਵੀ ਤੌਰ 'ਤੇ pagehide ਹੈਂਡਲਰਾਂ ਦੇ ਲੰਮੇ ਸਮੇਂ ਤੱਕ ਚੱਲਣ ਕਰਕੇ)।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "ਪੰਨੇ ਕੋਲ ਮੁੱਖ ਫ੍ਰੇਮ ਵਿੱਚ ਇੱਕ ਅਣਲੋਡ ਹੈਂਡਲਰ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "ਪੰਨੇ ਕੋਲ ਉਪ ਫ੍ਰੇਮ ਵਿੱਚ ਇੱਕ ਅਣਲੋਡ ਹੈਂਡਲਰ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ਬ੍ਰਾਊਜ਼ਰ ਨੇ ਵਰਤੋਂਕਾਰ ਏਜੰਟ ਓਵਰਰਾਈਡ ਸਿਰਲੇਖ ਨੂੰ ਬਦਲ ਦਿੱਤਾ।"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "ਪੰਨੇ ਜਿਨ੍ਹਾਂ ਨੇ ਵੀਡੀਓ ਜਾਂ ਆਡੀਓ ਰਿਕਾਰਡ ਕਰਨ ਦੀ ਪਹੁੰਚ ਦਿੱਤੀ ਹੈ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "ਪੰਨੇ ਜੋ WebDatabase ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "ਪੰਨੇ ਜੋ WebHID ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "ਪੰਨੇ ਜੋ WebLocks ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "ਪੰਨੇ ਜੋ WebNfc ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "ਪੰਨੇ ਜੋ WebOTPService ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ bfcache ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC ਵਾਲੇ ਪੰਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਬੰਦ ਹੈ ਕਿਉਂਕਿ WebRTC ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾ ਚੁੱਕੀ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "ਪੰਨੇ ਜੋ WebShare ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket ਵਾਲੇ ਪੰਨੇ, ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਬੰਦ ਹੈ ਕਿਉਂਕਿ WebSocket ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾ ਚੁੱਕੀ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport ਵਾਲੇ ਪੰਨੇ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਵਿੱਚ ਦਾਖਲ ਨਹੀਂ ਹੋ ਸਕਦੇ।"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "ਪੂਰੇ ਪੰਨੇ ਦਾ ਕੈਸ਼ੇ ਬੰਦ ਹੈ ਕਿਉਂਕਿ WebTransport ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾ ਚੁੱਕੀ ਹੈ।"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "ਪੰਨੇ ਜੋ WebXR ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਨ, ਉਹ ਫ਼ਿਲਹਾਲ ਪੂਰੇ ਪੰਨੇ ਦੇ ਕੈਸ਼ੇ ਲਈ ਯੋਗ ਨਹੀਂ ਹਨ।"}}