{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS izskata vērtība slider-vertical nav standartizēta un tiks noņemta."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Tās pašas izcelsmes politikas ierobežoju<PERSON>, iestatot funkciju document.domain, vairs netiek izman<PERSON>ta, un šī iespēja tiks atspējota pēc noklusējuma. Šis darbības pārtraukšanas brīdinājums ir saistīts ar citas izcelsmes piekļuvi, kas tika iespējota, iestatot document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Funkcijas window.alert aktivizēšana no citas izcelsmes iframe ietvariem vairs netiek atbalstīta un nākotnē tiks noņemta."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Funkcijas window.confirm aktivizēšana no citas izcelsmes iframe ietvariem vairs netiek atbalstīta un nākotnē tiks noņemta."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Datu atbalsts: parametrā SVGUseElement vairs netiek atbalstīti vietrāži URL, un nākotnē tie tiks noņemti."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Metodes getCurrentPosition() un watchPosition() vairs nedarbojas nedrošos avotos. <PERSON> i<PERSON> š<PERSON>, apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Metodes getCurrentPosition() un watchPosition() vairs netiek izmantotas nedrošos avotos. <PERSON> i<PERSON> š<PERSON>, apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() no longer works on insecure origins. To use this feature, you should consider switching your application to a secure origin, such as HTTPS. See https://goo.gle/chrome-insecure-origins for more details."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Ir atrasts <h1> tags, kas atrodas elementā <article>, <aside>, <nav> vai <section>, kam nav norādīts fonta lielums. Tuvākajā nākotnē šī pārlūkprogramma mainīs šī virsraksta teksta lielumu. Plašāku informāciju skatiet vietnē https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Lauks RTCPeerConnectionIceErrorEvent.hostCandidate vairs netiek atbalstīts. Tā vietā izmantojiet lauku RTCPeerConnectionIceErrorEvent.address vai RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> a<PERSON> datu pieprasījuma navigator.credentials.get() formāts vairs netiek atbalstīts. <PERSON><PERSON><PERSON><PERSON>, atjauniniet savu i<PERSON>, lai i<PERSON><PERSON><PERSON> jauno formātu."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Tirgotāja izcelsme un patvaļīgi noteikti dati no pakalpojumu skripta notikuma canmakepayment ir novecojuši un tiks noņemti: topOrigin, paymentRequestOrigin, methodData un modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups lauks dailyUpdateUrl, kuram iestatīta vērtība joinAdInterestGroup(), ir pārdēvēts par updateUrl, lai precīzāk atspoguļotu tā darbību."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Saskarne Intl.v8BreakIterator vairs netiek atbalstīta. T<PERSON>s v<PERSON>, <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> saskarni Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Specifikācijas izmaiņu dēļ metodes SourceBuffer.abort() <PERSON><PERSON><PERSON><PERSON><PERSON>, lai priekšlaikus pārtrauktu remove() asinhrono diapazona no<PERSON>, ir novecojusi. Tiek plānots pārtraukt tai atbalstu. Tās vietā ieteicams izmantot notikuma “updateend” uztveršanu. Metode abort() ir paredzēta tikai asinhronas multivides līdzekļu pievienošanas priekšlaicīgai pārtraukšanai vai parsētāja statusa atiestatīšanai."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Paziņojumu API vairs nevar izmantot no nedrošiem avotiem. Apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "No citas izcelsmes iframe ietvara vairs nevar pieprasīt atļauju saskarnei Paziņojumu API. Apsveriet iespēju pieprasīt atļauju no augšējā līmeņa ietvara vai atvērt jaunu logu."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Metodes createImageBitmap opcija imageOrientation: 'none' vairs netiek atbalstīta. Tās vietā izmantojiet metodi createImageBitmap ar opciju '{imageOrientation: 'from-image'}'."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Tagos “img”, “video” un “canvas” norā<PERSON>t vērtību “overflow: visible”, vizu<PERSON>ls saturs var tikt rādīts ārpus elementa robežām. Skatiet lapu https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "Saskarnei paymentManager.instruments ir pārtraukts atbalsts. Tā vietā maksājumu apdarinātājiem izmantojiet instalēšanu tieši laikā."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Ar jūsu PaymentRequest izsaukumu tika apieta Content-Security-Policy (satura drošības politikas — SDP) direktīva “connect-src”. Šādai apiešanai ir pārtraukts atbalsts. SDP direktīvai “connect-src” pievienojiet maksājuma veida identifikatoru no PaymentRequest API (laukā supportedMethods)."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "Saskarne StorageType.persistent vairs netiek atbalstīta. <PERSON><PERSON><PERSON> viet<PERSON>, <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON>et standartizētu navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Elements <source src> ar galveno elementu <picture> nav derī<PERSON>, tād<PERSON><PERSON> tiks ignorēts. T<PERSON> vietā, l<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>et <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Metode webkitCancelAnimationFrame ir piesaistīta nod<PERSON>tājam. Tās vietā izmantojiet standarta metodi cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Metode webkitRequestAnimationFrame ir piesaistīta nod<PERSON>tāja<PERSON>. Tās vietā izmantojiet standarta metodi requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Saskarne HTMLVideoElement.webkitDisplayingFullscreen vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Saskarne HTMLVideoElement.webkitEnterFullScreen() vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Saskarne HTMLVideoElement.webkitEnterFullscreen() vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Saskarne HTMLVideoElement.webkitExitFullScreen() vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Saskarne HTMLVideoElement.webkitExitFullscreen() vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Saskarne HTMLVideoElement.webkitSupportsFullscreen vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Plānojam pārtraukt atbalstu saskarnei API chrome.privacy.websites.privacySandboxEnabled, kaut gan atpakaļsaderības nolūkiem tā būs aktīva līdz laidiena M113 publicēšanai. Tās vietā izmantojiet chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled un chrome.privacy.websites.adMeasurementEnabled. Skatiet vietni https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ierobežojums DtlsSrtpKeyAgreement ir noņemts. <PERSON><PERSON><PERSON> šim ierobežojumam esat noteicis vērtību “false”, un tas tiek interpretēts kā mēģinājums izmantot noņemto metodi SDES key negotiation. Šī funkcionalitāte ir noņemta. Tās vietā izmantojiet pakalpo<PERSON>, kas atbalsta metodi DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ierobežojums DtlsSrtpKeyAgreement ir noņemts. <PERSON><PERSON><PERSON> šim ierobežojumam esat noteicis vērtību “true”, taču tas nedarbojās. Skaidrības labad varat noņemt šo ierobežojumu."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Uz izsaukumu balstītā metode getStats() vairs netiek atbalstīta un tiks noņemta. Tās vietā izmantojiet specifikācijām atbilstošo metodi getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Saskarne Range.expand() vairs netiek atbalstīta. Tās vietā izmantojiet saskarni Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opcija rtcpMuxPolicy vairs nav pieejama un tiks noņemta."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Metode speechSynthesis.speak() bez lietotāja aktivizācijas vairs netiek izmantota un tiks noņemta."}, "generated/Deprecation.ts | UnloadHandler": {"message": "<PERSON><PERSON><PERSON><PERSON> atcelšanas notikumu uztvēr<PERSON>ju darb<PERSON>ba ir p<PERSON>, un tie tiks noņ<PERSON>."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. See https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter atribūta “isFallbackAdapter” darb<PERSON><PERSON> ir p<PERSON>, tā vietā izmantojiet GPUAdapterInfo atribūtu “isFallbackAdapter”."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhronā saskarne XMLHttpRequest galvenajā pavedienā vairs netiek atbalstīta, jo tā negatīvi ietekmēja galalietotāju pieredzi. Plašāku informāciju skatiet vietnē https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Izkārtojuma nobīdes rodas, ja elementi pārvietojas bez lietotāja mijiedarbības. [Izpētiet izkārtojuma nobīdes cēloņ<PERSON>](https://web.dev/articles/optimize-cls), pie<PERSON><PERSON><PERSON>, elementu pievienošanu un noņemšanu vai to fonta izmaiņas lapas ielādes laikā."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "<PERSON>onta piepra<PERSON>"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> if<PERSON>e"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Izkārtojuma nobīdes kopas sākuma laiks: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nevarēja noteikt izkārtojuma nobī<PERSON> c<PERSON>"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Nav izkārtojuma nobīžu"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Izkārtoju<PERSON> no<PERSON>"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Galvenie izkārtojuma nobī<PERSON>i"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Vissliktākā kopa"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Sliktākā<PERSON> iz<PERSON>oju<PERSON> nobī<PERSON> kopa"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Kešatmiņas TTL vērtība"}, "models/trace/insights/Cache.ts | description": {"message": "Iestatot ilgu keša<PERSON> m<PERSON>, lapas atkārtoti apmeklējumi varētu paātrināties. [Uzziniet vairāk](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nav pieprasīju<PERSON> ar neefektīvām kešatmiņas politikām"}, "models/trace/insights/Cache.ts | others": {"message": "Vēl {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Pie<PERSON><PERSON>ī<PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Efektīva kešatmiņas darbības ilguma i<PERSON>"}, "models/trace/insights/DOMSize.ts | description": {"message": "Liels DOM koks var paildzināt stila aprēķinus un izkārtojuma plūduma pārk<PERSON>, ietekmējot lapas reaģētspēju. Liels DOM koks arī palielinās atmiņas lietojumu. [U<PERSON><PERSON>t, kā izvairīties no pārmērīga DOM lieluma](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elements"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM dziļums"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statist<PERSON><PERSON> dati"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM lieluma optimizācija"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Elementu k<PERSON>ts"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "<PERSON><PERSON><PERSON> pirmais tīkla pieprasījums ir vissvarīg<PERSON>.  Samaziniet latentumu, novē<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ātru servera atbildi un iespējojot teksta sa<PERSON>."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON>ika veikta no<PERSON> ({PH1} novir<PERSON><PERSON><PERSON><PERSON>, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON> atbildēja lēni (novērotais ātrums: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Saspiešana netika lietota."}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON>z<PERSON>irās no novirz<PERSON>šanas"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Serveris atbild ātri (novērotais ātrums: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Tiek lietota teksta sasp<PERSON>šana"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Servera atbildes laiks"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Dokumenta pieprasījuma latentums"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Nesaspiestu datu le<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON>i"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Avots"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, dub<PERSON><PERSON><PERSON> JavaScript moduļus no pakām, lai sama<PERSON>tu nevajad<PERSON><PERSON><PERSON> baitu a<PERSON>, ko patērē tīkla darb<PERSON>."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript kodu dublikāti"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Lai teksts vienmēr būtu redzams, ieteicams parametram [font-display](https://developer.chrome.com/blog/font-display) iestatīt vērtību swap vai optional. Lai mazinātu izkārt<PERSON><PERSON><PERSON> nobīdes, swap var optimiz<PERSON>t vēl v<PERSON>, i<PERSON><PERSON><PERSON><PERSON> [fonta rādītā<PERSON> ignor<PERSON>](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Fonts"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Fonta attēlojums"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Nelietder<PERSON><PERSON> i<PERSON> laiks"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(an<PERSON><PERSON><PERSON>)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Daudzi API, lasot izkārto<PERSON><PERSON> ģeometriju, liek renderēšanas programmai apturēt skripta izpildi, lai aprēķinātu stilu un izkārtojumu. Uzziniet vairāk par [plūduma piespiedu pā<PERSON>](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) un tās novēršanu."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Steka trasējums"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON>ū<PERSON><PERSON> piespiedu p<PERSON>"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Visilgākais funkcijas izsaukums"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> plū<PERSON><PERSON> p<PERSON> laiks"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[neat<PERSON><PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Samazinot attēlu lejupielādes laiku, var tikt uzlabots lapas ielādes laiks un LCP. [Uzziniet vairāk par attēlu lieluma optimizāciju](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (apt. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nav optimiz<PERSON><PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON><PERSON><PERSON> lie<PERSON>a optimiz<PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Vēl {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Attēlu piegādes uzla<PERSON>"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Attēla sa<PERSON> koeficienta pali<PERSON> var uzlabot šī attēla lejupielā<PERSON>."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "<PERSON><PERSON> attēla <PERSON> (WebP, AVIF) izman<PERSON>šana vai attēlu saspiešanas koeficienta palielināšana varētu uzlabot š<PERSON> attēla leju<PERSON>l<PERSON>."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON><PERSON> attēla faila lie<PERSON> ({PH1}) pārsniedz attēlošanai vajadzīgo lielum<PERSON> ({PH2}). Izmantojiet adapt<PERSON>vos attēlus, lai samazin<PERSON>tu attēla leju<PERSON>l<PERSON> lielum<PERSON>."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Izmantojot video form<PERSON><PERSON>, nevis G<PERSON> attēlus, var uzlabot animēta satura lejupielādes lie<PERSON>u."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Vispirms izpētiet garāko posmu. [<PERSON><PERSON><PERSON><PERSON> var samazināt](https://web.dev/articles/optimize-inp#optimize_interactions). <PERSON> apstrā<PERSON> ilgumu, [optimiz<PERSON><PERSON><PERSON> galvenā <PERSON> i<PERSON>ma<PERSON>](https://web.dev/articles/optimize-long-tasks) (parasti JS)."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Ievades aiz<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nav konstatēta <PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> il<PERSON>s"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP pēc posma"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizējiet LCP, padarot LCP attēlu nekavējoties [atklājamu](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) HTML struktūrā un [novēršot atlikto ielādi](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Tiek lietota vērtība fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "atliktā ielāde netiek lietota"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP attēls tika ielādēts {PH1} pēc agrākā sāku<PERSON>ta."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Netika konstatēts LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Netika konstatēts neviens LCP resurss, jo LCP nav attēls"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Pieprasīju<PERSON> ir atrodams sākotnējā dokumentā"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP pieprasījuma atklāšana"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Katram [posmam ir noteiktas uz<PERSON>bo<PERSON> stratēģijas](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideālā gadījumā lielāko daļu LCP laika ir jāveido resursu iel<PERSON>dei, nevis aizkavei."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Elementa atveidošanas aizkave"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "<PERSON><PERSON> 75. procentile"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Netika konstatēts LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Re<PERSON>rsa iel<PERSON> aiz<PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON><PERSON><PERSON> iel<PERSON> il<PERSON>s"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Laiks līdz pirma<PERSON>m baitam"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP pēc fāzes"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>i"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Pārlūka ielikumi un pārveidošanas kodi ļauj vecākos pārlūkos izmantot jaunas JavaScript funkcijas. Tomēr modernos pārlūkos daudzi no tiem nav nepieciešami. Ieteicams mainīt JavaScript būvēšanas procesu, lai netiktu transpilētas [pamata līmeņa](https://web.dev/articles/baseline-and-polyfills) funkcijas, ja vien nezināt, ka jums ir jāatbalsta vecāki pārlūki. [Uzziniet, kāpēc lielākajā daļā vietņu var izvietot ES6+ kodu bez transpilēšanas](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)."}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Mantotais JavaScript kods"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 un HTTP/3 sniedz daudzas priekšrocības salīdzinājumā ar HTTP/1.1, pie<PERSON><PERSON><PERSON>, multipleks<PERSON><PERSON><PERSON>. [Uzziniet vairāk par modernā HTTP i<PERSON>](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Neviens pieprasījums neizmanto protokolu HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokols"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Pie<PERSON><PERSON>ī<PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderns HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON>ā<PERSON>mad<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Pie<PERSON><PERSON>ī<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Avots"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Laiks"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Aptuvenais LCP ietaupījums"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Neizmantota iepriekšēja pieslēgšanās. <PERSON><PERSON><PERSON>, vai atribūts crossorigin tiek izmantots pareizi."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Novērsiet kritisko pieprasījumu ķēdes](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains), sama<PERSON>ot ķēžu garumu, samazinot resursu lejupielādes apjomu vai atliekot nevajadzīgo resursu lejupielādi, lai uzlabotu lapas ielādi."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Pievienojiet [iepriek<PERSON><PERSON><PERSON> savienojuma izveides](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) norādes saviem svarīgākajiem avotiem, taču centieties izmantot ne vairāk par četriem avotiem."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Iepriek<PERSON><PERSON><PERSON><PERSON><PERSON> kandi<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritiskā ceļa latentums:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nav neviena atveido<PERSON>, ko ietekmētu tīkla atkarības"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nav nevienas papildu <PERSON>, kas būtu pie<PERSON> i<PERSON>riekšēja<PERSON> pie<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "neviena sākumadrese netika iepriekš <PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[iepriekšē<PERSON> pieslēgšanas](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) norādes palīdz pārlūkprogrammai izveidot savienojumu agrāk lapas ielādes laikā, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>ot laiku, kad tiek iesniegts pirmais pieprasījums attiecīgajai sākumadresei. Tālāk ir norādītas sākumlapas, kurām lapa tika iepriekš pieslēgta."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON> at<PERSON> koks"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Tika atrasti vairāk nekā četri preconnect savienojumi. Tie jāizmanto ierobežoti un tikai pašām svarīgākajām sākumadresēm."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Neizmantota iepriekšēja pieslēgšanās. Izmantojiet preconnect savienojumus tikai s<PERSON>, ko lapa, v<PERSON><PERSON><PERSON><PERSON>, pie<PERSON><PERSON><PERSON><PERSON>."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Novērsiet kritisko pieprasījumu ķēdes, sama<PERSON>ot ķēžu garumu, samazinot resursu lejupielādes apjomu vai atliekot nevajadzīgo resursu lejupiel<PERSON>di, lai uz<PERSON><PERSON>u lapas ielādi."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Pieprasījumi bloķē lapas sākotn<PERSON> atveidi, un tas var aizkavēt LCP. [Atliekot vai iekļaujot](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) šos tīkla piepras<PERSON>, tos varat pārvietot ārpus kritiskā ceļa."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Šai navigācijai nav atveidošanas bloķēšanas pieprasījumu"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Pie<PERSON><PERSON>ī<PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Atveidošanas bloķēšanas pieprasījumi"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ja izmaksas par stila pārrēķināšanu saglabājas augstas, tās var samazināt atlasītāja optimizācija. [Optimizējiet atlasītājus](https://developer.chrome.com/docs/devtools/performance/selector-stats), iestatot augstu pagājušā laika vērtību un augstu lēna ceļa % vērtību. Atbilstības izmaksas arī var samazināt, izmantojot vienkāršāku atlasītāju, mazāku skaitu atlasītāju, mazāku DOM un DOM koku ar mazāku līmeņu skaitu."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>s"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Netika atrasti CSS atlasītāja dati. Veiktspējas paneļa iestatījumos ir jāiespējo CSS atlasītāja statistika."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Mēģinājumi noteikt atbilstību"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Atbilstību skaits"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS atlasītāja izmaksas"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Kopā"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Galvenā <PERSON> laiks"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> puse"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>u lie<PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Tre<PERSON><PERSON><PERSON> puses kods var ievērojami ietekmēt ielādes veiktspēju. Lai piešķirtu prioritāti savas lapas saturam, [ierobežojiet un atlieciet trešās puses koda ielādi](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON> atrasts trešās puses saturs"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses"}, "models/trace/insights/Viewport.ts | description": {"message": "<PERSON>a skatvieta nav optimizēta mobilajām ier<PERSON>, mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar pieskārieniem var būt [aizkave līdz pat 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Pieskāriena aizkave mobilaj<PERSON> i<PERSON>"}, "models/trace/insights/Viewport.ts | title": {"message": "Skatvietas optimizācija mobilajām ierīcēm"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, saņemot GET pie<PERSON>, ir piemērotas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Kešatmiņā var saglabāt tikai lapas ar statusa kodu 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Pārlūkā Chrome tiks konstatēts mēģinājums izpildīt JavaScript kodu kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek pieprasīts AppBanner, pa<PERSON><PERSON>k nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Pilnīgu saglab<PERSON><PERSON><PERSON> kešatmiņā atspējoja karodziņu iestatījumi. <PERSON> šajā ierīcē to iesp<PERSON><PERSON><PERSON> lo<PERSON>, atveriet lapu chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Komandrinda atspējoja pilnīgu saglab<PERSON><PERSON><PERSON> ke<PERSON>tmiņ<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "<PERSON>ln<PERSON><PERSON> sagla<PERSON><PERSON> ke<PERSON> ir at<PERSON>, jo atmiņ<PERSON> pietrū<PERSON> vietas."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Pilnīgu saglab<PERSON><PERSON><PERSON> kešatmiņ<PERSON> neatbalsta deleģētais elements"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON>ādes atveidotājs atspējoja pilnīgu saglab<PERSON><PERSON><PERSON> ke<PERSON>tmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON><PERSON> nevar saglab<PERSON>t k<PERSON>, jo tajā ir BroadcastChannel instance ar reģistrētiem klausītājiem."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> ir galvene cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON><PERSON><PERSON>ņ<PERSON> tika mērķtiecīgi notīrīta."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON><PERSON><PERSON> lapas dati tika izlikti no kešatmiņas, lai kešatmiņā varētu saglabāt citu lapu."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Lapas ar spraudņiem pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Nav definēts"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne FileChooser API, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne File System Access API, nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izman<PERSON>ts multivides ierī<PERSON><PERSON> dispe<PERSON>, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> brīdī darboj<PERSON>s multivides atskaņotājs."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek izmantota saskarne MediaSession API un kurās ir iestatīts atskaņošanas stāvoklis, nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek izmantota saskarne MediaSession API un kurās ir iestatīti darbību apdar<PERSON>ātāji, nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir atspējota ekrāna lasītāja dēļ."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON> apdarinā<PERSON><PERSON><PERSON><PERSON>, nav piemērotas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne Serial API, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON>, k<PERSON><PERSON>s tiek izmantota saskarne WebAuthetication API, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebBluetooth API, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebUSB API, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Pilnīga saglabā<PERSON>na kešatmiņ<PERSON> ir atspējota, jo <PERSON>, kur<PERSON> tiek izmantota direktīva Cache-Control: no-store, ir atspējoti s<PERSON>k<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne DedicatedWorker vai <PERSON>let, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokuments tika aizvērts vēl pirms bija pabeigta tā ielāde."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rādīts lietotnes reklāmkarogs."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs Chrome paroļu pārvaldnieks."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, notika DOM attīrīšana."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs DOM attīrītāja skatītājs."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir at<PERSON>, jo pap<PERSON>šināju<PERSON> izmantoja ziņojumapmaiņas API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Paplašinājumiem ar ilgstošu savienojumu jāpārtrauc savienojums pirms pilnīgas saglabāšanas kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> ar il<PERSON><PERSON><PERSON>, tika mēģināts sūt<PERSON>t ziņojumus uz i<PERSON>, kas pilnīgi saglabāti kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Pilnīga saglab<PERSON><PERSON> kešatmiņ<PERSON> ir atspējota paplaš<PERSON><PERSON><PERSON><PERSON> dē<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika parād<PERSON>ts modāls lapas dialoglodzi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, veidlapas atkārtotas iesniegšanas vai http paroļu dialoglodziņš."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rād<PERSON>ta be<PERSON> lapa."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rā<PERSON><PERSON><PERSON> josla par atmiņas trūkumu."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija atļ<PERSON><PERSON> pieprasījumi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs uznirstošo elementu bloķētājs."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika parā<PERSON><PERSON><PERSON> d<PERSON> p<PERSON><PERSON> informāci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Funk<PERSON>j<PERSON> <PERSON><PERSON><PERSON><PERSON>” šī lapa tika identificēta kā ļaunprātīga un tika bloķēts uznirstošais logs."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON> norit<PERSON>ja lapas pilnīga saglabā<PERSON> ke<PERSON>miņ<PERSON>, tika aktivizēts pakalpojuma skripts."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Pilnīga saglabā<PERSON>na kešatmiņā ir atspējota dokumenta kļūdas dēļ."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON>ts elements FencedFrames, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON><PERSON><PERSON> lapas dati tika izlikti no kešatmiņas, lai kešatmiņā varētu saglabāt citu lapu."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir piešķirta piekļ<PERSON> multivides straumei, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir noteikta veida iegults saturs (piemēram, PDF faili), pa<PERSON>laik nav piemērotas pilnīgai saglabā<PERSON><PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izman<PERSON>, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir atvērts IndexedDB savienojums, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir atspējota IndexedDB notikuma dēļ."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Tika izmantotas nepiemērotas API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmi<PERSON>, kur<PERSON><PERSON> paplašinājumi iepludina JavaScript."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmi<PERSON>, kur<PERSON><PERSON> pap<PERSON>inājumi iepludina StyleSheet."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Iekš<PERSON>ja <PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Pilnīga saglabā<PERSON> ke<PERSON>mi<PERSON> ir at<PERSON>, jo kāds JavaScript tīkla pieprasījums saņēma resursu ar galveni <PERSON>ache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Pilnīga saglab<PERSON><PERSON> keša<PERSON>mi<PERSON> ir at<PERSON>, jo tika saņemts saites darbības pārbaudes ziņojuma sūtīšanas pieprasīju<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek izman<PERSON>ta tastatūras bloķēšana, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Lapa tika aizvērta vēl pirms bija pabeigta tās iel<PERSON>de."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, kuru galvenā resursa direktīva ir cache-control:no-cache, nevar pilnīgi saglabāt kešatmiņ<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, kuru galvenā resursa direktīva ir cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika pārtraukta pirms kešatmiņā pilnīgi saglabātas lapas atjauno<PERSON>nas."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Lapas dati tika izlikti no ke<PERSON><PERSON><PERSON>, jo aktīvajā tīkla savienojumā tika saņemts pārmērīgs daudzums datu. Pārlūkā Chrome tiek ierobežots datu daudzums, ko lapa var saņemt, kad tiek saglabāta kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvi i<PERSON> vai XHR notikumi, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "<PERSON><PERSON><PERSON> lapas dati tika izlikti no kešatmiņas, jo aktīvs tīkla savienojuma pieprasījums ietvēra novirzī<PERSON>nu."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "<PERSON><PERSON><PERSON> lapas dati tika izlikti no kešatmiņas, jo tīkla savienojums bija atvērts pārāk ilgi. Pārlūkā Chrome tiek ierobežots laiks, k<PERSON><PERSON> lapa var saņemt datus, kam<PERSON>r norit tās saglab<PERSON><PERSON>na kešatmiņ<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON>, kurām nav derīgas atbildes galvenes, nevar pilnīgi saglabāt kešatmiņ<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON>na notika citā ietvarā, nevis galvenajā ietvarā."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> notiek IndexedDB operācijas, pašlaik nav piemērotas pilnīgai saglabāšanai atmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla piepra<PERSON>, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla i<PERSON> piepra<PERSON>, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla piepra<PERSON>, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs XHR tīkla pieprasījums, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izman<PERSON>ta saskarne <PERSON>, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izman<PERSON>ta funkcija “Attēls attēlā”, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>, pa<PERSON><PERSON><PERSON> nav piemērotas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "<PERSON>pa tika atvērta, <PERSON><PERSON><PERSON><PERSON><PERSON> metodi window.open(), un citā cilnē ir atsauce uz šo lapu, vai arī lapa tika atvērta logā."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Kešatmiņā pilnīgi saglabātas lapas atveidotājs a<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Kešatmiņā pilnīgi saglabātas lapas atveidotāja darbība tika pārtraukta."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika pieprasītas audio ierakstīšanas atļaujas, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepras<PERSON> atļ<PERSON>jas, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepras<PERSON>tas atļaujas sinhronizācijai fonā vai i<PERSON>, pa<PERSON>laik nav piemērotas pilnīgai sagla<PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika pieprasīta MIDI ierīču atļauja, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepras<PERSON>tas paziņoju<PERSON> atļaujas, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tika piepras<PERSON>ta piek<PERSON> kr<PERSON>tuvei, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tika piepras<PERSON> video iera<PERSON><PERSON><PERSON><PERSON><PERSON> atļaujas, pa<PERSON><PERSON>k nav piemērotas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var saglabāt tikai tādas lapas, kuru URL shēma ietver HTTP vai HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON><PERSON> noritēja lapas pilnīga saglabā<PERSON>na ke<PERSON>miņ<PERSON>, lapu pieprasīja pakalpojuma skrip<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Pakalpojuma skripts mēģināja nosūtīt rekvizītu MessageEvent lapai, kas tiek pilnīgi saglabāta kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker reģistrācija tika atcelta, kamēr noritēja lapas pilnīga saglabāšana kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Lapas dati tika izlikti no kešatmiņas, jo tika aktivizēts pakalpojuma skripts."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Pārlūks Chrome tika <PERSON>, un kešatmiņā pilnīgi saglabātie ieraksti tika notīrīti."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izmantota saskarne <PERSON>d<PERSON>orker, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izmantota saskarne SpeedRecognizer, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne SpeechSynthesis, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "<PERSON><PERSON>e ietvarā tika sākta p<PERSON>, bet tā netika pabe<PERSON>ta."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, kuru apakšresursa direktīva ir cache-control:no-cache, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, kuru apakšresursa direktīva ir cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON> pā<PERSON>nie<PERSON><PERSON> maksim<PERSON>s laiks lapas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā, un lapas derīguma termi<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Sākot pilnīgu sa<PERSON><PERSON><PERSON><PERSON>, lap<PERSON> iest<PERSON> noil<PERSON> (iespējams, to izraisīja ieil<PERSON>i lapas paslēpšanas notikumu apdarin<PERSON><PERSON><PERSON> darb<PERSON>)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Lapas galvenajā ietvarā ir iel<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON> a<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Lapas apa<PERSON>š<PERSON> ir i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> a<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tika mainīta lietotāja aģenta ignorēšanas galvene."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> ir piešķirta piekļuves video vai audio ierakstīšanai, pa<PERSON><PERSON>k nav piemērotas pilnīgai saglabāša<PERSON> kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "<PERSON><PERSON>, kur<PERSON>s tiek izmantots WebDatabase, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebHID, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebLocks, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebNfc, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebOTPService, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebRTC, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Pilnīga sagla<PERSON><PERSON> ke<PERSON> ir at<PERSON>, jo tika izmantots protokols WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantots starpniekserveris WebShare, pašlaik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantots protokols WebSocket, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Pilnīga sagla<PERSON> ke<PERSON> ir at<PERSON>, jo tika izmantots protokols WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebTransport, nevar pilnīgi saglabāt kešatmiņā."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Pilnīga sagla<PERSON><PERSON> ke<PERSON> ir <PERSON>, jo tika izmantots protokols WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebXR, pa<PERSON>laik nav piemērotas pilnīgai saglabāšanai kešatmiņā."}}