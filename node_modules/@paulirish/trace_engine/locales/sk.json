{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Autorizácie sa nebude týkať zástupný symbol (*) pri spracovaní CORS Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Ak chcete zakázať predvolenú integráciu prenosu, použite namiesto selektora -internal-media-controls-overlay-cast-button atribút disableRemotePlayback."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Hodnota vzhľadu šablón kaskádo<PERSON><PERSON>ch štýlov slider-vertical nie je štandardizo<PERSON> a bude odstránená."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Požiadavky zdr<PERSON><PERSON><PERSON>, k<PERSON><PERSON>ch webové adresy zahrnujú odstránené prázdne znaky \\(n|r|t) a znaky „menej ako“ (<), sú blokované. Ak chcete načítať tieto zdroje, odstráňte nové riadky a kódujte znaky „menej ako“ z miest, ako sú hodnoty atribútu prvkov."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Podpora funkcie chrome.loadTimes() bola ukončená. Namiesto nej použite štandardizované rozhranie API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Podpora funkcie chrome.loadTimes() bola ukončená. Namiesto nej použite štandardizované rozhranie API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Podpora funkcie chrome.loadTimes() bola ukončená. Namiesto nej použite štandardizované rozhranie API: nextHopProtocol v rámci funkcie Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Súbory cookie so znakom \\(0|r|n) budú namiesto skrátenia odmietnuté."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Podpora uvoľnenia pravidla z rovnakých zdrojov nastavením funkcie document.domain bola ukončená a táto možnosť bude predvolene zakázaná. Upozornenie na ukončenie podpory pochádza z prístupu z iných zdrojov, ktorý bol povolený nastavením funkcie document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Podpora spúšťania funkcie window.alert z prvkov iframe z rôznych zdrojov bola ukončená a v budúcnosti bude odstránená."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Podpora spúšťania funkcie window.confirm z prvkov iframe z rôznych zdrojov bola ukončená a v budúcnosti bude odstránená."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Podpora údajov: podpora webových adries v prvku SVGUseElement bola ukončená a v budúcnosti bude odstránená."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Funkcie getCurrentPosition() a watchPosition() už nefungujú v nezabezpečených zdrojoch. Ak chcete túto funkciu používať, mali by ste zvážiť prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Podpora funkcií getCurrentPosition() a watchPosition() bola ukončená v nezabezpečených zdrojoch. Ak chcete túto funkciu p<PERSON>žívať, mali by ste zv<PERSON><PERSON><PERSON><PERSON> prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() už nefunguje v nezabezpečených zdrojoch. Ak chcete túto funkciu p<PERSON>žívať, mali by ste zv<PERSON><PERSON>i<PERSON> prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "V prvku <article>, <aside>, <nav> alebo <section> bola n<PERSON> z<PERSON> <h1>, ktor<PERSON> nemá špecifikovanú veľkosť písma. V blízkej budúcnosti sa veľkosť tohto textu nadpisu v tomto prehliadači zmení. Viac sa dozviete na https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 for more information."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Podpora funkcie RTCPeerConnectionIceErrorEvent.hostCandidate bola ukončená. Použite namiesto nej funkciu RTCPeerConnectionIceErrorEvent.address alebo RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Tento form<PERSON> navigator.credentials.get() o digitálne prihlasovacie údaje je zastaraný. Aktualizujte volanie tak, aby p<PERSON>ž<PERSON><PERSON>o nový formát."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Podpora údajov o zdroji obchodníka a ľubovoľných údajov z udalosti obsluhy canmakepaymentbola ukončená a tieto údaje budú odstránené: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Web požiadal o podzdroj zo siete, ku ktorému má prístup iba na základe privilegovanej pozície siete svojich používateľov. Tieto požiadavky vystavujú neverejné zariadenia a servery internetu, čím zvyšujú riziko útoku zahrnujúceho falšovanie požiadaviek z iného webu alebo úniku informácií. Na zmiernenie týchto rizík Chrome ukončuje podporu požiadaviek neverejných podzdrojov pri ich odoslaní z nezabezpečených kontextov a začne ich blokovať."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Pole dailyUpdateUrl položky InterestGroups odovzdané položke joinAdInterestGroup() bolo premenované na updateUrl, aby presnejšie vyjadrovalo vlastné správanie."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Podpora volania Intl.v8BreakIterator bola ukončená. Namiesto neho použite volanie Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Šablóny CSS nie je možné načítať z webových adries file:, pokiaľ sa nekončia príponou súboru .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Podpora použitia funkcie SourceBuffer.abort() na prerušenie odstránenia asynchrónneho rozsahu možnosti remove() bola ukončená z dôvodu zmeny špecifikácie. Podpora bude v budúcnosti odstránená. Namiesto toho by ste mali prijímať udalosť updateend. Funkcia abort() je určená iba na prerušenie pripojenia asynchrónnych médií alebo resetovanie stavu analyzátora."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Podpora nastavenia funkcie MediaSource.duration pod najvyššiu časovú pečiatku prezentácie akýchkoľvek kódovaných rámov vo vyrovnávacej pamäti bola ukončená z dôvodu zmeny špecifikácie. V budúcnosti bude odstránená podpora implicitného odstránenia skrátených médií vo vyrovnávacej pamäti. Namiesto toho by ste mali vykonať explicitnú funkciu remove(newDuration, oldDuration) v prípade všetkých možností sourceBuffers, kde platí newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI požiada o povolenie na použitie, aj keď exkluzívne správy systému (SysEx) nie sú vo funkcii MIDIOptions špecifikované."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Rozhranie Notification API už nie je možné používať z nezabezpečených zdrojov. Mali by ste zv<PERSON>ži<PERSON> prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Povolenie pre rozhranie Notification API už nie je možné požadovať z prvku iframe od iných zdrojov. Namiesto toho by ste mali zvážiť požadovanie povolenia z rámu najvyššej úrovne alebo otvorenie nového okna."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Podpora možnosti imageOrientation: 'none' v nástroji createImageBitmap bola ukončená. Použite namiesto nej nástroj createImageBitmap pomocou možnosti {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> partner p<PERSON><PERSON><PERSON><PERSON> (D)TLS. Požiada<PERSON><PERSON> ho, aby tento problém odstránil."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Ak v značkách img, značkách videa a značkách canvas špecifikujete atribútoverflow: visible, môže to spôsobiť, že vytvoria vizuálny obsah mimo hraníc prvku. Prejdite na https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "Podpora funkcie paymentManager.instruments bola ukončená. Použite namiesto nej inštaláciu v pravej chvíli pre obslužné nástroje platieb."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Volanie PaymentRequest obišlo direktívu connect-src pravidiel na zabezpečenie obsahu (PZO). Podpora tohto obchádzania bola ukončená. Pridajte identifikátor spôsobu platby z rozhrania API PaymentRequest (v poli supportedMethods) do direktívy connect-src svojich PZO."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "Podpora funkcie StorageType.persistent bola ukončená. Použite namiesto nej štandardizovanú funkciu navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Funkcia <source src> s na<PERSON><PERSON><PERSON> funk<PERSON> <picture> je <PERSON><PERSON>, a preto bude ignorovaná. Namiesto nich použite funkciu <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame je metóda špecifická pre konkrétneho dodávateľa. Použite namiesto nej štandardnú metódu cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame je metóda špecifická pre konkrétneho dodávateľa. Použite namiesto nej štandardnú metódu requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitDisplayingFullscreen bola ukončená. Použite namiesto neho Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitEnterFullScreen() bola ukončená. Použite namiesto neho Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitEnterFullscreen() bola ukončená. Použite namiesto neho Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitExitFullScreen() bola ukončená. Použite namiesto neho Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitExitFullscreen() bola ukončená. Použite namiesto neho Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitSupportsFullscreen bola ukončená. Použite namiesto neho Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Ukončujeme podporu rozhrania API chrome.privacy.websites.privacySandboxEnabled, ale zostane aktívne na zaistenie spätnej kompatibility až do vydania verzie M113. Používajte namiesto neho chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled a chrome.privacy.websites.adMeasurementEnabled. Viac sa dozviete na https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Obmedzenie DtlsSrtpKeyAgreement bolo odstránené. Pre toto obmedzenie ste špecifikovali hodnotu false, ktor<PERSON> je interpretovaná ako pokus o použitie odstránenej metódy SDES key negotiation. T<PERSON>to funkcia bola odstránená. Namiesto nej použite službu, ktorá podporuje funkciu DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Obmedzenie DtlsSrtpKeyAgreement bolo odstránené. Pre toto obmedzenie ste špecifikovali hodnotu true, ktorá nemala žiadny účinok. Toto obmedzenie však môžete odstrániť pre poriadok."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Podpora metódy callback-based getStats() bola ukončená. <PERSON><PERSON><PERSON> metóda bude odstránená. Použite namiesto nej spec-compliant getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Podpora rozhrania Range.expand() bola ukončená. Použite namiesto neho Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Požiadav<PERSON>dr<PERSON>jov, ktor<PERSON>ch webové adresy zahrnujú vložené p<PERSON>ovacie ú<PERSON> (napr. **********************/), s<PERSON> blokovan<PERSON>."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Podpora možnosti rtcpMuxPolicy bola ukončená a táto možnosť bude odstránená."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer bude v<PERSON>žadovať izoláciu od iných zdrojov. Viac sa dozviete na https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Podpora funkcie speechSynthesis.speak() bez aktivácie používateľa bola ukončená a funkcia bude odstránená."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Podpora prijímačov udalostí uvoľnenia z pamäte bola ukončená a prijímače budú odstránené."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Rozšírenia by sa mali prihlásiť do izolovania od iných zdrojov, ak chcú ďalej používať funkciu SharedArrayBuffer. Prejdite na https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Podpora atribútu GPUAdapter isFallbackAdapter bola ukončená. Použite namiesto neho atribút GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON odpovede nepodporuje kódovanie UTF‑16 v rámci funkcie XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Podpora synchrónnej funkcie XMLHttpRequest v hlavnom vlákne bola ukončená pre nepriaznivé účinky na prostredie koncového používateľa. Viac sa dozviete na https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Zmeny rozloženia sa vyskytujú, keď sa prvky pohnú bez interakcie používateľa. [Vyšetrite príčiny zmien rozloženia](https://web.dev/articles/optimize-cls), ako s<PERSON> pridávanie, odstraňovanie prvkov alebo zmena ich písiem pri načítavaní stránky."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Žiadosť o písmo"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Vložený prvok iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> zmeny rozloženia @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nepodarilo sa rozpoznať žiadne príčiny zmien rozloženia"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Žiadne zmeny rozloženia"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Príčiny zmien rozloženia"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Najčastejšie príčiny zmien rozloženia"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Obrázkový prvok bez rozmerov"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Najhorší klaster zmeny rozloženia"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL vyrovnávacej pamäte"}, "models/trace/insights/Cache.ts | description": {"message": "Dlhá životnosť vyrovnávacej pamäte môže zrýchliť opakované návštevy stránky. [Ďalšie informácie](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Žiadne požiadavky s neefektívnymi pravidlami vyrovnávacej pamäte"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON><PERSON><PERSON> ({PH1})"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Požiadavka"}, "models/trace/insights/Cache.ts | title": {"message": "Efektívne využívanie celých období vyrovnácej pamäte"}, "models/trace/insights/DOMSize.ts | description": {"message": "Rozsiahly model DOM môže predĺžiť výpo<PERSON><PERSON> štýlov a preformátovaní rozložení, čím ovplyvní responzívnosť stránky. Okrem toho zvýši využitie pamäte. [Ako sa vyhnúť nadmernej veľkosti modelu DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "Prvok"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Najviac podradených položiek"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Hĺbka modelu DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Štatistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimalizácia veľkosti modelu DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Celkový počet prvkov"}, "models/trace/insights/DOMSize.ts | value": {"message": "Hodnota"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Vaša prvá požiadavka siete je najdôležitejšia.  Zabr<PERSON><PERSON><PERSON> presmerovaniam, z<PERSON><PERSON> rýchlu odozvu servera a povoľte kompresiou textu, aby ste zn<PERSON><PERSON><PERSON> latenc<PERSON>."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Požiadavka obsahovala presmerovania (počet: {PH1}, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Server odpovedal pomaly (pozorované {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nebola použitá žiadna kompresia"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Vyhýba sa presmerovaniam"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Server odpoved<PERSON> rýchlo (pozorované {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Používa sa kompresia textu"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Presmerovania"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Čas odpovede servera"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latencia žiadosti o dokument"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Neskomprimovaný súbor na stiahnutie"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Odstráňte z balíčkov veľké duplicitné moduly JavaScriptu, aby ste zredukovali nepotrebné bajty spotrebované aktivitou siete."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplicitný JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Zvážte nastavenie písma [font-display](https://developer.chrome.com/blog/font-display) na swap alebo optional, aby bol text konzistentne viditeľný. swap je možné ďalej optimalizovať a zjemniť tak zmeny rozloženia [prepísaním metriky písma](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Písmo"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Zob<PERSON>nie <PERSON>"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Stratený čas"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonymné)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Mnohé rozhrania API, k<PERSON><PERSON> čítajú geometriu rozlo<PERSON>, nútia vykresľovací modul pozastaviť spustenie skriptu, aby mohli vypočítať štýl a rozloženie. [Ďalšie informácie o vynútenom preformátovaní a jeho zmierňovaní](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Sledovanie zásobníka"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON>yn<PERSON><PERSON><PERSON> preform<PERSON>"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Najčastejšie volanie funkcie"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Celkový čas preformátovania"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[bez at<PERSON><PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Znížením času sťahovania obrázkov môžete zlepšiť registrovaný čas načítania stránky a vykreslenie najväčšieho prvku. [Ďalšie informácie o optimalizácii veľkosti obrázkov](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (odhad. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Žiadne optimalizovateľné obrázky"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimalizácia veľkosti súboru"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON><PERSON><PERSON> ({PH1})"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Zlepšenie zobrazovania obrázkov"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Zvýšením faktora kompresie obrázka sa môže zlepšiť jeho veľkosť na stiahnutie."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Veľkosť tohto obrázka na stiahnutie by sa mohla zlepšiť použitím moderného formátu o<PERSON>r<PERSON> (WebP, AVIF) alebo zvýšením kompresie obrázka."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Tento súbor obrázka je v<PERSON>, než má <PERSON> ({PH1}) vzhľadom na jeho zobrazené rozmery ({PH2}). Znížte veľkosť obrázka na stiahnutie použitím responzívnych obrázkov."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Používanie videoformátov namiesto gifov môže zlepšiť veľkosť animovaného obsahu na stiahnutie."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Začnite skúmať najdlhšiu fázu. [Oneskorenia sa dajú minimalizovať](https://web.dev/articles/optimize-inp#optimize_interactions). Ak chcete skrátiť čas spracovania, [optimalizujte náklady hlavného vlákna](https://web.dev/articles/optimize-long-tasks), vo v<PERSON><PERSON><PERSON><PERSON> prípadov JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Trvanie"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Oneskorenie vstupu"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Neboli zistené žiadne interakcie"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Oneskorenie prezentácie"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Trvanie spracovania"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Oneskorenie vykreslenia po interakcii podľa fázy"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimalizujte vykreslenie najväčšieho prvku tým, že jeho obrázok okamžite [zviditeľníte](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) v jazyku HTML a [zabránite lenivému načítaniu](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "prioritanačítania=použitá možnosť Vysoká"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Mala by by<PERSON> použitá hodnota fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "lenivé načítanie nebolo použité"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Obrázok vykreslenia najväčšieho prvku bol načítaný {PH1} po najskoršom začatí."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nebolo zistené žiadne vykreslenie najväčšieho prvku"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nebol zistený žiadny zdroj vykreslenia najväčšieho prvku, pretože vykreslenie najväčšieho prvku nie je obrázok"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Žiadosť je viditeľná v pôvodnom dokumente"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Nepriame vyhľadávanie žiadostí o vykreslenie najväčšieho prvku"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON> [fáza má špecifické stratégie zlepšenia](https://web.dev/articles/optimize-lcp#lcp-breakdown). V ideálnom prípade by väčšina času vykreslenia najväčšieho prvku mala byť vynaložená na načítanie zdrojov, ktoré nie sú v rozsahu oneskorenia."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Trvanie"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Oneskorenie vykreslenia prvku"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75. percentil poľa"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nebolo zistené žiadne vykreslenie najväčšieho prvku"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Oneskorenie načítania zdroja"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Trvanie načítania zdroja"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Oneskorenie prvého bajtu"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Vykreslenie najväčšieho prvku podľa fázy"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Stratené baj<PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Viacnásobné vyplnenia a transformácie umožňujú starším prehliadačom používať nové funkcie JavaScriptu. Mnohé z nich však nie sú pre moderné prehliadače potrebné. Ak nutne nemusíte podporovať staršie prehlia<PERSON>, zv<PERSON>žte úpravu procesu kompilácie JavaScriptu tak, aby nedochádzalo k transpilovaniu [z<PERSON><PERSON>n<PERSON><PERSON>](https://web.dev/articles/baseline-and-polyfills) funkcií. [Prečo väčšina webov môže nasadiť kód ES6+ bez transpilácie](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Starý JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 a HTTP/3 ponúkajú v porovnaní s protokolom HTTP/1.1 mnoho výhod, napríklad multiplexnú moduláciu. [Ďalšie informácie o používaní moderného protokolu HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Žiadna požiadavka nepoužila HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Požiadavka"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderný protokol HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Požiadavka"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Čas"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Odhadované úspory vykreslenia najväčšieho prvku"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Nepoužívané pred<PERSON> pripo<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, či atrib<PERSON>t crossorigin používate správne."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Vyhnite sa zreťazeniu kľúčových žiadostí](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) skrátením dĺžky reťazcov, aby ste zn<PERSON><PERSON><PERSON> veľkosť sťahovan<PERSON>ch zdrojov, alebo odl<PERSON><PERSON>te sťahovanie nepotrebných zdrojov, <PERSON><PERSON><PERSON> zlepšíte načítanie stránky."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Pridajte indikátory [predbežn<PERSON>ho pripojenia](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pre najdôležitej<PERSON><PERSON> zdroje, ale snažte sa použiť najviac štyri."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidáti na predbežné pripojenie"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Maximálna latencia kľúčovej cesty:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Žiadne úlohy vykresľovania ovplyvnené dependenciami siete"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Žiadne ďalšie zdroje nie sú vhodnými kandidátmi na predbežné pripojenie"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "žiadne zdroje neboli predbežne pripojené"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Indikátory [predbežn<PERSON><PERSON> pripojenia](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pomáhajú prehliadaču nadviazať pripojenie skôr pri na<PERSON><PERSON><PERSON>í s<PERSON>, <PERSON>í<PERSON> sa šetrí čas pri prvej žiadosti o tento zdroj. Nasledujúce sú zdroje, ku ktorým sa stránka predbežne pripojila."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Predbežne pripojené zdroje"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Strom dependencií siete"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Našli sa viac ako štyri pripojenia preconnect. Mali by byť používané úsporne a iba pre najdôležitejšie zdroje."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Nepoužívané predbežné pripojenie. preconnect používajte iba v prípade zdrojov, o ktoré stránka pravdepodobne požiada."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Predíďte zreťazeniu kľúčových žiadostí skrátením dĺžky reťazcov, aby ste zn<PERSON><PERSON><PERSON> veľkosť sťahovan<PERSON><PERSON> zdrojov, alebo odlo<PERSON>te sťahovanie nepotrebných zdrojov, <PERSON><PERSON><PERSON> zlepšíte načítanie stránky."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Požiadavky blokuj<PERSON> počiatočné vykreslenie stránky, čo môže spôsobiť oneskorenie vykreslenia najväčšieho prvku. [Oneskorením alebo vložením](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) môžete tieto požiadavky na sieť presunúť mimo kritickej cesty."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Trvanie"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Pre túto navigáciu nie sú k dispozícii žiadne požiadavky na blokovanie vykreslenia"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Žiadosť"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Žiadosti o blokovanie vykreslenia"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ak sú náklady na prepočítanie štýlu stále vysoké, optimalizáciou selektora ich môžete znížiť. [Optimalizujte selektory](https://developer.chrome.com/docs/devtools/performance/selector-stats) s dlhým časom uplynutia a vysokým percentom pomalej cesty. Jednoduchšie selektory, men<PERSON>, <PERSON><PERSON><PERSON> model DOM a plytší model DOM znížia náklady na párovanie."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Uplynutý čas"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nenašli sa žiadne údaje selektora CSS. Štatistické údaje selektora CSS je potrebné povoliť v nastaveniach panela výkonnosti."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Pokusy o zhodu"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Počet zhôd"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Náklady selektora CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Najčastej<PERSON><PERSON> selektory"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Čas hlavného vlákna"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON>ia strana"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Veľkosť prenosu"}, "models/trace/insights/ThirdParties.ts | description": {"message": "<PERSON><PERSON>d tretej strany môže výrazne ovplyvniť výkonnosť načítavania. [Zredukujte a odložte načítanie kódu tretej strany](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), aby sa priorizoval obsah va<PERSON><PERSON> s<PERSON>."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Nenašli sa žiadne tretie strany"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON> strany"}, "models/trace/insights/Viewport.ts | description": {"message": "Ak oblasť zobrazenia nie je optimalizovaná pre mobily, interakcie klepnutím môžu byť [oneskorené až o 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Oneskorenie klepnutia v mobilnom zariadení"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimalizácia oblasti zobrazenia pre mobily"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Iba stránky načítané prostredníctvom požiadavky GET môžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Do vyrovnávacej pamäte sa dajú ukladať iba stránky so stavovým kódom 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome rozpoznal pokus o spustenie JavaScriptu počas uloženia vo vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> o AppBanner, <PERSON><PERSON>lne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná experimentálnymi funkciami. Prejdite na chrome://flags/#back-forward-cache a aktivujte ju miestne v tomto zariadení."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná príkazovým riadkom."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná z dôvodu nedostatočnej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Delegát nepodporuje spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná pre predbežný vykresľovací modul."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Stránka sa nedá uložiť do vyrovnávacej pamäte, pretože má inštanciu BroadcastChannel s registrovanými prijímačmi."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Strán<PERSON> s hlavičkou cache-control:no-store nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Vyrovnávacia pamäť bola zámerne vyčistená."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, aby bolo do nej možné ulož<PERSON>ť inú."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Stránky s doplnkami momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Neurčené"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Stránky s rozhraním FileChooser API nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Stránky s rozhraním File System Access API momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Stránky s nástrojom Media Device Dispatcher nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "V čase odchodu hral Prehrávač médií."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Stránky s rozhraním MediaSession API a nastaveným stavom prehrávania nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Stránky s rozhraním MediaSession API a nastavenými obslužnými nástrojmi akcií momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Čítačka obrazovky spôsobila deaktiváciu spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Stránky s nástrojom SecurityHandler nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Stránky s rozhraním Serial API nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Stránky s rozhraním WebAuthetication API nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Stránky s rozhraním WebBluetooth API nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Stránky s rozhraním WebUSB API nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná, preto<PERSON>e sú<PERSON>y cookie sú z<PERSON>zané na stránke, ktorá používa Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Stránky s vyhradenou obsluhou alebo workletom momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Načítavanie dokumentu nebolo dokončené, kým ste odli<PERSON> preč."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "V čase odchodu bol zobrazený banner aplikácie."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "V čase odchodu bol zobrazený správca hesiel Chromu"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "V čase odchodu prebiehala extrakcia pomocou nástroja DOM."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "V čase odchodu bol otvorený zobrazovač nástroja DOM Distiller."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Rozšírenia používajúce rozhranie Messaging API spôsobili deaktiváciu spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Rozšírenia s dlhotrvajúcim pripojením by mali pred získaním prístupu k spätnej vyrovnávacej pamäti pripojenie ukončiť."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Rozšírenia s dlhotrvajúcim pripojením sa pokúsili odoslať správy snímkam v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Rozšírenia spôsobili deaktiváciu spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "V čase odchodu bolo na stránke zobrazené modálne dialógové okno, ktoré sa mohlo napríklad týkať opätovného odoslania formulára alebo hesla HTTP."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "V čase odchodu bola zobrazená offline stránka."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "V čase odchodu bol zobrazený intervenčný pruh znamenajúci nedostatok pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "V čase odchodu bola odoslaná žiadosť o povolenie."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "V čase odchodu bola zobrazená možnosť blokovania vyskakovacích okien."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "V čase odchodu boli zobrazené podrobnosti Bezpečného prehliadania."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezpečné prehliadanie považuje túto stránku sa obťažujúcu a zablokovalo vyskakovacie okno."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Bola aktivovaná obsluha, keď sa stránka nachádzala v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná pre chybu v dokumente."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, nie je mož<PERSON>é uložiť v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, aby bolo do nej možné ulož<PERSON>ť inú."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> k <PERSON>u <PERSON>, <PERSON><PERSON>lne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Stránky s určitými druhmi vloženého o<PERSON>ahu (napr. súbormi PDF) momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Stránky s nástrojom IdleManager momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Stránky s otvoreným pripojením IndexedDB momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná v dôsledku udalosti IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Boli použité nevhodné rozhrania API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, do ktorých rozšírenia vložili JavaScript, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON>, do ktorých rozšírenia vložili šablónuStyleSheet, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Interná chyba."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná, pretože nejaká sieťová požiadavka v jazyku JavaScript dostala zdroj s hlavičkou Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná v dôsledku žiadosti so signálom keepalive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Strán<PERSON> so zámkou klávesnice momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Načítavanie stránky nebolo dokončené, kým ste odli<PERSON> pre<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hlavný zdroj má hlavičku cache-control:no-cache, momentálne nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hlavný zdroj má hlavičku cache-control:no-store, nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Na<PERSON><PERSON><PERSON><PERSON> bola zrušená skôr, ako do<PERSON>lo k obnoveniu stránky zo spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, pretože aktívne pripojenie k sieti dostalo príliš veľa dát. Chrome obmedzuje objem dát, ktor<PERSON> môže dostávať stránka vo vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stránky s načítavaním počas prenosu() alebo XHR momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Strán<PERSON> bola vylúčená zo spätnej vyrovnávacej pamäte, pretože aktívna sieťová požiadavka zahŕňala presmerovanie."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, pretože pripojenie k sieti bolo príliš dlho otvorené. Chrome obmedzuje obdo<PERSON>, počas ktorého môže stránka vo vyrovnávacej pamäti dostávať dáta."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Stránky bez platnej hlavičky odpovede nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigácia sa uskutočnila v inom ako hlavnom ráme."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Stránky s prebiehajúcimi transakciami IndexedDB momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Strán<PERSON> so sieťovou požiadavkou počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Stránky so sieťovou požiadavkou na načítanie počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Strán<PERSON> so sieťovou požiadavkou počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Stránky so sieťovou požiadavkou XHR počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Stránky s nástrojom PaymentManager momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Stránky s obrazom v obraze momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Stránky zobrazujúce používateľské rozhranie na tlač momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Strán<PERSON> bola otvorená pomocou prvku window.open() a ďalšia karta má odkaz na ňu, pr<PERSON><PERSON>ne stránka otvorila okno."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Proces vykresľovacieho modulu pre danú stránku v spätnej vyrovnávacej pamäti spadol."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Proces vykresľovacieho modulu pre danú stránku v spätnej vyrovnávacej pamäti bol zrušený."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON>dali o povolenia na snímanie zvuku, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre senzory, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, ktor<PERSON> p<PERSON>ž<PERSON>i o synchronizáciu na pozadí alebo načítanie povolení, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre MIDI, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre upozornenia, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o prístup k ukladaciemu <PERSON>u, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON>dal<PERSON> o povolenia na snímanie videí, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Do vyrovnávacej pamäte sa dajú uložiť iba str<PERSON>, k<PERSON><PERSON><PERSON> schéma webovej adresy je HTTP alebo HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Stránku si nárokovala obsluha počas obdobia, kedy sa nachádza v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Obsluha sa pokúsila odoslať stránke v spätnej vyrovnávacej pamäti MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Registrácia funkcie ServiceWorker bola zrušená, keď sa stránka nachádzala v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Stránka bola vylúčená zo spätnej vyrovnávacej pamäte z dôvodu aktivácie obsluhy."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome reštartoval a vymazal záznamy v spätnej vyrovnávacej pamäti."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Stránky s rozhraním SharedWorker momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Stránky s rozhraním SpeechRecognizer momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Stránky s rozhraním SpeechSynthesis momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Prvok iframe na stránke spustil navigáciu, ktorá nebola dokončená."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> podzdroj má hlavičku cache-control:no-cache, nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> podzdroj má hlavičku cache-control:no-store, momentálne nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Stránka prekročila maximálny čas v spätnej vyrovnávacej pamäti a vypršala."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Pri zadávaní stránky do spätnej vyrovnávacej pamäte vypršal časový limit (pravdepodobne to spôsobili dlhodobo spustené obslužné nástroje skrytia stránky)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Stránka má v hlavnom ráme obslužný nástroj uvoľnenia z pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Stránka má v podráme obslužný nástroj uvoľnenia z pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Prehliadač zmenil hlavičku prepísania používateľského agenta."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> u<PERSON> pr<PERSON>p na nahrávanie videa alebo zvuku, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Stránky s rozhraním WebDatabase momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Stránky s rozhraním WebHID momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Stránky s rozhraním WebLocks momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Stránky s rozhraním WebNfc momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Stránky s rozhraním WebOTPService momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Stránky s rozhraním WebRTC nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná, pretože bol použitý protokol WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Stránky s rozhraním WebShare momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Stránky s rozhraním WebSocket nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná, pretože bol použitý protokol WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Stránky s rozhraním WebTransport nemajú prístup do spätnej vyrovnávacej pamäte."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná, pretože bol použitý protokol WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Stránky s rozhraním WebXR momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}}