{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers өңдеуі кезінде авторизация процесі алмастырғыш таңбамен (*) қорғалмайды."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Әдепкі Cast интеграциясын өшіру үшін -internal-media-controls-overlay-cast-button таңдау құралын емес, disableRemotePlayback төлсипатын пайдалану керек."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "slider-vertical CSS көрініс мәні стандартталмаған және ол өшіріледі."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "URL мекенжайларында \\(n|r|t) өшірілген бос орын таңбаларынан тұратын ресурс сұраулары да, кіші таңбалар (<) да бөгеледі. Бұл ресурстарды жүктеу үшін элемент төлсипатының мәндері сияқты кіші таңбаларды кодтап, жаңа жолдарды өшіріңіз."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() қолданыстан шығарылды, оның орнына стандартталған API-ды пайдаланыңыз: навигация уақыты 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() қолданыстан шығарылды, оның орнына стандартталған API-ды пайдаланыңыз: сурет салу уақыты."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() қолданыстан шығарылды, оның орнына стандартталған API-ды пайдаланыңыз: nextHopProtocol (навигация уақыты 2)."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) таңбасынан тұратын cookie файлдарынан бас тартылады, бірақ олар алынып тасталмайды."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain мәнін тағайындау арқылы бірдей дереккөз саясатын өңдеу процесі қолданыстан шығарылды және әдепкісінше өшіріледі. Бұл қолданыстан шығару туралы хабарландыру document.domain параметрі арқылы қосылған дереккөзаралық рұқсатқа арналған."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Дереккөзаралық iframe кадрларынан window.alert іске қосу функциясы қолданыстан шығарылды және келешекте өшіріледі."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Дереккөзаралық iframe кадрларынан window.confirm іске қосу функциясы қолданыстан шығарылды және келешекте өшіріледі."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Деректер қолдауы: SVGUseElement элементіндегі URL мекенжайлары ескірген және болашақта өшіріледі."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() және watchPosition() бұдан былай қауіпті дереккөздерде қолданылмайды. Бұл функцияны пайдалану үшін қолданбаны HTTPS сияқты қауіпсіз дереккөзге ауыстырып көріңіз. Толығырақ мәлімет алу үшін https://goo.gle/chrome-insecure-origins бетін қараңыз."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() және watchPosition() элементтері қауіпті дереккөздерде қолданыстан шығарылды. Бұл функцияны пайдалану үшін қолданбаны HTTPS сияқты қауіпсіз дереккөзге ауыстырып көріңіз. Толығырақ мәлімет алу үшін https://goo.gle/chrome-insecure-origins бетін қараңыз."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() бұдан былай қауіпті дереккөздерде қолданылмайды. Бұл функцияны пайдалану үшін қолданбаны HTTPS сияқты қауіпсіз дереккөзге ауыстырып көріңіз. Толығырақ мәлімет алу үшін https://goo.gle/chrome-insecure-origins бетін қараңыз."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<h1> тэгі табылған <article>, <aside>, <nav> немесе <section> тэгтерінде қаріп өлшемі көрсетілмеген. Бұл браузерде жақын арада осы тақырып мәтінінің өлшемі өзгереді. Толық ақпаратты https://developer.mozilla.org/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 сайтынан қараңыз."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate қолданыстан шығарылды. Оның орнына RTCPeerConnectionIceErrorEvent.address немесе RTCPeerConnectionIceErrorEvent.port пайдаланыңыз."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Цифрлық тіркелу деректеріне арналған navigator.credentials.get() сұрауының форматы ескірді. Жаңа форматты пайдалану үшін шақыруды жаңартыңыз."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment қызметі жұмысшысының оқиғасынан алынған сатушы тегі мен арбитраж деректері қолданыстан шығарылған, сондықтан topOrigin, paymentRequestOrigin, methodData, modifiers нұсқаларында болмайды."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Веб-сайт оны пайдаланушылардың артықшылықты желі позициясына байланысты рұқсат берілуі мүмкін жалғыз желіден қосалқы ресурсты сұрады. Мұндай сұраулар жария емес құрылғылар мен серверлерді интернетке жария етеді, осылайша жасанды сайтаралық сұраумен (CSRF) шабуылдау және/немесе ақпараттың жылыстау қаупін арттырады. Бұл қауіптерді азайту үшін Chrome браузері қауіпті контекстерден бағытталған жария емес қосалқы ресурстарды сұрау функциясын қолданыстан шығарды және оларды бөгейтін болады."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Әрекетін дәлірек бейнелеу үшін joinAdInterestGroup() ішіне өткен InterestGroups dailyUpdateUrl өрісінің атауы updateUrl деп өзгертілді."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator қолданыстан шығарылды. Оның орнына Intl.Segmenter пайдаланыңыз."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css файл кеңейтімімен аяқталмайтын file: URL мекенжайларынан CSS-ті жүктеу мүмкін емес."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Cпецификация өзгерісіне байланысты remove() әдісінің асинхронды аралықты өшіруін тоқтату үшін бұрынғыдайSourceBuffer.abort() опциясын пайдалана алмайсыз. Келешекте ол өшіріледі. updateend сигналын күтіңіз. abort() әдісі асинхронды медиақұралды қосуды тоқтатуға немесе талдау құралын бастапқы күйге қайтаруға ғана арналған."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "MediaSource.duration параметріне буферленген, кодталған кадрлардың ең жоғарғы презентациялық уақыт белгісінен төмен мән тағайындау функциясы қолданыстан шығарылды. Қиылған, буферленген медиамазмұнды бүркемелеп өшіру функциясы келешекте қолжетімсіз болады. Оның орнына sourceBuffers буферлерінің барлығында цензурасыз remove(newDuration, oldDuration) шартын (newDuration < oldDuration негізінде) орындау қажет."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions нұсқасында sysex көрсетілмесе, Web MIDI пайдалану рұқсатын сұрайды."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Хабарландыру API-ын бұдан былай қауіпті дереккөздерден пайдалануға болмайды. Қолданбаны HTTPS сияқты қауіпсіз дереккөзге ауыстырып көріңіз. Толығырақ мәлімет алу үшін https://goo.gle/chrome-insecure-origins бетін қараңыз."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Хабарландыру API-ы бойынша рұқсатты бұдан былай дереккөзаралық iframe кадрынан сұрау мүмкін емес. Оның орнына жоғары деңгейлі кадрдан рұқсат сұрау немесе жаңа терезеде ашу нұсқаларын қарастырып көріңіз."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap ішіндегі imageOrientation: 'none' опциясы ескірген. Оның орнына \"{imageOrientation: \"from-image\"}\" опциясымен createImageBitmap пайдаланыңыз."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Серіктес ескірген (D)TLS нұсқасын қарастыруда. Мәселені шешу үшін серіктеске жүгініңіз."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video және canvas тэгтерінде overflow: visible мәні көрсетілсе, олар көрнекі контентті элементтің шектерінен тыс беруі мүмкін. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md бетін қараңыз."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments қолданыстан шығарылды. Орнына төлем өңдегіштері үшін \"дәл уақытында\" (JIT) орнату параметрін қолданыңыз."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest қоңырауы Контент қауіпсіздігі саясатының connect-src директивасын айналып өтті. Бұл айналып өту жолы қолданыстан шығарылды. PaymentRequest API интерфейсінің (supportedMethods өрісінде) төлеу әдісі идентификаторын Контент қауіпсіздігі саясатының connect-src директивасына қосыңыз."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent қолданыстан шығарылды. Оның орнына стандартты navigator.storage пайдаланыңыз."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> басты элементінен тұратын <source src> элемент жарамсыз болуы себепті еленбеді. Оның орнына <source srcset> пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame белгілі бір жеткізушіге арналған. Оның орнына стандартты cancelAnimationFrame пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame белгілі бір жеткізушіге арналған. Оның орнына стандартты requestAnimationFrame пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ескірген. Оның орнына Document.fullscreenElement пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ескірген. Оның орнына Element.requestFullscreen() пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ескірген. Оның орнына Element.requestFullscreen() пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ескірген. Оның орнына Document.exitFullscreen() пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ескірген. Оның орнына Document.exitFullscreen() пайдаланыңыз."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ескірген. Оның орнына Document.fullscreenEnabled пайдаланыңыз."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "M113 шығарылымына дейін кері үйлесімдік үшін қосулы болып қалатынымен, API chrome.privacy.websites.privacySandboxEnabled интерфейсін қолданыстан шығарып жатырмыз. Оның орнына chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled және chrome.privacy.websites.adMeasurementEnabled пайдаланыңыз. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled бетін көріңіз."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement шектеуі өшірілді. Бұл шектеу үшін false мәнін көрсеттіңіз, ол өшірілген SDES key negotiation әдісін пайдалану әрекеті ретінде қарастырылған. Бұл функция өшірілді, оның орнына DTLS key negotiation элементін қолдайтын қызметті пайдаланыңыз."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement шектеуі өшірілді. Бұл шектеу үшін true мәнін көрсеттіңіз, оның әсері болмады. Жүйелілікті қамтамасыз ету үшін мұндай шектеуді өшіруіңізге болады."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Кері қоңырау шалуға негізделген getStats() ескірген және жойылады. Оның орнына сипаттамаға сәйкес getStats() пайдаланыңыз."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ескірген. Оның орнына Selection.modify() пайдаланыңыз."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL мекенжайларында ендірілген тіркелу деректері (мысалы, **********************/) бар қосалқы ресурс сұраулары бөгеледі."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy опциясы қолданыстан шығарылды және өшіріледі."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer элементі дереккөзаралық оқшаулауды талап етеді. Толығырақ мәлімет алу үшін https://developer.chrome.com/blog/enabling-shared-array-buffer/ бетін қараңыз."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Пайдаланушыны белсендірместен speechSynthesis.speak() саясатын шақыру мүмкіндігі қолданыстан шығарылды және өшіріледі."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Босату оқиғасы тыңдаушылары қолданыстан шығарылды және өшіріледі."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer элементін одан әрі пайдалану үшін кеңейтімдерде дереккөзаралық оқшаулау нұсқасы таңдалуы керек. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ бетін қараңыз."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter төлсипаты ескірді, оның орнына GPUAdapterInfo isFallbackAdapter төлсипатын пайдаланыңыз."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest сұрауының json жауабы бойынша UTF-16 қолжетімсіз."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Түпкі пайдаланушының жұмысына кері әсерін тигізетіндіктен, негізгі тізбектегі XMLHttpRequest синхронды элементі қолданыстан шығарылды. Анықтама алу үшін https://xhr.spec.whatwg.org/ бетін қараңыз."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимация"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Формат өзгерістері элементтер пайдаланушы әрекетінсіз жылжытылғанда орын алады. Бет жүктелген кезде элементтерді қосу не жою немесе олардың қарпін өзгерту сияқты [формат өзгерістерінің себептерін зерттеңіз](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Қаріп сұрауы"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Формат өзгерісінің кластері: {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Макет өзгерісінің себептері анықталмады"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Формат өзгерістері жоқ"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Формат өзгерісінің себептері"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Ең өзекті формат өзгерісінің себептері"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Ең нашар кластер"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Ең нашар формат өзгерісінің кластері"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Кэштің қолданылу мерзімі"}, "models/trace/insights/Cache.ts | description": {"message": "Кэштің ұзақ қолданылу уақыты бетке қайта кіру жылдамдығын арттырады. [Толық ақпарат алыңыз](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Тиімсіз кэш саясаттарына қатысты сұраулар жоқ."}, "models/trace/insights/Cache.ts | others": {"message": "Тағы {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Сұрау"}, "models/trace/insights/Cache.ts | title": {"message": "Тиімді кэштің қолданылу мерзімдерін пайдалану"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM үлкен болса, стильдерді есептеу және форматты қайта есептеу ұзақтығы артып, беттің жауап беруіне әсер етеді. Қолданылатын жад та артады. [Шамадан тыс DOM өлшеміне жол бермеу әдісі туралы ақпарат алыңыз](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Элемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Туынды элементтердің максималды саны"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM тереңдігі"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистика"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM өлшемін оңтайландыру"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Жалпы элементтер саны"}, "models/trace/insights/DOMSize.ts | value": {"message": "Мән"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Бірінші желі сұрауы өте маңызды.  Оның кідірісін басқа жаққа бағыттауға жол бермеу, сервердің жылдам жауап беруін қамтамасыз ету және мәтінді сығу функциясын қосу арқылы азайтыңыз."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Басқа жаққа бағыттау әрекеттері болды ({PH1} басқа жаққа бағыттау, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер баяу жауап берді (анықталған уақыт: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Сығылмады."}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Басқа жаққа бағыттауға жол бермейді."}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер жылдам жауап береді (анықталған уақыт: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Мәтінді сығады."}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Басқа жаққа бағыттау"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Сервердің жауап беру уақыты"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Құжат сұрауының кідірісі"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Сығылмаған күйінде жүктеп алу"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Қайталанатын байттар"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Дереккөз"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Желіні пайдалану дерегі тұтынатын қажетсіз байт санын азайту үшін пакеттерден үлкен, қайталанатын JavaScript модульдерін өшіріңіз."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Қайталанатын JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Мәтін тұрақты көрініп тұруы үшін, [font-display](https://developer.chrome.com/blog/font-display) үзіндісін swap немесе optional мәніне орнатыңыз. Макет өзгерістерін азайту үшін [қаріп көрсеткішін қайта анықтау](https://developer.chrome.com/blog/font-fallbacks) арқылы swap үзіндісін әрі қарай оңтайландыруға болады."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Қаріп"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Қаріп көрсету"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Жұмсалған уақыт"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(анонимді)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Әдетте формат геометриясын оқитын көптеген API интерфейстері стиль мен форматты есептеу үшін рендеринг механизмін скриптіні орындауды тоқтатуға мәжбүрлейді. [Қайта есептеуге мәжбүрлеу](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) және оны азайту туралы толық ақпарат алыңыз."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Стэкті трассалау"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Қайта есептеуге мәжбүрлеу"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Ең көп уақыт алатын функциялық шақыру"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Жалпы қайта есептеу уақыты"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[қолтаңбасыз]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Суреттерді жүктеп алу уақыты азайса, бет пен LCP-ді қабылданған жүктеу уақыты азаяды. [Сурет өлшемін оңтайландыру туралы толық ақпарат](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (шамамен {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Оңтайлауға болатын суреттер жоқ"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Файл өлшемін оңтайландыру"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Тағы {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Суреттің жеткізілуін жақсарту"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Суретті сығу коэффициенті артса, бұл суреттің жүктеп алынатын өлшемі азаяды."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Заманауи сурет форматы (WebP, AVIF) қолданылса немесе суретті сығу деңгейі арттырылса, бұл суреттің жүктеп алынатын өлшемі азаяды."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Бұл сурет файлы өзінің көрсетілген өлшемдері ({PH2}) үшін қажет ({PH1}) мәннен үлкен. Суреттің жүктеп алынатын өлшемін азайту үшін бейімделгіш суреттерді пайдаланыңыз."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF орнына бейне форматтары қолданылса, анимациялық контенттің жүктеп алынатын өлшемі азаяды."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Ең ұзақ кезеңмен зерттеуді бастаңыз. [Кідірістерді азайтуға болады](https://web.dev/articles/optimize-inp#optimize_interactions). Өңдеу уақытын қысқарту үшін [негізгі тізбек шығындарын](https://web.dev/articles/optimize-long-tasks), жиі JS-ті оңтайландырыңыз."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Ұзақтық"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Енгізу кідірісі"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Ешқандай әрекеттестік анықталмады"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Кезең"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Презентация кідірісі"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Өңдеу ұзақтығы"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Кезең бойынша INP мәні"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP суреті HTML-ден бірден [табылатындай](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) қылып, LCP көрсеткішін оңтайландырыңыз және [кейінге қалдырылған жүктеуге](https://web.dev/articles/lcp-lazy-loading) жол бермеңіз."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high қолданылды"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high қолданылуы керек"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "Кейінге қалдырылған жүктеу сипаты қолданылмады"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Ең ерте бастапқы нүктеден кейін LCP кескіні {PH1} жүктелді"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ешқандай LCP анықталмады"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Ешқандай LCP ресурсы анықталмады, себебі LCP сурет емес"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Сұрауды бастапқы құжаттан табуға болады"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP сұрауын табу"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Әрбір [кезеңнің жақсарту стратегиялары өзгеше](https://web.dev/articles/optimize-lcp#lcp-breakdown). LCP уақытының көп бөлігі кідірістермен емес, ресурстарды жүктеумен өтуі керек."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Ұзақтық"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Элемент рендерингісінің кідірісі"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Өрістің 75-процентилі"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ешқандай LCP анықталмады"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Кезең"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Ресурсты жүктеу кідірісі"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Ресурсты жүктеу ұзақтығы"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Алғашқы байтқа дейінгі уақыт"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Кезең бойынша LCP көрсеткіші"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипт"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Пайдаланылмаған байттар"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Полифилдер мен түрлендіру параметрлері ескі браузерлерде жаңа JavaScript функцияларын пайдалануға мүмкіндік береді. Дегенмен олардың көпшілігі заманауи браузерлер үшін қажет емес. Ескі браузерлердің қолдауын қалдырғыңыз келмесе, JavaScript құру процесін [негізгі](https://web.dev/articles/baseline-and-polyfills) функцияларға компиляция жасамайтындай етіп өзгертуге кеңес береміз. [Көптеген сайтқа ES6+ кодын қолдану үшін неліктен компиляция жасау керек еместігі туралы ақпарат алыңыз.](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Ескірген JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 және HTTP/3 протоколдары HTTP/1.1 протоколына қарағанда көбірек артықшылықты (мысалы, мультиплекстеу) ұсынады. [Заманауи HTTP протоколын пайдалану туралы толық ақпарат алыңыз](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Ешбір сұрауда HTTP/1.1 протоколы пайдаланылмады."}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Сұрау"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Заманауи HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Дереккөз"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Сұрау"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Дереккөз"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Уақыт"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "LCP-дің болжалды үнемделетін уақыты"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Алдын ала байланыстырылған дереккөздер қолданылмады. crossorigin төлсипатының дұрыс қолданылғанын тексеріңіз."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "Беттің жүктелуін жақсарту үшін тізбектерді қысқарту, ресурстардың жүктеп алынатын өлшемін азайту немесе қажетсіз ресурстарды жүктеп алуды кейінге қалдыру арқылы [маңызды сұраулар тізбегінің құрылуына жол бермеңіз](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Ең маңызды дереккөздерге [алдын ала байланыстыру](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) тұспалдарын қосыңыз. Бірақ 4-тен асырмай пайдалануға тырысыңыз."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Нұсқаларды алдын ала байланыстыру"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Маңызды жолдың максималды кідірісі:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Желі тәуелділіктері әсер ететін рендеринг тапсырмасы жоқ"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Ешқандай қосымша дереккөз алдын ала байланыстырудың жақсы нұсқалары емес"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ешқандай дереккөз алдын ала байланыстырылмаған"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[Алдын ала байланыстыру](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) тұспалдары браузерге байланысты бет жүктелген кезде ертерек орнатуға мүмкіндік беріп, сол дереккөзге арналған бірінші сұрау жасалған кезде, уақытты үнемдейді. Бет алдын ала байланысатын дереккөздер берілген."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Алдын ала байланыстырылған дереккөздер"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Желінің тәуелділік тармағы"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Кемінде 4 preconnect байланысы табылды. Оларды үнемдеу және ең маңызды дереккөздерге ғана қолдану қажет."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Алдын ала байланыстырылған дереккөздер қолданылмады. preconnect функциясын тек бет сұрайтын дереккөздер үшін пайдаланыңыз."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Беттің жүктелуін жақсарту үшін тізбектерді қысқарту, ресурстардың жүктеп алынатын өлшемін азайту немесе қажетсіз ресурстарды жүктеп алуды кейінге қалдыру арқылы маңызды сұраулар тізбегінің құрылуына жол бермеңіз."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Сұраулар LCP кідірістерін тудыруы мүмкін беттің бастапқы рендерингісін блоктап жатыр. [Кейінге қалдыру немесе ендіру](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) нәтижесінде бұл желі сұраулары маңызды жолдан тыс жылжытылуы мүмкін."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Ұзақтық"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Бұл навигация үшін рендерингіні блоктау сұраулары жоқ"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Сұрау"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Рендерингіні блоктау сұраулары"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Стильді қайта есептеу шығындары жоғары болып қалса, таңдау құралын оңтайландыру арқылы оларды азайтуға болады. Өткен уақыт пен баяу жол %-ын жоғары қылып, [таңдау құралдарын оңтайландырыңыз](https://developer.chrome.com/docs/devtools/performance/selector-stats). Таңдау құралдары қарапайым және аз болса, DOM шағын және жеңіл болса, сәйкестендіру шығындары азаяды."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Өткен уақыт"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS таңдау құралының деректері табылмады. CSS таңдау құралының статистикасын өнімділік панелінің параметрлерінде қосу қажет."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Сәйкестендіру әрекеттерінің саны"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Сәйкестіктер саны"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS таңдау құралының шығындары"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Ең көп пайдаланылған таңдау құралдары"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Жа<PERSON><PERSON>ы"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Негізгі тізбек уақыты"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Үшінші тарап"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Тасымалданатын дерек өлшемі"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Үшінші тарап коды жүктеу өнімділігіне едәуір әсер етуі мүмкін. Бет контентіне басымдық беру үшін [үшінші тарап кодын жүктеуді азайтыңыз және кейінге қалдырыңыз](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Үшінші тараптар табылмады"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Үшінші тараптар"}, "models/trace/insights/Viewport.ts | description": {"message": "Қарау терезесі мобильдік құрылғы үшін оңтайландырылмаса, түрту әрекеттері [300 мс-қа дейін кідіруі](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) мүмкін."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Мобильдік құрылғыны түрту кідірісі"}, "models/trace/insights/Viewport.ts | title": {"message": "Мобильдік құрылғыға арналған қарау терезесін оңтайландыру"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET сұрауы арқылы жүктелген беттер ғана алға-артқа өту кэшіне жарайды."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX күй коды бар беттер ғана кэштеледі."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome браузері кэштеу кезінде JavaScript бағдарламасын іске қосу талпынысын анықтады."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Алға-артқа өту кэші жалаушалар арқылы өшірілді. Оны осы құрылғыда қосу үшін chrome://flags/#back-forward-cache бетіне өтіңіз."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Алға-артқа өту кэші пәрмен жолы арқылы өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Жад жеткіліксіз болғандықтан, алға-артқа өту кэші өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Бағытталған құрал алға-артқа өту кэшіне қолдау көрсетпейді."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Алдын ала рендерер үшін алға-артқа өту кэші өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Бет кэштелмейді, себебі онда тіркелген тыңдаушылардан тұратын BroadcastChannel көшірмесі бар."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "\"cache-control:no-store\" тақырыбы бар беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кэш әдейі тазартылды."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Басқа беттің кэштелуіне мүмкіндік беру үшін, бет кэштен шығарылды."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Плагиндерден тұратын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Анықталмаған"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API-ын пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Файл жүйесіне кіру API-ын пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Сақтау құрылғысы диспетчерін пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Media Player кері шыққан бойдан ойнатылды."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API интерфейсін пайдаланатын беттер және ойнату күйін орнату алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API-ын пайдаланатын және әрекет өңдегіштерді тағайындайтын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Алға-артқа өту кэші экранды оқу құралына байланысты өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler-ді пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API-ды пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API-ын пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API-ын пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API-ын пайдаланатын беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Алға-артқа өту кэші өшірілген, себебі cookie файлдары Cache-Control: no-store пайдаланатын бетте өшірілген."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Dedicated Worker немесе Worklet интерфейсіндегі беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Құжат одан шыққанға дейін жүктеуді аяқтамады."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Шығу кезінде қолданба баннері көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Шығу кезінде Chrome Құпия сөздер реттегіші көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Шығу кезінде DOM дистилляциясы орындалып жатты."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Шығу кезінде DOM дистилляциясы функциясының көру құралы көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Алға-артқа өту кэші хабар алмасу API-ын қолданатын кеңейтімдерге байланысты өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Алға-артқа өту кэшіне сақтау кезінде тұрақты кеңейтімдер байланысты үзуі тиіс."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Тұрақты кеңейтімдер алға-артқа өту кэшіндегі кадрларға хабарлар жібермекші болды."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Алға-артқа өту кэші кеңейтімдерге байланысты өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Шығу кезінде бет үшін үлгіні қайта жіберу немесе http құпия сөзі диалогтік терезесі сияқты модальді диалогтік терезе көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Шығу кезінде офлайн бет көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Шығу кезінде жадтың жеткіліксіздігін білдіретін тақта көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Шығу кезінде рұқсат сұралды."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Шығу кезінде қалқымалы терезе бөгегіші көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Шығу кезінде Safe Browsing мәліметтері көрсетілді."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing функциясы бұл бетті ереже бұзушы деп есептеп, қалқымалы терезені бөгеді."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Бет алға-артқа өту кэшінде болған кезде, Service Worker іске қосылды."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Құжатта болған ақауға байланысты алға-артқа өту кэші өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames қолданатын беттерді bfcache кэшінде сақтау мүмкін емес."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Басқа беттің кэштелуіне мүмкіндік беру үшін, бет кэштен шығарылды."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Медиа трансляциялау рұқсаты берілген беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Ендірілген контенттің (мысалы, PDF) белгілі бір түрлері бар беттер алға-артқа өту кэші үшін әзірше талаптарға жауап бермейді."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Ашық IndexedDB байланысы бар беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB оқиғасына байланысты алға-артқа өту кэші өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Жарамсыз API-лар пайдаланылды."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Кеңейтімдер арқылы JavaScript ендірілген беттер қазір алға-артқа өту кэші үшін жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Кеңейтімдер арқылы StyleSheet ендірілген беттер қазір алға-артқа өту кэші үшін жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Ішкі қате шықты."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Алға-артқа өту кэші өшірулі, себебі кейбір JavaScript желісінің сұрауы Cache-Control: no-store тақырыбы бар ресурсты алды."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Keepalive сұрауына байланысты алға-артқа өту кэші өшірілді."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Пернетақта құлпын пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Бет одан шыққанға дейін жүктеуді аяқтамады."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Негізгі ресурсында \"cache-control:no-cache\" мәні бар беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Негізгі ресурсында \"cache-control:no-store\" мәні бар беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Бет алға-артқа өту кэшінен қалпына келтірілмей тұрып, оған өтуден бас тартылды."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Белсенді желі байланысы тым көп дерек қабылдағандықтан, бет кэштен шығарылды. Chrome браузері кэштеу кезінде бет қабылдайтын деректер көлемін шектейді."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Белсенді fetch() немесе XHR мәні бар беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Белсенді желі сұрауы бағытты ауыстырғандықтан, бет алға-артқа өту кэшінен шығарылды."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Желі байланысы тым ұзақ ашық тұрғандықтан, бұл бет кэштен шығарылды. Chrome браузері кэштеу кезінде беттің деректі қабылдауға жұмсайтын уақытын шектейді."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Жарамды жауап тақырыбы жоқ беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Шолу негізгі кадрдан тыс кадрда орындалды."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Ағымдағы индекстелген DB транзакциялары бар беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Белсенді желі сұрауынан тұратын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Белсенді шығару желісін сұрау беттері қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Белсенді желі сұрауынан тұратын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Белсенді XHR интерфейсіндегі желі сұрауы бар беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "\"Суреттегі сурет\" режимін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Басып шығару интерфейсін көрсететін беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Бет \"window.open()\" арқылы ашылды және басқа қойындының оған сілтемесі бар немесе бет терезені ашты."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Алға-артқа өту кэшіндегі беттің рендеринг процесі бұзылды."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Алға-артқа өту кэшіндегі беттің рендеринг процесі жойылды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Аудио жазуға рұқсат сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Датчик рұқсаттарын сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Фондық синхрондауды немесе іріктеу рұқсаттарын сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI рұқсаттарын сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Хабарландырулар рұқсаттарын сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Жадқа кіруге рұқсат сұраған беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Бейнеге түсіруге рұқсаттар сұраған беттер алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "URL схемасы HTTP/HTTPS кеңейтіміндегі беттер ғана кэштеледі."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Бет алға-артқа өту кэшінде болған кезде, оған Service Worker арқылы шағым түсірілді."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service Worker параметрі алға-артқа өту кэшіндегі бетке MessageEvent үзіндісін жібермекші болды."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Бет алға-артқа өту кэшінде болған кезде, ServiceWorker функциясын тіркеуден бас тартылды."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Service Worker қосылғандықтан, бет алға-артқа өту кэшінен шығарылды."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome өшіп қосылып, алға-артқа өту кэшінің жазбаларын тазалады."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Беттегі iframe орындалмаған шолуды бастады."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Ішкі ресурсында \"cache-control:no-cache\" мәні бар беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Ішкі ресурсында \"cache-control:no-store\" мәні бар беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Бет алға-артқа өту кэшіндегі максималды уақыттан асып кетті және оның мерзімі аяқталды."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Беттің алға-артқа өту кэшіне кіру уақыты өтіп кетті (бетті ұзақ жасыратын өңдегіштерге байланысты болуы мүмкін)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Негізгі кадрдағы бетте босату өңдегіші бар."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Қосалқы кадрдағы бетте босату өңдегіші бар."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Браузер пайдаланушы агентін қайта анықтау тақырыбын өзгертті."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Бейне немесе аудио жазуға рұқсат берген беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc интерфейсіндегі беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService параметрін пайдаланатын беттер қазір \"bfcache\" мәні үшін жарамсыз."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC интерфейсіндегі беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Алға-артқа өту кэші өшірілген, себебі WebRTC пайдаланылған."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket протоколындағы беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Алға-артқа өту кэші өшірілген, себебі WebSocket пайдаланылған."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport интерфейсіндегі беттер алға-артқа өту кэшіне кірмейді."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Алға-артқа өту кэші өшірілген, себебі WebTransport пайдаланылған."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR параметрін пайдаланатын беттер қазір алға-артқа өту кэшіне жарамайды."}}