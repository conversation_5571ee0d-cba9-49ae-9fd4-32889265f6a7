{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "W przypadku obsługi nagłówka CORS Access-Control-Allow-Headers autoryzacja nie będzie przeprowadzana przy użyciu symboli wieloznacznych (*)."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "<PERSON><PERSON> w<PERSON> domyślną integrację przesyłania, nie używaj selektora -internal-media-controls-overlay-cast-button. Zamiast tego użyj atrybutu disableRemotePlayback."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "War<PERSON>ść wyglądu CSS slider-vertical nie jest ustandaryzowana i zostanie usunięta."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Żądania zasobów, których adresy URL zawierają znaki \\(n|r|t) z usuniętymi odstępami i znaki mniejszości (<), są blokowane. Usuń nowe wiersze i zakoduj znaki mniejszości z miejsc takich jak wartości atrybutów elementów, aby umożliwić wczytywanie tych zasobów."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Wycofano interfejs chrome.loadTimes(). Użyj standaryzowanego interfejsu API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Wycofano chrome.loadTimes(). Użyj standaryzowanego interfejsu API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Interfejs chrome.loadTimes() został wycofany. Użyj standaryzowanego interfejsu API: nextHopProtocol w Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Pliki cookie zawierające znak \\(0|r|n) będą odrzucane (nie będą skracane)."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Złagodzenie zasady dotyczącej tej samej domeny przez ustawienie document.domain zostało wycofane i będzie domyślnie wyłączone. To ostrzeżenie o wycofaniu dotyczy dostępu z tej samej domeny, kt<PERSON>ry był włączony przez ustawienie document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Możliwość aktywowania funkcji window.alert z elementów iframe z innych domen została wycofana i w przyszłości zostanie usunięta."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Możliwość aktywowania funkcji window.confirm z elementów iframe z innych domen została wycofana i w przyszłości zostanie usunięta."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Obsługa danych: adresy URL w funkcji SVGUseElement zostały wycofane i w przyszłości zostaną usunięte."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Metody getCurrentPosition() i watchPosition() nie działają już w przypadku niezabezpieczonych źródeł. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Wycofano metody getCurrentPosition() i watchPosition() w niezabezpieczonych źródłach. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "Metoda getUserMedia() nie działa już w przypadku niezabezpieczonych źródeł. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Znaleziono tag <h1> w elementach <article>, <aside>, <nav> lub <section>, kt<PERSON>ry nie ma określonego rozmiaru czcionki. W najbliższym czasie rozmiar tego nagłówka w tej przeglądarce ulegnie zmianie. Więcej informacji znajdziesz na stronie https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Pole RTCPeerConnectionIceErrorEvent.hostCandidate zostało wycofane. Zamiast niego użyj pola RTCPeerConnectionIceErrorEvent.address lub RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ten format prośby navigator.credentials.get() o cyfrowe dane uwierzytelniające został wycofany. Zaktualizuj wywołanie, aby u<PERSON><PERSON><PERSON> nowego formatu."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Dane pochodzenia sprzedawcy i arbitralne dane ze zdarzenia skryptu service worker canmakepayment nie są już używane i zostaną usunięte: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Witryna zażądała zasobu podrzędnego z sieci, do której miała dostęp wyłącznie z powodu uprawnień sieci użytkownika. Takie żądania ujawniają w internecie niepubliczne urządzenia i serwery, zwiększając ryzyko ataku polegającego na sfałszowaniu żądania z innej witryny (CSRF) lub wycieku informacji. Aby zapobiegać temu ryzyku, wycofujemy z Chrome możliwość przesyłania żądań do niepublicznych zasobów podrzędnych, gdy są wysyłane z niezabezpieczonych kontekstów. Chrome będzie je blokować."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Nazwa pola dailyUpdateUrl dla elementu InterestGroups przekazanego do joinAdInterestGroup() została zmieniona na updateUrl, aby trafniej odzwierciedlała jego działanie."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Interfejs Intl.v8BreakIterator został wycofany. Zamiast niego użyj interfejsu Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS nie można ładować z adresów URL file:, chyba że są zakończone rozszerzeniem pliku .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Używanie metody SourceBuffer.abort() w celu przerwania procesu usuwania zakresu asynchronicznego przez metodę remove() zostało wycofane z powodu zmiany specyfikacji. Obsługa zostanie wycofana w przyszłości. Zamiast tego zacznij nasłuchiwać zdarzenia updateend. Metoda abort() służy tylko do przerywania procesu dołączania asynchronicznych multimediów lub resetowania stanu parsera."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Ustawianie MediaSource.duration poniżej najwyższej sygnatury czasowej prezentacji dowolnych buforowanych ramek kodu zostało wycofane z powodu zmiany specyfikacji. W przyszłości wycofamy możliwość usuwania przyciętych multimediów buforowanych. Zamiast tego użyj bezpośrednio metod remove(newDuration, oldDuration) w przypadku wszystkich elementów sourceBuffers, gdzie newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI poprosi o uprawnienia do użycia, nawet jeśli w interfejsie MIDIOptions nie określono komunikatów SysEx."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Nie można już używać interfejsu Notification API z niezabezpieczonych źródeł. Przełącz aplikację na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Żądanie uprawnienia dla interfejsu Notification API nie może już być wysyłane z elementów iframe z innych domen. Rozważ wysłanie żądania o przyznanie uprawnienia z ramki najwyższego poziomu lub otwarcie nowego okna."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opcja imageOrientation: 'none' w metodzie createImageBitmap została wycofana. Użyj tej metody z opcją „{imageOrientation: 'from-image'}”."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partner negocjuje przestarzałą wersję (D)TLS. Poproś go o rozwiązanie problemu."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON><PERSON> okreś<PERSON><PERSON> w<PERSON><PERSON> overflow: visible w tagach img, video i canvas, mogą one wyświ<PERSON><PERSON>ć treści wizualne poza granicami elementu. Więcej informacji znajdziesz na https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "Interfejs paymentManager.instruments został wycofany. Zamiast niego w przypadku modułów obsługi płatności używaj instalacji „just-in-time”."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Twoje wywołanie PaymentRequest pomijało dyrektywę Content Security Policy (CSP) connect-src. To ominięcie zostało wycofane. Dodaj identyfikator formy płatności z interfejsu API PaymentRequest (w polu supportedMethods) do dyrektywy CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "Interfejs StorageType.persistent został wycofany. Zamiast niego użyj standaryzowanego interfejsu navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Element <source src> z elementem nadrzędnym <picture> jest nieprawidłowy i dlatego jest ignorowany. Zamiast niego użyj elementu <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame jest specyficzna dla dostawcy. Użyj standardowej metody cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame jest specyficzna dla dostawcy. Użyj standardowej metody requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Interfejs HTMLVideoElement.webkitDisplayingFullscreen został wycofany. Użyj interfejsu Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Interfejs HTMLVideoElement.webkitEnterFullScreen() został wycofany. Użyj interfejsu Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Interfejs HTMLVideoElement.webkitEnterFullscreen() został wycofany. Użyj interfejsu Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Interfejs HTMLVideoElement.webkitExitFullScreen() został wycofany. Użyj interfejsu Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Interfejs HTMLVideoElement.webkitExitFullscreen() został wycofany. Użyj interfejsu Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Interfejs HTMLVideoElement.webkitSupportsFullscreen został wycofany. Użyj interfejsu Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Wycofujemy interfejs API chrome.privacy.websites.privacySandboxEnabled, ale pozostanie on aktywny, aby zap<PERSON><PERSON><PERSON> zgodn<PERSON> wsteczną do czasu opublikowania wersji M113. Zamiast niego używaj interfejsów chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled i chrome.privacy.websites.adMeasurementEnabled. Więcej informacji znajdziesz na stronie https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ograniczenie DtlsSrtpKeyAgreement zostało usunięte. W przypadku tego ograniczenia określono warto<PERSON> false, co jest interpretowane jako próba użycia usuniętej metody SDES key negotiation. Ta funkcja została usunięta. Użyj usługi, która obsługuje DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ograniczenie DtlsSrtpKeyAgreement zostało usunięte. W przypadku tego ograniczenia określono wartość true, która nie ma zastosowania. W razie potrzeby moż<PERSON>z usun<PERSON> to ograniczenie."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Metoda getStats() z wywołaniem zwrotnym nie jest już używana i zostanie usunięta. Użyj metody getStats() zgodnej ze specyfikacją."}, "generated/Deprecation.ts | RangeExpand": {"message": "Interfejs Range.expand() został wycofany. Użyj interfejsu Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Żądania zasobów podrzędnych, których adresy URL zawierają umieszczone dane logowania (np. **********************/), są blokowane."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opcja rtcpMuxPolicy jest wycofana i zostanie usunięta."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer bę<PERSON><PERSON> wymagać witryny izolowanej od zasobów z innych domen. Więcej informacji znajdziesz na stronie https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Możliwość wywołania interfejsu speechSynthesis.speak() bez aktywacji użytkownika jest wycofana i zostanie usunięta."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Niezaładowane detektory zdarzeń zostały wycofane i zostaną usunięte"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Aby nadal u<PERSON><PERSON><PERSON>ć obiektu Shared<PERSON><PERSON><PERSON><PERSON>er, rozszerzenia powinny korzystać z witryn izolowanych od zasobów z innych domen. Więcej informacji znajdziesz na stronie https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atrybut GPUAdapter isFallbackAdapter został wycofany. Zamiast niego użyj atrybutu GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Kodowanie UTF-16 nie jest obsługiwane w kodzie JSON odpowiedzi na żądanie XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchroniczny interfejs XMLHttpRequest w głównym wątku został wycofany z powodu negatywnego wpływu na sposób obsługi. Więcej informacji znajdziesz na stronie https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacja"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Przesunięcia układu występują, gdy elementy poruszają się bez interakcji ze strony użytkownika. [Zbadaj przyczyny przesunięć układu](https://web.dev/articles/optimize-cls) takie jak dodanie lub usunięcie elementów lub zmiana czcionek podczas wczytywania strony."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Prośba o czcionkę"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Wstrzyknięty element iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> układu dla {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nie udało się wykryć przyczyn problemów związanych z przesunięciem układu"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON> prz<PERSON><PERSON> układu"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Przyczyny problemów związanych z przesunięciem układu"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Najczęstsze przyczyny problemów związanych z przesunięciem układu"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Element graficzny bez określonego rozmiaru"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON>j<PERSON><PERSON><PERSON> klast<PERSON>"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Najgorszy klaster przesunięć układu"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Czas przechowywania danych w pamięci podręcznej"}, "models/trace/insights/Cache.ts | description": {"message": "Długi czas przechowywania w pamięci podręcznej może przyspieszyć ponowne otwarcie strony. [Więcej informacji](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Brak żądań z zasadami nieefektywnej pamięci podręcznej"}, "models/trace/insights/Cache.ts | others": {"message": "Inne elementy: {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Żądanie"}, "models/trace/insights/Cache.ts | title": {"message": "Używaj efektywnego czasu przechowywania w pamięci podręcznej"}, "models/trace/insights/DOMSize.ts | description": {"message": "Duży DOM może wydłużać obliczenia stylów i przeformatowania układu, co wpływa na responsywność strony. Duży DOM zwiększy też wykorzystanie pamięci. [Jak unikać nadmiernego rozmiaru DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Maksymalna liczba elementów podrzędnych"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Głębokość DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statystyki"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optymalizuj rozmiar DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Łączna liczba elementów"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Pierwsze żądanie sieciowe jest najważniejsze.  Zmniejsz jego opóźnienie, unikając przekierowań, zapewniając szybką reakcję serwera i włączając kompresję tekstu."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Zawierało przekierowania (liczba przekierowań: {PH1}, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Serwer zareagował z opóźnieniem (zaobserwowany czas: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "<PERSON><PERSON> z<PERSON> komp<PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Nie używa przekierowań"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON> odpowiada s<PERSON> (zaobserwowany czas: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Stosuje kompresję tekstu"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Przekierowania"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON> reakcji serwera"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Czas oczekiwania w przypadku żądania dokumentu"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Pobieranie nieskompresowanych plików"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Zduplikowane bajty"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Źródło"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Usuń z pakietów duplikaty dużych modułów JavaScript, aby zmniejszyć ilość danych niepotrzebnie przesyłanych w sieci."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplikat kodu JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Sugerujemy ustawienie właściwości [font-display](https://developer.chrome.com/blog/font-display) na swap lub optional, aby tekst był zawsze widoczny. Element swap można dodatkowo zoptymalizować za pomocą [zastąpień danych o czcionkach](https://developer.chrome.com/blog/font-fallbacks) – pozwoli to zmniejszyć przesunięcia układu."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Czcionka"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Wyświetlanie czcionki"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Stracony czas"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anoni<PERSON><PERSON>)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "W celu obliczenia stylu i układu wiele interfejsów API zwykle odczytujących geometrię układu wymusza na mechanizmie renderowania wstrzymanie wykonywania skryptu. Dowiedz się więcej o [wymuszonym przeformatowaniu](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) i sposobach jego ograniczenia."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON> s<PERSON>u"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Wymuszone przeformatowanie"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Najczęstsze wywołanie funkcji"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Łączny czas przeformatowania"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ni<PERSON><PERSON><PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Skrócenie czasu pobierania obrazów może poprawić postrzegany czas wczytywania strony i LCP. [Dow<PERSON>z się więcej o optymalizowaniu rozmiaru obrazu](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (szac. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Brak obrazów do zoptymalizowania"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Zoptymaliz<PERSON>j rozmiar pliku"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Inne elementy: {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Ulepsz dostarczanie obrazów"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Zwiększenie współczynnika kompresji obrazu może zmniejszyć rozmiar pobierania."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Pobieranie tego obrazu może <PERSON>, je<PERSON><PERSON> uży<PERSON>sz nowoczesnego formatu (WebP, AVIF) lub zwiększysz kompresję obrazu."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ten plik obrazu jest wię<PERSON><PERSON> niż powinien ({PH1}) przy wyświetlanych wymiarach ({PH2}). Aby zmniejszyć rozmiar pobierania obrazu, użyj obrazów elastycznych."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Używanie formatów wideo zamiast GIF-ów może zmniejszyć rozmiar pobieranych treści animowanych."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Zacznij analizę od najdłuższego etapu. [<PERSON><PERSON><PERSON> zminimalizować opóźnienia](https://web.dev/articles/optimize-inp#optimize_interactions). A<PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> czas przetwarzania, [zoptymalizuj koszty wątku głównego](https://web.dev/articles/optimize-long-tasks), często JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Czas trwania"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Opóźnienie wejściowe"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON><PERSON> wyk<PERSON> interakcji"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Faza"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Opóźnienie prezentacji"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Czas przetwarzania"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP według etapu"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Zoptymalizuj LCP, zapewniając natychmiastową [w<PERSON><PERSON><PERSON><PERSON><PERSON>](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) obrazu LCP w kodzie HTML i [unikając leniwego ładowania](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Zastosowano fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Na<PERSON><PERSON>y zastosować fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "nie zastosowano leniwego ładowania"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Obraz LCP został wczytany {PH1} po najstarszym punkcie początkowym."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "<PERSON><PERSON> wykryto LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "<PERSON><PERSON> w<PERSON><PERSON><PERSON>, ponieważ LCP nie jest obrazem"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Prośbę można znaleźć w dokumencie początkowym"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Wykrywanie żądań LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON><PERSON><PERSON> [etap ma określone strategie poprawy](https://web.dev/articles/optimize-lcp#lcp-breakdown). W idealnym przypadku większość czasu LCP powinna być poświęcona na ładowanie zasobów, a nie na opóźnienia."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Czas trwania"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Opóźnienie renderowania elementu"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Pole p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "<PERSON><PERSON> wykryto LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Faza"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Opóźnienie ładowania zasobów"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON>zas wczytywania zasobu"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Czas do pierwszego bajtu"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP według etapu"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Skrypt"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Zmar<PERSON>wan<PERSON> baj<PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Elementy polyfill i przekształcenia umożliwiają obsługę nowych funkcji JavaScript przez starsze przeglądarki. Jednak nowoczesne przeglądarki nie potrzebują wielu takich elementów. Rozważ zmianę procesu kompilacji JavaScriptu, aby nie transpilować funkcji [podstawowych](https://web.dev/articles/baseline-and-polyfills), chyba że potrzebna jest obsługa starszych przeglądarek. [Dlaczego większość witryn może wdrażać kod ES6+ bez transpilacji](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "<PERSON>zy kod JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "Protokoły HTTP/2 i HTTP/3 mają wiele zalet w porównaniu z HTTP/1.1, np. multipleksowanie. [Więcej informacji o korzystaniu z nowoczesnego protokołu HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Żadne żądanie nie używało protokołu HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokół"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Żądanie"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Nowoczesny protokół HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Źródło"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Żądanie"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Źródło"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Czas"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Szacunkowe oszczędności LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Nieużywany link typu preconnect. Sprawdź, czy atrybut crossorigin jest używany prawidłowo."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> ł<PERSON>ie strony, [unikaj tworzenia łańcuchów żądań krytycznych](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains): sk<PERSON><PERSON><PERSON> ł<PERSON>, zmniejsz rozmiar pobieranych zasobów lub opóźnij pobieranie zasobów, które nie są niezbędne."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Do<PERSON><PERSON> [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) do najważnie<PERSON><PERSON><PERSON> źródeł, ale staraj się używać ich nie więcej niż 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandydaci do połączenia w trybie preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Maksymalne opóźnienie ścieżki krytycznej:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Zależności sieciowe nie mają wpływu na żadne zadania renderowania"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Żadne dodatkowe źródła nie są dobrymi kandydatami do połączenia w trybie preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "nie nawiązano połączeń ze źródłami w trybie preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "W<PERSON><PERSON><PERSON><PERSON> [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pomagają przeglądarce nawiązać połączenie wcześniej w procesie wczytywania strony, co pozwala zaoszczędzić czas podczas wysyłania pierwszego żądania do tego źródła. Poniżej znajdziesz listę źródeł, z którymi strona nawiązała połączenie w trybie preconnect."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Źródła połączone w trybie preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Drzewo zależności sieciowych"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Znaleziono więcej niż 4 linki preconnect. Takich linków należy używać oszczędnie i tylko w przypadku najważniejszych źródeł."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Nieużywany link typu preconnect. Linki preconnect stosuj tylko do źródeł, o które strona prawdopodobnie poprosi."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ł<PERSON> strony, unikaj tworzenia łańcuchów żądań krytycznych: s<PERSON><PERSON><PERSON><PERSON> ł<PERSON>, zmniejsz rozmiar pobieranych zasobów lub opóźnij pobieranie zasobów, które nie są niezbędne."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Żądania blokują początkowe renderowanie strony, co może opóźniać LCP. [Opóźnianie lub wstawianie](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) może skierować te żądania sieciowe poza ścieżkę krytyczną."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Czas trwania"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Brak żądań blokujących renderowanie dla tej nawigacji"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Żądanie"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Prośby o zablokowanie renderowania"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Je<PERSON><PERSON> koszty ponownego obliczania stylu są wysokie, optymalizacja selektora może je obniżyć. [Zoptymalizuj selektory](https://developer.chrome.com/docs/devtools/performance/selector-stats), które mają zarówno długi czas działania, jak i wysoki odsetek ścieżek powolnych. Prostsze selektory, mniejsza ich liczba oraz mniejszy i płytszy DOM pozwolą zmniejszyć koszty dopasowywania."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Upłynęło"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nie znaleziono danych selektora CSS. Statystyki selektora CSS muszą być włączone w ustawieniach panelu wydajności."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Próby dopasowania"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Liczba zgodnych elementów"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Koszty selektora CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Najpopularniejsze selektory"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Łącznie"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Czas w wątku głównym"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON> witryny"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Rozmiar przesłanych danych"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Kod spoza witryny może znacznie spowalniać wczytywanie stron. [Ogranicz i opóźnij wczytywanie takiego kodu](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), aby nadać priorytet treściom ze swojej strony."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Nie znaleziono treści zewnętrznych"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON> witryny"}, "models/trace/insights/Viewport.ts | description": {"message": "Interakcje dotykowe mogą być [opóźnione nawet o 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/), je<PERSON>li widoczny obszar nie jest zoptymalizowany dla telefonów."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Opóźnienie kliknięcia na urządzeniu mobilnym"}, "models/trace/insights/Viewport.ts | title": {"message": "Zoptymalizuj widoczny obszar dla telefonów"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Tylko strony wczytane za pomocą żądania GET kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "W pamięci mogą być przechowywane tylko strony z kodem stanu 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Przeglądarka Chrome wykryła próbę wykonania JavaScriptu podczas przechowywania strony w pamięci podręcznej."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON><PERSON>, które poprosiły o AppBanner, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona przez flagi. Wejdź na chrome://flags/#back-forward-cache, aby włączyć ją lokalnie na tym urządzeniu."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona przez wiersz poleceń."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu niewystarczającej ilości pamięci."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej nie jest obsługiwana w ramach przekazywania."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona w przypadku wstępnego renderowania."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Strona nie może być przechowywana w pamięci podręcznej, bo zawiera wystąpienie BroadcastChannel z zarejestrowanymi detektorami."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Strony z nagłówkiem cache-control:no-store nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON><PERSON>ć podręczna została celowo wyczyszczona."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Strona została usunięta z pamięci podręcznej, aby umożliwić przechowywanie w niej innej strony."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "St<PERSON><PERSON>, które zawierają w<PERSON>ki, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON>eok<PERSON><PERSON><PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Strony, które używają interfejsu FileChooser API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Strony, które używają interfejsu File System Access API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON>, które używają dyspozytora nośnika danych, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON>dt<PERSON><PERSON><PERSON>ł włą<PERSON>, gdy użytkownik zamknął stronę."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Strony, które używają interfejsu MediaSession API i określają stan odtwarzania, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Strony, które używają interfejsu MediaSession API i określają elementy działania, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu czytnika ekranu."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> uży<PERSON>ją <PERSON>, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Strony, które używają interfejsu Serial API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Strony, które używają interfejsu WebAuthetication API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Strony, które używają interfejsu WebBluetooth API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Strony, które używają interfejsu WebUSB API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, bo na stronie, która korzysta z elementu Cache-Control: no-store, wyłączone są pliki cookie."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Strony, które używają dedykowanej instancji roboczej lub workletu, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokument nie został w pełni wczytany, zanim użytkownik go zamknął."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Podczas zamykania strony widoczny był baner aplikacji."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Podczas zamykania strony widoczny był menedżer haseł <PERSON>rome."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Podczas zamykania strony aktywny był proces destylacji DOM."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Podczas zamykania strony widoczna była przeglądarka narzędzia DOM Distiller."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, bo rozszerzenia używają interfejsu Messaging API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Rozszerzenia od dłuższego czasu połączone powinny zakończyć połączenie przed uzyskaniem dostępu do pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Rozszerzenia od dłuższego czasu połączone próbowały wysłać wiadomości do ramek w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu rozszerzeń."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Podczas zamykania strony wyświetliło się okno modalne takie jak ponowne przesłanie formularza lub okno hasła http."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Podczas zamykania strony pokazywana była strona offline."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Podczas zamykania strony widoczny był pasek interwencji związanej z brakiem miejsca w pamięci."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Podczas zamykania strony pojawiły się prośby o uprawnienia."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Podczas zamykania strony widoczne było blokowanie wyskakujących okienek."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Podczas zamykania strony wyświetliły się szczegóły Bezpiecznego przeglądania."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezpieczne przeglądanie wykryło na tej stronie nadużycie i zablokowało wyskakujące okienko."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Skrypt service worker z<PERSON><PERSON><PERSON>, gdy strona znajdowała się w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu błędu w dokumencie."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Stron używających FencedFrames nie można przechowywać w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Strona została usunięta z pamięci podręcznej, aby umożliwić przechowywanie w niej innej strony."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Strony, kt<PERSON>re mają przyznany dostęp do strumienia multimediów, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Strony zawierające niektóre rodzaje umieszczonych treści (np. pliki PDF) nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "St<PERSON><PERSON>, kt<PERSON>re używają <PERSON>a, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Strony, które mają otwarte połączenie IndexedDB, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu zdarzenia IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Użyto nieodpowiednich interfejsów API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Strony z interfejsem JavaScript wstrzykiwanym przez rozszerzenia nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Strony z interfejsem StyleSheet wstrzykiwanym przez rozszerzenia nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Błąd wewnętrzny."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, ponieważ niektóre żądania sieciowe JavaScript otrzymały zasób z nagłówkiem Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu żądania utrzymywania aktywności."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> używają blokady klaw<PERSON>tury, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Strona nie została w pełni wczytana, zanim użytkownik ją opuścił."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ch zasoby główne mają nagłówek cache-control:no-cache, nie mogą korzysta<PERSON> z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Strony, kt<PERSON><PERSON>ch zasoby główne mają nagłówek cache-control:no-store, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Nawigacja została anulowana przed przywróceniem strony z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Strona została usunięta z pamięci podręcznej, ponieważ aktywne połączenie sieciowe otrzymało za dużo danych. Chrome ogranicza il<PERSON> danych, kt<PERSON>re strona moż<PERSON> o<PERSON><PERSON>, gdy jest przechowywana w pamięci podręcznej."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re mają przesyłane żądanie fetch() lub XHR, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Strona została usunięta z pamięci podręcznej stanu strony internetowej, ponieważ aktywne żądanie sieciowe wiązało się z przekierowaniem."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Strona została usunięta z pamięci podręcznej, ponieważ połączenie sieciowe było zbyt długo otwarte. Chrome ogranicza il<PERSON> c<PERSON>, przez który strona może otrzymywać dane podczas przechowywania w pamięci podręcznej."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON><PERSON>, które nie mają prawidłowego nagłówka odpowiedzi, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Nawigacja odbywała się w ramce innej niż ramka główna."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Strony z trwającymi transakcjami indeksowanej bazy danych nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Strony z przesyłanym żądaniem sieciowym nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Strony z przesyłanym żądaniem sieciowym fetch() nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Strony z przesyłanym żądaniem sieciowym nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Strony z przesyłanym żądaniem sieciowym XHR nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "St<PERSON>y, które używają PaymentManagera, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON><PERSON>, które używają obrazu w obrazie, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Strony, które wyświetlają interfejs drukowania, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Strona została otwarta za pomocą „window.open()” i inna karta się do niej odnosi lub strona otworzyła okno."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "W trakcie renderowania strony w pamięci podręcznej stanu strony internetowej wystąpił błąd."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Proces renderowania strony w pamięci podręcznej stanu strony internetowej został przerwany."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "St<PERSON>y, które poprosiły o przyznanie uprawnień do nagrywania dźwięku, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Strony, które poprosiły o przyznanie uprawnień dotyczących czujników, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do pobierania lub synchronizacji w tle, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "St<PERSON>y, które poprosiły o przyznanie uprawnień dotyczących MIDI, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do powiadomień, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "St<PERSON>y, które poprosiły o przyznanie dostępu do pamięci, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do nagrywania filmów, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "W pamięci mogą być przechowywane tylko strony, kt<PERSON><PERSON>ch schemat URL to HTTP/HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Strona została wywołana przez skrypt service worker podczas korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Skrypt service worker p<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON>ć MessageEvent stronie w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Skrypt service worker by<PERSON>, gdy strona znajdowała się w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Strona została usunięta z pamięci podręcznej stanu strony internetowej z powodu aktywacji skryptu service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Przeglądarka Chrome uruchomiła się ponownie i wyczyściła wpisy w pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re używają <PERSON>d<PERSON>ker, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re używają SpeechRecognizer, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "St<PERSON><PERSON>, które używają SpeechSynthesis, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Element iframe na stronie rozpoczął nawigację, która się nie zakończyła."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>rych zasoby podrzędne mają nagłówek cache-control:no-cache, nie mogą korzysta<PERSON> z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Strony, kt<PERSON>rych zasoby podrzędne mają nagłówek cache-control:no-store, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Strona przekroczyła limit czasu w pamięci podręcznej stanu strony internetowej i wygasła."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Strona przekroczyła limit czasu w trakcie zapisywania w pamięci podręcznej stanu strony internetowej (prawdopodobnie z powodu długotrwałych wyrażeń pagehide)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Strona ma wyrażenie unload w ramce głównej."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Strona ma wyrażenie unload w ramce podrzędnej."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Przeglądarka zmieniła nagłówek zastępowania klienta użytkownika."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re mają przyznany dostęp do nagrywania filmu lub dźwięku, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Strony, które używają WebDatabase, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Strony, które używają WebHID, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Strony, które używają WebLocks, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "St<PERSON>y, które używają WebNfc, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Strony, które używają WebOTPService, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Strony z WebRTC nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, ponieważ użyto WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Strony, które używają WebShare, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Strony z WebSocket nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, ponieważ korzystasz z WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Strony z WebTransport nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, ponieważ korzystasz z WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Strony, które używają WebXR, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}}