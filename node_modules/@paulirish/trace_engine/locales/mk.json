{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Овластувањето нема да биде покриено со симболот за џокер (*) во ракувањето на Access-Control-Allow-Headers со CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "За да се оневозможи стандардната интеграција на Cast, треба да се користи атрибутот disableRemotePlayback наместо избирачот -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Вредноста на изгледот на CSS slider-vertical не е стандардизирана и ќе се отстрани."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Барањата на ресурси чии URL-адреси содржеле и отстранети знаци за празно место \\(n|r|t) и знаци за „помало од“ (<) се блокирани. Отстранете ги знаците за нови редови и кодирајте ги знаците за „помало од“ од местата како што се вредностите на атрибути на елементи со цел да се вчитаат ресурсиве."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() не се поддржува. Наместо тоа, користете го стандардизираниот API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() не се поддржува. Наместо тоа, користете го стандардизираниот API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() не се поддржува. Наместо тоа, користете стандардизиран API: nextHopProtocol во Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Колачињата што содржат знак \\(0|r|n) ќе бидат отфрлени наместо скратени."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Олеснувањето на правилото за исто потекло со поставување document.domain не се поддржува и ќе биде стандардно оневозможено. Предупредувањево за неподдржување е за пристап од вкрстено потекло што е овозможен со поставување document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Активирањето window.alert од iframes со вкрстено потекло не се поддржува и ќе биде отстрането во иднина."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Активирањето window.confirm од iframes со вкрстено потекло не се поддржува и ќе биде отстрането во иднина."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Поддршка за податоци: URL-адресите во SVGUseElement не се поддржува и ќе се отстрани во иднина."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() и watchPosition() веќе не функционираат на небезбедни потекла. За да ја користите функцијава, размислете да ја префрлите апликацијата на безбедно потекло, како што е HTTPS. Видете на https://goo.gle/chrome-insecure-origins за повеќе детали."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() и watchPosition() не се поддржуваат на небезбедни потекла. За да ја користите функцијава, размислете да ја префрлите апликацијата на безбедно потекло, како што е HTTPS. Видете на https://goo.gle/chrome-insecure-origins за повеќе детали."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() веќе не функционира на небезбедни потекла. За да ја користите функцијава, размислете да ја префрлите апликацијата на безбедно потекло, како што е HTTPS. Видете на https://goo.gle/chrome-insecure-origins за повеќе детали."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Најдена е ознака <h1> во рамките на <article>, <aside>, <nav> или <section> што нема назначена големина на фонтот. Големината на текстот на насловов ќе се промени во овој прелистувач во блиска иднина. За повеќе информации, погледнете на https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate не се поддржува. Наместо тоа, користете RTCPeerConnectionIceErrorEvent.address или RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Овој формат за барањето navigator.credentials.get() за дигитални акредитиви не се поддржува. Ажурирајте го повикот за да го користите новиот формат."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Потеклото на трговец и произволните податоци од случајот на услужен работник canmakepayment не се поддржани и ќе се отстранат: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Веб-сајтот побара подресурс од мрежа до која можеше да пристапи само поради привилегираната мрежна положба на нејзините корисници. Овие барања откриваат уреди и услуги на интернет што не се јавни, зголемувајќи го ризикот за напад со фалсификат на барање од повеќе сајтови (CSRF) и/или протекување на податоците. За да ги ублажи овие ризици, Chrome не поддржува барања до подресурси што не се јавни кога се иницирани од небезбедни контексти и ќе започне да ги блокира."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Полето dailyUpdateUrl на InterestGroups што продолжува на joinAdInterestGroup() е преименувано во updateUrl, за да го прикажува неговото однесување попрецизно."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator не се поддржува. Наместо тоа, користете Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS не може да се вчита од URL-адреси на file:, освен ако не завршуваат со екстензијата на датотека .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Користењето на SourceBuffer.abort() за да се прекине отстранувањето на асинхрониот опсег на remove() не се поддржува поради промена на спецификацијата. Поддршката ќе се отстрани во иднина. Наместо тоа, треба да го слушате настанот updateend. abort() е наменето само за прекинување на асинхроните аудиовизуелни содржини или за ресетирање на состојбата на анализаторот."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Поставувањето на MediaSource.duration под највисокиот временски печат за презентација за кои било кодирани рамки во привремена меморија не се поддржува поради промена на спецификацијата. Поддршката за имплицитно отстранување на скратените аудиовизуелни содржини во привремената меморија ќе биде отстрането во иднина. Наместо тоа, треба да изведете експлицитно remove(newDuration, oldDuration) на сите sourceBuffers, каде што newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI ќе бара дозвола за користење дури и ако sysex не е наведен во MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "API за известувања веќе не може да се користи од небезбедни потекла. Размислете да ја префрлите апликацијата на безбедно потекло, како што е HTTPS. Видете на https://goo.gle/chrome-insecure-origins за повеќе детали."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Веќе не може да се бара дозвола за API за известувања од iframe со вкрстено потекло. Наместо тоа, размислете да побарате дозвола од рамка од највисоко ниво или отворете нов прозорец."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Опцијата imageOrientation: 'none' во createImageBitmap не се поддржува. Наместо тоа, користете createImageBitmap со опција „{imageOrientation: 'from-image'}“."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Вашиот партнер преговара со застарена верзија на (D)TLS. Проверете со партнерот за да го поправи ова."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Одредувањето overflow: visible на ознаки img, video и canvas може да предизвика да произведат визуелни содржини надвор од границите на елементот. Видете на https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments не се поддржува. Наместо тоа, користете го инсталирање точно на време за ракувачите со плаќања."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Вашиот повик за PaymentRequest ја заобиколи директивата за connect-src на Content-Security-Policy (CSP). Ова заобиколување не се поддржува. Додајте идентификатор на начинот на плаќање од API на PaymentRequest (во полето supportedMethods) на вашата директива за connect-src од CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent не се поддржува. Наместо тоа, користете стандардизиран navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> со надредена <picture> е неважечки и затоа се игнорира. Наместо тоа, користете <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame зависи од продавачот. Наместо тоа, користете го стандардниот метод cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame зависи од продавачот. Наместо тоа, користете го стандардниот метод requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen не се поддржува. Наместо тоа, користете Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() не се поддржува. Наместо тоа, користете Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() не се поддржува. Наместо тоа, користете Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() не се поддржува. Наместо тоа, користете Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() не се поддржува. Наместо тоа, користете Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen не се поддржува. Наместо тоа, користете Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled нема да се поддржува, но ќе остане активен за компатибилност со постари верзии до изданието M113. Наместо тоа, користете chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled и chrome.privacy.websites.adMeasurementEnabled. Погледнете на https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ограничувањето DtlsSrtpKeyAgreement е отстрането. Назначивте вредност „false“ за ограничувањево, што се интерпретира како обид за користење на отстранетиот метод за SDES key negotiation. Оваа функционалност е отстранета. Наместо тоа, користете услуга што поддржува DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ограничувањето DtlsSrtpKeyAgreement е отстрането. Назначивте вредност „true“ за ограничувањево, којашто немаше никакво влијание, но може да го отстраните ограничувањево заради уредност."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() според повратен повик не се поддржува и ќе се отстрани. Наместо тоа, кристете getStats() што се придржува до спецификациите."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() не се поддржува. Наместо тоа, користете Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Барањата на подресурси чии URL-адреси содржат вметнати акредитиви (на пр., **********************/) се блокирани."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Опцијата rtcpMuxPolicy не се поддржува и ќе биде отстранета."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer ќе бара изолација со вркстено потекло. Видете на https://developer.chrome.com/blog/enabling-shared-array-buffer/ за повеќе детали."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() без активирање од корисникот не се поддржува и ќе биде отстрането."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Бришењето слушатели на настанот не се поддржува и ќе се отстрани."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Екстензиите треба да прифатат изолација со вкрстено потекло за да продолжат со користење на SharedArrayBuffer. Видете на https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Атрибутот на GPUAdapter isFallbackAdapter не е поддржан. Наместо тоа, користете атрибут на GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 не е подржано со одговор json во XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхронизираното XMLHttpRequest на главната низа не се поддржува поради неговите штетни ефекти врз доживувањето на крајниот корисник. За повеќе помош, проверете на https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимација"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Промени на изгледот се појавуваат кога елементите се преместуваат без интеракција од корисниците. [Истражете ги причините за промените на изгледот](https://web.dev/articles/optimize-cls), како додавање и отстранување елементи или променување на нивните фонтови додека се вчитува страницата."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Барање за фонт"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Вметната iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Група промени на изгледот @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Не можеше да се откријат причинители на промени на изгледот"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Нема промени на изгледот"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Причинители на промени на изгледот"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Најголеми причинители на промени на изгледот"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Елемент со слика без одредена големина"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Најлоша група"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Најлоша група промени на изгледот"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL на кеш"}, "models/trace/insights/Cache.ts | description": {"message": "Долгиот рок на траење на кешот може да ги забрза повторните посети на вашата страница. [Дознајте повеќе](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Нема барања со неефикасни правила за кеш"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} други"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Барање"}, "models/trace/insights/Cache.ts | title": {"message": "Користете ефикасни рокови на траење на кешот"}, "models/trace/insights/DOMSize.ts | description": {"message": "Голем DOM може да го зголеми времетраењето на пресметките на стиловите и преуредувањата на изгледот, што влијае на приспособливоста на страницата. Голем DOM ќе ја зголеми и искористената меморија. [Дознајте како да избегнете прекумерна големина на DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Елемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Повеќето подредени елементи"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Длабочина на DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистика"}, "models/trace/insights/DOMSize.ts | title": {"message": "Оптимизирајте ја големината на DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Вкупно елементи"}, "models/trace/insights/DOMSize.ts | value": {"message": "Вредност"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Вашето прво мрежно барање е најважно.  Намалете ја неговата латенција со избегнување пренасочувања, гарантирање брз одговор на серверот и овозможување компримирање текст."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Имаше пренасочувања ({PH1} пренасочувања, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Серверот одговори бавно (забележано: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Не е извршено компримирање"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Избегнува пренасочувања"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Серверот одговара брзо (забележано: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Применува компримирање на текст"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Пренасочувања"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Време на одговор на серверот"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Латенција на барање на документ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Некомпримирано преземање"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Дупликат бајти"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Извор"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Отстранете ги големите, дупликат JavaScript-модули од пакетите за да ги намалите непотребните бајти што ги троши мрежната активност."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Дупликат JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Размислете да поставите [font-display](https://developer.chrome.com/blog/font-display) на swap или optional за да се погрижите текстот да биде видлив постојано. swap може да се оптимизира дополнително за да ги ублажува промените на изгледот со [отфрлања на метриката на фонтовите](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Фонт"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Приказ на фонтови"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Потрошено време"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(анонимно)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Многу API, што обично ја читаат геометријата на изгледот, го принудуваат софтверот за обработка да го паузира извршувањето на скриптата за да ги пресмета стилот и изгледот. Дознајте повеќе за [принудното преуредување](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) и неговите олеснувања."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stack trace"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Принудно преуредување"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Функциски повик што троши најмногу време"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Вкупно време на преуредување"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[неназначено]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Намалувањето на времето на преземање слики може да го подобри забележаното време на вчитување на страницата и LCP. [Дознајте повеќе за оптимизирањето на големината на сликата](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Проц. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Нема слики што може да се оптимизираат"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Оптимизирајте ја големината на датотеката"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} други"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Подобрете го доставувањето слики"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Зголемувањето на факторот на компримирање на сликата може да ја подобри големината на преземање на сликава."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Користењето модерен формат на слика (WebP, AVIF) или зголемувањето на компримирањето на сликата може да ја подобри големината на преземање на сликава."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Датотекава со слика е поголема отколку што треба да биде ({PH1}) за своите прикажани димензии ({PH2}). Користете приспособливи слики за да ја намалите големината на преземање на сликата."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Користењето видеоформати наместо GIF-датотеки може да ја подобри големината на преземање на анимираните содржини."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Започнете да истражувате со најдолгата фаза. [Доцнењата може да се минимизираат](https://web.dev/articles/optimize-inp#optimize_interactions). За да се намали времетраењето на обработката, [оптимизирајте ги трошоците за главната низа](https://web.dev/articles/optimize-long-tasks), често JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Времетра<PERSON>ње"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Доцнење на влезните податоци"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Не се откриени интеракции"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Фаза"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Доцнење на презентацијата"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Времетраење на обработката"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP според фаза"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Оптимизирајте го LCP така што ќе ја направите LCP-сликата [доста<PERSON>на](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) од HTML веднаш и [ќе избегнете мрзливо вчитување](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Применето: fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Треба да се примени fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "не е применето мрзливо вчитување"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP-сликата се вчитуваше {PH1} по најраното време на започнување."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Не е откриена LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Не е откриен ресурс на LCP бидејќи LCP не е слика"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Барањето е достапно во првичниот документ"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Откривање LCP-барање"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Секоја [фаза има конкретни стратегии на подобрување](https://web.dev/articles/optimize-lcp#lcp-breakdown). Идеално, поголемиот дел од времето на LCP треба да се потроши на вчитување на ресурсите, а не на доцнења."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Времетра<PERSON>ње"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Доцнење при прикажувањето елементи"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Поле p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Не е откриена LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Фаза"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Доцнење при вчитувањето ресурси"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Времетраење на вчитувањето на ресурсот"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Време до првиот бајт"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP по фаза"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипта"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Потрошени бајти"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Полифилите и трансформациите им овозможуваат на постарите прелистувачи да ги користат новите функции на JavaScript. Сепак, многу не се потребни за модерните прелистувачи. Сменете го вашиот процес на создавање JavaScript за да не се транспилираат функции на [Основната линија](https://web.dev/articles/baseline-and-polyfills), освен ако знаете дека мора да поддржувате постари прелистувачи. [Дознајте зошто повеќето од сајтовите може да извршуваат код ES6+ без транспилирање](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Постара верзија на JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 и HTTP/3 нудат многу повеќе придобивки од HTTP/1.1, како мултиплексирање. [Дознајте повеќе за користењето модерен HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Нема барања што користат HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Барање"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Модерен HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Извор на податоци"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Барање"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Извор"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Време"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Проц. заштеда од LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Неискористено поврзување однапред. Проверете дали атрибутот crossorigin се користи правилно."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Избегнувајте поврзување на критичните барања](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) со намалување на должината на синџирите, намалување на големината на преземање на ресурсите или одложување на преземањето на непотребните ресурси за да се подобри вчитувањето на страницата."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Додајте навестувања за [поврзување однапред](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) до најважните извори на податоци, но обидете се да користите повеќе од 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Кандидати за поврзување однапред"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Максимална критична латенција на патека:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Мрежните зависности не влијаат на задачите за прикажување"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Нема дополнителни извори на податоци што се добри кандидати за поврзување однапред"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "нема однапред поврзани извори на податоци"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Навестувањата за [поврзување однапред](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) му помагаат на прелистувачот да воспостави врска порано во вчитувањето на страниците, што штеди време при првото барање за тој извор на податоци. Следниве се изворите на податоци со коишто страницата е поврзана однапред."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Однапред поврзани извори на податоци"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Дрво на мрежна зависност"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Најдени се повеќе од 4 preconnect врски. Треба да се користат умерено и само до најважните извори на податоци."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Неискористено поврзување однапред. Користете само preconnect за извори на податоци што страницата веројатно ќе ги побара."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Избегнувајте поврзување на критичните барања со намалување на должината на синџирите, намалување на големината на преземање на ресурсите или одложување на преземањето на непотребните ресурси за да се подобри вчитувањето на страницата."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Барањата го блокираат првичното прикажување на страницата, што може да ја забави LCP. [Одложувањето или подредувањето во линии](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) може да ги премести овие барања на мрежата надвор од критичната патека."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Времетра<PERSON>ње"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Нема барања за блокирање на прикажувањето за навигацијава"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Барање"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Прикажување барања за блокирање"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ако трошоците за пресметување на стилот повторно останат високи, оптимизацијата на избирачите може да ги намали. [Оптимизирајте ги избирачите](https://developer.chrome.com/docs/devtools/performance/selector-stats) и со многу поминато време и со висока бавна патека %. Поедноставни избирачи, помалку избирачи, помал DOM и поплиток DOM ќе ги намалат трошоците за совпаѓање."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Изминато време"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Не се најдени податоци за CSS-избирач. Статистичките податоци за CSS-избирач треба да се овозможат во поставките на таблата за изведба."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Обиди за совпаѓање"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Број на совпаѓања"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Трошоци за CSS-избирачи"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Највредни избирачи"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Вкупно"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Време на главната низа"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Трета страна"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Големина на префрлање"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Кодот од трети страни може значително да влијае на изведбата при вчитување. [Намалете и одложете го вчитувањето код од трети страни](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) за да се даде приоритет на содржините на страницата."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Не се најдени трети страни"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Трети страни"}, "models/trace/insights/Viewport.ts | description": {"message": "Интеракциите со допир може да [доцнат најмногу 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ако видливата порта не е оптимизирана за мобилен."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Задоцнет допир поради лоша видлива порта за мобилен"}, "models/trace/insights/Viewport.ts | title": {"message": "Оптимизирајте ги рамките на приказ за мобилен"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Само страниците вчитани преку барање GET ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Само страниците со код на статус од 2XX може да се кешираат."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome откри обид за извршување на JavaScript додека беше во кешот."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Страниците што побарале AppBanner во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Кешот за движење наназад и нанапред е оневозможен од ознаки. Одете на chrome://flags/#back-forward-cache за да го овозможите локално на уредов."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Кешот за движење наназад и нанапред е оневозможен од редот за наредби."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Кешот за движење наназад и нанапред е оневозможен поради недоволно меморија."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Делегатот не го поддржува кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Кешот за движење наназад и нанапред е оневозможен за претходна обработка."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Страницата не може да се кешира бидејќи има примерок на BroadcastChannel со регистрирани слушатели."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Страниците со заглавје cache-control:no-store не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кешот е избришан намерно."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Страницата беше исфрлена од кешот за да се дозволи кеширање на друга страница."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Страниците што содржат приклучоци во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Недефинирано"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Страниците што користат FileChooser API не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Страниците што користат API за пристап со системот датотеки не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Страниците што користат „Оператор за преносливи уреди“ не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Плеерот беше пуштен додека се напушташе."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Страниците што користат MediaSession API и имаа поставено состојба на репродукција не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Страниците што користат MediaSession API и поставуваат ракувачи за дејства не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Кешот за движење наназад и нанапред е оневозможен поради читачот на екранот."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Страниците што користат SecurityHandler не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Страниците што користат сериски API не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Страниците што користат WebAuthetication API не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Страниците што користат WebBluetooth API не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Страниците што користат WebUSB API не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Кешот за движење наназад и нанапред е оневозможен затоа што колачињата се оневозможени на страница што користи Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Страниците што користат доделен работник или функционалност со една задача во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Документот не заврши со вчитување пред да се напушти."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Се прикажуваше „Рекламен натпис за апликации“ при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Се прикажуваше „Управник со лозинки на Chrome“ при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-дестилацијата беше во тек при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Се прикажуваше „Прикажувач на DOM-дестилатор“ при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Кешот за движење наназад и нанапред е оневозможен поради ектензиите што користат API за размена на пораки."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Екстензиите со долготрајна врска треба да ја затворат врската пред да се зачуваат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Екстензии со долготрајна врска се обидоа да испратат пораки до рамки во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Кешот за движење наназад и нанапред е оневозможен поради ектензиите."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Се прикажуваше модален дијалог (како што е дијалог за повторно поднесување формулар или дијалог за лозинка за http) при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Се прикажуваше офлајн страница при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Се прикажуваше лента за „Интервенција при недостиг на меморија“ при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Имаше барања за дозволи при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Се прикажуваше блокатор на скокачки програми при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Се прикажуваа детали за „Безбедно прелистување“ при напуштањето."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "„Безбедно прелистување“ сметаше дека оваа страница е злонамерна и ја блокираше скокачката програма."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Активиран е услужен работник додека страницата беше во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Кешот за движење наназад и нанапред е оневозможен поради грешка на документот."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Страниците што користат FencedFrames не може да се складираат во bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Страницата беше исфрлена од кешот за да се дозволи кеширање на друга страница."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Страниците што имаат одобрен пристап до стримувани аудиовизуелни содржини во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Страниците што имаат одредени видови вметнати содржини (на пр. PDF-датотеки) во моментов не се подобни за кеш за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Страниците што користат IdleManager во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Страниците што имаат отворена IndexedDB-врска во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Кешот за движење наназад и нанапред е оневозможен поради настан IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Користени се API што не ги исполнуваат условите."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Страниците во коишто е вметнат JavaScript преку екстензии во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Страниците во коишто е вметнат StyleSheet преку екстензии во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Внатр<PERSON>шна грешка."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Кешот за движење наназад и нанапред е оневозможен затоа што некое мрежно барање за JavaScript примило ресурс со заглавие Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Кешот за движење наназад и нанапред е оневозможен поради барање за одржување врска."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Страниците што користат заклучување на тастатурата во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Страницата не заврши со вчитување пред да се напушти."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Страниците чијшто главен ресурс има cache-control:no-cache не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Страниците чијшто главен ресурс има cache-control:no-store не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Навигацијата беше откажана пред да може страницата да се врати од кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Страницата беше исфрлена од кешот бидејќи активна мрежна врска прими премногу податоци. Chrome го ограничува количеството податоци што страницата може да ги прими додека се кешира."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Страниците што имаат fetch() или XHR во тек во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Страницата беше исфрлена од кешот за движење наназад и нанапред бидејќи барањето од активна мрежа требаше да се пренасочи."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Страницата беше исфрлена од кешот бидејќи мрежната врска се отвораше предолго. Chrome го ограничува временскиот период кога страницата може да прима податоци додека се кешира."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Страници што немаат важечко заглавје за одговор не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Настана навигација во рамка што не е главната рамка."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Страниците со тековни индексирани DB-трансакции во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Страниците со мрежно барање во тек во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Страниците со мрежно барање за вчитување во тек во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Страниците со мрежно барање во тек во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Страниците со мрежно XHR-барање во тек во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Страниците што користат PaymentManager во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Страниците што користат „Слика во слика“ во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Страниците што прикажуваат кориснички интерфејс за печатење во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Страницата е отворена со „window.open()“ и друга картичка има упатување до неа или страницата отворила прозорец."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Процесот на прикажување за страницата во кешот за движење наназад и нанапред падна."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Процесот на прикажување за страницата во кешот за движење наназад и нанапред е сопрен."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Страниците што побарале дозволи за снимање аудио во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Страниците што побарале дозволи за сензори во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Страниците што побарале дозволи за синхронизација или вчитување во заднина во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Страниците што побарале дозволи за MIDI во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Страниците што побарале дозволи за известувања во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Страниците што побарале пристап до капацитетот во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Страниците што побарале дозволи за снимање видео во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Само страниците чијашто шема за URL е HTTP/HTTPS може да се кешираат."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Страницата беше присвоена од услужен работник додека беше во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Услужен работник се обиде да ѝ испрати MessageEvent на страницата во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker не беше регистриран додека страницата беше во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Страницата беше исфрлена од кешот за движење наназад и нанапред поради активирање услужен работник."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome се рестартираше и ги избриша записите од кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Страниците што користат SharedWorker во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Страниците што користат SpeechRecognizer во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Страниците што користат SpeechSynthesis во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Рамката iframe на страницата започна навигација што не се заврши."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Страниците чијшто подресурс има cache-control:no-cache не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Страниците чијшто подресурс има cache-control:no-store не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Страницата го надмина максималното време во кешот за движење наназад и нанапред и времето истече."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Истече времето додека страницата се внесуваше во кешот за движење наназад и нанапред (веројатно поради долготрајни ракувачи за сокривање страница)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Страницата има ракувач за бришење во главната рамка."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Страницата има ракувач за бришење во подредена рамка."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Прелистувачот го промени заглавјето за отфрлање на корисникот агент."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Страниците што имаат одобрен пристап за снимање видео или аудио во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Страниците што користат WebDatabase во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Страниците што користат WebHID во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Страниците што користат WebLocks во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Страниците што користат WebNfc во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Страниците што користат WebOTPService во моментов не ги исполнуваат условите за bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Страниците со WebRTC не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Кешот за движење наназад и нанапред е оневозможен затоа што се користи WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Страниците што користат WebShare во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Страниците со WebSocket не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Кешот за движење наназад и нанапред е оневозможен затоа што се користи WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Страниците со WebTransport не може да се внесат во кешот за движење наназад и нанапред."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Кешот за движење наназад и нанапред е оневозможен затоа што се користи WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Страниците што користат WebXR во моментов не ги исполнуваат условите за кешот за движење наназад и нанапред."}}