{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS の Access-Control-Allow-Headers の処理では、Authorization はワイルドカード記号（*）で表されなくなります。"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "デフォルトの Cast の統合を無効にするには、-internal-media-controls-overlay-cast-button セレクタではなく disableRemotePlayback 属性を使用する必要があります。"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS の appearance 値 slider-vertical は標準化されておらず、削除される予定です。"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "URL に削除された空白文字（\\(n|r|t)）と未満文字（<）が含まれるリソース リクエストはブロックされます。これらのリソースを読み込むには、要素の属性値などの場所にある改行を削除し、未満文字をエンコードしてください。"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() は非推奨となっています。代わりに標準化 API の Navigation Timing 2 を使用してください。"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() は非推奨となっています。代わりに標準化 API の Paint Timing を使用してください。"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() は非推奨となっています。代わりに標準化 API である Navigation Timing 2 の nextHopProtocol を使用してください。"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) 文字が含まれる Cookie は、切り捨てではなく拒否されます。"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain の設定による同一オリジン ポリシーの緩和は非推奨であり、デフォルトで無効になる予定です。この非推奨の警告は、document.domain の設定により有効にしたクロスオリジンのアクセスに対して行われます。"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "クロスオリジンの iframe から window.alert をトリガーすることは非推奨であり、今後削除される予定です。"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "クロスオリジンの iframe から window.confirm をトリガーすることは非推奨であり、今後削除される予定です。"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "データのサポート: SVGUseElement の URL は非推奨となり、今後削除される予定です。"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() と watchPosition() は、保護されていないオリジンでは使用できなくなりました。この機能を使用する場合は、アプリケーションを安全なオリジン（HTTPS など）に切り替えることを検討してください。詳しくは、https://goo.gle/chrome-insecure-origins をご覧ください。"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() と watchPosition() は、保護されていないオリジンでは非推奨となっています。この機能を使用する場合は、アプリケーションを安全なオリジン（HTTPS など）に切り替えることを検討してください。詳しくは、https://goo.gle/chrome-insecure-origins をご覧ください。"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() は、保護されていないオリジンでは使用できなくなりました。この機能を使用する場合は、アプリケーションを安全なオリジン（HTTPS など）に切り替えることを検討してください。詳しくは、https://goo.gle/chrome-insecure-origins をご覧ください。"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>、<aside>、<nav>、<section> 内に、フォントサイズが指定されていない <h1> タグが見つかりました。まもなくこのブラウザでは、この見出しのテキストのサイズが変更されます。詳しくは、https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 をご覧ください。"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate のサポートは終了しました。代わりに RTCPeerConnectionIceErrorEvent.address または RTCPeerConnectionIceErrorEvent.port を使用してください。"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "この形式は、デジタル認証情報の navigator.credentials.get() リクエストで非推奨となりました。呼び出しを更新して新しい形式を使用してください。"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment　Service Worker イベントの、次の販売者のオリジンと任意のデータのサポートは終了し、削除される予定です。topOrigin、paymentRequestOrigin、methodData、modifiers。"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "ウェブサイトが、ネットワークで特権を持つユーザーのみアクセス可能な、ネットワークのサブリソースをリクエストしました。これらのリクエストにより、非公開のデバイスやサーバーがインターネット上で参照できるようになるため、クロスサイト リクエスト フォージェリ（CSRF）攻撃や情報漏洩のリスクが高まります。これらのリスクを軽減するため、Chrome では、保護されていないコンテキストからの非公開サブリソースへのリクエストは非推奨となっており、今後ブロックされるようになる予定です。"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "動作を正確に反映させるため、joinAdInterestGroup() に渡される InterestGroups の dailyUpdateUrl フィールドの名前を updateUrl に変更しました。"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator は非推奨になりました。代わりに Intl.Segmenter を使用してください。"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "末尾が .css ファイル拡張子ではない CSS を、file: の URL から読み込むことはできません。"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove() の非同期の範囲の削除を中止するための SourceBuffer.abort() の使用は、仕様変更により非推奨となっており、今後サポートされなくなります。代わりに updateend イベントをリッスンします。なお、abort() については、非同期メディアの追加の中止と、パーサーの状態のリセットのみを行います。"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "バッファ済みコードフレームの最も高いプレゼンテーション タイムスタンプを下回る MediaSource.duration の設定は、仕様変更により非推奨となりました。切り捨てられたバッファ済みメディアの暗黙的な削除は、今後サポートされなくなります。代わりに、すべての sourceBuffers で明示的に remove(newDuration, oldDuration) を実行してください。このとき、newDuration < oldDuration となるよう指定してください。"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI は、MIDIOptions で SysEx が指定されていない場合でも使用許可を要求します。"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "通知 API は保護されていないオリジンから使用できなくなっている可能性があります。アプリケーションを安全なオリジン（HTTPS など）に切り替えることを検討してください。詳しくは、https://goo.gle/chrome-insecure-origins をご覧ください。"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "通知 API の権限は、クロスオリジンの iframe からリクエストできなくなっている可能性があります。最上位フレームからの権限をリクエストするか、代わりに新しいウィンドウを開くことを検討してください。"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap の imageOrientation: 'none' オプションは非推奨になりました。createImageBitmap を使用する際は、代わりに {imageOrientation: 'from-image'} オプションを使用してください。"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "パートナーは古い（D）TLS バージョンの交渉を行っています。パートナーに確認してこれを修正してもらってください。"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img タグ、video タグ、canvas タグに overflow: visible を指定すると、要素の境界外にビジュアル コンテンツが作成される場合があります。https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md をご覧ください。"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments は非推奨になりました。支払いハンドラには、代わりに JIT インストールを使用してください。"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest 呼び出しが Content-Security-Policy（CSP）connect-src ディレクティブをバイパスしました。このバイパスは非推奨となっています。PaymentRequest API（supportedMethods フィールド）のお支払い方法 ID を CSP の connect-src ディレクティブに追加してください。"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent のサポートは終了しました。代わりに、標準化された navigator.storage を使用してください。"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> で <picture> 親要素を使用すると無効となり、無視されます。代わりに <source srcset> を使用してください。"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame はベンダー固有です。代わりに標準の cancelAnimationFrame を使用してください。"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame はベンダー固有です。代わりに標準の requestAnimationFrame を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen は非推奨になりました。代わりに Document.fullscreenElement を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() は非推奨になりました。代わりに Element.requestFullscreen() を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() は非推奨になりました。代わりに Element.requestFullscreen() を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() は非推奨になりました。代わりに Document.exitFullscreen() を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() は非推奨になりました。代わりに Document.exitFullscreen() を使用してください。"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen は非推奨になりました。代わりに Document.fullscreenEnabled を使用してください。"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled は非推奨になりますが、下位互換性を確保するため、リリース M113 まで引き続き有効です。代わりに、chrome.privacy.websites.topicsEnabled、chrome.privacy.websites.fledgeEnabled、chrome.privacy.websites.adMeasurementEnabled を使用してください。詳しくは https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled をご覧ください。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement の制約は削除されました。この制約に指定されている false 値は、削除された SDES key negotiation の方法を使用する試みとして解釈されます。この機能は削除されたため、DTLS key negotiation をサポートしているサービスで代用してください。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement の制約は削除されました。この制約に指定されている true 値は適用されませんが、この制約を削除するとシンプルにできます。"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "コールバックベースの getStats() は非推奨になっており、今後削除されます。代わりに、仕様に準拠した getStats() を使用してください。"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() は非推奨になりました。代わりに Selection.modify() を使用してください。"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL に埋め込み認証情報（**********************/ など）が含まれるサブリソース リクエストはブロックされます。"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy オプションは非推奨であり、削除される予定です。"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer ではクロスオリジン分離が必要となります。詳しくは、https://developer.chrome.com/blog/enabling-shared-array-buffer/ をご覧ください。"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "ユーザー アクティベーションのない speechSynthesis.speak() は非推奨であり、削除される予定です。"}, "generated/Deprecation.ts | UnloadHandler": {"message": "unload イベント リスナーはサポートが終了したため、今後削除されます。"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "拡張機能で引き続き SharedArrayBuffer を使用するは、クロスオリジン分離を有効にする必要があります。https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ をご覧ください。"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter 属性は非推奨です。代わりに、GPUAdapterInfo isFallbackAdapter 属性を使用してください。"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 は、XMLHttpRequest のレスポンス JSON ではサポートされていません。"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "メインスレッドの同期的な XMLHttpRequest は、エンドユーザーのエクスペリエンスに悪影響があるため、非推奨となっています。詳しくは、https://xhr.spec.whatwg.org/ をご覧ください。"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "アニメーション"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "レイアウト シフトは、ユーザーの操作なしで要素が移動する場合に発生します。[レイアウト シフトの原因を調査](https://web.dev/articles/optimize-cls)してください。たとえば、ページの読み込み時に要素が追加、削除される、フォントが変更されるなどの原因が考えられます。"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "フォントのリクエスト"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "挿入された iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "レイアウト シフト クラスタ @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "レイアウト シフトの原因を検出できませんでした"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "レイアウト シフトなし"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "レイアウト シフトの原因"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "レイアウト シフトの主な原因"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "最も影響の大きいクラスタ"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "最も影響の大きいレイアウト シフト クラスタ"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "キャッシュの TTL"}, "models/trace/insights/Cache.ts | description": {"message": "キャッシュの有効期間を長くすると、再訪問したユーザーへのページの読み込み速度を向上できます。[詳細](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "非効率的キャッシュ ポリシーの対象となるリクエストはありません"}, "models/trace/insights/Cache.ts | others": {"message": "他 {PH1} 件"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "リクエスト"}, "models/trace/insights/Cache.ts | title": {"message": "効率的なキャッシュ保存期間を使用する"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM サイズが大きいと、スタイルの計算とレイアウトのリフローに時間がかかり、ページの応答性に影響する可能性があります。DOM サイズが大きいと、メモリ使用量も増加します。[過度な DOM サイズの回避方法の詳細](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "要素"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "子の最大数"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM の深さ"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "統計情報"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM サイズを最適化する"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "合計要素数"}, "models/trace/insights/DOMSize.ts | value": {"message": "値"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "最初のネットワーク リクエストは最も重要です。リダイレクトを回避し、サーバー応答を高速に保ち、テキスト圧縮を有効にして、レイテンシを削減します。"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "リダイレクトあり（{PH1} 件のリダイレクト、+ {PH2}）"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "サーバーの応答が遅い（観測値 {PH1}）"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "圧縮の適用なし"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "リダイレクトなし"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "サーバーの応答が速い（観測値 {PH1}）"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "テキスト圧縮の適用あり"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "リダイレクト"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "サーバーの応答時間"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ドキュメント リクエストのレイテンシ"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "非圧縮のダウンロード"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "重複するバイト"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ソース"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "重複する大きい JavaScript モジュールをバンドルから削除すると、ネットワーク アクティビティで不必要に消費されるデータ量を減らすことができます。"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "重複する JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "テキストの表示を統一するため、[font-display](https://developer.chrome.com/blog/font-display) を swap または optional に設定することを検討してください。swap をさらに最適化して、[フォント指標のオーバーライド](https://developer.chrome.com/blog/font-fallbacks)でレイアウト シフトを軽減できます。"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "フォント"}, "models/trace/insights/FontDisplay.ts | title": {"message": "フォント表示"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "無駄になった時間"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "（匿名）"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "一般的に、レイアウト ジオメトリの読み取りを行う多くの API は、レンダリング エンジンによるスクリプトの実行を強制的に一時停止して、スタイルとレイアウトを計算します。詳しくは、[強制リフロー](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)とその緩和策をご覧ください。"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "スタック トレース"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "強制リフロー"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "上位の関数呼び出し"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "合計リフロー時間"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ソース不明]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "画像のダウンロード時間を短縮すると、ページ読み込みの体感時間と LCP を改善できます。[画像サイズの最適化に関する詳細](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1}（推定 {PH2}）"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "最適化できる画像はありません"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ファイルサイズの最適化"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "他 {PH1} 件"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "画像配信を改善する"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "画像の圧縮係数を増やすと、この画像のダウンロード サイズが改善する可能性があります。"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "最新の画像形式（WebP、AVIF）を使用するか、画像の圧縮率を高くすると、この画像のダウンロード サイズが改善する可能性があります。"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "この画像ファイルは、表示サイズ（{PH2}）に必要なサイズ（{PH1}）より大きくなっています。画像のダウンロード サイズを小さくするには、レスポンシブ画像を使用します。"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF ではなく動画形式を使用すると、アニメーション コンテンツのダウンロード サイズが改善する可能性があります。"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "最も長いフェーズから調査を開始します。[遅延を最小限に抑えることができます](https://web.dev/articles/optimize-inp#optimize_interactions)。処理期間を削減するには、[メインスレッドのコスト（通常は JS）を最適化](https://web.dev/articles/optimize-long-tasks)します。"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "所要時間"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "入力遅延"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "操作は検出されませんでした"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "フェーズ"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "表示の遅延"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "処理期間"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "フェーズ別の INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP 画像を HTML からすぐに[検出できる](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)ようにし、[遅延読み込みを回避](https://web.dev/articles/lcp-lazy-loading)して、LCP を最適化します"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "「fetchpriority=high」が適用されました"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "「fetchpriority=high」を適用する必要があります"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "遅延読み込みが適用されていません"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP のイメージは、最も早い開始点から {PH1} 後に読み込まれました。"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP は検出されませんでした"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP が画像ではないため、LCP リソースは検出されませんでした"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "リクエストは最初のドキュメントで検出可能です"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP リクエストの検出"}, "models/trace/insights/LCPPhases.ts | description": {"message": "各[フェーズには、それに適した改善戦略があります](https://web.dev/articles/optimize-lcp#lcp-breakdown)。理想的なのは、LCP 時間のほとんどがリソースの読み込みに使われ、遅延に費やされないことです。"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "所要時間"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "要素のレンダリングの遅延"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "実際 p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP は検出されませんでした"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "フェーズ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "リソース読み込みの遅延"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "リソースの読み込み時間"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "フェーズ別の LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "スクリプト"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "不要なバイト"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "ポリフィルと変換を使用すると、従来のブラウザで新しい JavaScript 機能を使用できるようになります。ただし、その機能の多くは最新ブラウザでは必要ありません。従来のブラウザをサポートする必要がある場合を除き、[Baseline](https://web.dev/articles/baseline-and-polyfills) の機能をトランスパイルしないように JavaScript ビルドプロセスを変更することを検討してください。[ほとんどのサイトでトランスパイルせずに ES6+ コードをデプロイできる理由](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "以前の JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 と HTTP/3 には、多重化など、HTTP/1.1 と比べて多くのメリットがあります。[最新の HTTP の使用について](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 を使用したリクエストはありません"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "プロトコル"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "リクエスト"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "最新の HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "オリジン"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "リクエスト"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ソース"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "時間"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "推定される LCP 短縮時間"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "未使用の事前接続。crossorigin 属性が適切に使用されていることを確認してください。"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "チェーンの長さを縮小する、リソースのダウンロード サイズを抑える、不要なリソースのダウンロードを遅らせるなどしてページの読み込み速度を改善し、[クリティカル リクエスト チェーンを回避](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)してください。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "最も重要なオリジンに[事前接続](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)のヒントを追加します。ヒントを追加するオリジンは 4 つ以下にすることをおすすめします。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "事前接続の候補"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "クリティカル パスの最大待ち時間:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ネットワークの依存関係の影響を受けるレンダリング処理はありません"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "事前接続の候補となるオリジンは他にありません"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "事前接続したオリジンはありません"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[事前接続](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)のヒントは、ブラウザがページの読み込みの早い段階で接続を確立できるようにして、そのオリジンに対する最初のリクエストの実行時間を短縮します。以下は、ページから事前接続したオリジンです。"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "事前接続したオリジン"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ネットワークの依存関係ツリー"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4 つ以上の preconnect 接続が見つかりました。これらの接続は控えめに、重要なソースにのみ使用してください。"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "未使用の事前接続。preconnect は、ページでリクエストされる可能性が高いオリジンにのみ使用してください。"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "チェーンの長さを縮小する、リソースのダウンロード サイズを抑える、不要なリソースのダウンロードを遅らせるなどしてページの読み込み速度を改善し、クリティカル リクエスト チェーンを回避してください。"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "リクエストがページの最初のレンダリングをブロックしているため、LCP が遅れる可能性があります。これらのネットワーク リクエストを[遅らせるかインライン化](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)すると、クリティカル パスから移動できます。"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "所要時間"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "このナビゲーションのレンダリングをブロックしているリクエストはありません"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "リクエスト"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "レンダリングをブロックしているリクエスト"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "スタイルの再計算のコストが引き続き高い場合は、セレクタの最適化によってコストを削減できます。経過時間が長く、遅いパスの割合（%）が高い[セレクタを最適化](https://developer.chrome.com/docs/devtools/performance/selector-stats)します。セレクタの複雑さや数、DOM のサイズや深さを抑えて、一致コストを削減します。"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "経過時間"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS セレクタのデータは見つかりませんでした。パフォーマンス パネルの設定で CSS セレクタの統計情報を有効にする必要があります。"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "マッチング試行回数"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "一致数"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS セレクタのコスト"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "主なセレクタ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "合計"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "メインスレッド時間"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "サードパーティ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "転送サイズ"}, "models/trace/insights/ThirdParties.ts | description": {"message": "サードパーティのコードによって、読み込み速度が著しく低下する可能性があります。ページのコンテンツを優先させるには、[サードパーティのコードの読み込みを減らして遅らせ](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)ます。"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "サードパーティのコンテンツは見つかりませんでした"}, "models/trace/insights/ThirdParties.ts | title": {"message": "サードパーティ"}, "models/trace/insights/Viewport.ts | description": {"message": "ビューポートがモバイル向けに最適化されていない場合、タップ操作が[最大 300 ミリ秒遅延](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)する可能性があります。"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "モバイルタップの遅延"}, "models/trace/insights/Viewport.ts | title": {"message": "ビューポートをモバイル向けに最適化する"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "バックフォワード キャッシュの対象になるのは、GET リクエストで読み込まれたページのみです。"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "キャッシュに保存できるのは、ステータス コードが 2XX のページのみです。"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "キャッシュにある間に JavaScript を実行しようとしていることが検出されました。"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "バックフォワード キャッシュはフラグにより無効になっています。chrome://flags/#back-forward-cache に移動して、このデバイスでローカルに有効化してください。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "バックフォワード キャッシュはコマンドラインにより無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "バックフォワード キャッシュはメモリ不足のため無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "バックフォワード キャッシュは委任には対応していません。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "バックフォワード キャッシュは事前レンダリングのため無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "このページにはリスナーが登録された BroadcastChannel インスタンスがあるため、キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Cache-Control: no-store ヘッダーがあるページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "キャッシュは意図的に消去されました。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "別のページをキャッシュに保存できるようにするため、ページがキャッシュから削除されました。"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "プラグインを含むページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "未定義"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "メディア デバイス ディスパッチャーを使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "ページから移動するときにメディア プレーヤーが再生中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API を使用しており再生状態にあるページは、バックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API を使用しておりアクション ハンドラを設定するページは、バックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "バックフォワード キャッシュはスクリーン リーダーが原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthentication API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API を使用するページはバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store を使用するページで Cookie が無効になっているため、バックフォワード キャッシュは無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "専用のワーカーまたはワークレットを使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "読み込みが終了していないドキュメントから移動しました。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "ページから移動するときにアプリバナーが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "ページから移動するときに Chrome パスワード マネージャーが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "ページから移動するときに DOM 抽出の処理が実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "ページから移動するときに DOM Distiller ビューアが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "バックフォワード キャッシュは、Messaging API を使用している拡張機能が原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "バックフォワード キャッシュを使用する前に、持続的に接続している拡張機能の接続を終了する必要があります。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "持続的に接続している拡張機能で、バックフォワード キャッシュのフレームへのメッセージ送信が試行されていました。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "バックフォワード キャッシュは拡張機能が原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "ページから移動するときにフォームの再送信や HTTP パスワード ダイアログなどのモーダル ダイアログがページに表示されていました。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "ページから移動するときにオフラインのページが表示されていました。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "ページから移動するときにメモリ不足介入バーが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "ページから移動するときに権限リクエストが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "ページから移動するときにポップアップ ブロッカーが実行中でした。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "ページから移動するときにセーフ ブラウジングのメッセージが表示されていました。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "セーフ ブラウジングでこのページが不正と判断され、ポップアップがブロックされました。"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "ページがバックフォワード キャッシュにある間に、Service Worker が起動されました。"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "バックフォワード キャッシュはドキュメントのエラーが原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Fenced Frame を使用しているページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "別のページをキャッシュに保存できるようにするため、ページがキャッシュから削除されました。"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "メディア ストリーム アクセスが可能なページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "特定の種類の埋め込みコンテンツ（PDF など）があるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "IndexedDB 接続が開いているページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "バックフォワード キャッシュは IndexedDB イベントが原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "対象外の API が使用されました。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "拡張機能によって JavaScript が挿入されるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "拡張機能によって StyleSheet が挿入されるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "内部エラーが発生しました。"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "一部の JavaScript ネットワーク リクエストで Cache-Control: no-store ヘッダーを含むリソースが返されたため、バックフォワード キャッシュは無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "バックフォワード キャッシュはキープアライブ リクエストが原因で無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "キーボード ロックを使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "読み込みが終了していないページから移動しました。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Cache-Control: no-cache のメインリソースがあるページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Cache-Control: no-store のメインリソースがあるページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ページがバックフォワード キャッシュから復元される前に移動がキャンセルされました。"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "アクティブなネットワーク接続による受信データが多すぎたため、ページがキャッシュから削除されました。Chrome は、キャッシュに保存されたページが受信できるデータ量に上限を設けています。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "処理中の fetch() または XHR があるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "アクティブなネットワーク リクエストにリダイレクトが含まれていたため、ページがバックフォワード キャッシュから削除されました。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ネットワーク接続の時間が長すぎたため、ページがキャッシュから削除されました。Chrome は、キャッシュに保存されたページがデータを受信できる時間に上限を設けています。"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "有効なレスポンス ヘッダーのないページはバックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "メインフレーム以外のフレームで移動が行われました。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "インデックス登録済み DB のトランザクションが進行中のページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "処理中のネットワーク リクエストがあるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "処理中の fetch ネットワーク リクエストがあるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "処理中のネットワーク リクエストがあるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "処理中の XHR ネットワーク リクエストがあるページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "ピクチャー イン ピクチャーを使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "印刷 UI を表示するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "このページは、window.open() を使用して開かれ、他のタブから参照されているか、ウィンドウが開かれています。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "バックフォワード キャッシュにあるページのレンダラ プロセスに問題が発生しました。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "バックフォワード キャッシュにあるページのレンダラ プロセスが強制終了されました。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "音声キャプチャの権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "センサーの権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "バックグラウンド同期またはバックグラウンド フェッチの権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI の権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "通知の権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "ストレージ アクセスをリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "動画キャプチャの権限をリクエストしたページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "キャッシュに保存できるのは、URL スキームが HTTP または HTTPS のページのみです。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "ページがバックフォワード キャッシュにある間に Service Worker に要求されました。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service Worker がバックフォワード キャッシュ内のページに MessageEvent を送信しようとしました。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ページがバックフォワード キャッシュにある間に Service Worker が登録解除されました。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Service Worker の起動により、ページがバックフォワード キャッシュから削除されました。"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome が再起動し、バックフォワード キャッシュの内容が消去されました。"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "ページ上で iframe が移動を開始し、完了しませんでした。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Cache-Control: no-cache のサブリソースがあるページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Cache-Control: no-store のサブリソースがあるページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "このページは、バックフォワード キャッシュの最大保存時間を超えたため期限切れとなりました。"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "ページのバックフォワード キャッシュへの保存がタイムアウトしました（長時間実行中の pagehide ハンドラが原因である可能性があります）。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "ページのメインフレームにアンロード ハンドラがあります。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "ページのサブフレームにアンロード ハンドラがあります。"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ブラウザによりユーザー エージェント オーバーライド ヘッダーが変更されました。"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "録画や録音が可能なページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNFC を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC を使用するページは、バックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "バックフォワード キャッシュは、WebRTC が使用されているため無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket を使用するページはバックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "バックフォワード キャッシュは、WebSocket が使用されているため無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport を使用するページはバックフォワード キャッシュに保存できません。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "バックフォワード キャッシュは、WebTransport が使用されているため無効になっています。"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR を使用するページは、現時点ではバックフォワード キャッシュの対象になりません。"}}