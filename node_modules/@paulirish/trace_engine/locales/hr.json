{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Authorization will not be covered by the wildcard symbol (*) in CORS Access-Control-Allow-Headers handling."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "The disableRemotePlayback attribute should be used in order to disable the default Cast integration instead of using -internal-media-controls-overlay-cast-button selector."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Vrijednost izgleda CSS-a slider-vertical nije standardizirana i uklonit će se."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resource requests whose URLs contained both removed whitespace \\(n|r|t) characters and less-than characters (<) are blocked. Please remove newlines and encode less-than characters from places like element attribute values in order to load these resources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is deprecated, instead use standardized API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies containing a \\(0|r|n) character will be rejected instead of truncated."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Popuštanje pravila istog porijekla postavljanjem document.domain obustavlja se i onemogućit će se prema zadanim postavkama. Ovo upozorenje o obustavljanju odnosi se na pristupanje iz više izvora koje je bilo omogućeno postavkom document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Pokretanje window.alert iz iframea različitih izvora obustavlja se i ubuduće će biti uklonjeno."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Pokretanje API-ja window.confirm iz iframesa različitih izvora obustavlja se i ubuduće će biti uklonjeno."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Podrška za podatke: URL-ovi u SVGUseElementu obustavljeni su i ubuduće će se ukloniti."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() i watchPosition() više ne rade na nesigurnim izvorima. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() i watchPosition() obustavljaju se na nesigurnim izvorima. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() no longer works on insecure origins. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<PERSON>nađ<PERSON> je oznaka <h1> unutar oznake <article>, <aside>, <nav> ili <section> za koju nije navedena veličina fonta. Veličina teksta naslova mijenjat će se u ovom pregledniku u bliskoj budućnosti. Više informacija potražite na https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Polje RTCPeerConnectionIceErrorEvent.hostCandidate je obustavljeno. Upotrijebite RTCPeerConnectionIceErrorEvent.address ili RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ovaj je format za navigator.credentials.get() zahtjev za digitalne vjerodajnice zastario. Ažurirajte poziv da biste upotrebljavali novi format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Podaci o izvoru trgovca i proizvoljni podaci o događaju uslužnog alata canmakepayment obustavljeni su te će se ukloniti: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "The website requested a subresource from a network that it could only access because of its users' privileged network position. These requests expose non-public devices and servers to the internet, increasing the risk of a cross-site request forgery (CSRF) attack, and/or information leakage. To mitigate these risks, Chrome deprecates requests to non-public subresources when initiated from non-secure contexts, and will start blocking them."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Polje dailyUpdateUrl u strukturi InterestGroups koje je proslijeđeno na joinAdInterestGroup() preimenovano je u updateUrl kako bi preciznije odražavalo njegovo ponašanje."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator je obustavljen. Umjesto njega upotrijebite Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS cannot be loaded from file: URLs unless they end in a .css file extension."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Upotreba SourceBuffer.abort() za prekid uklanjanja asinkronog raspona za remove() obustavljena je zbog promjene specifikacija. Podrška će se ukloniti u budućnosti. Umjesto toga trebali biste osluškivati događaj updateend. abort() može prekinuti samo stanje dodavanja asinkronog medija ili raščlanjivača vraćanja na zadano."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setting MediaSource.duration below the highest presentation timestamp of any buffered coded frames is deprecated due to specification change. Support for implicit removal of truncated buffered media will be removed in the future. You should instead perform explicit remove(newDuration, oldDuration) on all sourceBuffers, where newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI will ask a permission to use even if the sysex is not specified in the MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification API možda se više neće upotrebljavati iz nesigurnih izvora. Trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao to je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Možda se više neće tražiti dopuštenje za Notification API iz iframea različitih izvora. Trebali biste razmisliti o traženju dopuštenja okvira najviše razine ili o otvaranju novog prozora."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opcija imageOrientation: 'none' u stavci CreateImageBitmapa je obustavljena. Umjesto nje upotrijebite oznaku createImageBitmap s opcijom \"{imageOrientation: 'from-image'}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Your partner is negotiating an obsolete (D)TLS version. Please check with your partner to have this fixed."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "<PERSON>ko navedete overflow: visible za oznake img, video i canvas, te oznake mogu proizvesti vizualni sadržaj izvan granica elemenata. Pogledajte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments je obustavljen. Umjesto toga koristite just-in-time instalaciju za rukovatelje plaćanjem."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Vaš je poziv PaymentRequest zaobilazio direktivu connect-src Pravila o sigurnosti sadržaja (CSP). To je zaobilaženje obustavljeno. Dodajte identifikator načina plaćanja iz PaymentRequest API-ja (u polju supportedMethods) direktivi connect-src CSP-a."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent je obustavljen. Umjesto njega upotrijebite standardizirani navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> s nadređenim <picture> nevažeći je i stoga se zanemaruje. Umjesto toga koristite <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame odnosi se na dobavljača. Upotrijebite standardni cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame odnosi se na dobavljača. Umjesto njega upotrijebite standardni requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingfullscreen je obustavljen. Umjesto njega koristite Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() je obustavljen. Umjesto njega koristite Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() je obustavljen. Umjesto njega koristite Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() je obustavljen. Umjesto njega koristite Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() je obustavljen. Umjesto njega koristite Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen je obustavljen. Umjesto njega koristite Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Obustavljamo API chrome.privacy.websites.privacySandboxEnabled, ali će ostati aktivan za kompatibilnost s prijašnjim verzijama do izdanja M113. Umjesto toga upotrijebite chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled i chrome.privacy.websites.adMeasurementEnabled. Posjetite https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Uklonjeno je ograničenje DtlsSrtpKeyAgreement. Naveli ste vrijednost false za ograničenje, što se tumači kao pokušaj korištenja uklonjene metode SDES key negotiation. Ta je funkcija uklonjena. Umjesto nje koristite uslugu koja podržava DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Uklonjeno je ograničenje DtlsSrtpKeyAgreement. Naveli ste vrijednost true za ovo ograničenje, što nije im<PERSON>, ali možete ukloniti ovo ograničenje radi jasnoće."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "GetStats() na temelju poziva obustavljen je i uklonit će se. Umjesto toga upotrijebite getStats() usklađen sa specifikacijama."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() je obustavljen. Umjesto njega upotrijebite Select.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subresource requests whose URLs contain embedded credentials (e.g. **********************/) are blocked."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opcija rtcpMuxPolicy obustavlja se i uklonit će se."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer will require cross-origin isolation. Više pojedinosti pročitajte na vezi https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() bez aktivacije korisnika obustavlja se i uklonit će se."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Uklanjanje učitavanja slušatelja događaja obustavlja se i uklonit će se."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensions should opt into cross-origin isolation to continue using SharedArrayBuffer. Posjetite https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atribut GPUAdapter isFallbackAdapter je ukinut. Umjesto njega upotrijebite atribut GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 is not supported by response json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinkroni XMLHttpRequest na glavnoj niti obustavlja se zbog negativnog utjecaja na doživljaj krajnjeg korisnika. Za dodatnu pomoć posjetite https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacija"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Do pomaka izgleda dolazi kada se elementi pomiču bez korisničke interakcije. [Istražite uzroke pomaka izgleda](https://web.dev/articles/optimize-cls), kao što su dodavanje i uklanjanje elemenata ili promjena njihovih fontova tijekom učitavanja stranice."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Zahtjev za font"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Ugra<PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "<PERSON><PERSON><PERSON> pomaka i<PERSON> @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nije otkriven nijedan uzrok pomaka izgleda"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON>ema poma<PERSON> i<PERSON>a"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Uzroci pomaka izgleda"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Najčešći uzroci pomaka izgleda"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "<PERSON><PERSON><PERSON><PERSON> skupina pomaka i<PERSON>gleda"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL predmemoriranja"}, "models/trace/insights/Cache.ts | description": {"message": "Dugotrajno predmemoriranje može ubrzati ponovljene posjete vašoj stranici. [Saznajte više](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "<PERSON>ema <PERSON>va s neučinkovitim pravilima predmemoriranja"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON> {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Upotrijebi učinkovito trajanje predmemorije"}, "models/trace/insights/DOMSize.ts | description": {"message": "Veliki DOM može produžiti trajanje proračuna stila i ponovnih proračuna izgleda, što utječe na responzivnost stranice. Veliki DOM također će povećati upotrebu memorije. [Saznajte kako izbjeći pretjeranu veličinu DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Većina podređenih elemenata"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Dubina DOM-a"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizirajte veličinu DOM-a"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON><PERSON> broj elemena<PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "Vrijednost"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Vaš je prvi zahtjev za mrežu najvažniji.  Smanjite latenciju izbjegavanjem preusmjeravanja, osiguravanjem brzog odgovora poslužitelja i omogućavanjem kompresije teksta."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON>ilo je preusmjeravanja (broj preusmjeravanja: {PH1}; više od {PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Poslužitelj je odgovorio sporo (zabilježeno: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "<PERSON>je primijenjena kompresija"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Izbjegava preusmjeravanja"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Poslužitelj br<PERSON> o<PERSON> (zabilježeno: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Primjenjuje kompresiju teksta"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Vrijeme odgovora poslužitelja"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latencija zahtjeva za dokument"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Preuzimanje bez kompresije"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Uklonite velike JavaScript module iz skupova u njihove duplikate kako biste smanjili nepotrebne bajtove koje troši mrežna aktivnost."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplicirani JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Savjetu<PERSON><PERSON> vam da [font-display](https://developer.chrome.com/blog/font-display) postavite na swap ili optional kako biste osigurali dosljednu vidljivost teksta. swap može se dodatno optimizirati kako bi se ublažili pomaci izgleda pomoću [prevladavanja mjernih podataka o fontu](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Font"}, "models/trace/insights/FontDisplay.ts | title": {"message": "P<PERSON>z fonta"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Izgubljeno vrijeme"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonimno)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Mnogi API-jevi, obično oni koji čitaju geometriju izgleda, prisiljavaju alat za renderiranje da pauzira izvršavanje skripte radi proračuna stila i izgleda. Saznajte više o [prisilnom ponovnom proračunu](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) i njegovom ublažavanju."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Praćenje snopa"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Prisilni ponovni proračun"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Najčešći poziv funkcije"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Ukupno vrijeme ponovnog proračuna"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[unattributed]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Smanjenje vremena preuzimanja slika može poboljšati percipirano vrijeme učitavanja stranice i LCP. [Saznajte više o optimizaciji veličine slike](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (proc. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nema slika koje se mogu optimizirati"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimizirajte veličinu datoteke"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON> {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Poboljšanje preuzimanja i upotrebe slika"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Povećanje koeficijenta kompresije slike može poboljšati veličinu preuzimanja te slike."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Upotreba modernog formata slike (WebP, AVIF) ili povećanje kompresije slike može poboljšati veličinu preuzimanja ove slike."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON>va je slikovna datoteka veća nego što treba biti ({PH1}) za prikazane dimenzije ({PH2}). Upotrijebite responzivne slike da biste smanjili veličinu preuzimanja slike."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Upotreba videoformata umjesto GIF-ova može poboljšati veličinu preuzimanja animiranog sadržaja."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Započnite s najdu<PERSON><PERSON> razdobljem. [Kaš<PERSON><PERSON><PERSON> se mogu svesti na najmanju moguću mjeru](https://web.dev/articles/optimize-inp#optimize_interactions). Da biste skratili trajanje obrade, [optimizirajte troškove glavne niti](https://web.dev/articles/optimize-long-tasks), koji su često JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Kašnjenje unosa"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nije otkrivena nijedna interakcija"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Faza"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Kašnjenje pre<PERSON>cije"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> obra<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Vrijeme između interakcija (INP) po fazi"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizirajte LCP tako da odmah [otkrijete](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) sliku LCP-a iz HTML-a i [izbjegnete odgođeno učitavanje](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "primijenjeno je dohvaćanje prioriteta=visoko"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "treba primijeniti dohvaćanje prioriteta=visoko"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "nije primijenjeno odgođeno učitavanje"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Slika LCP-a učitana je {PH1} nakon najranije početne toč<PERSON>."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nije otkriven LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "<PERSON><PERSON> otkriven nijedan resurs LCP-a jer LCP nije slika"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Zahtjev se može otkriti u početnom dokumentu"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Otkrivanje zahtjeva za vrijeme učitavanja punog sadržaja (LCP)"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Svaka [faza ima određene strategije poboljšanja](https://web.dev/articles/optimize-lcp#lcp-breakdown). U idealnom slučaju većina vremena LCP-a trebala bi se potrošiti na učitavanje resursa, a ne na kašnjenja."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Kašnjenje <PERSON>a"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Polje p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nije otkriven LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Faza"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Kašnjenje učitavanja resursa"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Trajanje učitavanja resursa"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Vrijeme do prvog bajta (TTFB)"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Vrijeme učitavanja punog sadržaja (LCP) po fazi"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Skripta"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Neiskorišteni bajtovi"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Kodovi polyfill i transform omogućuju starijim preglednicima da upotrebljavaju nove značajke JavaScripta. Međutim, mnogi nisu potrebni za moderne preglednike. Razmislite o izmjeni procesa izrade JavaScripta kako ne bi transpilirao značajke [Baseline](https://web.dev/articles/baseline-and-polyfills), osim ako znate da morate podržavati starije preglednike. [Saznajte zašto većina web-lokacija može implementirati ES6+ kôd bez transpiliranja](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Stari JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 i HTTP/3 nude brojne prednosti u odnosu na HTTP/1.1, kao što je sabiranje signala u jedan signal. [Saznajte više o upotrebi modernog HTTP-a](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Nijedan zahtjev nije upotrebljavao HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderni HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>je<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Proc. ušteda LCP-a"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Povezivanje unaprijed nije upotrijebljeno. Provjerite upotrebljava li se atribut crossorigin ispravno."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Izbjegavajte lančano povezivanje kritičnih zahtjeva](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) tako da skratite duljinu lanaca, smanjite veličinu resursa za preuzimanje ili odgodite preuzimanje resursa koji nisu nužni kako biste poboljšali učitavanje stranice."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Dodajte prilagodbe za [povezivanje unaprijed](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) u svoje najvažnije izvore, ali pokušajte upotrijebiti najviše četiri."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidati za povezivanje unaprijed"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Maks. latencija kritič<PERSON>g puta:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nema zadataka renderiranja na koje utječu mrežne zavisnosti"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nijedan dodatni izvor nije dobar kandidat za povezivanje unaprijed"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "nijedan izvor nije unaprijed povezan"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Prilagodbe za [povezivanje unaprijed](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pomažu pregledniku da uspostavi vezu ranije tijekom učitavanja stranice, čime se štedi vrijeme kada se uputi prvi zahtjev za taj izvor. U nastavku su izvori s kojima je stranica unaprijed povezana."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Unaprijed povezani izvori"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Pronađeno je više od četiri veze preconnect. Te bi se veze trebale rijetko upotrebljavati i samo za najvažnije izvore."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Povezivanje unaprijed nije upotrijebljeno. Koristite preconnect samo za izvore za koje će stranica vjerojatno slati zahtjeve."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Izbjegavajte lančano povezivanje kritičnih zahtjeva tako da skratite duljinu lanaca, smanjite veličinu resursa za preuzimanje ili odgodite preuzimanje resursa koji nisu nužni da biste poboljšali učitavanje stranice."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Zahtjevi blokiraju početno renderiranje stranice zbog čega učitavanje punog sadržaja (LCP) može kasniti. [Odgađanjem ili ugrađivanjem](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ti se mrežni zahtjevi mogu premjestiti s kritične putanje."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nema z<PERSON>jeva za blokiranje renderiranja za ovu navigaciju"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Zahtjevi koji su blokirali renderiranje"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Ako troškovi za ponovo izračunaj stil ostanu visoki, optimizacija alata za odabir može ih smanjiti. [Optimizirajte selektore](https://developer.chrome.com/docs/devtools/performance/selector-stats) s visokim proteklim vremenom i visokim postotkom sporog puta. Jednostavniji selektori, manji broj selektora, manji DOM i plići DOM smanjit će troškove uparivanja."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Proteklo vrijeme"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nisu pronađeni podaci o selektoru CSS-a. Statistički podaci o selektoru CSS-a moraju se omogućiti u postavkama ploče izvedbe."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Pokušaji podudaranja"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Troškovi selektora CSS-a"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Najčešći selektori"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Ukupno"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON><PERSON>me glav<PERSON> niti"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "T<PERSON>ća strana"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Veličina prijenosa"}, "models/trace/insights/ThirdParties.ts | description": {"message": "<PERSON><PERSON>d treće strane može znatno utjecati na izvedbu učitavanja. [Smanjite i odgodite učitavanje koda treće strane](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) da biste prioritet dali sadržaju svoje stranice."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON> pronađena nijedna treća strana"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> strane"}, "models/trace/insights/Viewport.ts | description": {"message": "Interakcije dodirom mogu [kasniti do 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ako vidljivi dio nije optimiziran za mobilne uređaje."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Odgoda za dodir na mobilnom uređaju"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimizirajte vidljivi dio za mobilne uređaje"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Samo stranice koje su učitane putem GET zahtjeva ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Jedino se stranice sa šifrom statusa 2XX mogu predmemorirati."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome je otkrio pokušaj izvršavanja JavaScripta dok je u predmemoriji."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Stranice koje su zatražile AppBanner trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Oznake onemogućuju predmemoriranje cijele stranice. Otvorite chrome://flags/#back-forward-cache da biste ga lokalno omogućili na ovom uređaju."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Naredbeni redak onemogućio je predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog nedovoljno memorije."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Ovlaštenje ne podržava predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Predučitavač je onemogućio predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Stranica se ne može predmemorirati jer ima instancu BroadcastChannel s registriranim slušateljima."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Stranice sa zaglavljem cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Predmemorija je namjerno izbrisana."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Stranica je izbačena iz predmemorije kako bi se omogućilo predmemoriranje druge stranice."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Stranice koje sadrže dodatke trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Stranice koje koriste FileChooser API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Stranice koje koriste File System Access API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Stranice koje koriste otpremnik za medijski uređaj ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Tijekom napuštanja stranice u tijeku je bila reprodukcija Media Playera."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Stranice koje koriste MediaSession API i postave stanje reprodukcije ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Stranice koje koriste MediaSession API i postave rukovatelje radnjom ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog čitača zaslona."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Stranice koje koriste SecurityHandler ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Stranice koje koriste Serial API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Stranice koje koriste WebAuthetication API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Stranice koje koriste WebBluetooth API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Stranice koje koriste WebUSB API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Predmemoriranje cijele stranice onemogućeno je jer su kolačići onemogućeni na stranici koja upotrebljava Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Stranice koje koriste predviđeni alat ili radni zadatak trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokument nije završio s učitavanjem prije napuštanja stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Tijekom napuštanja stranice bio je prisutan natpis aplikacije."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Tijekom napuštanja stranice bio je prisutan Chromeov Upravitelj zaporki."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Tijekom napuštanja stranice u tijeku je bilo sažimanje DOM-a."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Tijekom napuštanja stranice bio je prisutan preglednik za sažimanje DOM-a."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog proširenja koja koriste API za slanje poruka."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Proširenja s dugotrajnijom vezom trebaju zatvoriti vezu prije pristupanja predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Proširenja s dugotrajnijom vezom pokušala su poslati poruke okvirima u predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog proširenja."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Modalni dijalog, kao <PERSON>to je ponovno slanje obrasca ili dijalog sa zaporkom za http prikazao se za stranicu tijekom napuštanja stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Tijekom napuštanja stranice prikazala se offline stranica."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Tijekom napuštanja stranice bila je prisutna traka za intervenciju zbog nedovoljno memorije."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Tijekom napuštanja stranice postojali su zahtjevi za dopuštenja."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Tijekom napuštanja stranice bio je prisutan alat za blokiranje skočnih prozora."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Pojedinosti o sigurnom pregledavanju prikazale su se tijekom napuštanja stranice."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON><PERSON>rno pregledavanje ovu je stranicu smatralo zlonamjernom i blokiralo je skočne prozore."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Uslužni alat aktiviran je dok je u tijeku bilo predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog pogreške dokumenta."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Stranice koje upotrebljavaju FencedFrames ne mogu se pohraniti u predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Stranica je izbačena iz predmemorije kako bi se omogućilo predmemoriranje druge stranice."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Stranice koje su odobrile pristup streamanju medijskih sadržaja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Stranice koje imaju određene vrste ugrađenog sadr<PERSON>aja (npr. PDF-ovi) trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Stranice koje koriste IdleManager trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Stranice koje imaju otvorenu IndexedDB vezu trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog IndexedDB događaja."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Ko<PERSON>šteni su API-ji koji ne ispunjavaju kriterije."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Stranice u koje je JavaScript ugrađen putem proširenja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Stranice u koje je StyleSheet ugrađen putem proširenja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Interna pogreška."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Predmemoriranje cijele stranice onemogućeno je jer je neki zahtjev za JavaScript mrežu primio resurs sa zaglavljem Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog keepalive zahtjeva."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Stranice koje koriste zaključavanje tipkovnice trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Stranica nije završila s učitavanjem prije napuštanja stranice."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> glavni resurs ima cache-control:no-cache ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji glavni izvor ima cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Kretanje je otkazano prije nego što se stranica mogla vratiti iz predmemoriranja cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Stranica je izbačena iz predmemorije jer je aktivna mrežna veza primila previše podataka. Chrome ograničava količinu podataka koje stranica može primiti tijekom predmemoriranja."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stranice koje imaju dohvaćanje ili XHR u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Stranica je izbačena iz predmemoriranja cijele stranice jer je aktivni zahtjev za mrežu uključivao preusmjeravanje."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Stranica je izbačena iz predmemorije jer je mrežna veza bila predugo otvorena. Chrome ograničava količinu vremena tijekom kojeg stranica može primiti podatke za vrijeme predmemoriranja."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Stranice koje nemaju važeći odgovor ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Kretanje se dogodilo u okviru koji nije glavni."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Stranice s indeksiranim DB transakcijama trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Stranice sa zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Stranice sa zahtjevom za dohvaćanje mreže u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Stranice sa zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Stranice s XHR zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Stranice koje koriste PaymentManager trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Stranice koje koriste sliku u slici trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Stranice koje prikazuju korisničko sučelje za ispisivanje trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Stranica je otvorena pomoću metode window.open(), a druga kartica navodi referencu na nju ili je stranica otvorila prozor."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Proces generiranja za stranicu u predmemoriranju cijele stranice se srušio."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Postupak generiranja za stranicu u predmemoriranju cijele stranice je prekinut."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Stranice koje su zatražile dopuštenja za snimanje zvučnih zapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Stranice koje su zatražile dopuštenja za senzor trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Stranice koje su zatražile pozadinsku sinkronizaciju ili dopuštenja za dohvaćanje trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Stranice koje su zatražile dopuštenja za MIDI trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Stranice koje su zatražile dopuštenja za obavijesti trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Stranice koje su zatražite pristup pohrani trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Stranice koje su zatražile dopuštenja za snimanje videozapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "<PERSON>ino se stranice čija je shema URL-a HTTP/HTTPS mogu predmemorirati."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Stranicu je preuzeo uslužni alat dok je u predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Uslužni alat pokušao je stranici kod koje je u tijeku predmemoriranje cijele stranice poslati MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Otkazana je registracija za ServiceWorker dok je u tijeku bilo predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Stranica je izbačena iz predmemoriranja cijele stranice zbog aktivacije uslužnog alata."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome se ponovno pokrenuo i izbrisao unose predmemoriranja cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Stranice koje koriste SharedWorker trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Stranice koje koriste SpeechRecognizer trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Stranice koje koriste SpeechSynthesis trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Iframe na stranici pokrenuo je kretanje koje nije dovršeno."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-cache ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Stranica je premašila maksimalno vrijeme u predmemoriranju cijele stranice i istekla je."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Isteklo je pristupanje predmemoriranju cijele stranice (vjerojatno zbog rukovatelja sakrivanjem stranice koji su bili dugo pokrenuti)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Stranica ima unload rukovatelj u glavnom okviru."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Stranica ima unload rukovatelj u podokviru."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Preglednik je promijenio zaglavlje nadjačavanja korisničkog agenta."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Stranice koje su odobrile pristup snimanju videozapisa ili audiozapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Stranice koje koriste WebDatabase trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Stranice koje koriste WebHID trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Stranice koje koriste WebLocks trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Stranice koje koriste WebNfc trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Stranice koje koriste WebOTPService trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Stranice s WebRTC-om ne mogu pristupiti predmemoriranju cijele stranice"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Predmemoriranje cijele stranice onemogućeno je jer je upotrijebljen WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Stranice koje koriste WebShare trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Stranice s WebSocketom ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Predmemoriranje cijele stranice onemogućeno je jer je upotrijebljen WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Stranice s WebTransportom ne mogu pristupiti predmemoriranju cijele stranice."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Predmemoriranje cijele stranice onemogućeno je jer je upotrijebljen WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Stranice koje koriste WebXR trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}}