{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Prieigos teisė nebus įtraukta naudojant pakaitos simbolį (*) vykdant CORS funkcijos „Access-Control-Allow-Headers“ apdorojimą."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "<PERSON><PERSON> numatytąjį „Cast“ integravimą turėtų būti naudoja<PERSON> „disableRemotePlayback“ atributas vietoj „-internal-media-controls-overlay-cast-button“ parinkiklio."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS išvaizdos vertė „slider-vertical“ nėra standartizuota ir bus pašalinta."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Šaltinių užklausos, kurių URL buvo pašalintų matomų tarpų „\\(n|r|t)“ simbolių ir simbolių „mažiau ne“ („<“), yra u<PERSON><PERSON>. Pašalinkite naujas eilutes ir užkoduokite simbolius „mažiau nei“ tokiose vietose kaip elementų atributų vertės, kad įkeltumėte šiu<PERSON>ius."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Funkcijos „chrome.loadTimes()“ teikimas nutrauktas; vietoj jos naudokite standartizuotą API: „2 naršymo laiko <PERSON>“."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Funkcijos „chrome.loadTimes()“ teikimas nutrauktas; vietoj jos naudokite standartizuotą API: „Vizualizavimo laiko žym<PERSON>“."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Funkcijos „chrome.loadTimes()“ teikimas nutrauktas; vietoj jos naudokite standartizuotą API: „nextHopProtocol“ skiltyje „2 naršymo laiko žymės“."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra „\\(0|r|n)“ simbolis, bus atmesti, o ne sutrumpinti."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "To paties šaltinio politikos apribojimų atlaisvinimo nustatant „document.domain“ funkcijos teikimas nutrauktas ir ji bus išjungta pagal numatytuosius nustatymus. Šis teikimo nutraukimo perspėjimas skirtas skirtingų šaltinių prieigai, kuri buvo įgalinta nustačius „document.domain“."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Funkcijos „window.alert“ su<PERSON><PERSON><PERSON><PERSON> i<PERSON> skirtingų šaltinių „iframe“ teikimas nutrauktas ir ateityje ji bus pašalinta."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Funkcijos „window.confirm“ suak<PERSON><PERSON><PERSON> i<PERSON> skirtingų šaltinių „iframe“ teikimas nutrauktas ir ateityje ji bus pašalinta."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Duomenų palaikymas: „SVGUseElement“ URL nebenaudojamas ir ateityje bus pašalintas."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "„getCurrentPosition()“ ir „watchPosition()“ nebeveikia nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Funkcijų „getCurrentPosition()“ ir „watchPosition()“ teikimas nutrauktas nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "„getUserMedia()“ nebeveikia nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<PERSON><PERSON> <h1> ž<PERSON><PERSON> <article>, <aside>, <nav> arba <section>, kurioje nėra nurodytas šrifto dydis. Netrukus šio antraštės teksto dydis šioje naršyklėje pasikeis. Jei reikia daugiau informacijos, žr. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "„RTCPeerConnectionIceErrorEvent.hostCandidate“ teikimas nutrauktas. Vietoj jos naudokite funkciją „RTCPeerConnectionIceErrorEvent.address“ arba „RTCPeerConnectionIceErrorEvent.port“."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Šio navigator.credentials.get() užklausos skaitmeninių prisijungimo duomenų formato teikimas nutrauktas. Atnaujinkite skambutį, kad būtų naudojamas naujas formatas."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Prekybininko šaltinio ir atsitiktinių duomenų iš „canmakepayment“ pagalbinio „JavaScript“ failo įvykio teikimas nutrauktas ir jie bus pašalinti: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "S<PERSON><PERSON><PERSON> pateikė papildomo šaltinio užklausą iš tinklo, kurį ji galėtų pasiekti tik dėl savo naudotojų privilegijuotosios tinklo pozicijos. Šiose užklausose parodomi ne vieši įrenginiai ir serveriai internete, todėl padidėja užklausos iš trečiosios šalies svetainės klastojimo (angl. „cross-site request forgery“, CSRF) atakos ir (arba) informacijos nutekėjimo rizika. Siekdama sumažinti šią riziką, „Chrome“ nutraukia užklausų ne viešiems papildomiems šaltiniams teikimą, kai jos inicijuojamos iš nesaugios aplinkos, ir pradės jas blokuoti."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "„InterestGroups“ laukas „dailyUpdateUrl“, perdu<PERSON>s „joinAdInterestGroup()“, pervardytas į „updateUrl“, kad tiksliau atspindėtų jo elgseną."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "„Intl.v8BreakIterator“ teikimas nutrauktas. Vietoj jos naudokite funkciją „Intl.Segmenter“."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS kalbos negalima įkelti iš „file:“ URL, nebent jie baigiasi „.css“ failo plėtiniu."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "„SourceBuffer.abort()“ naudojimas siekiant nutraukti „remove()“ nesinchronizuoto diapazono pašalinimą nutrauktas dėl specifikacijos pakeitimo. Ateityje palaikymas bus pašalintas. Turėtumėte klausytis updateend įvykio. „abort()“ skirtas naudoti tik norint nutraukti nesinchronizuotos medijos pridėjimą arba iš naujo nustatyti analizavimo įrankio būseną."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "„MediaSource.duration“ nustatymas žemiau nei bet kurio į buferį įrašyto užkoduoto rėmelio aukščiausia pristatymo laiko žymė nutrauktas dėl specifikacijos pakeitimo. Sutrumpintos į buferį įrašytos medijos numatomo pašalinimo palaikymas ateityje bus pašalintas. Vietoj to turėtumėte atlikti aiškų „remove(newDuration, oldDuration)“ visuose „sourceBuffers“, kur „newDuration < oldDuration“."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Žiniatinklio MIDI prašys naudo<PERSON>, net jei sistemos išskirtinis pranešimas nenurodytas funkcijoje „MIDIOptions“."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Pranešimų API nebegalima naudoti iš nesaugių šaltinių. Turėtumėte apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Pranešimų API leidimo užklausų nebegalima teikti iš skirtingų šaltinių „iframe“. Vietoj to turėtumėte apsvarstyti galimybę teikti leidimo užklausą iš aukščiausio lygio rėmelio arba atidaryti naują langą."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Parinkties „imageOrientation: 'none'“, es<PERSON><PERSON><PERSON> „createImageBitmap“, teikimas nutrauktas. Vietoj jos naudokite „createImageBitmap“ su parinktimi „{imageOrientation: 'from-image'}“."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Jūsų partneris derasi <PERSON> (D)TLS versijos. Susisiekite su partneriu, kad tai sutvarkytumėte."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON><PERSON> „img“, „video“ ir „canvas“ n<PERSON><PERSON><PERSON><PERSON> „overflow: visible“, vaizdo turinys gali būti sukurtas už elemento ribų. Žr. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "„paymentManager.instruments“ teikimas nutrauktas. Mokėjimų doroklėms naudokite tiesioginį diegimą."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "„PaymentRequest“ iškvietimas apėjo turinio apsaugos politikos (CSP) direktyvą „connect-src“. Šis apėjimas nebenaudojamas. Pridėkite mokėjimo metodo identifikatorių iš „PaymentRequest“ API (lauke „supportedMethods“) prie CSP direktyvos „connect-src“."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "„StorageType.persistent“ teikimas nutrauktas. Vietoj to naudokite standartizuotą „navigator.storage“."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Elementas „<source src>“ su „<picture>“ aukštesniuoju elementu netinkamas, todėl jo nepaisoma. Vietoj jo naudokite „<source srcset>“."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "„webkitCancelAnimationFrame“ priklauso nuo konkretaus paslaugų teikėjo. Vietoj jo naudokite įprastą metodą „cancelAnimationFrame“."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "„webkitRequestAnimationFrame“ priklauso nuo konkretaus paslaugų teikėjo. Vietoj jo naudokite įprastą metodą „requestAnimationFrame“."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "„HTMLVideoElement.webkitDisplayingFullscreen“ nebeteikiama. Vietoj jos naudokite „Document.fullscreenElement“."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "„HTMLVideoElement.webkitEnterFullScreen()“ nebeteikiama. Vietoj jos naudokite „Element.requestFullscreen()“."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "„HTMLVideoElement.webkitEnterFullscreen()“ nebeteikiama. Vietoj jos naudokite „Element.requestFullscreen()“."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "„HTMLVideoElement.webkitExitFullScreen()“ nebeteikiama. Vietoj jos naudokite „Document.exitFullscreen()“."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "„HTMLVideoElement.webkitExitFullscreen()“ nebeteikiama. Vietoj jos naudokite „Document.exitFullscreen()“."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "„HTMLVideoElement.webkitSupportsFullscreen“ nebeteikiama. Vietoj jos naudokite „Document.fullscreenEnabled“."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Nutrauksime API „chrome.privacy.websites.privacySandboxEnabled“ te<PERSON><PERSON><PERSON>, bet ji liks a<PERSON>, kad b<PERSON><PERSON><PERSON> užtikrintas atgalinis suder<PERSON>, kol bus išleista M113 versija. Dabar naudokite „chrome.privacy.websites.topicsEnabled“, „chrome.privacy.websites.fledgeEnabled“ ir „chrome.privacy.websites.adMeasurementEnabled“. Žr. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Apribojimas „DtlsSrtpKeyAgreement“ pašalintas. Nurodėte šio apriboji<PERSON> „false“ vertę, kuri interpretuojama kaip bandymas naudoti pašalint<PERSON> „SDES key negotiation“ metodą. Ši funkcija pašalinta; vietoj jos naudokite paslaugą, kuri palaiko „DTLS key negotiation“."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Apribojimas „DtlsSrtpKeyAgreement“ pašalintas. Nurodėte šio apribojimo „true“ vertę, kuri neturėjo jokio poveikio, bet galite pašalinti šį apribojimą, kad būtų tvarkingiau."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Atskambinimo parinktis „getStats()“ nebeteikiama ir bus pašalinta. Vietoj jos naudokite specifikaciją atitinkančią parinktį „getStats()“."}, "generated/Deprecation.ts | RangeExpand": {"message": "„Range.expand()“ nebeteikiama. Vietoj jos naudokite „Selection.modify()“."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Papildomų šaltinių užklausos, kurių URL yra įterptų prisijungimo duomenų (pvz., „**********************/“), yra u<PERSON><PERSON>."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "„rtcpMuxPolicy“ parinkties teikimas nutrauktas ir ji bus pa<PERSON>lint<PERSON>."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "Funkcijai „SharedArrayBuffer“ reikalinga apsauga nuo subjektų iš kitų domenų. Jei reikia išsamesnės informacijos, žr. https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Funkcijos „speechSynthesis.speak()“ be naudo<PERSON>jo suaktyvinimo te<PERSON> nutrauktas ir ji bus pašalinta."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Iškėlimo įvykių apdorojimo priemonių teikimas nutrauktas ir jos bus pašalintos."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Plėtiniams reikia pasirinkti apsaugą nuo subjektų iš kitų domenų, norint toliau naudoti „SharedArrayBuffer“. Žr. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "„GPUAdapter“ atributo „isFallbackAdapter“ teikimas nutrauk<PERSON>; vietoj jo naudokite „GPUAdapterInfo“ atributą „isFallbackAdapter“."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 nepalaikomas atsako JSON naudojant funkciją „XMLHttpRequest“"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinchroninės funkcijos „XMLHttpRequest“ pagrindinėje grupėje teikimas nutrauktas dėl jos žalingo poveikio galutinio naudotojo funkcijoms. Jei reikia daugiau pagalbos, žr. https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animacija"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Išdėstymo poslinkiai įvyksta, kai elementai juda be naudotojo sąveikos. [Išnagrinėkite išdėstymo poslinkių priežastis](https://web.dev/articles/optimize-cls), pvz., elementų pridėjimą, pa<PERSON>linimą arba šriftų keitimą, kai puslapis įkeliamas."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Šrifto užklausa"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Įterptas „iframe“"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Išdėstymo poslinkio grupė {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nepavyko aptikti jokių išdėstymo poslinkio priežasčių"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Nėra išdėstymo poslinkių"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Išdėstymo poslinkio priežastys"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> išdėstymo poslinkio priežastys"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Blogiausia grupė"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Blogiausia išdėstymo poslinkio grupė"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Talpyklos TTL"}, "models/trace/insights/Cache.ts | description": {"message": "Jei talpykla galios ilgiau, greičiau sulauksite pakartotinių apsilankymų puslapyje. [Sužinokite daugiau](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nėra užklausų su neefektyviomis talpyklos politikos nuostatomis"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} kt."}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Užklausa"}, "models/trace/insights/Cache.ts | title": {"message": "Naudoti efektyvius talpyklos galioji<PERSON> terminus"}, "models/trace/insights/DOMSize.ts | description": {"message": "D<PERSON>l didelio DOM elementų skaičiaus gali būti ilgiau skaičiuojami stiliai ir atliekami išdėstymo perskaičiavimai, o tai daro įtaką puslapio interaktyvumui. Dėl didelio DOM elementų skaičiaus bus sunaudojama daugiau atminties. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išvengti per didelio DOM dydžio](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elementas"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Daugiausia antrinių elementų"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "D<PERSON> gylis"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizuoti DOM dydį"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "<PERSON><PERSON> viso element<PERSON>"}, "models/trace/insights/DOMSize.ts | value": {"message": "Vertė"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Pirmoji tinklo užklausa yra svarbiausia.  Sumažinkite delsą vengdami peradresavimų, užtikrindami spartų serverio atsaką ir įgalindami teksto glaudinimą."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Buvo peradresavimų (peradresavimų: {PH1}, + {PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON> at<PERSON> l<PERSON>tai (stebėta {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Glaudinimas <PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Išvengiama peradresavimo"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON> gre<PERSON>i at<PERSON> (stebėta {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Taikomas te<PERSON> glaudin<PERSON>"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON> atsako laikas"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Dokumento užklausos delsa"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Nesuglaudintas atsisiuntimas"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Pasik<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON> did<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „JavaScript“ modulius iš grupių, kad tinklo veikla be reikalo nenaudotų baitų."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Tiksli „JavaScript“ kopija"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Apsvarstykite galimybę nustatyti [font-display](https://developer.chrome.com/blog/font-display) kaip swap arba optional, kad tekstas būtų nuolat matomas. swap galima toliau optimizuoti, kad būtų sumažinti išdėstymo poslinkiai naudojant [šrifto metrikos nepaisym<PERSON>](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(an<PERSON><PERSON><PERSON>)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Daugelis API, paprastai skaitant išd<PERSON><PERSON><PERSON> geometriją, ver<PERSON>ia pateikimo variklį pristabdyti scenarija<PERSON> v<PERSON>, kad būt<PERSON> apskaičiuotas stilius ir išdėstymas. Sužinokite daugiau apie [priverstinį perskaičiavimą](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) ir jo ma<PERSON>."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Populiariausias funkcijos k<PERSON>"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Bendras perskaičiavimui skirtas laikas"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[nepris<PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Sutrumpinus vaizdų atsisiuntimo laiką, galima pagerinti puslapio ir DTŽ įkėlimo laiką. [Sužinokite daugiau apie vaizdo dydžio optimizavimą](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (numat. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nėra vaizdų, kuriuos būtų galima optimizuoti"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "<PERSON>ailo d<PERSON> optimi<PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} kt."}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Vaizdų teikimo patobulinimas"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Padidinus vaizdo glaudin<PERSON> k<PERSON>, gali sumažėti šio vaizdo atsisiuntimo dydis."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Naudojant šiuolaikinį vaizdo formatą („WebP“, AVIF) arba padidinus vaizdo glau<PERSON>, <PERSON>io vaizdo atsisiuntimo dydis gali būti <PERSON>."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "<PERSON><PERSON> vaizdo failas yra did<PERSON>, nei re<PERSON> ({PH1}), atsižvelgiant į jo rodomus matmen<PERSON> ({PH2}). Naudokite interaktyvius vaiz<PERSON>, kad sumažintumėte vaizdo atsisiuntimo dydį."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Naudojant vaizdo įrašų formatus vietoj GIF gali būti sumažintas animuoto turinio atsisiuntimo dydis."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Pradėkite tyrimą nuo ilgiausios fazės. [Galite sumažinti vėlavimą](https://web.dev/articles/optimize-inp#optimize_interactions). Kad apdorojimo trukm<PERSON> būtų trumpesnė, [optimizuokite pagrindinės grup<PERSON> m<PERSON>](https://web.dev/articles/optimize-long-tasks), dažnai JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Trukmė"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Įvesties delsa"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Neaptikta jokių sąveikų"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fazė"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Pristat<PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "SKŽ pagal etapą"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizuokite DTŽ, kad DTŽ vaizdas būtų [aptinkamas](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) iš karto iš HTML ir [išvengtumėte asinchroninio įkėlimo](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "„fetchpriority=high“ pritaikyta"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "<PERSON><PERSON> b<PERSON><PERSON> „fetchpriority=high“"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "asinchroninis įkėlimas netaikomas"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "DTŽ vaizdas įkeltas {PH1} po anksčiausio pradžios taško."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "DTŽ neaptikta"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nerasta jokių DTŽ išteklių, nes DTŽ nėra vaizdas"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Užklausą galima aptikti pradiniame dokumente"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "DTŽ užklausos aptikimas"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Kiekvienai [fazei taikomos konkrečios tobulinimo strategijos](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idealiu atveju didžiąją DTŽ laiko dalį turėtų sudaryti šaltinių įkėlimas, o ne vėlavimai."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Trukmė"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Elemento pateikimo delsa"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75-ojo procent<PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "DTŽ neaptikta"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fazė"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Šaltinio įkėlimo delsa"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Šaltinio įkėlimo trukmė"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Laikas iki pirmojo baito"}, "models/trace/insights/LCPPhases.ts | title": {"message": "DTŽ pagal etapą"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Iššvaistytų baitų"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Naudojant kodus ir transformacijas senesnėse naršyklėse galima naudoti naujas „JavaScript“ funkcijas. Tačiau dauguma jų nereikalingi modernioms naršyklėms. Apsvarstykite galimybę pakeisti „JavaScript“ kompiliavimo procesą, kad nebūtų transpiliuojamos [pagrindinės](https://web.dev/articles/baseline-and-polyfills) funkcijos, nebent žinote, kad turite palaikyti senesnes naršykles. [Sužinokite, kodėl dauguma svetainių gali diegti ES6+ kodą be transpiliavimo](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Pasenusi „JavaScript“"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "Naudodami HTTP/2 ir HTTP/3 gaunate daugiau prana<PERSON>umų, nei naudodami HTTP/1.1, pvz., multipleksavimą. [Sužinokite daugiau apie šiuolaikinio HTTP naudojimą](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "<PERSON><PERSON><PERSON>, kuriose naudojamas HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokolas"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Užklausa"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Modernus HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Užklausa"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Numatomas trumpesnis DTŽ"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Nepanaudotas išankstinis su<PERSON>. Pat<PERSON><PERSON><PERSON><PERSON>, ar atributas „crossorigin“ naudojamas tin<PERSON>."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Venkite svarbiausių užklausų grandinių](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains), sutrumpindami grandines, sumažindami atsisiunčiamų šaltinių dydį arba atidėdami nebūtinų šaltinių atsisiuntimą, kad puslapio įkėlimas būtų sklandesnis."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Pridėkite [i<PERSON><PERSON><PERSON><PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) nurodymų prie svarbiausių šaltinių, bet pabandykite naudoti daugiau nei keturis."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "<PERSON>šankstinis kandi<PERSON> susiejima<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Didžiausia svarbaus kelio del<PERSON>:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nėra pateikimo užduočių, paveiktų tinklo priklausomybių"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nėra jokių papildomų šaltinių, kurie būt<PERSON> tinkami išankstiniam susiejimui"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "jokie šaltiniai nebuvo iš anksto susieti"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[I<PERSON><PERSON>tin<PERSON> susieji<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) nurodymai padeda naršyklei užmegzti ryšį anksčiau įkeliant puslapį, taupant laiką pateiki<PERSON> pirm<PERSON>j<PERSON> to šal<PERSON><PERSON>ž<PERSON>. Toliau pateikiami šaltiniai, prie kurių puslapis iš anksto prisijungė."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "<PERSON><PERSON><PERSON>jų šaltinių medis"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON><PERSON> daug<PERSON> nei keturios „preconnect“ sąsajos. <PERSON><PERSON> turi būti naudojamos taupiai ir susiejant tik su svarbiausiais šaltin<PERSON>is."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Nepanaudotas išankstinis susiejimas. Naudokite tik „preconnect“ <PERSON><PERSON><PERSON><PERSON><PERSON>, kurių užklausas puslapis greičiausiai pateiks."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Venkite svarbiausių užklausų grandinių, sutrumpindami grandines, sumažindami atsisiunčiamų šaltinių dydį arba atidėdami nebūtinų šaltinių atsisiuntimą, kad puslapio įkėlimas būtų sklandesnis."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Užklausos blo<PERSON>oja pradinį pu<PERSON><PERSON> pat<PERSON>, d<PERSON><PERSON> kurio DTŽ gali vėluoti. [Atidėliojant arba įterpiant](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) <PERSON><PERSON> tinklo užklausas galima pašalinti iš kritinio kelio."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Trukmė"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nėra šios navigacijos pateikimo blokavimo užklausų"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Užklausa"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Pateikimo blokavimo užklausos"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Jei stiliaus perska<PERSON>čiavimo mokesčia<PERSON> i<PERSON><PERSON> dideli, juos galima sumažinti optimizavus parinkiklius. [Optimizuokite parinkiklius](https://developer.chrome.com/docs/devtools/performance/selector-stats), kai pra<PERSON>j<PERSON>s laikas ir lėto kelio proc. yra dideli. Paprastesni parinkikliai, mažia<PERSON> parink<PERSON>, mažesnis DOM ir seklesnis DOM sumažins atitikimo mokesčius."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nerasta CSS kalbos parinkiklio duomenų. CSS kalbos parinkiklio statistika turi būti įgalinta našumo skydelio nustatymuose."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Bandymai rasti atitikčių"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Atitikči<PERSON> s<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS kalbos parinkiklio m<PERSON>i"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Populiariausi <PERSON>i"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "<PERSON><PERSON> viso"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Treč<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Failų perkėlimo dyd<PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Trečiosios šalies kodas gali smarkiai paveikti įkėlimo našumą. [Sumažinkite ir atidėkite trečiosios šalies kodo įkėlimą](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), kad būtų nustatytas puslapio turinio prioritetas."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Nerasta trečiųjų šalių"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Trečios<PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "Palietimo sąveikos gali [vėluoti iki 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/), jei <PERSON><PERSON> sritis neoptimizuota mobiliesiems."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Palietimo delsa mobiliesiems"}, "models/trace/insights/Viewport.ts | title": {"message": "Peržiūros srities optimizavimas mobiliesiems"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON>, įkeltiems naudojant GET <PERSON>, galima taik<PERSON>i il<PERSON>ai<PERSON>o viso puslapio saugojimo talpykloje funkciją."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Talpykloje galima saugoti tik puslapius, kuri<PERSON> būsenos kodas yra 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "„Chrome“ aptiko bandymą vykdyti „JavaScript“, kol saugoma talpykloje."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> „AppBanner“ <PERSON><PERSON><PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta taikant žymas. Jei norite įgalinti ją vietiniu mastu šiame įrenginyje, apsilankykite adresu chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta taikant komandos eilutę."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl nepakankamos atminties."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Perduodant il<PERSON><PERSON><PERSON>o viso puslapio saugojimo talpykloje funkcija nepalaikoma."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl išankstinio pateikimo priemonės."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Puslapio negalima saugoti saugykloje, nes jame yra „BroadcastChannel“ objektas su registruotomis apdorojimo priemonėmis."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Puslapiams su antrašte „cache-control:no-store“ negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Talpykla tyčia išvalyta."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, kad būt<PERSON> leidžiama talpykloje saugoti kitą puslapį."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra papildinių, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Neapib<PERSON><PERSON><PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudoja<PERSON> „FileChooser“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose naudojama failų sistemos prieigos API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojamas „Media Device Dispatcher“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON> le<PERSON> buvo leidž<PERSON>a prieš i<PERSON> iš jos."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „MediaSession“ API ir nustatyta atkūrimo būsena, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „MediaSession“ API ir nustatytos veiksmų doroklės, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl ekrano skaitytuvo."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „SecurityHandler“, negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose naudojama serijos API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebAuthetication“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebBluetooth“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebUSB“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes slapukai išjungti puslapyje, k<PERSON><PERSON> naudo<PERSON> „Cache-Control: no-store“."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojamas <PERSON>asis scenarijus arba scenarija<PERSON> da<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokumentas nebuvo visiškai įkeltas prieš išeinant iš jo."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Na<PERSON>šant buvo rodoma programų reklamjuostė."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> „Chrome“ slaptažodžių tvarkytuvė."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON> DOM distiliavimas."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON><PERSON> „DOM Distiller Viewer“."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl plėtinių, kuriems naudojama susirašinėjimo API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Ilgalaikio ryšio plėtiniai turi nutraukti ryšį prieš ilgalaikį viso puslapio iš<PERSON>ugojimą talpykloje."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Ilgalaikio ryšio plėtiniai bandė siųsti pranešimus į ilgalaikio viso puslapio saugojimo talpyklo<PERSON> kadrus."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl plėtinių."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodomas puslapio modalinio dialogo langas, pvz., pakartotinio formos pateikimo arba HTTP slaptažodžio dialogo langas."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodomas neprisi<PERSON>gus pasiekiamas pusla<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rod<PERSON> „Out-Of-Memory Intervention“ juosta."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo pateikta leidimo užklausų."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON> su<PERSON> i<PERSON>š<PERSON>nčiųjų langų blokavimo priemonė."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodoma saugaus naršymo išsami informacija."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON><PERSON>us naršymo įrankis nusprend<PERSON>, kad <PERSON><PERSON>o puslapiu p<PERSON><PERSON><PERSON><PERSON><PERSON>, ir u<PERSON><PERSON><PERSON><PERSON><PERSON> iššokan<PERSON><PERSON><PERSON><PERSON> langus."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Paslaugos scenarijus buvo suaktyvintas, kol puslapiui buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Ilgalaikis viso puslapio sa<PERSON>jimas talpykloje išjungtas dėl dokumento klaidos."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „FencedFrames“, negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpykloje."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, kad būt<PERSON> leidžiama talpykloje saugoti kitą puslapį."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON>uose suteikta medijos srautinio perdavimo prieiga, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra tam tikro tipo įterpto turinio (pvz., PDF), <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugoji<PERSON> talpykloje."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „IdleManager“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>se veikia atviras „IndexedDB“ ry<PERSON><PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Ilgalaikis viso puslapio saug<PERSON>jimas talpykloje išjungtas dėl įvykio „IndexedDB“."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Buvo naudojamos netinkamos API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> įterpta „JavaScript“ naudo<PERSON><PERSON> p<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> įterpta „StyleSheet“ naudo<PERSON><PERSON> p<PERSON><PERSON><PERSON>, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes kai kurios „JavaScript“ tinklo užklausos gavo šaltinį su antrašte „Cache-Control: no-store“."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl palaikymo užklausos."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON> funk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Puslapis nebuvo visiškai įkeltas prieš išeinant iš jo."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> pagrindiniame šaltinyje yra „cache-control:no-cache“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> pagrindiniame šaltinyje yra „cache-control:no-store“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> buvo at<PERSON><PERSON><PERSON>, p<PERSON><PERSON> atkurti puslapį, kuriam taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, nes aktyvus tinklo r<PERSON>š<PERSON> gavo per daug duomenų. „Chrome“ riboja duomenų, kuriuos gali gauti talpykloje sa<PERSON>, kiekį."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra <PERSON><PERSON><PERSON> „fetch()“ arba XHR, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Puslapiui nebetaikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija, nes į aktyvaus tinklo užklausą buvo įtrauktas peradresavimas."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Puslapis buvo pa<PERSON><PERSON><PERSON> i<PERSON>, nes tinklo <PERSON> per ilgai buvo atviras. „Chrome“ riboja laikotarpį, per kurį talpykloje saugomas puslapis gali gauti duomenis."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> nėra tin<PERSON> at<PERSON>, negalima ta<PERSON>i ilgalaikio viso puslapio saugojimo talpyk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> veiksma<PERSON> atliktas ne pagrindiniame rėmelyje."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose yra vykdomų indeksuotų DB operacijų, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> pateikta s<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> pateikta s<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> pateikta s<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>se pateikta skrydžio XHR tinklo užklausa, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „PaymentManager“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> v<PERSON> v<PERSON>, <PERSON><PERSON><PERSON> metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> rod<PERSON>imo NS, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Puslapis buvo atidarytas naudojant „window.open()“ ir kitame skirtuke yra jo nuoroda arba puslapyje atidarytas langas."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Pateikėjo procesas pusla<PERSON>, kuriam taikoma il<PERSON>o viso puslapio saugojimo talpyk<PERSON>, užstrigo."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Pateikėjo procesas pusla<PERSON>, kuriam taikoma il<PERSON>o viso puslapio saugojimo talpykloje funkci<PERSON>, buvo nutrauktas."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> g<PERSON>o įrašo fiks<PERSON>mo leid<PERSON> už<PERSON>us<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> juti<PERSON> leidim<PERSON> užklaus<PERSON>, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> sinchronizavimo fone arba gavimo leid<PERSON> už<PERSON>us<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> MIDI leidimų užklausą, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> p<PERSON>š<PERSON>ų leidimų užklausą, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> sa<PERSON> p<PERSON>igo<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> vaiz<PERSON> įrašo fiks<PERSON>mo leidim<PERSON> už<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Talpykloje galima saugoti tik puslapius, kurių URL schema yra HTTP / HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Paslaugos scenarijus pat<PERSON> puslapį, kol jam buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Paslaugos scenarijus bandė si<PERSON>sti pusla<PERSON>, kuriam taikoma il<PERSON>o viso puslapio saugojimo talpykloje funkcija, „MessageEvent“."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Paslaugos scenarijus buvo išregistruotas, kol puslapiui buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Suaktyvinus paslaugos scenarijų puslapiui nebetaikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "„Chrome“ paleido iš naujo ir i<PERSON><PERSON> ilgalaikio viso puslapio saugojimo talpykloje įrašus."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „SharedWorker“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „SpeechRecognizer“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „SpeechSynthesis“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "„Iframe“ puslapyje pradėjo naršymo procesą, kuris nebuvo užbaigtas."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> papildomame šaltinyje yra „cache-control:no-cache“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> papildomame šaltinyje yra „cache-control:no-store“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> pusla<PERSON> la<PERSON>, taikant il<PERSON><PERSON><PERSON><PERSON> viso puslapio saugojimo talpy<PERSON><PERSON>, ir puslapis nebegal<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Baigėsi puslapio skirtasis laikas bandant pritaikyti ilgalaikio viso puslapio saugojimo talpykloje funkciją (gali būti dėl ilgai vykdomų puslapio slėpimo doroklių)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Puslapio pagrindiniame rėmelyje yra iškėlimo doroklė."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Puslapio papildomame rėmelyje yra iškėlimo doroklė."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pakeitė naudotojo priemonės nepaisymo <PERSON>š<PERSON>ę."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> suteikta vaizdo ar garso įrašymo prieiga, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebDatabase“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „WebHID“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „WebLock“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „WebNfc“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebOTPService“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Pus<PERSON>iams su „WebRTC“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes buvo naudojama „WebRTC“."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebShare“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Pus<PERSON>iams su „WebSocket“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes buvo naudojama „WebSocket“."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Pus<PERSON>iams su „WebTransport“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes buvo naudojama „WebTransport“."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebXR“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}}