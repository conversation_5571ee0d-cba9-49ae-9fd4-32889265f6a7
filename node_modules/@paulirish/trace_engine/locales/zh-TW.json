{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers 處理作業中無法使用萬用字元符號 (*) 標示授權。"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "應使用 disableRemotePlayback 屬性 (而非 -internal-media-controls-overlay-cast-button 選取器) 停用預設的 Cast 整合功能。"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS 外觀值 slider-vertical 並未標準化，因此系統會移除這個值。"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "如果資源要求網址中同時包含已移除的 \\(n|r|t) 字元和小於字元 (<)，系統會予以封鎖。請從元素屬性值等位置移除換行符號，並編碼小於字元，以便載入這些資源。"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() 已淘汰，請改用標準化 API：Navigation Timing 2。"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() 已遭淘汰，請改用標準化 API：Paint Timing。"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() 已淘汰，請改用標準化 API：Navigation Timing 2 中的 nextHopProtocol。"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "系統會拒絕包含 \\(0|r|n) 字元的 Cookie，而非截斷。"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "透過設定 document.domain 啟用相同來源政策的功能已淘汰，將於日後予以移除。如果是透過設定 document.domain 啟用跨來源存取功能，系統就會顯示這則淘汰警告訊息。"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "從跨來源 iframe 觸發 window.alert 的功能已淘汰，並將在日後移除。"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "從跨來源 iframe 觸發 window.confirm 的功能已淘汰，並將在之後移除。"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "SVGUseElement 不再支援「data: 網址」，並將於日後移除。"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() 和 watchPosition() 不再適用於不安全的來源。如要使用這個功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "不安全來源上的 getCurrentPosition() 和 watchPosition() 已遭淘汰。如要使用這個功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() 不再適用於不安全的來源。如要使用這個功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "在 <article>、<aside>、<nav> 或 <section> 中，系統發現有一個 <h1> 標記未指定字型大小。不久後，這個標題文字在此瀏覽器中的大小將有變動。詳情請參閱 https://developer.mozilla.org/zh-TW/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1。"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate 已淘汰，請改用 RTCPeerConnectionIceErrorEvent.address 或 RTCPeerConnectionIceErrorEvent.port。"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "數位憑證的 navigator.credentials.get() 要求格式已淘汰，請更新呼叫，以使用新格式。"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "「canmakepayment」Service Worker 事件的服務商家來源和任意資料目前已淘汰，並將在之後移除：topOrigin、paymentRequestOrigin、methodData、modifiers。"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "由於網站的使用者具有權限可存取網路位置，因此網站透過只能存取的網路要求一個子資源。這些要求會讓非公開裝置和伺服器暴露在網際網路上，導致遭到跨網站要求偽造 (CSRF) 攻擊和/或資訊外洩的風險增加。為降低風險，Chrome 會忽略不安全內容向非公開子資源發出的要求，並將開始封鎖這類要求。"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "傳遞到 joinAdInterestGroup() 的 InterestGroups dailyUpdateUrl 欄位已重新命名為 updateUrl，以便更準確地反映其行為。"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator 已淘汰，請改用 Intl.Segmenter。"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "除非 CSS 以 .css 的檔案副檔名結尾，否則無法從 file: 網址載入。"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "由於規格變更，系統已淘汰使用 SourceBuffer.abort() 取消 remove() 的非同步範圍移除作業，日後也將停止支援這項功能。建議你改為監聽 updateend 事件。abort() 的用途僅限於取消非同步媒體附加內容或重設剖析器狀態。"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "由於規格變更，系統已淘汰將 MediaSource.duration 設為低於任何緩衝編碼頁框的最高顯示時間戳記。日後將停止支援對已截斷緩衝媒體的隱性移除作業。請改為在 newDuration < oldDuration 的所有 sourceBuffers 上執行明確 remove(newDuration, oldDuration)。"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "即使在 MIDIOptions 中未指定系統專用 (SysEx) 訊息，Web MIDI 也會要求使用權限。"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "系統不再允許透過不安全的來源使用 Notification API。請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "系統已不再允許透過跨來源 iframe 要求 Notification API。請考慮透過頂層頁框要求權限，或改為開啟新視窗。"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap 中的 imageOrientation: 'none' 選項已淘汰，請改用 createImageBitmap 搭配「{imageOrientation: 'from-image'}」選項。"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "你的合作夥伴正在交涉已過時的傳輸層安全標準 (TLS)/DTLS 版本。請洽詢你的合作夥伴，請對方解決這個問題。"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "如果指定 img、video 和 canvas 標記的「overflow: visible」，可能會導致這些標記產生的視覺內容超出元素邊界。詳情請參閱 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "「paymentManager.instruments」已淘汰。請改用付款處理常式的即時安裝方法。"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "你的「PaymentRequest」呼叫略過了內容安全政策 (CSP)「connect-src」指令，但目前已無法再略過。請將 PaymentRequest API 的付款方式 ID (位於「supportedMethods」欄位) 新增到 CSP「connect-src」指令。"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent 已淘汰，請改用標準化的 navigator.storage。"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "具有 <picture> 父項的 <source src> 無效，因此予以忽略。請改用 <source srcset>。"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame 僅限供應商使用，請改用標準 cancelAnimationFrame。"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame 僅限供應商使用，請改用標準 requestAnimationFrame。"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen 已淘汰，請改用 Document.fullscreenElement。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() 已淘汰，請改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() 已淘汰，請改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() 已淘汰，請改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() 已淘汰，請改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen 已淘汰，請改用 Document.fullscreenEnabled。"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "我們已淘汰這個 API chrome.privacy.websites.privacySandboxEnabled，但為了提供回溯相容性，這個 API 可持續使用到版本 M113。請改用 chrome.privacy.websites.topicsEnabled、chrome.privacy.websites.fledgeEnabled 和 chrome.privacy.websites.adMeasurementEnabled。詳情請參閱 https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "已移除 DtlsSrtpKeyAgreement 限制條件。系統將你為這項限制指定的 false 值解讀為嘗試使用已移除的「SDES key negotiation」方法。這項功能已移除，請改用支援「DTLS key negotiation」的方法。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "已移除 DtlsSrtpKeyAgreement 限制條件。你為這項限制指定的 true 值已不再適用，你可以將這項限制移除以保持畫面整潔。"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "回呼式 getStats() 已淘汰，並將在之後移除。請改用符合規格要求的 getStats()。"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() 已淘汰，請改用 Selection.modify()。"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "如果子資源網址包含內嵌憑證 (例如 **********************/)，系統會予以封鎖。"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy 選項已遭淘汰，日後將予以移除。"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer 會要求跨來源隔離。詳情請參閱 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "無須使用者啟用即可呼叫 speechSynthesis.speak() 的功能已遭淘汰，日後將予以移除。"}, "generated/Deprecation.ts | UnloadHandler": {"message": "卸載事件監聽器已淘汰，並將於日後移除。"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "擴充功能應選擇啟用跨來源隔離功能，以便繼續使用 SharedArrayBuffer。詳情請參閱 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter 的 isFallbackAdapter 屬性已淘汰，請改用 GPUAdapterInfo 的 isFallbackAdapter 屬性。"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest 中的 JSON 回應不支援 UTF-16"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主執行緒上的同步 XMLHttpRequest 會對使用者體驗造成負面影響，因此已淘汰。如需更多說明，請前往 https://xhr.spec.whatwg.org/。"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "動畫"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "版面配置位移是指元素在使用者未進行任何互動時移動。[調查版面配置位移的原因](https://web.dev/articles/optimize-cls)，例如在網頁載入期間新增、移除元素，或變更字型。"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "字型要求"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "插入了 iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "版面配置位移叢集，開始時間：{PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "無法偵測任何版面配置位移主因"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "沒有版面配置位移"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "版面配置位移主因"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "版面配置位移的常見主因"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "最嚴重的叢集"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "最嚴重的版面配置位移叢集"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "快取存留時間"}, "models/trace/insights/Cache.ts | description": {"message": "延長快取生命週期可以加快使用者再次造訪網頁的速度。[瞭解詳情](https://web.dev/uses-long-cache-ttl/)。"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "沒有任何要求採用效率不佳的快取政策"}, "models/trace/insights/Cache.ts | others": {"message": "和另外 {PH1} 個項目"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "要求"}, "models/trace/insights/Cache.ts | title": {"message": "使用有效的快取生命週期"}, "models/trace/insights/DOMSize.ts | description": {"message": "大型 DOM 可能會增加樣式運算和版面配置自動重排的時間，影響網頁的回應速度。大型 DOM 也會增加記憶體用量。[瞭解如何避免 DOM 過大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。"}, "models/trace/insights/DOMSize.ts | element": {"message": "元素"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "子項數量上限"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM 深度"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "統計資料"}, "models/trace/insights/DOMSize.ts | title": {"message": "最佳化 DOM 大小"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "元素總數"}, "models/trace/insights/DOMSize.ts | value": {"message": "值"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "第一個網路要求最為重要。避免重新導向、確保伺服器快速回應，並啟用文字壓縮功能，即可縮短延遲時間。"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "有重新導向 ({PH1} 次重新導向，+{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "伺服器回應緩慢 (觀測結果：{PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "未套用壓縮功能"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "避免重新導向"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "伺服器回應迅速 (觀測結果：{PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "套用文字壓縮"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "重新導向"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "伺服器回應時間"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "文件要求延遲"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "下載內容未壓縮"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "重複的位元組數"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "來源"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "從套件中移除重複的大型 JavaScript 模組，降低網路活動產生的不必要流量。"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "重複的 JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "建議將 [font-display](https://developer.chrome.com/blog/font-display) 設為 swap 或 optional，確保文字始終可見。swap 可透過[字型指標覆寫](https://developer.chrome.com/blog/font-fallbacks)，進一步減少版面配置位移。"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "字型"}, "models/trace/insights/FontDisplay.ts | title": {"message": "字型顯示"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "浪費的時間"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(匿名)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "許多 API (通常是讀取版面配置幾何圖形) 會強制轉譯引擎暫停指令碼執行，以計算樣式和版面配置。進一步瞭解[強制自動重排](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)及其因應措施。"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "堆疊追蹤"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "強制自動重排"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "最耗時的函式呼叫"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "自動重排總時間"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[找不到出處]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "縮短圖片下載時間，可縮減使用者感知的網頁載入時間，並提升 LCP 指標的表現。[進一步瞭解如何最佳化圖片大小](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (預估大小為 {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "沒有可最佳化的圖片"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "最佳化檔案大小"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "和另外 {PH1} 個項目"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "提升圖片傳送效能"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "提高圖片壓縮係數可縮減這張圖片的下載大小。"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "使用新式圖片格式 (WebP、AVIF) 或提高圖片壓縮係數，可縮減這張圖片的下載大小。"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "這個圖片檔的原始尺寸 ({PH1}) 大於規定的顯示尺寸 ({PH2})。請使用回應式圖片，以縮減圖片下載大小。"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "建議使用影片格式 (而非 GIF)，可縮減動畫內容的下載大小。"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "從最長的階段開始調查，[可盡量縮短延遲時間](https://web.dev/articles/optimize-inp#optimize_interactions)。如要縮短處理時間，請[盡量降低主執行緒的成本](https://web.dev/articles/optimize-long-tasks)，通常是針對 JavaScript。"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "時間長度"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "輸入延遲"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "未偵測到任何互動"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "階段"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "簡報顯示延遲"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "處理時間"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "依階段顯示 INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "如要提升 LCP，請讓 LCP 圖片立即[顯示](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)在 HTML 中，[避免延遲載入](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "已套用 fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "應套用 fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "未套用延遲載入"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP 圖片載入時間比最早開始的時間點晚 {PH1}。"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "未偵測到 LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP 不是圖片，因此未偵測到 LCP 資源"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "你可以在初始文件中找到要求"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP 要求探索"}, "models/trace/insights/LCPPhases.ts | description": {"message": "每個[階段都有特定改善策略](https://web.dev/articles/optimize-lcp#lcp-breakdown)。理想狀態下，大部分的 LCP 時間應該用於載入資源，而非延遲。"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "時間長度"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "元素轉譯延遲"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "實際使用者的第 75 個百分位數"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "未偵測到 LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "階段"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "資源載入延遲"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "資源載入時長"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "依階段顯示 LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "指令碼"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "浪費的位元組數"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill 和轉換作業可讓舊版瀏覽器使用新版 JavaScript 的功能，但對新式瀏覽器來說，很多都是不需要的功能。除非你認為必須支援舊版瀏覽器，否則建議修改 JavaScript 建構程序，不要轉譯[基準](https://web.dev/articles/baseline-and-polyfills)功能。[瞭解為何大多數網站不需要轉譯，就能部署 ES6+ 程式碼](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "舊版 JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 和 HTTP/3 擁有許多 HTTP/1.1 沒有的優點，例如多工處理。[進一步瞭解如何使用新式 HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "沒有任何要求使用 HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "通訊協定"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "要求"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "新式 HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "來源"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "要求"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "來源"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "時間"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "預估 LCP 節省毫秒數"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "這是未使用的預先連結。請檢查 crossorigin 屬性的設定是否正確。"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "如要[避免鏈結關鍵要求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)，可以縮短鏈結長度、降低資源的下載大小或延後下載非必要資源，提高頁面載入速度。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "請新增最重要來源的 [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 提示，但盡量不要超過 4 個。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "預先連結候選來源"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "關鍵路徑延遲時間上限："}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "網路依附元件未影響任何算繪工作"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "沒有其他適合的預先連結候選來源"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "未預先連結任何來源"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 提示可協助瀏覽器在載入網頁時提早建立連結，因此首次向該來源發出要求時可節省時間。以下是網頁預先連結的來源。"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "預先連結的來源"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "網路依附元件樹狀結構"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "找到超過 4 個「preconnect」連結。請謹慎使用這些連結，僅用於最重要的來源。"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "這是未使用的預先連結。建議將 preconnect 僅用於網頁可能會要求的來源。"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "如要避免鏈結關鍵要求，可以縮短鏈結長度、降低資源的下載大小或延後下載非必要資源，提高頁面載入速度。"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "要求會阻礙網頁的初始轉譯作業，進而導致 LCP 延遲。你可以透過[延後或內嵌](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)的方式，將這些網路要求移出關鍵路徑。"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "時間長度"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "這項導覽作業沒有任何轉譯封鎖要求"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "要求"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "轉譯封鎖要求"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "如果「重新計算樣式」的成本仍然偏高，可以使用選取器最佳化功能降低成本。針對經過時間偏長及慢速路徑百分比偏高的[選取器進行最佳化](https://developer.chrome.com/docs/devtools/performance/selector-stats)。選取器越簡單、數量越少，DOM 的規模越小、結構越單純，越能降低比對成本。"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "經過時間"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "找不到任何 CSS 選取器資料。請在效能面板設定中啟用 CSS 選取器統計資料。"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "嘗試比對元素數"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "相符項目數"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS 選取器成本"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "最耗費資源的選取器"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "總計"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "主執行緒執行時間"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "第三方"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "傳輸大小"}, "models/trace/insights/ThirdParties.ts | description": {"message": "第三方程式碼可能會嚴重影響載入效能。請[減少第三方程式碼並延遲載入](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)，優先載入網頁內容。"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "找不到任何第三方"}, "models/trace/insights/ThirdParties.ts | title": {"message": "第三方"}, "models/trace/insights/Viewport.ts | description": {"message": "如果可視區域未針對行動裝置進行最佳化調整，輕觸互動可能會[延遲最多 300 毫秒](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "行動裝置輕觸延遲"}, "models/trace/insights/Viewport.ts | title": {"message": "針對行動裝置最佳化可視區域"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "只有透過 GET 要求載入的網頁才適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "只能快取狀態碼為 2XX 的網頁。"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome 偵測到嘗試在快取中執行 JavaScript 的作業。"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "已要求 AppBanner 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "由於旗標設定的關係，往返快取功能已停用。如要在這部裝置上啟用該功能，請前往 chrome://flags/#back-forward-cache。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "由於指令列設定的關係，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "由於記憶體不足，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "委派目標不支援往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "為了執行預先轉譯器，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "網頁有包含已註冊事件監聽器的 BroadcastChannel 例項，因此系統無法快取網頁。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "含有 cache-control:no-store 標頭的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "有人刻意清除快取。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "為了讓系統能夠快取其他網頁，這個網頁已從快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "含有外掛程式的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "未定義"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "使用 FileChooser API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "使用 File System Access API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "使用媒體裝置調度工具的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "使用者離開網頁時，媒體播放器正在播放內容。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "使用 MediaSession API 並設定播放狀態的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "使用 MediaSession API 並設定動作處理常式的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "由於螢幕閱讀器的關係，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "使用 SecurityHandler 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "使用 Serial API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "使用 WebBluetooth API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "使用 WebUSB API 的網頁不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "使用 Cache-Control: no-store 的頁面停用 Cookie，因此系統停用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "使用專屬 Worker 或 Worklet 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "文件未在使用者離開前完成載入。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "使用者離開網頁時，系統會顯示應用程式橫幅。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "使用者離開網頁時，系統會顯示 Chrome 密碼管理員。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "使用者離開網頁時，DOM distillation 正在處理中。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "使用者離開網頁時，系統會顯示 DOM Distiller Viewer。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "由於擴充功能使用訊息 API，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "在儲存至往返快取功能之前，可持續連線的擴充功能應中斷連線。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "可持續連線的擴充功能嘗試在往返快取中傳送訊息給畫面。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "由於擴充功能的關係，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "使用者離開網頁時，系統會在該頁面顯示強制回應對話方塊，例如重新提交表單或 HTTP 密碼對話方塊。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "使用者離開網頁時，系統會顯示離線頁面。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "使用者離開網頁時，系統會顯示 Out-Of-Memory Intervention。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "使用者離開網頁時，系統會要求權限。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "使用者離開網頁時，系統會顯示彈出式視窗攔截器。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "使用者離開網頁時，系統會顯示安全瀏覽詳細資料。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "安全瀏覽功能認定這個網頁有濫用疑慮，因此封鎖彈出式視窗。"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Service Worker 已在網頁儲存於往返快取時啟用。"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "由於文件發生錯誤，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "使用 FencedFrames 的網頁無法儲存在往返快取中。"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "為了讓系統能夠快取其他網頁，這個網頁已從快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "已授予媒體串流播放權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "如果網頁含有特定類型嵌入內容 (例如 PDF)，目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "使用 IdleManager 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "含有開放式 IndexedDB 連線的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "系統已因 IndexedDB 事件停用往返快取功能。"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "使用了不支援的 API。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "由擴充功能插入 JavaScript 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "由擴充功能插入 StyleSheet 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "內部錯誤。"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "部分 JavaScript 網路要求收到含有「Cache-Control: no-store」標頭的資源，因此往返快取已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "由於保持運作要求，往返快取功能已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "使用鍵盤鎖定功能的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "網頁未在使用者離開前完成載入。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "主要資源含有 cache-control:no-cache 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "主要資源含有 cache-control:no-store 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "瀏覽作業已在網頁從往返快取中還原前取消。"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "由於某個作用中的網路連線收到太多資料，因此該網頁已從快取中移除。Chrome 會限制網頁處於快取狀態時可接收的資料量。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "含有 in-flight fetch() 或 XHR 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "由於某個作用中的網路要求涉及重新導向，因此網頁已從往返快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "網路連線處於開放狀態太久，因此網頁已從快取中移除。Chrome 會限制網頁可在快取時接收資料的時間長度。"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "不含有效回應標頭的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "瀏覽作業是在主頁框以外的頁框中執行。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "網頁含有針對已建立索引的資料庫所執行的進行中交易，目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "含有 in-flight 網路要求的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "含有 in-flight fetch 網路要求的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "含有 in-flight 網路要求的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "含有 in-flight XHR 網路要求的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "使用 PaymentManager 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "使用子母畫面的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "顯示列印使用者介面的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "網頁已透過「window.open()」開啟且其他分頁含有該網頁的參照內容，或者網頁開啟了視窗。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "往返快取中網頁的轉譯器程序異常終止。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "往返快取中網頁的轉譯器程序已中斷。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "已要求音訊擷取權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "已要求感應器權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "已要求背景同步或擷取權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "已要求 MIDI 權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "已要求通知權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "已要求儲存空間存取權的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "已要求影片擷取權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "只能快取網址配置為 HTTP/HTTPS 的網頁。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "網頁已在儲存於往返快取時由 Service Worker 聲明擁有權。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service Worker 已嘗試向往返快取中的網頁傳送 MessageEvent。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Service Worker 已在網頁儲存於往返快取時取消註冊。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "系統啟用了 Service Worker，因此網頁已從往返快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome 已重新啟動，並清除往返快取項目。"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "使用 SharedWorker 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "使用 SpeechRecognizer 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "使用 SpeechSynthesis 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "網頁上 iframe 啟動的瀏覽作業並未完成。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "子資源含有 cache-control:no-cache 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "子資源含有 cache-control:no-store 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "網頁超出存放在往返快取中的時間上限，因此已失效。"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "將網頁儲存至往返快取的作業逾時 (可能原因為頁面隱藏事件處理常式的執行時間太長)。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "網頁在主頁框中含有卸載處理常式。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "網頁在子頁框中含有卸載處理常式。"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "瀏覽器已變更使用者代理程式覆寫標頭。"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "已授予影片或音訊錄製權限的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "使用 WebDatabase 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "使用 WebHID 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "使用 WebLocks 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "使用 WebNfc 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "使用 WebOTPService 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "使用 WebRTC 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "已使用 WebRTC，因此停用往返快取功能。"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "使用 WebShare 的網頁目前不適用往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "使用 WebSocket 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "已使用 WebSocket，因此停用往返快取功能。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "使用 WebTransport 的網頁無法儲存至往返快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "已使用 WebTransport，因此停用往返快取功能。"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "使用 WebXR 的網頁目前不適用往返快取。"}}