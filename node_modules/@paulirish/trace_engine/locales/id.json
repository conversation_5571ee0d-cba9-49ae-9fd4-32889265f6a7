{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Otorisasi tidak akan dicakup oleh simbol karakter pengganti (*) dalam penanganan Access-Control-Allow-Headers CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Dar<PERSON>ada menggunakan pemilih -internal-media-controls-overlay-cast-button, se<PERSON><PERSON><PERSON> gunakan disableRemotePlayback untuk menonaktifkan integrasi Cast default."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "<PERSON><PERSON> tampilan CSS slider-vertical tidak standar dan akan dihapus."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Permintaan resource yang URL-nya berisi karakter \\(n|r|t) spasi kosong yang dihapus dan karakter kurang dari (<) akan diblokir. Hapus baris baru dan enkode karakter kurang dari, dari tempat seperti nilai atribut elemen untuk memuat resource ini."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() tidak digunakan lagi. Sebagai gantinya, gunakan API standar: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() tidak digunakan lagi. Sebagai gantinya, gunakan API standar: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() tidak digunakan lagi. Sebagai gantinya, gunakan API standar: nextHopProtocol di Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "<PERSON><PERSON> yang berisi karakter \\(0|r|n) akan di<PERSON>lak, bukan dipotong."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Melonggarkan kebijakan asal yang sama dengan menyetel document.domain sudah tidak digunakan lagi, dan akan dinonaktifkan secara default. Peringatan penghentian ini ditujukan untuk akses lintas asal yang diaktifkan dengan menyetel document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Memicu window.alert dari iframe lintas origin tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Memicu window.confirm dari iframe lintas origin tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Dukungan untuk data: URL dalam SVGUseElement tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() dan watchPosition() tidak lagi berfungsi pada asal yang tidak aman. Untuk menggunakan fitur ini, sebaiknya Anda mengalihkan aplikasi ke asal yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() dan watchPosition() tidak digunakan lagi karena asal yang tidak aman. Untuk menggunakan fitur ini, sebaiknya Anda mengalihkan aplikasi ke asal yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() tidak lagi berfungsi pada asal yang tidak aman. Untuk menggunakan fitur ini, sebaiknya Anda mengalihkan aplikasi ke asal yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Ditemukan tag <h1> dalam <article>, <aside>, <nav>, atau <section> yang tidak memiliki ukuran font yang ditentukan. Ukuran teks judul ini akan berubah di browser ini dalam waktu dekat. Lihat https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 untuk mengetahui informasi selengkapnya."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate tidak digunakan lagi. Sebagai gantinya, gunakan RTCPeerConnectionIceErrorEvent.address atau RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Format permintaan navigator.credentials.get() untuk kredensial digital ini tidak digunakan lagi. Perbarui panggilan Anda untuk menggunakan format baru."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Data arbitrer dan asal penjual dari peristiwa pekerja layanan canmakepayment tidak digunakan lagi dan akan dihapus: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Situs meminta subresource dari jaringan yang hanya dapat diakses karena posisi jaringan istimewa penggunanya. Permintaan ini mengekspos perangkat dan server non-publik ke internet, yang meningkatkan risiko serangan pemalsuan permintaan lintas situs (CSRF), dan/atau kebocoran informasi. Untuk mengurangi risiko tersebut, Chrome menghentikan permintaan ke subresource non-publik saat dimulai dari konteks yang tidak aman, dan akan mulai memblokirnya."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Kolom dailyUpdateUrl dari InterestGroups yang diteruskan ke joinAdInterestGroup() telah diganti namanya menjadi updateUrl, untuk mencerminkan perilakunya dengan lebih akurat."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator tidak digunakan lagi. Sebagai gantinya, gunakan Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS tidak dapat dimuat dari URL file: kecuali jika diakhiri dengan ekstensi file .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Penggunaan SourceBuffer.abort() untuk membatalkan penghapusan rentang asinkron remove() tidak digunakan lagi karena perubahan spesifikasi. Dukungan akan dihapus pada masa mendatang. Sebagai gantinya, Anda harus memproses peristiwa updateend. abort() dimaksudkan untuk hanya membatalkan penambahan media asinkron atau mereset status parser."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Menyetel MediaSource.duration di bawah stempel waktu presentasi tertinggi dari semua bingkai berkode dan yang di-buffer kini tidak digunakan lagi karena perubahan spesifikasi. Dukungan untuk penghapusan implisit media yang di-buffer dan terpotong akan dihapus pada masa mendatang. Anda harus menjalankan remove(newDuration, oldDuration) eksplisit pada semua sourceBuffers, saat status menunjukkan newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI akan meminta izin untuk menggunakan sysex meskipun sysex tidak ditentukan dalam MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification API mungkin tidak lagi digunakan dari asal yang tidak aman. Sebaiknya Anda mengalihkan aplikasi Anda ke asal yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Izin untuk Notification API mungkin tidak lagi diminta dari iframe lintas asal. Sebaiknya Anda meminta izin dari bingkai level teratas atau membuka jendela baru."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opsi imageOrientation: 'none' di createImageBitmap tidak digunakan lagi. Sebagai gantinya, gunakan createImageBitmap dengan opsi {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partner <PERSON><PERSON>g menegosia<PERSON><PERSON> versi (D)TLS yang usang. Hubungi partner <PERSON><PERSON>."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Menentukan overflow: visible pada tag img, video, dan canvas dapat menyebabkannya menghasilkan konten visual di luar batas elemen. Lihat https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments tidak digunakan lagi. Sebagai gantinya, gunakan penginstalan tepat waktu untuk pengendali pembayaran."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Panggilan PaymentRequest mengabaikan perintah connect-src Ke<PERSON>jak<PERSON>an Ko<PERSON>n (CSP). Pengabaian ini tidak digunakan lagi. Tambahkan ID metode pembayaran dari PaymentRequest API (di kolom supportedMethods) ke perintah connect-src CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent tidak digunakan lagi. <PERSON><PERSON><PERSON>, gunakan navigator.storage standar."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> dengan induk <picture> tidak valid dan akan diabaikan. Sebagai gantinya, gunakan <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame adalah metode khusus vendor. Sebagai gantinya, gunakan cancelAnimationFrame standar."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame adalah metode khusus vendor. Sebagai gantinya, gunakan requestAnimationFrame standar."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen tidak digunakan lagi. Sebagai gantinya, gunakan Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() tidak digunakan lagi. Sebagai gantinya, gunakan Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() tidak digunakan lagi. Sebagai gantinya, gunakan Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() tidak digunakan lagi. Sebagai gantinya, gunakan Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() tidak digunakan lagi. Sebagai gantinya, gunakan Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen tidak digunakan lagi. Sebagai gantinya, gunakan Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "<PERSON><PERSON> penggunaan API chrome.privacy.websites.privacySandboxEnabled, meskipun akan tetap aktif untuk kompatibilitas mundur hingga rilis M113. Sebagai gantinya, gunakan chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, dan chrome.privacy.websites.adMeasurementEnabled. Lihat https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Batasan DtlsSrtpKeyAgreement dihapus. <PERSON>a telah menentukan nilai false untuk batasan ini, yang ditafsirkan sebagai upaya untuk menggunakan metode SDES key negotiation yang dihapus. Fungsi ini dihapus. Sebagai gantinya, gunakan layanan yang mendukung DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Batasan DtlsSrtpKeyAgreement dihapus. Anda telah menentukan nilai true untuk batasan ini, yang tidak be<PERSON>, tetapi <PERSON>a dapat menghapus batasan ini agar rapi."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "GetStats() berbasis callback tidak digunakan lagi dan akan dihapus. Sebagai gantinya, gunakan getStats() yang sesuai dengan spesifikasi."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() tidak digunakan lagi. Sebagai gantinya, gunakan Selection.Modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Permintaan subresource yang URL-nya berisi kredensial yang disematkan (mis. **********************/) akan diblokir."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opsi rtcpMuxPolicy tidak digunakan lagi dan akan di<PERSON>pus."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer akan memerlukan isolasi lintas asal. Lihat https://developer.chrome.com/blog/enabling-shared-array-buffer/ untuk detail selengkapnya."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() tanpa aktivasi pengguna tidak digunakan lagi dan akan dihapus."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Pemroses peristiwa penghapusan muatan tidak digunakan lagi dan akan di<PERSON>pus."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Ekstensi harus ikut serta dalam isolasi lintas asal untuk terus menggunakan SharedArrayBuffer. Lihat https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atribut isFallbackAdapter GPUAdapter tidak digunakan lagi, gunakan atribut isFallbackAdapter GPUAdapterInfo sebagai gantinya."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 tidak didukung oleh respons JSON di XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "XMLHttpRequest sinkron pada thread utama tidak digunakan lagi karena efeknya yang merugikan terhadap pengalaman pengguna akhir. Untuk mendapatkan bantuan lebih lanjut, buka https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Pergeseran tata letak terjadi saat elemen bergerak tanpa interaksi pengguna. [Selidiki penyebab perubahan tata letak](https://web.dev/articles/optimize-cls), seperti penambahan elemen, penghapusan elemen, atau perubahan font saat halaman dimuat."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Permintaan font"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON> yang dimas<PERSON>kan"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Cluster perubahan tata letak @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Tidak dapat mendeteksi penyebab perubahan tata letak"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Tidak ada perubahan tata letak"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han tata letak"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "<PERSON><PERSON><PERSON><PERSON> utama per<PERSON>han tata letak"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Cluster terburuk"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Cluster perubahan tata letak terburuk"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL Cache"}, "models/trace/insights/Cache.ts | description": {"message": "Durasi cache yang panjang dapat mempercepat kunjungan berulang ke halaman Anda. [Pelajari lebih lanjut](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Tidak ada permintaan dengan kebijakan cache yang tidak efisien"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} la<PERSON><PERSON>"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Gunakan durasi cache yang efisien"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM yang besar dapat meningkatkan durasi penghitungan gaya dan perubahan posisi/geometri tata letak, yang memengaruhi responsivitas halaman. DOM yang besar juga akan meningkatkan penggunaan memori. [Pelajari cara menghindari ukuran DOM yang berlebihan](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elemen"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Sebagian besar turunan"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Kedalaman DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistik"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimalkan ukuran DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Total elemen"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Permintaan jaringan pertama merupakan yang terpenting.  Kurangi latensinya dengan menghindari pengalihan, memastikan respons server yang cepat, dan mengaktifkan kompresi teks."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON> pengalihan ({PH1} pengalihan, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Server merespons dengan lambat ({PH1} dalam pengamatan)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Tidak ada kompresi yang diterapkan"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Server merespons dengan cepat ({PH1} dalam pengamatan)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Menerapkan kompresi teks"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Waktu respons server"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latensi permintaan dokumen"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Download tidak dikompresi"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Byte duplikat"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Sumber"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Menghapus modul JavaScript duplikat yang besar dari paket untuk mengurangi byte tidak perlu yang digunakan oleh aktivitas jaringan."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript Duplikat"}, "models/trace/insights/FontDisplay.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON> setel [font-display](https://developer.chrome.com/blog/font-display) ke swap atau optional untuk memastikan teks terlihat secara konsisten. swap dapat dioptimalkan lebih lanjut untuk mengurangi perubahan tata letak dengan [penggantian metrik font](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Font"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Tampilan font"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON><PERSON> te<PERSON>"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonim)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Banyak API, yang biasanya membaca geometri tata letak, memaksa mesin rendering menjeda eksekusi skrip untuk menghitung gaya dan tata letak. Pelajari lebih lanjut [per<PERSON>han posisi/geometri paksa](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) dan mitigasinya."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stack trace"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Ubah posisi/geometri yang dipaksa"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Panggilan fungsi teratas"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Total waktu perubahan posisi/geometri"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[tanpa atribut]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Mengurangi waktu download gambar dapat meningkatkan waktu pemuatan halaman dan LCP. [Pelajari lebih lanjut cara mengoptimalkan ukuran gambar](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON><PERSON><PERSON><PERSON> {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Tidak ada gambar yang diopti<PERSON>"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimalkan ukuran file"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} la<PERSON><PERSON>"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Meningkatkan penayangan gambar"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Meningkatkan faktor kompresi gambar dapat meningkatkan ukuran download gambar ini."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Menggunakan format gambar modern (WebP, AVIF) atau meningkatkan kompresi gambar dapat meningkatkan ukuran download gambar ini."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "File gambar ini lebih besar dari yang diperlukan ({PH1}) untuk dimensi yang ditampilkan ({PH2}). Gunakan gambar responsif untuk mengurangi ukuran download gambar."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Menggunakan format video, bukan GIF, dapat meningkatkan ukuran download konten animasi."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "<PERSON><PERSON> investigasi dengan fase terpanjang. [Penundaan dapat diminimalkan](https://web.dev/articles/optimize-inp#optimize_interactions). Untuk mengurangi durasi pemrosesan, [optimalkan biaya thread utama](https://web.dev/articles/optimize-long-tasks), yang sering kali berupa JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Penundaan input"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Tidak ada interaksi yang terdeteksi"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON> presentasi"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP menurut fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimalkan LCP dengan membuat gambar LCP [dapat ditemukan](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) dari HTML secara langsung, dan [menghindari pemuatan lambat](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high diterapkan"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high harus diterapkan"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "pemuatan lambat tidak diterapkan"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Gambar LCP dimuat {PH1} setelah titik awal paling awal."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Tidak ada LCP yang terdeteksi"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Tidak ada resource LCP yang terdeteksi karena LCP bukan gambar"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Permintaan dapat ditemukan di dokumen awal"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Penemuan permintaan LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Setiap [fase memiliki strategi peningkatan tertentu](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idealnya, sebagian besar waktu LCP harus digunakan untuk memuat resource, bukan untuk penundaan."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Penundaan render elemen"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "P75 sebenarnya"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Tidak ada LCP yang terdeteksi"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Penundaan pemuatan resource"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Durasi pemuatan resource"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP menurut fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Byte yang tidak digunakan"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill dan transformasi memungkinkan browser lama menggunakan fitur JavaScript yang baru. Namun, banyak di antara fitur tersebut yang tidak diperlukan browser modern. Pertimbangkan untuk mengubah proses build JavaScript Anda agar tidak mentranspilasi fitur [Baseline](https://web.dev/articles/baseline-and-polyfills), kecuali jika Anda tahu bahwa Anda harus mendukung browser lama. [Pelajari alasan sebagian besar situs dapat men-deploy kode ES6+ tanpa mentranspilasi](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript Versi Lama"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 dan HTTP/3 menawarkan banyak manfaat dibandingkan HTTP/1.1, seperti multiplexing. [Pelajari lebih lanjut cara menggunakan HTTP modern](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Tidak ada permintaan yang menggunakan HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP Modern"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origin"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Sumber"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Per<PERSON>raan penghematan LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Prakoneksi yang tidak digunakan. Pastikan atribut crossorigin digunakan dengan benar."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Hindari perantaian permintaan penting](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) dengan mengurangi panjang rantai, mengurangi ukuran download resource, atau menunda download resource yang tidak penting untuk mempercepat pemuatan halaman."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Tambahkan petunjuk [prakoneksi](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ke origin yang paling penting, tetapi usahakan untuk menggunakan tidak lebih dari 4 petunjuk."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidat prakoneksi"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latensi jalur penting maksimum:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Tidak ada tugas rendering yang terpengaruh oleh dependensi jaringan"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Tidak ada origin tambahan yang cocok untuk prakoneksi"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "tidak ada origin yang dihubungkan sebelumnya"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Petunjuk [prakone<PERSON>i](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) membantu browser membuat koneksi lebih awal saat halaman dimuat, se<PERSON>ga dapat menghemat waktu ketika permintaan ke origin tersebut dibuat pertama kali. Berikut ini adalah origin yang telah terhubung ke halaman sebelumnya."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origin yang dihubungkan sebelumnya"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Hierarki dependensi jaringan"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Ditemukan lebih dari 4 koneksi preconnect. Koneksi tersebut sebaiknya tidak sering digunakan dan hanya untuk origin yang paling penting."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Prakoneksi yang tidak digunakan. Hanya gunakan preconnect untuk origin yang kemungkinan akan diminta halaman."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Hi<PERSON>ri perantaian permintaan penting dengan mengurangi panjang rantai dan ukuran download resource, atau menunda download resource yang tidak penting untuk mempercepat pemuatan halaman."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Permintaan memblokir render awal halaman, yang dapat menunda LCP. [<PERSON><PERSON><PERSON> atau penyisipan](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) dapat memindahkan permintaan jaringan ini dari jalur penting."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Tidak ada permintaan pem<PERSON><PERSON><PERSON>ran rendering untuk navigasi ini"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Permintaan pemblokiran rendering"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Jika biaya Hitung Ulang Gaya tetap tinggi, pengopt<PERSON><PERSON> pemilih dapat menguranginya. [Optimalkan pemilih](https://developer.chrome.com/docs/devtools/performance/selector-stats) dengan waktu berlalu yang tinggi dan % jalur lambat yang tinggi. Pemilih yang lebih sederhana, pemilih yang lebih sedikit, DOM yang lebih kecil, dan <PERSON><PERSON> yang lebih dangkal semuanya akan mengurangi biaya pencocokan."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON><PERSON> be<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Data pemilih CSS tidak ditemukan. Statistik pemilih CSS harus diaktifkan di setelan panel performa."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Percobaan kecocokan"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Biaya Pemilih CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "W<PERSON><PERSON> thread utama"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON> ketiga"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Ukuran transfer"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Kode pihak ketiga dapat memberikan dampak signifikan terhadap performa pemuatan. [Kurangi dan tunda pemuatan kode pihak ketiga](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) untuk memprioritaskan konten halaman Anda."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Tidak ada pihak ketiga yang ditemukan"}, "models/trace/insights/ThirdParties.ts | title": {"message": "<PERSON><PERSON> ketiga"}, "models/trace/insights/Viewport.ts | description": {"message": "Interaksi ketuk mungkin [tertunda hingga 300 md](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) jika area tampilan tidak dioptimalkan untuk seluler."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "<PERSON>undaan ketuk di perangkat seluler"}, "models/trace/insights/Viewport.ts | title": {"message": "Mengoptimalkan area tampilan untuk seluler"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON><PERSON> halaman yang dimuat melalui permintaan GET yang dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Hanya halaman dengan kode status 2XX yang dapat di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome mendeteksi upaya untuk mengeksekusi JavaScript saat berada dalam cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Halaman yang meminta AppBanner saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Back-forward cache dinonaktifkan oleh tanda. Buka chrome://flags/#back-forward-cache untuk mengaktifkannya secara lokal di perangkat ini."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Back-forward cache dinonaktifkan oleh command line."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Back-forward cache dinonakt<PERSON><PERSON> karena memori tidak cukup."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Back-forward cache tidak didukung oleh penerima delegasi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Back-forward cache dinonaktifkan untuk pra-rendering."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Halaman tidak dapat di-cache karena memiliki instance BroadcastChannel dengan pemroses terdaftar."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Halaman dengan header cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON> senga<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Halaman dikeluarkan dari cache untuk memungkinkan halaman lain di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Halaman yang berisi plugin saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Tidak ditentukan"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Halaman yang menggunakan FileChooser API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Halaman yang menggunakan File System Access API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Halaman yang menggunakan Dispatcher Perangkat Media tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Pemutar media sedang berputar saat pengguna menutupnya."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Halaman yang menggunakan MediaSession API dan menyetel status pemutaran tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Halaman yang menggunakan MediaSession API dan menyetel pengendali tindakan tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> karena pemba<PERSON> layar."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Halaman yang menggunakan SecurityHandler tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Halaman yang menggunakan Serial API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Halaman yang menggunakan WebAuthentication API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Halaman yang menggunakan WebBluetooth API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Halaman yang menggunakan WebUSB API tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Back-forward cache dinonaktifkan karena cookie dinonaktifkan di halaman yang menggunakan Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Halaman yang menggunakan pekerja atau worklet khusus saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokumen belum selesai dimuat sebelum pengguna menutupnya."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Ada Banner Aplikasi saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Ada Pengelola Sandi Chrome saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Distilasi DOM sedang berlangsung saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Ada Penampil DOM Distiller saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Back-forward cache dinonaktifkan karena ekstensi menggunakan API pesan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Ekstensi yang memiliki koneksi persisten harus memutuskan koneksi tersebut sebelum disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Eks<PERSON>i yang memiliki koneksi persisten mencoba mengirim pesan ke frame dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> ka<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Dialog modal seperti pengiriman ulang formulir atau dialog sandi HTTP ditampilkan untuk halaman saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Halaman offline ditampilkan saat pengguna menutupnya."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Ada panel Intervensi Memori Habis saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Ada permintaan izin saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "<PERSON> pemblokir pop-up saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Detail Safe Browsing ditampilkan saat pengguna menutup halaman."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing menganggap halaman ini melanggar dan memblokir pop-up."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON> layanan diakti<PERSON>kan saat halaman berada dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Back-forward cache dinonakt<PERSON><PERSON> karena error dokumen."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Halaman yang menggunakan FencedFrames tidak dapat disimpan dalam bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Halaman dikeluarkan dari cache untuk memungkinkan halaman lain di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Halaman yang diberikan akses streaming media saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Halaman yang memiliki jenis konten tersemat tertentu (misalnya PDF) saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Halaman yang menggunakan IdleManager saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Halaman yang memiliki IndexedDB connection terbuka saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Back-forward cache dinonaktifkan karena peristiwa IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "API yang tidak memenuhi syarat telah digunakan."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Halaman yang disertai JavaScript oleh ekstensi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Halaman yang disertai StyleSheet oleh ekstensi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Error internal."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Back-forward cache dinonaktifkan karena beberapa permintaan jaringan JavaScript menerima resource dengan header Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> karena permintaan keepalive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Halaman yang menggunakan kunci Keyboard saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Halaman belum selesai dimuat sebelum pengguna menutupnya."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Halaman yang resource utamanya memiliki cache-control:no-cache tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Halaman yang resource utamanya memiliki cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigasi dibatalkan sebelum halaman dapat dipulihkan dari back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Halaman dikeluarkan dari cache karena koneksi jaringan aktif menerima terlalu banyak data. Chrome membatasi jumlah data yang dapat diterima halaman saat di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Halaman yang memiliki fetch() atau XHR yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Halaman dikeluarkan dari back-forward cache karena permin<PERSON>an jaringan aktif men<PERSON> pengal<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Halaman dikeluarkan dari cache karena koneksi jaringan terbuka terlalu lama. Chrome membatasi jumlah waktu untuk suatu halaman dapat menerima data saat di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Halaman yang tidak memiliki header respons yang valid tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON> ter<PERSON><PERSON> di frame selain frame utama."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Halaman dengan indexed DB transactions yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Halaman dengan permintaan jaringan yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Halaman dengan permintaan jaringan fetch yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Halaman dengan permintaan jaringan yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Halaman dengan permintaan jaringan XHR yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Halaman yang menggunakan PaymentManager saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Halaman yang menggunakan Picture-in-Picture saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Halaman yang menampilkan UI Pencetakan saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Halaman dibuka menggunakan 'window.open()' dan tab lain memiliki referensi ke halaman tersebut, atau halaman membuka jendela."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Proses rendering untuk halaman dalam back-forward cache mengalami error."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Proses rendering untuk halaman dalam back-forward cache dihentikan."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Halaman yang telah meminta izin perekaman audio saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Halaman yang telah meminta izin sensor saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Halaman yang telah meminta izin sinkronisasi atau pengambilan di latar belakang saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Halaman yang telah meminta izin MIDI saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Halaman yang telah meminta izin notifikasi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Halaman yang telah meminta akses penyimpanan saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Halaman yang telah meminta izin perekaman video saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "<PERSON><PERSON> halaman yang skema URL-nya adalah HTTP/HTTPS yang dapat di-cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "<PERSON><PERSON> diklaim oleh pekerja layanan saat berada dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> layanan mencoba mengirimkan MessageEvent ke halaman yang berada dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker menjadi tidak terdaftar saat halaman berada dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Halaman dikeluarkan dari back-forward cache ka<PERSON> akt<PERSON> p<PERSON><PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome dimulai ulang dan entri back-forward cache dihapus."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Halaman yang menggunakan SharedWorker saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Halaman yang menggunakan SpeechRecognizer saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Halaman yang menggunakan SpeechSynthesis saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "<PERSON><PERSON><PERSON> di halaman memulai navigasi yang tidak selesai."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Halaman yang subresource-nya memiliki cache-control:no-cache tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Halaman yang subresource-nya memiliki cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "<PERSON><PERSON> melebihi waktu maksimum dalam back-forward cache dan masa berlakunya telah berak<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "<PERSON><PERSON><PERSON> halaman habis saat disimpan dalam back-forward cache (kemungkinan karena pengendali pagehide yang berjalan lama)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Halaman memiliki pengendali penghapus muatan di frame utama."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Halaman memiliki pengendali penghapus muatan di sub frame."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Browser te<PERSON> men<PERSON> header penggantian agen pengguna."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Halaman yang diberikan akses untuk merekam video atau audio saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Halaman yang menggunakan WebDatabase saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Halaman yang menggunakan WebHID saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Halaman yang menggunakan WebLocks saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Halaman yang menggunakan WebNfc saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Halaman yang menggunakan WebOTPService saat ini tidak dapat disimpan dengan benar dalam bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Halaman dengan WebRTC tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Back-forward cache dinonaktifkan karena WebRTC telah digunakan."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Halaman yang menggunakan WebShare saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Halaman dengan WebSocket tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Back-forward cache dinonaktifkan karena WebSocket telah digunakan."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Halaman dengan WebTransport tidak dapat disimpan dalam back-forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Back-forward cache dinonaktifkan karena WebTransport telah digunakan."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Halaman yang menggunakan WebXR saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}}