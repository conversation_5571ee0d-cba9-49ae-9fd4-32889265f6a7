{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "അംഗീകാരത്തെ CORS Access-Control-Allow-Headers ഹാൻഡ്‌ലിംഗിൽ വൈൽഡ്‌കാർഡ് ചിഹ്നം (*) പരിഗണിക്കില്ല."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ഡിഫോൾട്ട് കാസ്റ്റ് ഇന്റഗ്രേഷൻ പ്രവർത്തനരഹിതമാക്കാൻ, -internal-media-controls-overlay-cast-button സെലക്‌ടറിന് പകരം disableRemotePlayback ആട്രിബ്യൂട്ട് ഉപയോഗിക്കണം."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "slider-vertical എന്ന CSS അപ്പീയറൻസ് മൂല്യം ഏകീകൃതമാക്കിയിട്ടില്ല, അത് നീക്കം ചെയ്യും."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "നീക്കം ചെയ്ത വൈറ്റ്‌സ്പെയ്‌സ് \\(n|r|t) പ്രതീകങ്ങളും ലെസ് ദാൻ പ്രതീകങ്ങളും (<) ഉള്ള URL-കൾ അടങ്ങിയ ഉറവിട അഭ്യർത്ഥനകൾ ബ്ലോക്ക് ചെയ്യുന്നു. ഈ ഉറവിടങ്ങൾ ലോഡ് ചെയ്യാൻ, എലമന്റ് ആട്രിബ്യൂട്ട് മൂല്യങ്ങൾ പോലുള്ള സ്ഥലങ്ങളിൽ നിന്ന് ന്യൂലൈനുകൾ നീക്കം ചെയ്ത്, ലെസ് ദാൻ പ്രതീകങ്ങൾ എൻകോഡ് ചെയ്യുക."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() അവസാനിപ്പിച്ചു, പകരം ഏകീകൃത API ഉപയോഗിക്കുക: നാവിഗേഷൻ ടൈമിംഗ് 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() അവസാനിപ്പിച്ചു, പകരം ഏകീകൃത API ഉപയോഗിക്കുക: പെയിന്റ് ടൈമിംഗ്."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() അവസാനിപ്പിച്ചു, പകരം ഏകീകൃത API ഉപയോഗിക്കുക: നാവിഗേഷൻ ടൈമിംഗ് 2-ലെ nextHopProtocol."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) പ്രതീകമുള്ള കുക്കികളെ വെട്ടിച്ചുരുക്കുന്നതിന് പകരം നിരസിക്കും."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain സജ്ജീകരിക്കുന്നതിലൂടെ സെയിം ഒറിജിൻ നയത്തിൽ ഇളവ് വരുത്തുന്നത് അവസാനിപ്പിച്ചു, ഇത് ഡിഫോൾട്ടായി പ്രവർത്തനരഹിതമാക്കും. അവസാനിപ്പിക്കൽ സംബന്ധിച്ച ഈ മുന്നറിയിപ്പ്, document.domain സജ്ജീകരിച്ച് പ്രവർത്തനക്ഷമമാക്കിയ ക്രോസ് ഒറിജിൻ ആക്‌സസുമായി ബന്ധപ്പെട്ടുള്ളതാണ്."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "ക്രോസ് ഒറിജിൻ iframes-ൽ നിന്നുള്ള window.alert പ്രവർത്തനക്ഷമമാക്കുന്നത് അവസാനിപ്പിച്ചു, ഭാവിയിൽ ഇത് നീക്കം ചെയ്യും."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "ക്രോസ് ഒറിജിൻ iframes-ൽ നിന്നുള്ള window.confirm പ്രവർത്തനക്ഷമമാക്കുന്നത് അവസാനിപ്പിച്ചു, ഭാവിയിൽ ഇത് നീക്കം ചെയ്യും."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "ഡാറ്റയ്ക്കുള്ള പിന്തുണ: SVGUseElement എലമെന്റിലെ URL-കൾ അവസാനിപ്പിച്ചു, ഭാവിയിൽ അവ നീക്കം ചെയ്യും."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "സുരക്ഷിതമല്ലാത്ത ഉറവിടങ്ങളിൽ getCurrentPosition(), watchPosition() എന്നിവ ഇനി പ്രവർത്തിക്കില്ല. ഈ ഫീച്ചർ ഉപയോഗിക്കാൻ, HTTPS പോലുള്ള സുരക്ഷിതമായ ഉറവിടത്തിലേക്ക് നിങ്ങളുടെ ആപ്പ് മാറ്റുന്ന കാര്യം പരിഗണിക്കുക. കൂടുതൽ വിശദാംശങ്ങൾക്ക് https://goo.gle/chrome-insecure-origins കാണുക."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "സുരക്ഷിതമല്ലാത്ത ഉറവിടങ്ങളിൽ getCurrentPosition(), watchPosition() എന്നിവ അവസാനിപ്പിച്ചു. ഈ ഫീച്ചർ ഉപയോഗിക്കാൻ, HTTPS പോലുള്ള സുരക്ഷിതമായ ഉറവിടത്തിലേക്ക് നിങ്ങളുടെ ആപ്പ് മാറ്റുന്ന കാര്യം പരിഗണിക്കുക. കൂടുതൽ വിശദാംശങ്ങൾക്ക് https://goo.gle/chrome-insecure-origins കാണുക."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "സുരക്ഷിതമല്ലാത്ത ഉറവിടങ്ങളിൽ getUserMedia() ഇനി പ്രവർത്തിക്കില്ല. ഈ ഫീച്ചർ ഉപയോഗിക്കാൻ, HTTPS പോലുള്ള സുരക്ഷിതമായ ഉറവിടത്തിലേക്ക് നിങ്ങളുടെ ആപ്പ് മാറ്റുന്ന കാര്യം പരിഗണിക്കുക. കൂടുതൽ വിശദാംശങ്ങൾക്ക് https://goo.gle/chrome-insecure-origins കാണുക."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav>, അല്ലെങ്കിൽ <section> എന്നതിൽ ഒരു നിർദ്ദിഷ്ട ഫോണ്ട് വലുപ്പം ഇല്ലാത്ത <h1> ടാഗ് കണ്ടെത്തി. സമീപഭാവിയിൽ ഈ ബ്രൗസറിൽ തലക്കെട്ട് ടെക്‌സ്‌റ്റിന്റെ വലുപ്പം മാറും. കൂടുതൽ വിവരങ്ങൾക്ക്, https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 കാണുക."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate അവസാനിപ്പിച്ചു. പകരം RTCPeerConnectionIceErrorEvent.address അല്ലെങ്കിൽ RTCPeerConnectionIceErrorEvent.port ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ഡിജിറ്റൽ ക്രെഡൻ‌ഷ്യലുകൾക്കുള്ള navigator.credentials.get() അഭ്യർത്ഥനയ്ക്കുള്ള ഈ ഫോർമാറ്റ് അവസാനിപ്പിച്ചിരിക്കുന്നു, പുതിയ ഫോർമാറ്റ് ഉപയോഗിക്കാനുള്ള നിങ്ങളുടെ തീരുമാനം അപ്ഡേറ്റ് ചെയ്യുക."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment സർവീസ് വർക്കർ ഇവന്റിൽ നിന്നുള്ള മെർച്ചന്റ് ഒറിജിനും ആർബിട്രറി ഡാറ്റയും അവസാനിപ്പിച്ചു, അവ നീക്കം ചെയ്യും: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "പ്രത്യേക നെറ്റ്‌വർക്ക് സ്ഥാനമുള്ള ഉപയോക്താക്കൾക്ക് മാത്രം ആക്‌സസ് ചെയ്യാവുന്ന ഒരു നെറ്റ്‌വർക്കിന്റെ സബ്റിസോഴ്‌സിനായി വെബ്സൈറ്റ് അഭ്യർത്ഥിച്ചു. ഈ അഭ്യർത്ഥനകൾ പബ്ലിക് അല്ലാത്ത ഉപകരണങ്ങളും സെർവറുകളും ഇന്റർനെറ്റിൽ വെളിപ്പെടുത്തുന്നു, ഇത് ക്രോസ് സൈറ്റ് വ്യാജ അഭ്യർത്ഥനയിലൂടെയുള്ള (CSRF) ആക്രമണം ഒപ്പം/അല്ലെങ്കിൽ വിവരങ്ങൾ ചോർത്താനുള്ള സാധ്യത വർദ്ധിപ്പിക്കുന്നു. ഈ അപകടസാധ്യതകൾ കുറയ്ക്കാൻ, സുരക്ഷിതമല്ലാത്ത സന്ദർഭങ്ങളിൽ നിന്ന് ആരംഭിക്കുന്ന പബ്ലിക് ഇതര സബ്റിസോഴ്‌സുകളിലേക്കുള്ള അഭ്യർത്ഥനകൾ Chrome അവസാനിപ്പിക്കുന്നു, ഇനി മുതൽ അവ ബ്ലോക്ക് ചെയ്യും."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "InterestGroups എന്നതിന്റെ joinAdInterestGroup() എന്നതിലേക്ക് പാസ് ചെയ്ത dailyUpdateUrl ഫീൽഡിനെ അതിന്റെ സ്വഭാവം കൂടുതൽ കൃത്യമായി പ്രതിഫലിപ്പിക്കുന്നതിനായി updateUrl എന്ന് പേരുമാറ്റി."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator അവസാനിപ്പിച്ചു. പകരം Intl.Segmenter ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css ഫയൽ എക്സ്റ്റൻഷനിൽ അവസാനിക്കാത്ത പക്ഷം, file: URL-കളിൽ നിന്ന് CSS ലോഡ് ചെയ്യാനാകില്ല."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "സ്പെസിഫിക്കേഷൻ മാറ്റം കാരണം remove() എന്നതിന്റെ സിങ്ക്രോണസ് അല്ലാത്ത പരിധിയുടെ നീക്കം ചെയ്യൽ റദ്ദാക്കാൻ SourceBuffer.abort() ഉപയോഗിക്കുന്നത് അവസാനിപ്പിച്ചു. ഭാവിയിൽ ഇതിന്റെ പിന്തുണ നീക്കം ചെയ്യും. പകരം നിങ്ങൾ updateend ഇവന്റ് കേൾക്കണം. സിങ്ക്രോണസ് അല്ലാത്ത മീഡിയ ചേർക്കലോ പാർസർ നില റീസെറ്റ് ചെയ്യലോ റദ്ദാക്കുക മാത്രമാണ് നിലവിൽ abort() എന്നതിന്റെ ഉദ്ദേശ്യം."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "ഏതെങ്കിലും ബഫർഡ് കോഡഡ് ഫ്രെയിമുകളുടെ ഏറ്റവും ഉയർന്ന പ്രസന്റേഷൻ ടൈംസ്റ്റാമ്പിന് താഴെ MediaSource.duration സജ്ജീകരിക്കുന്നത് സ്പെസിഫിക്കേഷൻ മാറ്റം കാരണം അവസാനിപ്പിച്ചു. വെട്ടിച്ചുരുക്കിയ ബഫർഡ് മീഡിയ നേരിട്ടല്ലാതെ നീക്കം ചെയ്യുന്നതിനുള്ള പിന്തുണ ഭാവിയിൽ നീക്കം ചെയ്യും. പകരം, newDuration < oldDuration ആയിട്ടുള്ള എല്ലാ sourceBuffers പ്രോപ്പർട്ടിയിലും നേരിട്ടുള്ള remove(newDuration, oldDuration) നിർവ്വഹിക്കണം."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions എന്നതിൽ sysex വ്യക്തമാക്കിയിട്ടില്ലെങ്കിലും, ഉപയോഗത്തിനായി വെബ് MIDI അനുമതി ആവശ്യപ്പെടും."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "സുരക്ഷിതമല്ലാത്ത ഉറവിടങ്ങളിൽ നിന്നുള്ള അറിയിപ്പ് API ഇനി ഉപയോഗിക്കാനാകില്ല. നിങ്ങളുടെ ആപ്പ്, HTTPS പോലുള്ള സുരക്ഷിത ഉറവിടത്തിലേക്ക് മാറ്റുന്ന കാര്യം പരിഗണിക്കുക. കൂടുതൽ വിശദാംശങ്ങൾക്ക് https://goo.gle/chrome-insecure-origins കാണുക."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "അറിയിപ്പ് API-ക്കുള്ള അനുമതി ഇനി ഒരു ക്രോസ് ഒറിജിൻ iframe-ൽ നിന്ന് അഭ്യർത്ഥിക്കാനില്ല. ഒരു ടോപ്പ് ലെവൽ ഫ്രെയിമിൽ നിന്ന് അനുമതി അഭ്യർത്ഥിക്കുന്നതോ പുതിയൊരു വിൻഡോ തുറക്കുന്നതോ പരിഗണിക്കുക."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap എന്നതിലെ imageOrientation: 'none' ഓപ്ഷൻ അവസാനിപ്പിച്ചു. പകരം '{imageOrientation: 'from-image'}' ഓപ്ഷൻ ഉള്ള createImageBitmap ഉപയോഗിക്കൂ."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "കാലഹരണപ്പെട്ട ഒരു (D)TLS പതിപ്പാണ് നിങ്ങളുടെ പങ്കാളി ഉപയോഗിക്കുന്നത്. ഇത് പരിഹരിക്കാൻ നിങ്ങളുടെ പങ്കാളിയെ ബന്ധപ്പെടുക."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, വീഡിയോ, കാൻവാസ് ടാഗുകളിൽ overflow: visible വ്യക്തമാക്കുന്നതിലൂടെ, എലമെന്റ് ബൗണ്ടുകൾക്ക് പുറത്ത് അവ വിഷ്വൽ ഉള്ളടക്കം സൃഷ്ടിക്കാനിടയാക്കാം. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md കാണുക."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments അവസാനിപ്പിച്ചു. പേയ്മെന്റ് കൈകാര്യം ചെയ്യുന്ന സംവിധാനത്തിന് പകരം just-in-time ഇൻസ്റ്റാൾ ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "നിങ്ങളുടെ PaymentRequest കോൾ, ഉള്ളടക്ക സുരക്ഷാ നയവുമായി (CSP) ബന്ധപ്പെട്ട connect-src ഡയറക്‌ടീവ് ബൈപാസ് ചെയ്തു. ഈ ബൈപാസ് അവസാനിപ്പിച്ചു. PaymentRequest API-ൽ (supportedMethods ഫീൽഡിൽ) നിന്ന് നിങ്ങളുടെ CSP connect-src ഡയറക്‌ടീവിലേക്ക് പേയ്മെന്റ് രീതി ഐഡന്റിഫയർ ചേർക്കുക."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent അവസാനിപ്പിച്ചു. പകരം, ഏകീകൃത navigator.storage ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> പേരന്റുള്ള <source src> അസാധുവായതിനാൽ ഇത് ഒഴിവാക്കി. പകരം <source srcset> ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame വെണ്ടർ അധിഷ്ഠിതമാണ്. പകരം സ്റ്റാൻഡേർഡ് cancelAnimationFrame ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame വെണ്ടർ അധിഷ്ഠിതമാണ്. പകരം സ്റ്റാൻഡേർഡ് requestAnimationFrame ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen അവസാനിപ്പിച്ചു. പകരം Document.fullscreenElement ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() അവസാനിപ്പിച്ചു. പകരം Element.requestFullscreen() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() അവസാനിപ്പിച്ചു. പകരം Element.requestFullscreen() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() അവസാനിപ്പിച്ചു. പകരം Document.exitFullscreen() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() അവസാനിപ്പിച്ചു. പകരം Document.exitFullscreen() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen അവസാനിപ്പിച്ചു. പകരം Document.fullscreenEnabled ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "ഞങ്ങൾ API chrome.privacy.websites.privacySandboxEnabled അവസാനിപ്പിക്കുകയാണ്, എന്നിരുന്നാലും M113 റിലീസ് ചെയ്യുന്നത് വരെ ബാക്ക്‌വേഡ് കോംപാറ്റിബിലിറ്റിക്കായി ഇത് സജീവമായിരിക്കും. പകരം chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled,chrome.privacy.websites.adMeasurementEnabled എന്നിവ ഉപയോഗിക്കുക. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled കാണുക."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement കൺസ്ട്രയന്റ് നീക്കം ചെയ്തു. ഈ കൺസ്ട്രയിന്റിന് false മൂല്യമാണ് നിങ്ങൾ വ്യക്തമാക്കിയത്, നീക്കം ചെയ്യപ്പെട്ട രീതിയായ SDES key negotiation ഉപയോഗിക്കാനുള്ള ശ്രമം ആയിട്ടാണ് ഇതിനെ കണക്കാക്കുന്നത്. ഈ ഫംഗ്ഷണാലിറ്റി നീക്കം ചെയ്തു; പകരം DTLS key negotiation പിന്തുണയ്ക്കുന്ന ഒരു സേവനം ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement കൺസ്ട്രയന്റ് നീക്കം ചെയ്തു. ഈ കൺസ്ട്രയിന്റിന് true മൂല്യമാണ് നിങ്ങൾ വ്യക്തമാക്കിയത്, മാറ്റമൊന്നും സംഭവിച്ചില്ലെങ്കിലും ഇത് ക്രമപ്പെടുത്താൻ ഈ കൺസ്ട്രയന്റ് നീക്കം ചെയ്യാവുന്നതാണ്."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "കോൾബാക്ക് അധിഷ്‌ഠിത getStats() അവസാനിപ്പിച്ചു, അത് നീക്കം ചെയ്യും. പകരം സ്‌പെസിഫിക്കേഷൻ അനുസരിച്ചുള്ള getStats() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() അവസാനിപ്പിച്ചു. പകരം Selection.modify() ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL-കളിൽ ക്രെഡൻഷ്യലുകൾ ഉൾച്ചേർത്ത സബ്റിസോഴ്‌സ് അഭ്യർത്ഥനകൾ (ഉദാ. **********************/) ബ്ലോക്ക് ചെയ്യുന്നു."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy ഓപ്ഷൻ അവസാനിപ്പിച്ചു, ഇത് നീക്കം ചെയ്യുന്നതുമാണ്."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer എന്നതിന് ക്രോസ് ഒറിജിൻ ഐസൊലേഷൻ ആവശ്യമാണ്. കൂടുതൽ വിശദാംശങ്ങൾക്ക് https://developer.chrome.com/blog/enabling-shared-array-buffer/ കാണുക."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "ഉപയോക്തൃ സജീവമാക്കൽ ഇല്ലാതെയുള്ള speechSynthesis.speak() അവസാനിപ്പിച്ചു, ഇത് നീക്കം ചെയ്യുന്നതുമാണ്."}, "generated/Deprecation.ts | UnloadHandler": {"message": "അൺലോഡ് ചെയ്യുന്ന ഇവന്റ് ലിസണറുകൾ അവസാനിപ്പിച്ചു, അത് നീക്കം ചെയ്യും."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer തുടർന്നും ഉപയോഗിക്കണമെങ്കിൽ, വിപുലീകരണങ്ങൾക്ക് ക്രോസ് ഒറിജിൻ ഐസൊലേഷൻ തിരഞ്ഞെടുക്കണം. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ കാണുക."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ആട്രിബ്യൂട്ട് അവസാനിപ്പിച്ചു, പകരം GPUAdapterInfo isFallbackAdapter ആട്രിബ്യൂട്ട് ഉപയോഗിക്കുക."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest എന്നതിൽ UTF-16-നെ response json പിന്തുണയ്ക്കുന്നില്ല"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "പ്രധാന ത്രെഡിലെ സിങ്ക്രോണസ് XMLHttpRequest, അന്തിമ ഉപയോക്താവിന്റെ അനുഭവത്തെ ദോഷകരമായി ബാധിക്കുന്നതിനാൽ അത് അവസാനിപ്പിച്ചിരിക്കുന്നു. കൂടുതൽ സഹായത്തിന്, https://xhr.spec.whatwg.org/ പരിശോധിക്കുക."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ആനിമേഷൻ"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "ഏതെങ്കിലും ഉപയോക്തൃ ഇടപെടൽ ഇല്ലാതെ ഘടകങ്ങൾ നീങ്ങുമ്പോൾ ലേഔട്ട് ഷിഫ്റ്റുകൾ സംഭവിക്കുന്നു. ഘടകങ്ങൾ ചേർക്കുന്നതും നീക്കം ചെയ്യുന്നതും പേജ് ലോഡ് ചെയ്യുമ്പോൾ അവയുടെ ഫോണ്ടുകൾ മാറുന്നതും പോലുള്ള [ലേഔട്ട് ഷിഫ്റ്റുകളുടെ കാരണങ്ങൾ അന്വേഷിക്കുക](https://web.dev/articles/optimize-cls)."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ഫോണ്ട് അഭ്യർത്ഥന"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "ഇഞ്ചക്റ്റ് ചെയ്ത iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "ലേഔട്ട് ഷിഫ്റ്റ് ക്ലസ്റ്റർ @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "ലേഔട്ട് ഷിഫ്റ്റ് കൾപ്രിറ്റുകളൊന്നും കണ്ടെത്താനായില്ല"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "ലേഔട്ട് ഷിഫ്റ്റുകൾ ഇല്ല"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "ലേഔട്ട് ഷിഫ്റ്റ് കൾപ്രിറ്റുകൾ"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "മുൻനിര ലേഔട്ട് ഷിഫ്റ്റ് കൾപ്രിറ്റുകൾ"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "വലിപ്പം വ്യക്തമാക്കാത്ത ചിത്ര ഘടകം"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "മോശം ക്ലസ്‌റ്റർ"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "മോശം ലേഔട്ട് ഷിഫ്റ്റ് ക്ലസ്‌റ്റർ"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "കാഷെ TTL"}, "models/trace/insights/Cache.ts | description": {"message": "ഡാറ്റ കാഷെയിൽ കൂടുതൽ സമയം സൂക്ഷിക്കുന്നത്, നിങ്ങളുടെ പേജിലേക്ക് വേഗത്തിൽ മടങ്ങാൻ ആളുകളെ സഹായിക്കും. [കൂടുതലറിയുക](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "കാര്യക്ഷമമല്ലാത്ത കാഷെ നയങ്ങളുമായി ബന്ധപ്പെട്ട അഭ്യർത്ഥനകൾ ഒന്നുമില്ല"}, "models/trace/insights/Cache.ts | others": {"message": "മറ്റുള്ള {PH1} എണ്ണം"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "അഭ്യർത്ഥിക്കുക"}, "models/trace/insights/Cache.ts | title": {"message": "കാഷെ കാലയളവ് കാര്യക്ഷമമായി ഉപയോഗിക്കുക"}, "models/trace/insights/DOMSize.ts | description": {"message": "ഒരു വലിയ DOM-ന് സ്റ്റൈൽ കാൽക്കുലേഷനുകളുടെയും ലേഔട്ട് റീഫ്ലോകളുടെയും ദൈർഘ്യം വർദ്ധിപ്പിക്കാൻ കഴിയും, ഇത് പേജ് റെസ്പോൺസീവ്നെസിനെ സ്വാധീനിക്കും. വലിയ DOM, മെമ്മറി ഉപയോഗം വർദ്ധിപ്പിക്കുകയും ചെയ്യുന്നു. [അമിതമായ DOM വലുപ്പം ഒഴിവാക്കുന്നത് എങ്ങനെയെന്ന് അറിയുക](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "എലമെന്റ്"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "മിക്ക കുട്ടികളും"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM ഡെപ്ത്"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "സ്ഥിതിവിവരക്കണക്ക്"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM വലുപ്പം ഒപ്‌റ്റിമൈസ് ചെയ്യുക"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "മൊത്തം എലമെന്റുകൾ"}, "models/trace/insights/DOMSize.ts | value": {"message": "മൂല്യം"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "നിങ്ങളുടെ ആദ്യ നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയാണ് ഏറ്റവും പ്രധാനപ്പെട്ടത്.  റീഡയറക്‌ടുകൾ ഒഴിവാക്കിയും വേഗത്തിലുള്ള സെർവർ പ്രതികരണം ഉറപ്പാക്കിയും ടെക്‌സ്‌റ്റ് കംപ്രഷൻ പ്രവർത്തനക്ഷമമാക്കിയും അതിന്റെ ലാറ്റൻസി കുറയ്ക്കുക."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "റീഡയറക്റ്റുകൾ ഉണ്ടായിരുന്നു ({PH1} റീഡയറക്റ്റുകൾ, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "സെർവർ സാവധാനമാണ് പ്രതികരിച്ചത് ({PH1} നിരീക്ഷിച്ചത്)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "കംപ്രഷനുകളൊന്നും പ്രയോഗിച്ചിട്ടില്ല"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "റീഡയറക്റ്റുകൾ ഒഴിവാക്കുന്നു"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "സെർവർ വേഗത്തിൽ പ്രതികരിക്കുന്നു ({PH1} നിരീക്ഷിച്ചത്)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ടെക്‌സ്റ്റ് കംപ്രഷൻ ബാധകമാക്കുന്നു"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "റീഡയറക്‌റ്റുകൾ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "സെർവർ പ്രതികരണ സമയം"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ഡോക്യുമെന്റ് അഭ്യർത്ഥിച്ചതിന് ശേഷമുള്ള പ്രതികരണ സമയം"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "കംപ്രസ് ചെയ്യാത്ത ഡൗൺലോഡ്"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ഡ്യൂപ്ലിക്കേറ്റ് ചെയ്ത ബെെറ്റുകൾ"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "ഉറവിടം"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "നെറ്റ്‌വർക്ക് ആക്റ്റിവിറ്റി ഉപയോഗിക്കുന്ന അനാവശ്യ ബൈറ്റുകൾ കുറയ്ക്കുന്നതിന് ബണ്ടിലുകളിൽ നിന്ന് വലുതും ഡ്യൂപ്ലിക്കേറ്റുമായ JavaScript മൊഡ്യൂളുകൾ നീക്കം ചെയ്യുക."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "ഡ്യൂപ്ലിക്കേറ്റ് ചെയ്ത JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "ടെക്സ്റ്റ് സ്ഥിരമായി ദൃശ്യമാകുന്നുവെന്ന് ഉറപ്പാക്കാൻ [font-display](https://developer.chrome.com/blog/font-display) എന്നത് swap അല്ലെങ്കിൽ optional ആയി സജ്ജീകരിക്കുന്നത് പരിഗണിക്കുക. ലേഔട്ട് ഷിഫ്‌റ്റുകൾ ലഘൂകരിക്കാൻ [ഫോണ്ട് മെട്രിക്ക് അസാധുവാക്കലുകൾ](https://developer.chrome.com/blog/font-fallbacks) ഉപയോഗിച്ച് swap കൂടുതലായി ഒപ്‌റ്റിമൈസ് ചെയ്യാം."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "ഫോണ്ട്"}, "models/trace/insights/FontDisplay.ts | title": {"message": "ഫോണ്ട് ഡിസ്‌പ്ലേ"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "പാഴാക്കിയ സമയം"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(അജ്ഞാതം)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "സാധാരണഗതിയിൽ ലേഔട്ട് ജ്യാമിതി വായിക്കുന്ന പല API-കളും, സ്റ്റൈലുകളും ലേഔട്ടും കണക്കാക്കാൻ സ്ക്രിപ്റ്റ് എക്സിക്യൂഷൻ താൽക്കാലികമായി നിർത്താൻ റെൻഡറിംഗ് എഞ്ചിനെ നിർബന്ധിക്കും. [നിർബന്ധിത റീഫ്ലോ](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts), അതിന്റെ മിറ്റിഗേഷനുകൾ എന്നിവയെ കുറിച്ച് കൂടുതലറിയുക."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "സ്റ്റാക്ക് ട്രെയ്‌സ്"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "നിർബന്ധിത റീഫ്ലോ"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "പ്രധാന ഫംഗ്ഷൻ കോൾ"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "മൊത്തം റീഫ്ലോ സമയം"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ആട്രിബ്യൂട്ട് ഇല്ല]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "ചിത്രങ്ങളുടെ ഡൗൺലോഡ് സമയം കുറയ്ക്കുന്നത് പേജിന്റെയും LCP-യുടെയും കണക്കാക്കുന്ന ലോഡ് സമയം മെച്ചപ്പെടുത്താൻ സഹായിക്കും. [ചിത്രത്തിന്റെ വലുപ്പം ഒപ്റ്റിമൈസ് ചെയ്യുന്നതിനെക്കുറിച്ച് കൂടുതലറിയുക](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ഏകദേശം {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ഒപ്റ്റിമൈസ് ചെയ്യാവുന്ന ചിത്രങ്ങളൊന്നുമില്ല"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ഫയൽ വലുപ്പം ഒപ്റ്റിമൈസ് ചെയ്യുക"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "മറ്റുള്ള {PH1} എണ്ണം"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ചിത്രത്തിന്റെ ഡെലിവറി മെച്ചപ്പെടുത്തൂ"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "ഇമേജ് കംപ്രഷൻ ഫാക്ടർ വർദ്ധിപ്പിക്കുന്നത് ഈ ചിത്രത്തിന്റെ ഡൗൺലോഡ് വലുപ്പം മെച്ചപ്പെടുത്തും."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "മോഡേൺ ഇമേജ് ഫോർമാറ്റ് (WebP, AVIF) ഉപയോഗിക്കുന്നതോ ഇമേജ് കംപ്രഷൻ വർദ്ധിപ്പിക്കുന്നതോ ഈ ചിത്രത്തിന്റെ ഡൗൺലോഡ് വലുപ്പം മെച്ചപ്പെടുത്തിയേക്കാം."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ഈ ചിത്ര ഫയൽ അതിന്റെ ദൃശ്യമാകുന്ന ഡയമെൻഷനുകൾക്ക് ({PH2}) ആവശ്യമുള്ളതിനേക്കാൾ വലുതാണ് ({PH1}). ചിത്രത്തിന്റെ ഡൗൺലോഡ് വലുപ്പം കുറയ്ക്കാൻ റെസ്പോൺസീവ് ചിത്രങ്ങൾ ഉപയോഗിക്കുക."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF-കൾക്ക് പകരം വീഡിയോ ഫോർമാറ്റുകൾ ഉപയോഗിക്കുന്നത് ആനിമേറ്റ് ചെയ്ത ഉള്ളടക്കത്തിന്റെ ഡൗൺലോഡ് വലുപ്പം മെച്ചപ്പെടുത്തും."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "ദൈർഘ്യമേറിയ ഘട്ടം ഉപയോഗിച്ച് അന്വേഷണം ആരംഭിക്കുക. [കാലതാമസം കുറയ്‌ക്കാം](https://web.dev/articles/optimize-inp#optimize_interactions). പ്രോസസ് ചെയ്യുന്ന കാലയളവ് കുറയ്‌ക്കാൻ, [പ്രധാന-ത്രെഡ് നിരക്കുകൾ ഒപ്റ്റിമൈസ് ചെയ്യുക](https://web.dev/articles/optimize-long-tasks) (മിക്കപ്പോഴും JS)."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "സമയ ദൈർഘ്യം"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ഇൻപുട്ട് കാലതാമസം"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ഇടപഴകലുകളൊന്നും കണ്ടെത്തിയില്ല"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ഘട്ടം"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "അവതരണത്തിലെ കാലതാമസം"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "പ്രോസസ് ചെയ്യുന്നതിനുള്ള കാലയളവ്"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "ഘട്ടം പ്രകാരമുള്ള INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP ചിത്രം HTML-ൽ നിന്ന് പെട്ടെന്ന് [കണ്ടെത്താനാകുന്നത്](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) ആക്കുന്നതിലൂടെയും [ലേസി ലോഡിംഗ് ഒഴിവാക്കുന്നതിലൂടെയും](https://web.dev/articles/lcp-lazy-loading) LCP ഒപ്റ്റിമൈസ് ചെയ്യുക"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high ബാധകമാക്കി"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high ബാധകമാക്കണം"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ലേസി ലോഡ് ബാധകമാക്കിയിട്ടില്ല"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "ആദ്യ സ്റ്റാർട്ട് പോയിന്റിന് ശേഷം {PH1} കഴിഞ്ഞ് LCP ചിത്രം ലോഡ് ചെയ്തു."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP ഒന്നും കണ്ടെത്തിയില്ല"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP ഒരു ഇമേജ് അല്ലാത്തതിനാൽ LCP ഉറവിടങ്ങളൊന്നും കണ്ടെത്തിയില്ല"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "ആദ്യ ഡോക്യുമെന്റിൽ അഭ്യർത്ഥന കണ്ടെത്താനാകും"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP അഭ്യർത്ഥന കണ്ടെത്തൽ"}, "models/trace/insights/LCPPhases.ts | description": {"message": "ഓരോ [ഘട്ടത്തിനും പ്രത്യേക മെച്ചപ്പെടുത്തൽ സ്‌ട്രാറ്റജികളുണ്ട്](https://web.dev/articles/optimize-lcp#lcp-breakdown). LCP സമയത്തിന്റെ ഭൂരിഭാഗവും ഉറവിടങ്ങൾ ലോഡ് ചെയ്യുന്നതിനാണ് ചെലവഴിക്കേണ്ടത്, കാലതാമസം സംബന്ധിച്ചല്ല."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "സമയ ദൈർഘ്യം"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "എലമെന്റ് റെൻഡർ ഡിലേ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ഫീൽഡ് p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP ഒന്നും കണ്ടെത്തിയില്ല"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ഘട്ടം"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ഉറവിടം ലോഡ് ചെയ്യുന്നതിൽ കാലതാമസം"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "റിസോഴ്‌സ് ലോഡ് ചെയ്യാൻ എടുക്കുന്ന സമയം"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "ആദ്യത്തെ ബൈറ്റിനുള്ള സമയം"}, "models/trace/insights/LCPPhases.ts | title": {"message": "ഘട്ടം പ്രകാരമുള്ള LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "സ്ക്രിപ്റ്റ്"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "പാഴാക്കിയ ബൈറ്റുകൾ"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill-കളും ട്രാൻസ്‌ഫോമുകളും, പുതിയ JavaScript ഫീച്ചറുകൾ ഉപയോഗിക്കാൻ പഴയ ബ്രൗസറുകളെ പ്രവർത്തനക്ഷമമാക്കുന്നു. എന്നിരുന്നാലും, ആധുനിക ബ്രൗസറുകൾക്ക് പല ഫീച്ചറുകളും ആവശ്യമില്ല. നിങ്ങൾക്ക് പഴയ ബ്രൗസറുകളെ പിന്തുണയ്ക്കണമെന്നറിയില്ലെങ്കിൽ [ബേസ്‌ലൈൻ](https://web.dev/articles/baseline-and-polyfills) ഫീച്ചറുകൾ ട്രാൻസ്‌പൈൽ ചെയ്യാതിരിക്കാൻ, 'JavaScript ബിൽഡ് പ്രോസസ്' പരിഷ്‌കരിക്കുന്നത് പരിഗണിക്കുക. [എന്തുകൊണ്ടാണ് മിക്ക സൈറ്റുകൾക്കും ട്രാൻസ്‌പൈൽ ചെയ്യാതെ തന്നെ ES6+ കോഡ് വിന്യസിക്കാൻ കഴിയുന്നതെന്ന് മനസ്സിലാക്കുക](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "ലെഗസി JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/1.1-നെ അപേക്ഷിച്ച് HTTP/2, HTTP/3 എന്നിവ, മൾട്ടിപ്ലെക്‌സിംഗ് പോലുള്ള നിരവധി ആനുകൂല്യങ്ങൾ ഓഫർ ചെയ്യുന്നു. [മോഡേൺ HTTP ഉപയോഗിക്കുന്നതിനെ കുറിച്ച് കൂടുതലറിയുക](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "അഭ്യർത്ഥനകളൊന്നും HTTP/1.1 ഉപയോഗിച്ചില്ല"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "പ്രോട്ടോക്കോൾ"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "അഭ്യർത്ഥിക്കുക"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "മോഡേൺ HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ഉറവിടം"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "അഭ്യർത്ഥിക്കുക"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "ഉറവിടം"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "സമയം"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "കണക്കാക്കിയ LCP ലാഭം"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "ഉപയോഗിക്കാത്ത \"മുൻകൂട്ടി കണക്റ്റ് ചെയ്ത ലിങ്കുകൾ'. crossorigin ആട്രിബ്യൂട്ട് ശരിയായ രീതിയിൽ ഉപയോഗിക്കുന്നുണ്ടോയെന്ന് പരിശോധിക്കുക."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "അഭ്യർത്ഥന നൽകുന്നത് കുറയ്ക്കുക, ഉറവിടങ്ങളുടെ ഡൗൺലോഡ് വലുപ്പം കുറയ്ക്കുക, പേജ് ലോഡ് ചെയ്യുന്നത് മെച്ചപ്പെടുത്താൻ, അത്യാവശ്യമല്ലാത്ത ഉറവിടങ്ങളുടെ ഡൗൺലോഡ് വൈകിപ്പിക്കൽ എന്നിവ വഴി [നിരവധി നിർണ്ണായക അഭ്യർത്ഥനകൾ തുടർച്ചയായി നൽകുന്നത് ഒഴിവാക്കുക](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "നിങ്ങളുടെ ഏറ്റവും പ്രധാനപ്പെട്ട ഒറിജിനുകളിൽ [പ്രീകണക്റ്റ്](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ഹിന്റുകൾ ചേർക്കുക, എന്നാൽ 4 അല്ലെങ്കിൽ അതിൽ താഴെ മാത്രം ഉപയോഗിക്കാൻ ശ്രമിക്കുക."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "പ്രീകണക്റ്റ് കാൻഡിഡേറ്റുകൾ"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "പ്രധാന പാതയുടെ പരമാവധി പ്രതികരണ സമയം:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "റെൻഡറിംഗ് ടാസ്‌ക്കുകളെയൊന്നും നെറ്റ്‌വർക്ക് ഡിപ്പൻഡൻസികൾ സ്വാധീനിക്കുന്നില്ല"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "പ്രീകണക്റ്റ് ചെയ്യുന്നതിന് അധിക ഉറവിടങ്ങളൊന്നും നല്ല കാൻഡിഡേറ്റുകൾ അല്ല"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ഉറവിടങ്ങളൊന്നും പ്രീകണക്റ്റ് ചെയ്തിരുന്നില്ല"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[പ്രീകണക്റ്റ്](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ഹിന്റുകൾ പേജ് ലോഡ് ചെയ്യാൻ ആരംഭിക്കുമ്പോൾ തന്നെ കണക്ഷൻ സ്ഥാപിക്കാൻ ബ്രൗസറിനെ സഹായിക്കുന്നു, അതുവഴി ആ ഉറവിടത്തിലേക്കുള്ള ആദ്യ അഭ്യർത്ഥന നടത്തുമ്പോൾ സമയം ലാഭിക്കാം. പേജ് പ്രീകണക്റ്റ് ചെയ്തിട്ടുള്ള ഉറവിടങ്ങൾ ഇനിപ്പറയുന്നവയാണ്."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "മുൻകൂട്ടി കണക്റ്റ് ചെയ്ത ഉറവിടങ്ങൾ"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "നെറ്റ്‌വർക്ക് ഡിപ്പെൻഡൻസി ട്രീ"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4-ൽ കൂടുതൽ preconnect കണക്ഷനുകൾ കണ്ടെത്തി. ഇവ മിതമായ നിലയിൽ, ഏറ്റവും പ്രധാനപ്പെട്ട ഉറവിടങ്ങൾക്ക് മാത്രമായി ഉപയോഗിക്കുക."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "ഉപയോഗിക്കാത്ത പ്രീകണക്റ്റ്. പേജ് അഭ്യർത്ഥിക്കാൻ സാധ്യതയുള്ള ഉറവിടങ്ങൾക്ക് മാത്രം preconnect ഉപയോഗിക്കുക."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "ചെയിനുകളുടെ ദൈർഘ്യം കുറച്ചുകൊണ്ടോ, റിസോഴ്‌സുകളുടെ ഡൗൺലോഡ് വലുപ്പം കുറച്ചുകൊണ്ടോ, പേജ് ലോഡ് മെച്ചപ്പെടുത്തുന്നതിന് അനാവശ്യ റിസോഴ്‌സുകളുടെ ഡൗൺലോഡ് മാറ്റിവച്ചുകൊണ്ടോ നിർണായക അഭ്യർത്ഥനകളെ ചെയിൻ ചെയ്യുന്നത് ഒഴിവാക്കുക."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "അഭ്യർത്ഥനകൾ പേജിന്റെ പ്രാരംഭ റെൻഡറിനെ തടയുന്നു, ഇത് LCP-യിൽ കാലതാമസം വരുത്തിയേക്കാം. [വൈകിപ്പിക്കൽ അല്ലെങ്കിൽ ഇൻലൈനിംഗിന്](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ഈ നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനകളെ പ്രധാന പാതയിൽ നിന്ന് നീക്കാൻ കഴിയും."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "സമയ ദൈർഘ്യം"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ഈ നാവിഗേഷനായി, റെൻഡർ ബ്ലോക്കിംഗ് അഭ്യർത്ഥനകളൊന്നുമില്ല"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "അഭ്യർത്ഥിക്കുക"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "റെൻഡർ ബ്ലോക്കിംഗ് അഭ്യർത്ഥനകൾ"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "റീകാൽക്കുലേറ്റ് സ്റ്റൈൽ നിരക്കുകൾ ഉയർന്ന നിലയിൽ തുടരുകയാണെങ്കിൽ, സെലക്ടർ ഒപ്റ്റിമൈസേഷന് അവ കുറയ്ക്കാനാകും. ഉയർന്ന കാലഹരണപ്പെടൽ സമയം, ഉയർന്ന സ്ലോ-പാത്ത് % എന്നിവ ഉപയോഗിച്ച് [സെലക്ടറുകളെ ഒപ്റ്റിമൈസ് ചെയ്യുക](https://developer.chrome.com/docs/devtools/performance/selector-stats). ലളിതമായ സെലക്‌ടറുകൾ, കുറച്ച് സെലക്‌ടറുകൾ, ചെറിയ DOM, ഷാലോ DOM എന്നിവയെല്ലാം പൊരുത്തപ്പെടൽ നിരക്ക് കുറയ്ക്കും."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "പ്രതികരണ സമയം"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS സെലക്‌ടർ ഡാറ്റയൊന്നും കണ്ടെത്തിയില്ല. പെർഫോമൻസ് പാനൽ ക്രമീകരണത്തിൽ CSS സെലക്‌ടർ സ്ഥിതിവിവരക്കണക്കുകൾ പ്രവർത്തനക്ഷമമാക്കേണ്ടതുണ്ട്."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "പൊരുത്തം കണ്ടെത്താൻ നടത്തിയ ശ്രമങ്ങൾ"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "പൊരുത്തപ്പെടുന്നവയുടെ എണ്ണം"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS സെലക്‌ടർ നിരക്കുകൾ"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "മുൻനിര സെലക്‌ടർമാർ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "മൊത്തം"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "പ്രധാന ത്രെഡ് സമയം"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "മൂന്നാം കക്ഷി"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ട്രാൻസ്ഫർ വലുപ്പം"}, "models/trace/insights/ThirdParties.ts | description": {"message": "ലോഡിന്റെ പ്രകടനത്തെ മൂന്നാം കക്ഷി കോഡ് വലിയ രീതിയിൽ സ്വാധീനിക്കും. നിങ്ങളുടെ പേജിന്റെ ഉള്ളടക്കത്തിന് മുൻഗണന നൽകുന്നതിന് [മൂന്നാം കക്ഷി കോഡ് ലോഡ് ചെയ്യുന്നത് കുറയ്ക്കുകയും വൈകിപ്പിക്കുകയും ചെയ്യുക](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "മൂന്നാം കക്ഷികളെയൊന്നും കണ്ടെത്തിയില്ല"}, "models/trace/insights/ThirdParties.ts | title": {"message": "മൂന്നാം കക്ഷികൾ"}, "models/trace/insights/Viewport.ts | description": {"message": "മൊബൈലിനായി വ്യൂപോർട്ട് ഒപ്റ്റിമൈസ് ചെയ്തിട്ടില്ലെങ്കിൽ, ടാപ്പ് ഇടപഴകലുകൾ [300 ms വരെ വൈകിയേക്കാം](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "മൊബൈൽ ടാപ്പ് വൈകൽ"}, "models/trace/insights/Viewport.ts | title": {"message": "മൊബൈലിനായി വ്യൂപോർട്ട് ഒപ്റ്റിമൈസ് ചെയ്യൂ"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET അഭ്യർത്ഥനയിലൂടെ ലോഡ് ചെയ്ത പേജുകൾക്ക് മാത്രമേ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്ക് യോഗ്യതയുള്ളൂ."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX സ്റ്റാറ്റസ് കോഡുള്ള പേജുകൾ മാത്രമേ കാഷെ ചെയ്യാനാകൂ."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "കാഷെയിലായിരിക്കുമ്പോൾ JavaScript നിർവ്വഹിക്കാനുള്ള ശ്രമം Chrome കണ്ടെത്തി."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner അഭ്യർത്ഥിച്ച പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "ഫ്ലാഗുകൾ, ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു. ഈ ഉപകരണത്തിൽ ഇത് ലോക്കലായി പ്രവർത്തനക്ഷമമാക്കാൻ chrome://flags/#back-forward-cache സന്ദർശിക്കുക."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെ ഫീച്ചറിനെ കമാൻഡ് ലൈൻ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "ആവശ്യത്തിന് മെമ്മറി ഇല്ലാത്തതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെ ഫീച്ചറിനെ പ്രതിനിധി പിന്തുണയ്ക്കുന്നില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "പ്രീറെൻഡറർക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "രജിസ്റ്റർ ചെയ്ത ലിസണറുകൾ അടങ്ങുന്ന BroadcastChannel ഇൻസ്റ്റൻസ് ഉള്ളതിനാൽ പേജ് കാഷെ ചെയ്യാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store ഹെഡ്ഡർ ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "കാഷെ മനപ്പൂർവ്വം മായ്ച്ചു."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "മറ്റൊരു പേജ് കാഷെ ചെയ്യാൻ അനുവദിക്കുന്നതിന്, കാഷെ ചെയ്യുന്നതിൽ നിന്ന് ഈ പേജ് ഒഴിവാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "പ്ലഗിന്നുകൾ അടങ്ങുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "നിർവചിച്ചിട്ടില്ലാത്തത്"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "മീഡിയ ഉപകരണ ഡെസ്‌പാച്ചർ ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "പുറത്ത് കടക്കുമ്പോൾ മീഡിയ പ്ലേയർ പ്ലേ ചെയ്യുന്നുണ്ടായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API ഉപയോഗിക്കുന്നതും പ്ലേബാക്ക് നില സജ്ജീകരിച്ചിട്ടുള്ളതുമായ പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API ഉപയോഗിക്കുന്നതും പ്രവർത്തനം കൈകാര്യം ചെയ്യുന്ന സംവിധാനം സജ്ജീകരിച്ചിരിക്കുന്നതുമായ പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "സ്ക്രീൻ റീഡർ കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store ഉപയോഗിക്കുന്ന പേജിൽ കുക്കികൾ പ്രവർത്തനരഹിതമാക്കിയതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "ഡെഡിക്കേറ്റഡ് വർക്കർ അല്ലെങ്കിൽ വർക്ക്‌ലെറ്റ് ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "പുറത്ത് കടക്കുന്നതിന് മുമ്പ് ഈ ഡോക്യുമെന്റ് പൂർണ്ണമായി ലോഡ് ചെയ്തില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "പുറത്ത് കടക്കുമ്പോൾ ആപ്പ് ബാനർ സജീവമായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "പുറത്ത് കടക്കുമ്പോൾ Chrome പാസ്‌വേഡ് മാനേജർ സജീവമായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "പുറത്ത് കടക്കുമ്പോൾ DOM ഡിസ്റ്റിലേഷൻ പുരോഗതിയിലായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "പുറത്ത് കടക്കുമ്പോൾ DOM ഡിസ്റ്റില വ്യൂവർ സജീവമായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "സന്ദേശമയയ്ക്കൽ API ഉപയോഗിക്കുന്ന വിപുലീകരണങ്ങൾ കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "ദീർഘകാല കണക്ഷനുള്ള വിപുലീകരണങ്ങൾ ബാക്ക്/ഫോർവേഡ് കാഷെയിലേക്ക് പ്രവേശിക്കും മുമ്പ് കണക്ഷൻ അവസാനിപ്പിക്കണം."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "ദീർഘകാല കണക്ഷനുള്ള വിപുലീകരണങ്ങൾ ബാക്ക്-ഫോർവേഡ് കാഷെയിലെ ഫ്രെയിമുകൾക്ക് സന്ദേശങ്ങൾ അയയ്ക്കാൻ ശ്രമിച്ചു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "വിപുലീകരണങ്ങൾ കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "പുറത്ത് കടക്കുമ്പോൾ, ഫോം വീണ്ടും സമർപ്പിക്കൽ പോലുള്ള മോഡൽ ഡയലോഗോ HTTP പാസ്‌വേഡ് ഡയലോഗോ പേജിൽ കാണിച്ചിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "പുറത്ത് കടക്കുമ്പോൾ ഓഫ്‌ലൈൻ പേജ് കാണിച്ചിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "പുറത്ത് കടക്കുമ്പോൾ, മെമ്മറി ഇല്ലാത്തതിന്റെ ഇന്റർവെൻഷൻ ബാർ സജീവമായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "പുറത്ത് കടക്കുമ്പോൾ അനുമതി അഭ്യർത്ഥനകൾ ഉണ്ടായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "പുറത്ത് കടക്കുമ്പോൾ പോപ്പ് അപ്പ് ബ്ലോക്കർ സജീവമായിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "പുറത്ത് കടക്കുമ്പോൾ സുരക്ഷിത ബ്രൗസിംഗിന്റെ വിശദാംശങ്ങൾ കാണിച്ചിരുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "സുരക്ഷിത ബ്രൗസിംഗ് ഈ പേജിനെ ദുരുപയോഗമായി കണക്കാക്കിയതിനാൽ പോപ്പ് അപ്പ് ബ്ലോക്ക് ചെയ്തു."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "പേജ് ബാക്ക്/ഫോർവേഡ് കാഷെയിലായിരുന്നപ്പോൾ സർവീസ് വർക്കർ സജീവമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ഡോക്യുമെന്റ് പിശക് കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames ഉപയോഗിക്കുന്ന പേജുകൾ bfcache-യിൽ സംഭരിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "മറ്റൊരു പേജ് കാഷെ ചെയ്യാൻ അനുവദിക്കുന്നതിന്, കാഷെ ചെയ്യുന്നതിൽ നിന്ന് ഈ പേജ് ഒഴിവാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "മീഡിയ സ്ട്രീം ആക്‌സസ് അനുവദിച്ച പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള അനുമതിയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "ചില, ഉൾച്ചേർത്ത ഉള്ളടക്കം (ഉദാ. PDF-കൾ) അടങ്ങുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്-ഫോർവേഡ് കാഷെക്ക് യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "സജീവമായ IndexedDB കണക്ഷനുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB ഇവന്റ് കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "യോഗ്യതയില്ലാത്ത API-കൾ ഉപയോഗിച്ചു."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "വിപുലീകരണങ്ങൾ വഴി JavaScript ഉൾച്ചേർത്തിരിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "വിപുലീകരണങ്ങൾ വഴി StyleSheet ഉൾച്ചേർത്തിരിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "ആന്തരിക പിശക്."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "ചില JavaScript നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയ്ക്ക് Cache-Control: no-store ഹെഡ്ഡർ ഉള്ള റിസോഴ്‌സ് ലഭിച്ചതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "സജീവമായി നിലനിർത്താനുള്ള അഭ്യർത്ഥന കാരണം ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "കീബോർഡ് ലോക്ക് ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "പുറത്ത് കടക്കുന്നതിന് മുമ്പ് ഈ പേജ് പൂർണ്ണമായി ലോഡ് ചെയ്തില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "പ്രധാന ഉറവിടത്തിൽ cache-control:no-cache ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "പ്രധാന ഉറവിടത്തിൽ cache-control:no-store ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ നിന്ന് പേജ് പുനഃസ്ഥാപിക്കുന്നതിന് മുമ്പ് നാവിഗേഷൻ റദ്ദാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "സജീവമായ ഒരു നെറ്റ്‌വർക്ക് കണക്ഷൻ വളരെയധികം ഡാറ്റ സ്വീകരിച്ചതിനാൽ കാഷെയിൽ നിന്ന് പേജ് ഒഴിവാക്കി. കാഷെ ചെയ്തിരിക്കുമ്പോൾ ഒരു പേജിന് സ്വീകരിക്കാനാകുന്ന ഡാറ്റയുടെ അളവ് Chrome പരിമിതപ്പെടുത്തിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "inflight fetch() അല്ലെങ്കിൽ XHR ഉള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "സജീവമായ ഒരു നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയിൽ റീഡയറക്ട് ചെയ്യൽ ഉൾപ്പെട്ടതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ നിന്ന് പേജ് ഒഴിവാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "ഒരു നെറ്റ്‌വർക്ക് കണക്ഷൻ ദീർഘനേരം സജീവമായിരുന്നതിനാൽ കാഷെയിൽ നിന്ന് പേജ് ഒഴിവാക്കി. കാഷെ ചെയ്തിരിക്കുമ്പോൾ ഒരു പേജിന് ഡാറ്റ സ്വീകരിക്കാനാകുന്ന സമയത്തിന്റെ അളവ് Chrome പരിമിതപ്പെടുത്തിയിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "സാധുവായ പ്രതികരണ ഹെഡ് ഇല്ലാത്ത പേജുകൾക്ക് ബാക്ക്-ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "പ്രധാന ഫ്രെയിം അല്ലാത്ത ഫ്രെയിമിലാണ് നാവിഗേഷൻ നടന്നത്."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "സജീവമായ indexed DB ഇടപാടുകളുള്ള പേജിന് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "in-flight നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "in-flight fetch നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "in-flight നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "in-flight XHR നെറ്റ്‌വർക്ക് അഭ്യർത്ഥനയുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "ചിത്രത്തിനുള്ളിൽ ചിത്രം ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "പ്രിന്റിംഗ് UI കാണിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "'window.open()' ഉപയോഗിച്ച് പേജ് തുറക്കുകയും മറ്റൊരു ടാബിൽ അതിന്റെ റഫറൻസ് അടങ്ങിയിരിക്കുകയും ചെയ്യുന്നു, അല്ലെങ്കിൽ പേജ് പുതിയൊരു വിൻഡോ തുറന്നിരിക്കുന്നു."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിലുള്ള പേജിന്റെ റെൻഡറർ പ്രോസസ് ക്രാഷായി."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിലുള്ള പേജിന്റെ റെൻഡറർ പ്രോസസ് ഇല്ലാതാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ഓഡിയോ ക്യാപ്‌ചർ അനുമതികൾ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "സെൻസർ അനുമതികൾ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "പശ്ചാത്തലം സമന്വയിപ്പിക്കലോ fetch അനുമതികളോ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI അനുമതികൾ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "അറിയിപ്പുകൾക്കുള്ള അനുമതികൾ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "സ്റ്റോറേജ് ആക്‌സസ് അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "വീഡിയോ ക്യാപ്‌ചർ അനുമതികൾ അഭ്യർത്ഥിച്ചിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "HTTP / HTTPS എന്ന URL സ്‌കീമുള്ള പേജുകൾ മാത്രമേ കാഷെ ചെയ്യാനാകൂ."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "പേജ് ബാക്ക്/ഫോർവേഡ് കാഷെയിലായിരിക്കുമ്പോൾ ഒരു സർവീസ് വർക്കർ ക്ലെയിം ചെയ്തു."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിലുള്ള പേജിലേക്ക് MessageEvent അയയ്ക്കാൻ ഒരു സർവീസ് വർക്കർ ശ്രമിച്ചു."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "പേജ് ബാക്ക്/ഫോർവേഡ് കാഷെയിലായിരുന്നപ്പോൾ ServiceWorker അൺരജിസ്റ്റർ ചെയ്തു."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "സർവീസ് വർക്കർ സജീവമാക്കിയതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ നിന്ന് പേജ് ഒഴിവാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome റീസ്റ്റാർട്ട് ചെയ്തതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ എൻട്രികൾ മായ്ച്ചു."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "പേജിലെ iframe ആരംഭിച്ച ഒരു നാവിഗേഷൻ പൂർത്തിയാക്കിയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "സബ്റിസോഴ്‌സിൽ cache-control:no-cache ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "സബ്റിസോഴ്‌സിൽ cache-control:no-store ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിലെ പരമാവധി സമയം കവിഞ്ഞതിനാൽ പേജ് കാലഹരണപ്പെട്ടു."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "ബാക്ക്/ഫോർവേഡ് കാഷെയിലേക്ക് പ്രവേശിക്കുമ്പോൾ പേജ് ടൈമൗട്ടായി (ദീർഘമായ റണ്ണിംഗുള്ള pagehide കൈകാര്യം ചെയ്യുന്ന സംവിധാനം കാരണമാകാനാണ് സാധ്യത)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "പേജിന്റെ പ്രധാന ഫ്രെയിമിൽ അൺലോഡ് ചെയ്യൽ കൈകാര്യം ചെയ്യുന്ന സംവിധാനമുണ്ട്."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "പേജിന്റെ ഉപ ഫ്രെയിമിൽ അൺലോഡ് ചെയ്യൽ കൈകാര്യം ചെയ്യുന്ന സംവിധാനമുണ്ട്."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ബ്രൗസർ, ഉപയോക്തൃ ഏജന്റ് അസാധുവാക്കൽ ഹെഡ്ഡർ മാറ്റി."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "വീഡിയോയോ ഓഡിയോയോ റെക്കോർഡ് ചെയ്യാൻ ആക്‌സസ് നൽകിയിട്ടുള്ള പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC ഉപയോഗിച്ചതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket ഉപയോഗിച്ചതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport ഉള്ള പേജുകൾക്ക് ബാക്ക്/ഫോർവേഡ് കാഷെയിൽ പ്രവേശിക്കാനാകില്ല."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport ഉപയോഗിച്ചതിനാൽ ബാക്ക്/ഫോർവേഡ് കാഷെ പ്രവർത്തനരഹിതമാക്കി."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR ഉപയോഗിക്കുന്ന പേജുകൾക്ക് നിലവിൽ ബാക്ക്/ഫോർവേഡ് കാഷെയ്ക്കുള്ള യോഗ്യതയില്ല."}}