{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers हाताळणीमध्ये वाइल्डकार्ड चिन्हाने (*) ऑथोरायझेशन कव्हर केले जाणार नाही."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "डीफॉल्ट Cast इंटिग्रेशन बंद करण्यासाठी, -internal-media-controls-overlay-cast-button सिलेक्टर वापरण्याऐवजी disableRemotePlayback अ‍ॅट्रिब्यूट वापरला जावा."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS स्वरूपाचे मूल्य slider-vertical प्रमाणित केलेले नाही आणि काढून टाकले जाईल."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "ज्यांच्या URL मध्ये काढून टाकलेले व्हाइटस्पेस \\(n|r|t) वर्ण आणि यापेक्षा कमी वर्ण (<) होते अशा स्रोत विनंत्या ब्लॉक केल्या गेल्या आहेत. हे स्रोत लोड करण्यासाठी, कृपया नवीन ओळी काढून टाका आणि घटक अ‍ॅट्रिब्यूट मूल्यांसारख्या ठिकाणांमधील यापेक्षा कमी वर्ण एन्कोड करा."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() डेप्रिकेट केले गेले आहे, त्याऐवजी मानक API: नेव्हिगेशन वेळ २ वापरा."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() डेप्रिकेट केले गेले आहे, त्याऐवजी मानक API: पेंट वेळ वापरा."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() डेप्रिकेट केले गेले आहे, त्याऐवजी नेव्हिगेशन वेळ २ मध्ये मानक API: nextHopProtocol वापरा."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) वर्णाचा समावेश असलेल्या कुकी कापल्या जाण्याऐवजी नाकारल्या जातील."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain सेट करून एकच ओरिजिन धोरण शिथिल करणे डेप्रिकेट केले गेले आहे आणि ते बाय डीफॉल्ट बंद केले जाईल. ही डेप्रिकेशन चेतावणी document.domain सेट करून सुरू केल्या गेलेल्या क्रॉस-ओरिजिन अ‍ॅक्सेससाठी आहे."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "क्रॉस ओरिजिन आयफ्रेमवरून window.alert ट्रिगर करणे डेप्रिकेट केलेले आहे आणि भविष्यात ते काढून टाकले जाईल."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "क्रॉस ओरिजिन आयफ्रेमवरून window.confirm ट्रिगर करणे डेप्रिकेट केलेले आहे आणि भविष्यात ते काढून टाकले जाईल."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "डेटासाठी सपोर्ट: SVGUseElement मधील URLs डेप्रिकेट केलेल्या आहेत आणि त्या भविष्यात काढून टाकल्या जातील."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() आणि watchPosition() आता असुरक्षित ओरिजिनवर काम करत नाहीत. हे वैशिष्ट्य वापरण्यासाठी, तुम्ही तुमचे अ‍ॅप्लिकेशन HTTPS सारख्या सुरक्षित ओरिजिनवर स्विच करणे विचारात घ्यावे. अधिक तपशिलांसाठी https://goo.gle/chrome-insecure-origins पहा."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "असुरक्षित ओरिजिनवर getCurrentPosition() आणि watchPosition() डेप्रिकेट केले गेले आहेत. हे वैशिष्ट्य वापरण्यासाठी, तुम्ही तुमचे अ‍ॅप्लिकेशन HTTPS सारख्या सुरक्षित ओरिजिनवर स्विच करणे विचारात घ्यावे. अधिक तपशिलांसाठी https://goo.gle/chrome-insecure-origins पहा."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() आता असुरक्षित ओरिजिनवर काम करत नाही. हे वैशिष्ट्य वापरण्यासाठी, तुम्ही तुमचे अ‍ॅप्लिकेशन HTTPS सारख्या सुरक्षित ओरिजिनवर स्विच करणे विचारात घ्यावे. अधिक तपशिलांसाठी https://goo.gle/chrome-insecure-origins पहा."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> किंवा <section> मध्ये असा <h1> टॅग आढळला आहे, ज्याचा फाँट आकार नमूद केलेला नाही. भविष्यात या ब्राउझरमध्ये शीर्षकाच्या मजकुराचा आकार बदलणार आहे. अधिक माहितीसाठी https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 पहा."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate हे डेप्रिकेट केले आहे. कृपया त्याऐवजी RTCPeerConnectionIceErrorEvent.address किंवा RTCPeerConnectionIceErrorEvent.port वापरा."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "डिजिटल क्रेडेंशियलच्या navigator.credentials.get() विनंतीसाठीचा हा फॉरमॅट डेप्रिकेट झाला आहे, कृपया नवीन फॉरमॅट वापरण्यासाठी तुमचा कॉल अपडेट करा."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment सेवा कर्मचारी इव्हेंटमधील व्यापाऱ्याचा ओरिजिन आणि आर्बिट्ररी डेटा डेप्रिकेट केला आहे आणि तो काढून टाकला जाईल: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "वेबसाइटने फक्त तिच्या वापरकर्त्यांच्या विशेषाधिकार असलेल्या नेटवर्क स्थितीमुळे अ‍ॅक्सेस करता आलेल्या नेटवर्कला सबरिसोर्सची विनंती केली. या विनंत्या सार्वजनिक नसलेली डिव्हाइस आणि सर्व्हर इंटरनेटवर उघड करतात, ज्यामुळे क्रॉस-साइट विनंती बनावटीकरण (CSRF) हल्ल्याचा आणि/किंवा माहितीच्या गळतीचा धोका वाढतो. हे धोके कमी करण्यासाठी, सार्वजनिक नसलेल्या सबरिसोर्सला केलेल्या विनंत्या असुरक्षित संदर्भांवरून केल्या असताना Chrome डेप्रिकेट करते, आणि त्या ब्लॉक करणे सुरू करेल."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "त्याचे वर्तन आणखी अचूकपणे दाखवण्यासाठी, joinAdInterestGroup() ला पास केलेल्या InterestGroups च्या dailyUpdateUrl या फील्डचे नाव बदलून updateUrl असे केले आहे."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator डेप्रिकेट केले आहे. त्याऐवजी कृपया Intl.Segmenter वापरा."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS चा शेवट .css फाइल एक्स्टेंशनमध्ये होत नसल्यास, त्या file: URL वरून डाउनलोड केल्या जाऊ शकत नाहीत."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "तपशिलातील बदलामुळे, remove() चे असिंक्रोनस रेंज काढून टाकणे रद्द करण्यासाठी SourceBuffer.abort() वापरणे डेप्रिकेट केले आहे. भविष्यात सपोर्ट काढून टाकला जाईल. तुम्ही त्याऐवजी updateend इव्‍हेंट ऐका. abort() फक्त असिंक्रोनस मीडिया जोडणी रद्द करण्यासाठी किंवा पार्सर स्थिती रीसेट करण्यासाठी उद्देशित आहे."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "तपशिलातील बदलामुळे, बफर केलेल्या कोणत्याही फ्रेमच्या सर्वोच्च प्रेझेंटेशन टाइमस्टँपच्या खाली MediaSource.duration सेट करणे डेप्रिकेट केले गेले आहे. कापलेला बफर केलेला मीडिया अस्पष्टरीत्या काढून टाकण्यास असलेला सपोर्ट भविष्यात काढून टाकला जाईल. त्याऐवजी तुम्ही सर्व sourceBuffers वर सुस्पष्ट remove(newDuration, oldDuration) करावे, जेथे newDuration < oldDuration आहे."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions मध्ये sysex नमूद केलेले नसले तरीही वेब MIDI वापरण्याची परवानगी मागेल."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "असुरक्षित ओरिजिनमधील सूचना API आता वापरला जाऊ शकत नाही. तुम्ही तुमचे अ‍ॅप्लिकेशन HTTPS सारख्या सुरक्षित ओरिजिनवर स्विच करणे विचारात घ्यावे. अधिक तपशिलांसाठी https://goo.gle/chrome-insecure-origins पहा."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "क्रॉस-ओरिजिन आयफ्रेमला आता सूचना API साठी परवानगीची विनंती केली जाऊ शकत नाही. तुम्ही उच्च पातळीवरील फ्रेमला परवानगीची विनंती करणे किंवा त्याऐवजी नवीन विंडो उघडणे विचारात घ्यावे."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap मधील imageOrientation: 'none' हा पर्याय डेप्रिकेट केलेला आहे. कृपया त्याऐवजी {imageOrientation: 'from-image'} पर्यायासह createImageBitmap वापरा."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "तुमचा भागीदार अप्रचलित (D)TLS आवृत्ती निगोशिएट करत आहे. याचे निराकरण करण्यासाठी कृपया तुमच्या भागीदाराशी संपर्क साधा."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "इमेज, व्हिडीओ आणि कॅन्व्हास टॅगवर overflow: visible नमूद केल्याने ते घटकांच्या मर्यादेबाहेरील व्हिज्युअल आशय तयार करू शकतात. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md पहा."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments हे डेप्रिकेट केले आहे. कृपया त्याऐवजी पेमेंट हँडलरसाठी जस्ट-इन-टाइम इंस्टॉल वापरा."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "तुमच्या PaymentRequest कॉलने आशय सुरक्षा धोरण (CSP) connect-src डिरेक्टिव्ह बायपास केले. बायपास डेप्रिकेट केला आहे. कृपया PaymentRequest API (supportedMethods फील्डमध्ये) मधून तुमच्या CSP connect-src डिरेक्टिव्हमध्ये पेमेंट पद्धत आयडेंटिफायर जोडा."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent हे डेप्रिकेट केले आहे. कृपया त्याऐवजी मानक navigator.storage चा वापर करा."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> पॅरेंटसह <source src> अवैध आहे आणि म्हणून त्याच्याकडे दुर्लक्ष केले गेले आहे. त्याऐवजी कृपया <source srcset> वापरा."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame ही विक्रेत्याशी संबंधित पद्धत आहे. कृपया त्याऐवजी cancelAnimationFrame ही साधारण पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame ही विक्रेत्याशी संबंधित पद्धत आहे. कृपया त्याऐवजी requestAnimationFrame ही साधारण पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Document.fullscreenElement ही पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Element.requestFullscreen() ही पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Element.requestFullscreen() ही पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Document.exitFullscreen() ही पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Document.exitFullscreen() ही पद्धत वापरा."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen ही पद्धत डेप्रिकेट केली आहे. कृपया त्याऐवजी Document.fullscreenEnabled ही पद्धत वापरा."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "आम्ही डेप्रिकेट करत आहोत API chrome.privacy.websites.privacySandboxEnabled, पण ते M113 रिलीझ होईपर्यंत बॅकवर्ड कंपॅटिबिलिटीसाठी अ‍ॅक्टिव्ह राहील. त्याऐवजी, कृपया chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled आणि chrome.privacy.websites.adMeasurementEnabled वापरा. पहा https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement ही मर्यादा काढून टाकली गेली आहे. या मर्यादेसाठी तुम्ही false मूल्य नमूद केले आहे, ज्याचा अर्थ काढून टाकलेली SDES key negotiation पद्धत वापरण्याचा प्रयत्न असा लावला जातो. ही कार्यक्षमता काढून टाकली गेली आहे; त्याऐवजी DTLS key negotiation ला सपोर्ट करणारी सेवा वापरा."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement ही मर्यादा काढून टाकली गेली आहे. या मर्यादेसाठी तुम्ही true मूल्य नमूद केले आहे, ज्याचा कोणताही परिणाम झाला नाही, पण नीटनेटकेपणासाठी तुम्ही ही मर्यादा काढून टाकू शकता."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "कॉलबॅकवर आधारित getStats() ही पद्धत डेप्रिकेट केली आहे आणि ती काढून टाकली जाईल. त्याऐवजी तपशिलांची पूर्तता करणारी getStats() ही पद्धत वापरा."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() ही पद्धत डेप्रिकेट केली आहे. त्याऐवजी कृपया Selection.modify() ही पद्धत वापरा."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ज्यांच्या URL मध्ये एम्बेड केलेली क्रेडेंशियल (उदा. **********************/) आहेत अशा सबरिसोर्स विनंत्या ब्लॉक केल्या गेल्या आहेत."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy पर्याय डेप्रिकेट केला गेला आहे आणि तो काढून टाकला जाईल."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedA<PERSON>yBuffer ला क्रॉस-ओरिजिन प्रतिबंधाची आवश्यकता असेल. अधिक तपशिलांसाठी https://developer.chrome.com/blog/enabling-shared-array-buffer/ पहा."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "वापरकर्ता अ‍ॅक्टिव्हेशनशिवाय speechSynthesis.speak() डेप्रिकेट केले गेले आहे आणे ते काढून टाकले जाईल."}, "generated/Deprecation.ts | UnloadHandler": {"message": "अनलोड इव्हेंटचे श्रोते हे डेप्रिकेट करून काढून टाकले जातील."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer वापरणे सुरू ठेवण्यासाठी एक्स्टेंशननी क्रॉस-ओरिजिन प्रतिबंधाची निवड करावी. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ पहा."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ॲट्रिब्यूट डेप्रिकेट केलेला आहे, त्याऐवजी GPUAdapterInfo isFallbackAdapter ॲट्रिब्यूट वापरा."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest मध्ये UTF-16 ला प्रतिसाद json चा सपोर्ट नाही"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "मुख्य थ्रेडवरील सिंक्रोनस XMLHttpRequest त्याच्या अंतिम वापरकर्त्याच्या अनुभवावर होणार्‍या अपायकारक परिणामामुळे डेप्रिकेट केले गेले आहे. अधिक मदतीसाठी, https://xhr.spec.whatwg.org/ पहा."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "अ‍ॅनिमेशन"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "घटक वापरकर्त्याच्या परस्परसंवादाशिवाय हलतात, तेव्हा लेआउट शिफ्ट होतात. [लेआउट शिफ्टची कारणे तपासा](https://web.dev/articles/optimize-cls), जसे की, पेज लोड होत असताना घटक जोडले किंवा काढून टाकले जातात किंवा त्यांचे फॉंट बदलले जातात."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "फॉंटची विनंती"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "इंजेक्ट केलेली आयफ्रेम"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "लेआउट शिफ्ट क्लस्टर @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "कोणतीही लेआउट शिफ्ट कल्प्रिट डिटेक्ट करता आली नाहीत"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "कोणताही लेआउट शिफ्ट केला नाही"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "लेआउट शिफ्ट कल्प्रिट"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "टॉप लेआउट शिफ्ट कल्प्रिट"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "आकार न दिलेला इमेजचा घटक"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "वाईट क्लस्टर"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "सर्वात वाईट लेआउट शिफ्ट क्लस्टर"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "कॅशे TTL"}, "models/trace/insights/Cache.ts | description": {"message": "दीर्घकाळ कॅशे लाइफटाइम हे तुमच्या पेजला वारंवार भेट देण्याचे प्रमाण वाढवू शकते. [अधिक जाणून घ्या](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "कार्यक्षम कॅशे धोरणे असलेल्या कोणत्याही विनंत्या नाहीत"}, "models/trace/insights/Cache.ts | others": {"message": "इतर {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "विनंती"}, "models/trace/insights/Cache.ts | title": {"message": "कार्यक्षम कॅशे आजीवन वापरा"}, "models/trace/insights/DOMSize.ts | description": {"message": "मोठा DOM हा शैलीच्या गणनांचा आणि लेआउट रीफ्लोचा कालावधी वाढवू शकतो, ज्यामुळे पेजच्या प्रतिसाद देण्याच्या क्षमतेवर परिणाम होत आहे. मोठ्या DOM मुळे मेमरीचा वापरदेखील वाढेल. [अतिरिक्त DOM आकार कसा टाळावा याबद्दल जाणून घ्या](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "घटक"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "बहुतांश लहान मुलांसाठी"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM खोली"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "आकडेवारी"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM चा आकार ऑप्टिमाइझ करा"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "एकूण घटक"}, "models/trace/insights/DOMSize.ts | value": {"message": "मूल्य"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "तुमची पहिली नेटवर्क विंनती सर्वात महत्त्वाची असते.  रीडिरेक्‍ट टाळून, जलद सर्व्हर प्रतिसादाची खात्री करून आणि मजकूर काँप्रेशन सुरू करून त्याची लेटन्सी कमी करा."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "यामध्ये रीडिरेक्‍ट होती ({PH1} रीडिरेक्‍ट, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "सर्व्हरने हळू प्रतिसाद दिला (निरीक्षण केलेले {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "काँप्रेशन लागू केले गेले नाही"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "रीडिरेक्ट टाळा"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "सर्व्हर जलद प्रतिसाद देतो (निरीक्षण केलेले{PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "मजकूर काँप्रेशन लागू करते"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "रीडिरेक्‍ट करते"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "सर्व्हर प्रतिसादाची वेळ"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "दस्तऐवज विनंतीशी संबंधित लेटन्सी"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "अनकाँप्रेस्ड डाउनलोड"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "डुप्लिकेट केलेली बाइट"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "स्रोत"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "नेटवर्क अ‍ॅक्टिव्हिटीद्वारे वापरल्या जाणाऱ्या अनावश्यक बाइट कमी करण्यासाठी बंडलमधून मोठी, डुप्लिकेट JavaScript मॉड्यूल काढून टाका."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "डुप्लिकेट केलेले JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "मजकूर सातत्याने दृश्यमान असल्याची खात्री करण्यासाठी [font-display](https://developer.chrome.com/blog/font-display) हे swap किंवा optional वर सेट करा. [फाँट मेट्रिक ओव्हरराइड](https://developer.chrome.com/blog/font-fallbacks) असलेली लेआउट शिफ्ट कमी करण्यासाठी swap ला आणखी ऑप्टिमाइझ केले जाऊ शकते."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "फाँट"}, "models/trace/insights/FontDisplay.ts | title": {"message": "फॉंट डिस्प्ले"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "वाया गेलेली वेळ"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(निनावी)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "बरेच APIs, साधारणतः लेआउटची भूमिती रीड करणारे, शैली आणि लेआउटची गणना करण्यासाठी रेंडरिंग इंजीनला स्क्रिप्ट अमलात आणण्यापासून थांबवण्याची सक्ती करतात. [सक्ती केलेला रीफ्लो](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) आणि त्याच्या मिटिगेशनविषयी अधिक जाणून घ्या."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "स्टॅक ट्रेस"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "सक्ती केलेला रीफ्लो"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "टॉप फंक्शन कॉल"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "रीफ्लोचा एकूण वेळ"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[नामोल्लेख नसलेले]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "इमेज डाउनलोड करण्याचा कालावधी कमी केल्यामुळे पेज आणि LCP च्या लोड कालावधीमध्ये सुधारणा होऊ शकते. [इमेजचा आकार ऑप्टिमाइझ करण्याबद्दल अधिक जाणून घ्या](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (अंदाजे {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "कोणत्याही ऑप्टिमाइझ करण्यायोग्य इमेज नाहीत"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "फाइलचा आकार ऑप्टिमाइझ करा"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "इतर {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "इमेज डिलिव्हरीमध्ये सुधारणा करा"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "इमेज काँप्रेशन घटक वाढवल्यामुळे या इमेजच्या डाउनलोड आकारामध्ये सुधारणा होऊ शकते."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "मॉडर्न इमेज फॉरमॅट (WebP, AVIF) वापरल्याने किंवा इमेजचे काँप्रेशन वाढवल्याने, इमेजच्या डाउनलोड आकारामध्ये सुधारणा होऊ शकते."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ही इमेज फाइल तिच्या दाखवलेल्या परिमाणांसाठी ({PH2}) आवश्यक असलेल्या परिमाणांहून मोठी ({PH1}) आहे. इमेजचा डाउनलोड आकार कमी करण्यासाठी सत्वर प्रतिसाद देणाऱ्या इमेज वापरा."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIFs ऐवजी व्हिडिओ फॉरमॅट वापरल्याने ॲनिमेटेड आशयाच्या डाउनलोड आकारामध्ये सुधारणा होऊ शकते."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "सर्वात मोठ्या टप्प्यासह तपासणी करण्यास सुरुवात करा. [विलंब कमी केले जाऊ शकतात](https://web.dev/articles/optimize-inp#optimize_interactions). प्रक्रियेचा कालावधी कमी करण्यासाठी, [मुख्य थ्रेडची शुल्के ऑप्टिमाइझ करणे](https://web.dev/articles/optimize-long-tasks) हे करा, बरेचदा JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "कालावधी"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "इनपुटमधील विलंब"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "कोणतेही संवाद डिटेक्ट केला नाही"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "टप्पा"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "प्रेझेंटेशनमधील विलंब"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "कालावधीवर प्रक्रिया करत आहे"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "टप्प्यानुसार INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTML वरून LCP इमेज लगेच [शोधसुलभ](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) करून आणि [लेझी-लोडिंग टाळून](https://web.dev/articles/lcp-lazy-loading) LCP ऑप्टिमाइझ करा"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high applied"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high लागू केले पाहिजे"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "लेझी लोड लागू केले नाही"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "सर्वात जास्त सुरुवातीच्या ठिकाणापासून LCP इमेज {PH1} नंतर लोड झाली."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "कोणतेही LCP आढळले नाही"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP ही इमेज नसल्यामुळे कोणतेही LCP स्रोत डिटेक्ट झाले नाहीत"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "सुरुवातीच्या दस्तऐवजामध्ये विनंती शोधसुलभ आहे"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP विनंतीसंबंधित डिस्कव्हरी"}, "models/trace/insights/LCPPhases.ts | description": {"message": "प्रत्येक [टप्प्यामध्ये सुधारणेसंबंधित विशिष्ट धोरणे आहेत](https://web.dev/articles/optimize-lcp#lcp-breakdown). आदर्शरीत्या, LCP चा बराचसा वेळ स्रोत लोड करण्यासाठी घालवणे आवश्यक आहे, उशीर न करता."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "कालावधी"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "घटक रेंडर करण्यामधील उशीर"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "फील्ड p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "कोणतेही LCP आढळले नाही"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "टप्पा"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "स्रोत लोड होण्यामधील उशीर"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "स्रोत लोड करण्याचा कालावधी"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "टाइम टू फर्स्ट बाइट"}, "models/trace/insights/LCPPhases.ts | title": {"message": "टप्प्यानुसार LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "स्क्रिप्ट"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "वापरलेल्या बाइट"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfills आणि ट्रान्सफॉर्मर हे नवीन JavaScript वैशिष्ट्ये वापरण्यासाठी जुने ब्राउझर सुरू करतात. तथापि, आधुनिक ब्राउझरसाठी बऱ्याच गोष्टी आवश्यक नाहीत. तुम्ही जुन्या ब्राउझरना सपोर्ट करणे आवश्यक आहे, हे तुम्हाला माहीत नसल्यास, [बेसलाइन](https://web.dev/articles/baseline-and-polyfills) वैशिष्ट्ये ट्रान्सपाइल न करण्यासाठी तुमची JavaScript तयार करण्याच्या प्रक्रियेमध्ये फेरबदल करण्याचा विचार करा. [बहुतांश साइट ट्रान्सपाइल न करता ES6+ कोड का डिप्लॉय करतात ते जाणून घ्या](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "लेगसी JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 आणि HTTP/3 हे HTTP/1.1 वर बरेच फायदे ऑफर करते, जसे की मल्टिप्लेक्सिंग. [आधुनिक HTTP वापरण्याविषयी अधिक जाणून घ्या](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "कोणत्याही विनंत्यांनी HTTP/1.1 वापरले नाही"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "प्रोटोकॉल"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "विनंती"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "मॉडर्न HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ओरिजिन"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "विनंती"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "स्रोत"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "वेळ"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "अंदाजे LCP बचती"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "न वापरलेल्या प्रीकनेक्ट. crossorigin ॲट्रिब्यूट योग्यरीत्या वापरले असल्याचे तपासा."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "पेज लोडमध्ये सुधारणा करण्यासाठी, चेनची लांबी कमी करून, स्रोतांच्या डाउनलोडचा आकार कमी करून किंवा अनावश्यक स्रोतांचे डाउनलोड पुढे ढकलून [महत्त्वपूर्ण विनंत्यांची चेन करणे टाळा](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "तुमच्या सर्वात महत्त्वाच्या ओरिजिनमध्ये [प्रीकनेक्ट](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) सूचना जोडा, पण ४ हून कमी वापरण्याचा प्रयत्न करा."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "प्रीकनेक्ट उमेदवार"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "अत्यंत महत्त्वाच्या पाथची कमाल लेटन्सी:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "नेटवर्कवर डिपेंडन्सीमुळे कोणत्याही रेंडरिंग टास्कवर परिणाम झाला नाही"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "प्रीकनेक्ट करण्यासाठी कोणतेही अतिरिक्त ओरिजिन चांगले उमेदवार नाही"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "कोणतेही ओरिजिन प्रीकनेक्ट केलेले नाही"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "सूचना [प्रीकनेक्ट](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ब्राउझरला पेज लोड करण्याआधी कनेक्शन स्थापित करण्यास मदत होते, ज्यामुळे त्या ओरिजिनसाठी पहिली विनंती केल्यावर वेळ वाचतो. हे पेज ज्या ओरिजिनशी प्रीकनेक्ट केले ते पुढीलप्रमाणे आहेत."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "प्रीकनेक्ट केलेली ओरिजिन"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "नेटवर्क डिपेंडन्सी ट्री"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "४ हून अधिक preconnect कनेक्शन सापडली आहेत. ही कमीत कमी आणि फक्त सर्वात महत्त्वाच्या ओरिजिनसाठीच वापरले पाहिजेत."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "न वापरलेले प्रीकनेक्ट. फक्त preconnect ओरिजिनसाठी वापरा, ज्यांची पेज विनंती करणे शक्य आहे."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "पेज लोडमध्ये सुधारणा करण्यासाठी, चेनची लांबी कमी करून, स्रोतांच्या डाउनलोडचा आकार कमी करून किंवा अनावश्यक स्रोतांचे डाउनलोड पुढे ढकलून गंभीर विनंत्यांना चेन करणे टाळा."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "विनंत्या पेजचे सुरुवातीचे रेंडर ब्लॉक करत आहे, ज्यामुळे LCP ला विलंब होऊ शकतो. [पुढे ढकलणे किंवा इनलाइन करणे](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) यांमुळे या नेटवर्क विनंत्या अत्यंत महत्त्वाच्या पाथच्या बाहेर हलवल्या जाऊ शकतात."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "कालावधी"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "या नेव्हिगेशनसाठी कोणत्याही रेंडर ब्लॉकिंग विनंत्या नाहीत"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "विनंती"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "ब्लॉक करण्याच्या विनंत्या रेंडर करा"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "शैलीची पुनर्गणना करणे यांची शुल्के उच्च राहत असल्यास, सिलेक्टर ऑप्टिमायझेशनमुळे ती कमी होऊ शकतात. लोटलेला उच्च कालावधी आणि उच्च कमी पाथची % या दोन्हीसह [सिलेक्टर ऑप्टिमाइझ करणे](https://developer.chrome.com/docs/devtools/performance/selector-stats) हे करा. आणखी साधे सिलेक्टर, आणखी कमी सिलेक्टर, आणखी लहान DOM, आणखी उथळ DOM या सर्व गोष्टींमुळे जुळण्याची शुल्के कमी होतील."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "संपलेली वेळ"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "कोणताही CSS सिलेक्टर डेटा आढळला नाही. CSS सिलेक्टर आकडेवारी परफॉर्मन्स पॅनेल सेटिंग्जमध्ये सुरू करणे आवश्यक आहे."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "प्रयत्न जुळवा"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "संख्या जुळवा"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS सिलेक्टर शुल्के"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "टॉप सिलेक्टर"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "एकूण"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "मुख्य थ्रेडचा वेळ"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "तृतीय पक्ष"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ट्रान्सफरचा आकार"}, "models/trace/insights/ThirdParties.ts | description": {"message": "तृतीय पक्ष कोड हा लोड परफॉर्मन्सवर लक्षणीयरीत्या परिणाम करू शकतो. तुमच्या पेजच्या आशयाला प्राधान्य देण्यासाठी, [तृतीय पक्ष कोड कमी करणे आणि तो लोड करणे पुढे ढकलणे](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) हे करा."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "कोणताही तृतीय पक्ष आढळला नाही"}, "models/trace/insights/ThirdParties.ts | title": {"message": "तृतीय पक्ष"}, "models/trace/insights/Viewport.ts | description": {"message": "व्ह्यूपोर्ट मोबाइलसाठी ऑप्टिमाइझ न केल्यास, टॅप करून केलेले संवाद [कमाल ३०० मिसेनी विलंब होणे](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) हे होऊ शकते."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "मोबाइल टॅपमधील विलंब"}, "models/trace/insights/Viewport.ts | title": {"message": "मोबाइलसाठी व्ह्यूपोर्ट ऑप्टिमायझ करा"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "फक्त GET विनंतीद्वारे लोड केलेली पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र आहेत."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "फक्त 2XX चा स्टेटस कोड असलेली पेज कॅशे केली जाऊ शकतात."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "कॅशेमध्ये असताना JavaScript अमलात आणण्याचा प्रयत्न Chrome ने डिटेक्ट केला आहे."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "ज्या पेजनी AppBanner ची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "फ्लॅगद्वारे बॅक/फॉरवर्ड कॅशे बंद केली गेली. ती या डिव्हाइसवर स्थानिकरीत्या सुरू करण्यासाठी chrome://flags/#back-forward-cache ला भेट द्या."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "कमांड लाइनद्वारे बॅक/फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "अपुर्‍या मेमरीमुळे बॅक/फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "बॅक/फॉरवर्ड कॅशेला प्रतिनिधीचा सपोर्ट नाही."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "प्रीरेंडररसाठी बॅक/फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "पेजमध्ये नोंदणी केलेल्या ऐकणार्‍यांसह BroadcastChannel इंस्टन्स असल्यामुळे ते कॅशे केले जाऊ शकत नाही."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "ज्या पेजमध्ये cache-control:no-store आहे, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "कॅशे हेतुपुरस्सर साफ केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "दुसर्‍या पेजला कॅशे केले जाऊ देण्यासाठी पेज कॅशेमधून काढले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "प्लगिनचा समावेश असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "नमूद न केलेले"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "मीडिया डिव्हाइस डिस्पॅचर वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "सोडत असताना मीडिया प्लेअर प्ले होत होता."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API वापरणारी आणि प्लेबॅक स्थिती सेट करणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API वापरणारी आणि कृती हँडलर सेट करणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "स्क्रीन रीडरमुळे बॅक-फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "सिरीअल API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthentication API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API वापरणारी पेज बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "बॅक/फॉरवर्ड कॅशे बंद केले आहे, कारण Cache-Control: no-store वापरणार्‍या पेजवर कुकी बंद केल्या आहेत."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "समर्पित कर्मचारी किंवा वर्कलेट वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "दस्तऐवज सोडण्यापूर्वी तो लोड होणे पूर्ण झाले नाही."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा अ‍ॅप बॅनर दिसत होते."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा Chrome पासवर्ड व्यवस्थापक दिसत होता."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा DOM डिसटिलेशन प्रगतिपथावर होते."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा DOM डिस्टिलर व्ह्यूअर दिसत होते."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "मेसेजिंग API वापरणाऱ्या एक्स्टेंशनमुळे बॅक/फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "दीर्घ कालावधीसाठी कनेक्शन असलेल्या एक्स्टेंशननी बॅक/फॉरवर्ड कॅशेमध्ये एंटर करण्यापूर्वी ते कनेक्शन बंद करणे आवश्यक आहे."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "दीर्घ कालावधीसाठी कनेक्शन असलेल्या एक्स्टेंशननी बॅक/फॉरवर्ड कॅशेमध्ये असलेल्या फ्रेमला मेसेज पाठवण्याचा प्रयत्न केला."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "एक्स्टेंशनमुळे बॅक/फॉरवर्ड कॅशे बंद केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा पेजसाठी पुन्हा सबमिट करणे किंवा http पासवर्ड डायलॉग यासारखा मोडल डायलॉग दाखवला गेला."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा ऑफलाइन पेज दाखवले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा आउट-ऑफ-मेमरी इंटरव्हेंशन बार दिसत होता."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा परवानग्यांची विनंती करण्यास सांगितले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा पॉपअप ब्लॉकर दिसत होते."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "दुसरीकडे नेव्हिगेट केले, तेव्हा सुरक्षित ब्राउझिंग संबंधित तपशील दाखवले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "सुरक्षित ब्राउझिंग नुसार हे पेज अपमानास्पद आहे आणि त्याचा पॉपअप ब्लॉक केला गेला."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "पेज बॅक/फॉरवर्ड कॅशेमध्ये असताना सेवा कर्मचारी अ‍ॅक्टिव्हेट केले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "दस्तऐवज एररमुळे बॅक/फॉरवर्ड कॅशे बंद केले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames वापरणारी पेज bfcache मध्ये स्टोअर केली जाऊ शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "दुसर्‍या पेजला कॅशे केले जाऊ देण्यासाठी पेज कॅशेमधून काढले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "ज्या पेजनी मीडिया स्ट्रीमचा अ‍ॅक्सेस दिला आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "विशिष्ट प्रकारचा एंबेड केलेला आशय (उदा. PDFs) असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "ज्या पेजमध्ये उघडलेले IndexedDB कनेक्शन आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB इव्हेंटमुळे बॅक/फॉरवर्ड कॅशे बंद केले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "अपात्र API वापरले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "ज्या पेजमध्ये एक्स्टेंशनद्वारे JavaScript इंजेक्ट केले आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "ज्या पेजमध्ये एक्स्टेंशनद्वारे StyleSheet इंजेक्ट केले गेले आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "अंतर्गत एरर."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "बॅक/फॉरवर्ड कॅशे बंद केले आहे, कारण कोणत्यातरी JavaScript नेटवर्क विनंतीला Cache-Control: no-store हेडर असलेले स्रोत मिळाले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "कीप-अलाइव्ह विनंतीमुळे बॅक/फॉरवर्ड कॅशे बंद केले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "कीबोर्ड लॉक वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "पेज सोडण्यापूर्वी ते लोड होणे पूर्ण झाले नाही."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "ज्या पेजच्या मुख्य स्रोतामध्ये cache-control:no-cache आहे, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "ज्या पेजच्या मुख्य स्रोतामध्ये cache-control:no-store आहे, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "बॅक/फॉरवर्ड कॅशेमधून पेज रिस्टोअर केले जाण्यापूर्वी नेव्हिगेशन रद्द केले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "अ‍ॅक्टिव्ह नेटवर्क कनेक्शनला खूप जास्त डेटा मिळाल्यामुळे पेज कॅशेमधून काढले गेले. पेज कॅशे केले असताना ते मिळवू शकत असलेल्या डेटाचे प्रमाण Chrome मर्यादित करते."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "ज्या पेजमध्ये इनफ्लाइट फेच() किंवा XHR आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "अ‍ॅक्टिव्ह नेटवर्क विनंतीमध्ये रीडिरेक्टचा समावेश असल्यामुळे पेज बॅक/फॉरवर्ड कॅशेमधून काढले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "नेटवर्क कनेक्शन खूप वेळ उघडे असल्यामुळे पेज कॅशेमधून काढले गेले. पेज कॅशे केले असताना ते मिळवू शकत असलेल्या डेटाला लागणार्‍या वेळाचे प्रमाण Chrome मर्यादित करते."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "ज्या पेजमध्ये वैध प्रतिसाद हेड नाही, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "नेव्हिगेशन हे मुख्य फ्रेमव्यतिरिक्त इतर फ्रेममध्ये घडले."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "सुरू असलेले अनुक्रमित केलेले DB व्यवहार असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "इन-फ्लाइट नेटवर्क विनंती असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "इन-फ्लाइट फेच नेटवर्क विनंती असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "इन-फ्लाइट नेटवर्क विनंती असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "इन-फ्लाइट XHR नेटवर्क विनंती असलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "चित्रात-चित्र वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "प्रिंटिंग UI दाखवणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "पेज 'window.open()' वापरून उघडले गेले आणि दुसर्‍या टॅबला त्याचा संदर्भ आहे किंवा पेजने विंडो उघडली."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "बॅक/फॉरवर्ड कॅशेमधील पेजसाठी रेंडरिंग प्रक्रिया क्रॅश झाली."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "बॅक/फॉरवर्ड कॅशेमधील पेजसाठी रेंडरिंग प्रक्रिया थांबवली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "ज्या पेजनी ऑडिओ कॅप्चर परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "ज्या पेजनी सेन्सर परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "ज्या पेजनी बॅकग्राउंड सिंक किंवा फेच परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "ज्या पेजनी MIDI परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "ज्या पेजनी सूचना परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "स्टोरेज अ‍ॅक्सेसची विनंती केलेली पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "ज्या पेजनी व्हिडिओ कॅप्चर परवानग्यांची विनंती केली आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "फक्त URL स्कीम ही HTTP / HTTPS असलेली पेज कॅशे केली जाऊ शकतात."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "पेज बॅक/फॉरवर्ड कॅशेमध्ये असताना त्यावर सेवा कर्मचार्‍याने दावा केला."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "सेवा कर्मचार्‍याने बॅक/फॉरवर्ड कॅशेमध्ये असलेल्या पेजला MessageEvent पाठवण्याचा प्रयत्न केला."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "पेज बॅक/फॉरवर्ड कॅशेमध्ये असताना ServiceWorker ची नोंदणी रद्द केली गेली."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "सेवा कर्मचारी अ‍ॅक्टिव्हेशनमुळे पेज बॅक/फॉरवर्ड कॅशेमधून काढले गेले."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome रीस्टार्ट झाले आणि त्याने बॅक/फॉरवर्ड कॅशे एंट्री साफ केल्या."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Shared<PERSON><PERSON><PERSON> वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "पेजवरील आयफ्रेमने नेव्हिगेशन सुरू केले, जे पूर्ण झाले नाही."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "ज्या पेजच्या सबरिसोर्समध्ये cache-control:no-cache आहे, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "ज्या पेजच्या सबरिसोर्समध्ये cache-control:no-store आहे, ती बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "पेजने बॅक/फॉरवर्ड कॅशेमध्ये कमाल वेळमर्यादा ओलांडली आणि ते एक्स्पायर झाले."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "बॅक/फॉरवर्ड कॅशे एंटर करताना पेजची वेळ संपली (संभवतः खूप वेळ रन झालेल्या पेजहाइड हँडलरमुळे)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "पेजला मुख्य फ्रेममध्ये अनलोड हँडलर आहे."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "पेजला सब फ्रेममध्ये अनलोड हँडलर आहे."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ब्राउझरने वापरकर्ता एजंट ओव्हरराइड हेडर बदलले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "ज्या पेजनी व्हिडिओ किंवा ऑडिओ रेकॉर्ड करण्यासाठी अ‍ॅक्सेस दिला आहे, ती सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC असलेली पेज बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "बॅक/फॉरवर्ड कॅशे बंद केले आहेत, कारण WebRTC वापरले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket असलेली पेज बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "बॅक/फॉरवर्ड कॅशे बंद केले आहेत, कारण WebSocket वापरले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport असलेली पेज बॅक/फॉरवर्ड कॅशे एंटर करू शकत नाहीत."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "बॅक/फॉरवर्ड कॅशे बंद केले आहेत, कारण WebTransport वापरले आहे."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR वापरणारी पेज सध्या बॅक/फॉरवर्ड कॅशेसाठी पात्र नाहीत."}}