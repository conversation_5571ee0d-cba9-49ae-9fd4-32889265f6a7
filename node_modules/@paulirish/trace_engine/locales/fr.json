{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "L'autorisation ne sera pas couverte par le caractère générique (*) dans la gestion Access-Control-Allow-Headers de CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Pour désactiver l'intégration Cast par défaut, utilisez l'attribut disableRemotePlayback plutôt que le sélecteur -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "La valeur d'apparence CSS slider-vertical n'est pas standardisée et sera supprimée."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Les requêtes de ressources dont les URL contiennent à la fois des espaces blancs \\(n|r|t) et des signes \"strictement inférieur à\" (<) supprimés sont bloquées. Veuillez supprimer les nouvelles lignes et encoder les symboles \"strictement inférieur à\" depuis des emplacements tels que des valeurs d'attribut d'élément pour pouvoir charger ces ressources."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() est obsolète. Utilisez l'API normalisée à la place : Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() est obsolète. Veuillez utiliser l'API normalisée à la place : Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() est obsolète. Veuillez utiliser l'API normalisée à la place : nextHopProtocol dans Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Les cookies contenant un caractère \\(0|r|n) ne seront pas tronqués, mais refusés."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "La possibilité de contourner la règle d'origine commune en définissant document.domain est obsolète et sera désactivée par défaut. Cet avertissement concerne l'accès multi-origine activé en définissant document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "La possibilité de déclencher window.alert depuis des iFrames multi-origines est obsolète et sera supprimée."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "La possibilité de déclencher window.confirm depuis des iFrames multi-origines est obsolète et sera supprimée."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "La prise en charge des URL \"data\" dans SVGUseElement est obsolète et sera supprimée à l'avenir."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() et watchPosition() ne fonctionnent plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre application, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() et watchPosition() sont obsolètes pour les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre application, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ne fonctionne plus sur les origines non sécurisées. Pour utiliser cette fonctionnalité, vous devriez envisager d'utiliser une origine sûre pour votre application, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Une balise <h1> a été trouvée dans un élément <article>, <aside>, <nav> ou <section> qui ne comporte pas de taille de police spécifiée. La taille de ce titre sera prochainement modifiée dans ce navigateur. Pour en savoir plus, consultez https://developer.mozilla.org/fr-FR/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate est obsolète. Veuillez utiliser RTCPeerConnectionIceErrorEvent.address ou RTCPeerConnectionIceErrorEvent.port à la place."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Ce format de demande navigator.credentials.get() pour les identifiants numériques est obsolète. Veuillez mettre à jour votre appel de façon à utiliser le nouveau format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Le point de départ du marchand et les données arbitraires provenant de l'événement service worker canmakepayment sont obsolètes et seront supprimés : topOrigin, paymentRequestOrigin, methodData et modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Le site Web a demandé une sous-ressource auprès d'un réseau auquel il pouvait uniquement accéder en raison de la position privilégiée de ses utilisateurs sur le réseau. Ces requêtes exposent les serveurs et les appareils non publics à Internet, ce qui augmente le risque d'attaque par falsification des requêtes intersites (CSRF) et/ou les fuites d'informations. Pour réduire ces risques, Chrome n'accepte plus les requêtes auprès de sous-ressources non publiques émises depuis des contextes non sécurisés et va commencer à les bloquer."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Le champ dailyUpdateUrl de InterestGroups transmis à joinAdInterestGroup() a été renommé updateUrl pour refléter plus précisément son comportement."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator est obsolète. Veuillez utiliser Intl.Segmenter à la place."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS ne peut pas être chargé depuis des URL file:, sauf si elles se terminent par une extension de fichier .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "La possibilité d'utiliser SourceBuffer.abort() pour annuler la suppression de la plage asynchrone par remove() est obsolète, en raison d'une modification de la spécification. Sa prise en charge sera supprimée prochainement. Écoutez plutôt l'événement updateend. abort() sert uniquement à annuler l'ajout d'un contenu multimédia asynchrone ou à réinitialiser l'état de l'analyseur."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "La possibilité de régler MediaSource.duration à une valeur inférieure au code temporel de présentation le plus élevé de n'importe quel frame codé en mémoire tampon est obsolète en raison d'une modification de la spécification. La possibilité de supprimer implicitement des contenus multimédias tronqués en mémoire tampon sera supprimée prochainement. À la place, vous devriez exécuter remove(newDuration, oldDuration) de manière explicite sur tous les sourceBuffers, où newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI demandera une autorisation d'utilisation, même si le SysEx n'est pas spécifié dans les MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "L'API Notification ne peut plus être utilisée depuis des origines non sécurisées. Vous devriez envisager d'utiliser une origine sûre pour votre application, par exemple HTTPS. Pour en savoir plus, consultez https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "L'autorisation pour l'API Notification ne peut plus être demandée depuis un iFrame multi-origine. À la place, vous devriez envisager de demander l'autorisation depuis un frame de haut niveau ou d'ouvrir une nouvelle fenêtre."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "L'option imageOrientation: 'none' dans createImageBitmap est obsolète. Veuillez plutôt utiliser createImageBitmap avec l'option \"{imageOrientation: 'from-image'}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Votre partenaire négocie une version obsolète de (D)TLS. Contactez-le pour qu'il corrige ce problème."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Si vous spécifiez overflow: visible sur \"img\", \"video\" et \"canvas\", ces tags risquent de générer du contenu visuel en dehors des limites de l'élément. Voir https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments est obsolète. Veuillez utiliser l'installation \"juste-à-temps\" pour les gestionnaires de paiement."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Votre appel PaymentRequest a contourné la directive CSP (Content-Security-Policy) connect-src. Ce contournement est obsolète. Veuillez ajouter l'identifiant du mode de paiement de l'API PaymentRequest (dans le champ supportedMethods) à la directive CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent est obsolète. Veuillez plutôt utiliser la version normalisée navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "L'élément <source src> avec un parent <picture> n'est pas valide et est donc ignoré. Veuillez utiliser l'attribut <source srcset> à la place."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame est spécifique au fournisseur. Veuillez plutôt utiliser la méthode standard cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame est spécifique au fournisseur. Veuillez plutôt utiliser la méthode standard requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen est obsolète. Veuillez plutôt utiliser Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() est obsolète. Veuillez plutôt utiliser Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() est obsolète. Veuillez plutôt utiliser Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() est obsolète. Veuillez plutôt utiliser Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() est obsolète. Veuillez plutôt utiliser Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen est obsolète. Veuillez plutôt utiliser Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Nous abandonnons l'API chrome.privacy.websites.privacySandboxEnabled, bien qu'elle reste active pour assurer la rétrocompatibilité jusqu'à la version M113. Veuillez plutôt utiliser chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled et chrome.privacy.websites.adMeasurementEnabled. Consultez https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "La contrainte DtlsSrtpKeyAgreement a été supprimée. Vous avez spécifié une valeur false pour cette contrainte, ce qui est interprété comme une tentative d'utiliser la méthode SDES key negotiation, qui a été supprimée. Cette fonctionnalité a été supprimée. À la place, utilisez un service compatible avec DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "La contrainte DtlsSrtpKeyAgreement a été supprimée. Vous avez spécifié une valeur true pour cette contrainte, qui est sans effet. Vous pouvez toutefois la supprimer pour plus de clarté."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "La méthode getStats() basée sur le rappel est obsolète et sera supprimée. Utilisez plutôt la méthode getStats() conforme aux spécifications."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() est obsolète. Veuillez plutôt utiliser Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Les requêtes de sous-ressources dont les URL contiennent des identifiants intégrés (**********************/, par exemple) sont bloquées."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "L'option rtcpMuxPolicy est obsolète et sera supprimée."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer nécessite une isolation multi-origine. Pour en savoir plus, consultez https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "La fonctionnalité speechSynthesis.speak() sans activation de l'utilisateur est obsolète et sera supprimée."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Les écouteurs d'événements de déchargement sont obsolètes et seront supprimés."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Les extensions doivent activer l'isolation multi-origine pour pouvoir continuer à utiliser SharedArrayBuffer. Consultez https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "L'attribut isFallbackAdapter de GPUAdapter est obsolète. Utilisez plutôt l'attribut isFallbackAdapter de GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Les réponses json n'acceptent pas l'encodage UTF-16 dans XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "La requête XMLHttpRequest synchrone sur le thread principal est obsolète en raison de son impact négatif sur l'expérience de l'utilisateur final. Si vous avez besoin d'aide, consultez https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animation"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Les décalages de mise en page se produisent lorsque des éléments se déplacent sans aucune interaction de l'utilisateur. [Étudiez les causes des décalages de mise en page](https://web.dev/articles/optimize-cls), comme l'ajout ou la suppression d'éléments, ou le changement de polices lors du chargement de la page."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Demande de police"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "iFrame injecté"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Cluster de décalage de mise en page à {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Impossible de détecter les causes des décalages de mise en page"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Aucun décalage de mise en page"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Causes des décalages de mise en page"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Principales causes des décalages de mise en page"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Pire cluster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Pire cluster de décalage de mise en page"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Cache de la valeur TTL"}, "models/trace/insights/Cache.ts | description": {"message": "Une longue durée de mise en cache peut accélérer les visites répétées sur votre page. [En savoir plus](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Aucune requête avec des règles inefficaces liées au cache"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} autres"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Utiliser des durées de mise en cache efficaces"}, "models/trace/insights/DOMSize.ts | description": {"message": "Un grand DOM peut allonger la durée des calculs de style et des ajustements de la mise en page, ce qui impacte la réactivité de la page. Un grand DOM sollicite davantage la mémoire. [Découvrez comment éviter une taille de DOM excessive.](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "É<PERSON>ment"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "La plupart des enfants"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Profondeur DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistique"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimiser la taille du DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Nombre total d'éléments"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Votre première requête réseau est la plus importante.  Réduisez sa latence en évitant les redirections, en assurant une réponse rapide du serveur et en activant la compression de texte."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "A eu des redirections ({PH1} redirections, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Le serveur a répondu lentement ({PH1} observées)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Aucune compression appliquée"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Pas de redirections"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Le serveur répond rapidement ({PH1} observées)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Applique la compression de texte"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redirections"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Temps de réponse du serveur"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latence de la demande de document"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Téléchargement non compressé"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Octets en double"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Source"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Supprimez les modules JavaScript volumineux et en double de vos groupes pour réduire les débits d'octets superflus sur le réseau."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript en double"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Envisagez de définir [font-display](https://developer.chrome.com/blog/font-display) sur swap ou optional pour vous assurer que le texte est toujours visible. swap peut être encore optimisé pour atténuer les décalages de mise en page avec des [remplacements de métriques de police](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Police"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Affichage de la police"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Temps perdu"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonyme)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "De nombreuses API, qui lisent généralement la géométrie de la mise en page, forcent le moteur de rendu à suspendre l'exécution du script afin de calculer le style et la mise en page. En savoir plus sur l'[ajustement de la mise en page forcé](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) et les stratégies d'atténuation"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON> de <PERSON> pile"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Ajustement forcé de la mise en page"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Appel de fonction le plus fréquent"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Temps total d'ajustement de la mise en page"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[non attribué]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Réduire le temps de téléchargement des images peut améliorer le temps de chargement perçu de la page et le LCP. [En savoir plus sur l'optimisation de la taille des images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (environ {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Aucune image optimisable"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimiser la taille du fichier"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} autres"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Améliorer l'affichage des images"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Augmenter le taux de compression de l'image pourrait réduire sa taille de téléchargement."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Vous pourriez réduire la taille de téléchargement de cette image en utilisant un format d'image moderne (WebP, AVIF) ou en augmentant le taux de compression de l'image."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Compte tenu de ses dimensions affichées ({PH2}), ce fichier image est plus volumineux que nécessaire ({PH1}). Utilisez des images responsives pour réduire la taille de téléchargement de l'image."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Utiliser des formats vidéo plutôt que des GIF peut réduire la taille de téléchargement du contenu animé."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Commencez par étudier la phase la plus longue. [Les délais peuvent être réduits au maximum.](https://web.dev/articles/optimize-inp#optimize_interactions) Pour réduire la durée de traitement, [optimisez les coûts du thread principal](https://web.dev/articles/optimize-long-tasks), souvent JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "<PERSON><PERSON><PERSON> à l'entrée utilisateur"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Aucune interaction détectée"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Phase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "<PERSON><PERSON><PERSON>sent<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Durée de traitement"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP par phase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimisez le LCP en rendant l'image LCP [visible](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) immédiatement à partir du code HTML et en [évitant le chargement différé](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high appliqué"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high doit être appliqué"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "chargement différé non appliqué"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Image LCP chargée {PH1} après le premier point de départ."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Aucun LCP détecté"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Aucune ressource LCP détectée, car le LCP n'est pas une image"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "La demande est visible dans le document initial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Détection de la requête LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Chaque [phase comporte des stratégies d'amélioration spécifiques](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idéalement, la plupart du temps LCP devrait être consacré au chargement des ressources et non aux délais."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "<PERSON><PERSON><PERSON>'affichage de l'élément"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "75e centile du champ"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Aucun LCP détecté"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Phase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "<PERSON><PERSON><PERSON> de chargement de la ressource"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Durée de chargement de la ressource"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP par phase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Octets perdus"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Les polyfills et les transformations permettent aux navigateurs plus anciens d'utiliser les nouvelles fonctionnalités JavaScript. Dans la majorité des cas cependant, ils ne sont pas nécessaires aux navigateurs récents. Envisagez de modifier votre processus de compilation JavaScript pour ne pas transpiler les fonctionnalités [Baseline](https://web.dev/articles/baseline-and-polyfills), sauf si vous savez que vous devez prendre en charge les navigateurs plus anciens. [Découvrez pourquoi la plupart des sites peuvent déployer du code ES6+ sans transpiler.](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Ancien JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 et HTTP/3 offrent de nombreux avantages par rapport à HTTP/1.1, comme le multiplexage. [En savoir plus sur l'utilisation de protocole HTTP récent](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Aucune requête n'a utilisé HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocole"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP récent"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origine"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Source"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Temps"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Économies estimées pour LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Préconnexion inutilisée. Vérifiez que l'attribut crossorigin est utilisé correctement."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Évitez les chaînes de requêtes critiques](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) en réduisant la longueur des chaînes ou la taille de téléchargement des ressources, ou en reportant le téléchargement de ressources inutiles, afin d'améliorer le chargement des pages."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Ajoutez des hints de [préconnexion](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) à vos origines les plus importantes, mais essayez de ne pas en utiliser plus de quatre."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidats pour la préconnexion"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latence maximale du chemin critique :"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Aucune tâche de rendu impactée par les dépendances réseau"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Aucune autre origine n'est un bon candidat pour la préconnexion"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "aucune origine n'a été préconnectée"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Les hints de [préconnexion](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) aident le navigateur à établir une connexion plus tôt dans le chargement de page, ce qui permet de gagner du temps lors de la première requête pour cette origine. Voici les origines auxquelles la page s'est préconnectée."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origines préconnectées"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Arborescence du réseau"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Plus de quatre connexions preconnect ont été identifiées. Elles doivent être utilisées avec parcimonie et limitées aux origines les plus importantes."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Préconnexion inutilisée. N'utilisez preconnect que pour les origines que la page est susceptible de demander."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "<PERSON><PERSON><PERSON>z les chaînes de requêtes critiques en réduisant la longueur des chaînes ou la taille de téléchargement des ressources, ou en reportant le téléchargement de ressources inutiles, afin d'améliorer le chargement des pages."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Les requêtes bloquent le rendu initial de la page, ce qui peut causer un retard LCP. [Le report ou l'intégration](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) de ces requêtes réseau peut les écarter du chemin critique."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Aucune requête de blocage de l'affichage pour cette navigation"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Requêtes de blocage de l'affichage"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Si les coûts de recalcul du style restent élevés, vous pouvez les réduire en [optimisant les sélecteurs](https://developer.chrome.com/docs/devtools/performance/selector-stats) pour lesquels le temps écoulé et le pourcentage à chemin lent sont élevés. Des sélecteurs plus simples et moins nombreux, ainsi qu'un DOM plus petit et superficiel réduiront tous les coûts de mise en correspondance."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Temps écoulé"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "<PERSON><PERSON><PERSON> donnée de sélecteur CSS trouvée. <PERSON><PERSON> de<PERSON> activer les statistiques du sélecteur CSS dans les paramètres du panneau \"Performances\"."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Tentatives de correspondance"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Nombre de correspondances"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Coûts des sélecteurs CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Principaux sélecteurs"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Temps d'exécution sur le thread principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Tiers"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Le code tiers peut affecter considérablement les performances de chargement des pages. [R<PERSON><PERSON><PERSON><PERSON> et différez le chargement du code tiers](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) pour donner la priorité au contenu de votre page."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Aucun contenu tiers trouvé"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Tiers"}, "models/trace/insights/Viewport.ts | description": {"message": "Les interactions tactiles peuvent être [retardées jusqu'à 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) si la fenêtre d'affichage n'est pas optimisée pour les mobiles."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Retard de l'appui sur mobile"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimiser la fenêtre d'affichage pour les mobiles"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Seules les pages chargées via une demande GET sont éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Seules les pages dont le code d'état est 2XX peuvent être mises en cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome a détecté une tentative d'exécuter JavaScript alors que la page se trouve dans le cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Les pages qui ont demandé une AppBanner ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Le cache amélioré est désactivé dans chrome://flags. Accédez à chrome://flags/#back-forward-cache pour l'activer en local sur cet appareil."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "La ligne de commande a désactivé le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Le cache amélioré est désactivé en raison d'une mémoire insuffisante."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Le délégué n'accepte pas le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Le cache amélioré est désactivé pour le prérendu."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "La page ne peut pas être mise en cache, car elle contient une instance BroadcastChannel avec des écouteurs inscrits."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Les pages qui contiennent l'en-tête cache-control:no-store ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Le cache a été effacé volontairement."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "La page a été exclue du cache afin de laisser la place à une autre."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Les pages contenant des plug-ins ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Non défini"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Les pages qui utilisent l'API FileChooser ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Les pages qui utilisent l'API File System Access ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Les pages qui utilisent Media Device Dispatcher ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "La page a été quittée alors qu'un lecteur multimédia s'exécutait."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Les pages qui utilisent l'API MediaSession et définissent un état de lecture ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Les pages qui utilisent l'API MediaSession et définissent des gestionnaires d'action ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Le cache amélioré est désactivé en raison du lecteur d'écran."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Les pages qui utilisent SecurityHandler ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Les pages qui utilisent l'API Serial ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Les pages qui utilisent l'API WebAuthentication ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Les pages qui utilisent l'API WebBluetooth ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Les pages qui utilisent l'API WebUSB ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Le cache amélioré est désactivé, car les cookies sont désactivés sur une page qui utilise Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Les pages qui utilisent un worker ou un worklet dédié ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "La page a été quittée avant que le document soit entièrement chargé."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "La page a été quittée alors qu'une bannière d'appli était présente."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "La page a été quittée alors que le Gestionnaire de mots de passe Chrome était présent."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "La page a été quittée alors que la distillation DOM était en cours."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "La page a été quittée alors que DOM Distiller Viewer était présent."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Le cache amélioré est désactivé en raison d'extensions utilisant une API d'envoi de messages."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Les extensions à connexion longue durée doivent fermer la connexion avant d'accéder au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Des extensions à connexion longue durée ont tenté d'envoyer des messages aux frames dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Le cache amélioré est désactivé en raison des extensions."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "La page a été quittée alors qu'une boîte de dialogue modale pour renvoyer un formulaire ou indiquer un mot de passe http, par exemple, était affichée pour cette page."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "La page a été quittée alors que la page hors connexion était affichée."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "La page a été quittée alors que la barre d'intervention pour mémoire insuffisante était présente."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "La page a été quittée alors qu'il y avait des demandes d'autorisation."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "La page a été quittée alors que le bloqueur de pop-up était présent."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "La page a été quittée alors que les détails de la navigation sécurisée étaient affichés."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "La navigation sécurisée a considéré cette page comme abusive et a bloqué le pop-up."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Un service worker a été activé alors que la page se trouvait dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Le cache amélioré est désactivé à cause d'une erreur liée au document"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Les pages qui utilisent FencedFrames ne peuvent pas être stockées dans bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "La page a été exclue du cache afin de laisser la place à une autre."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Les pages sur lesquelles l'accès au flux multimédia est autorisé ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Les pages contenant certains types de contenus intégrés (par exemple, des PDF) ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Les pages qui utilisent IdleManager ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Les pages avec une connexion IndexedDB active ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Le cache amélioré est désactivé en raison d'un événement IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Des API non éligibles ont été utilisées."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Les pages dans lesquelles des extensions injectent une JavaScript ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Les pages dans lesquelles des extensions injectent une StyleSheet ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON> interne."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Le cache amélioré est désactivé, car une requête réseau JavaScript a reçu une ressource avec l'en-tête Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Le cache amélioré est désactivé en raison d'une requête de message keepalive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Les pages qui utilisent le verrouillage du clavier ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "La page a été quittée avant d'être entièrement chargée."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Les pages dont la ressource principale contient cache-control:no-cache ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Les pages dont la ressource principale contient cache-control:no-store ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "La navigation a été annulée avant que la page puisse être restaurée à partir du cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "La page a été exclue du cache, car une connexion réseau active a reçu trop de données. Chrome limite la quantité de données qu'une page mise en cache peut recevoir."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Les pages ayant une requête fetch() ou XHR en cours ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "La page a été exclue du cache amélioré, car une requête réseau active impliquait une redirection."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "La page a été exclue du cache, car elle est restée ouverte à une connexion réseau trop longtemps. Chrome limite la durée pendant laquelle une page mise en cache peut recevoir des données."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Les pages dépourvues d'un en-tête de réponse valide ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "La navigation a eu lieu dans un frame autre que le principal."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "La page avec des transactions DB indexées en cours n'est actuellement pas éligible au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Les pages ayant une requête réseau en cours ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Les pages avec une requête réseau fetch() en cours ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Les pages ayant une requête réseau en cours ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Les pages ayant une requête réseau XHR en cours ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Les pages qui utilisent PaymentManager ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Les pages qui utilisent le Picture-in-picture ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Les pages qui affichent l'UI d'impression ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Soit la page a été ouverte avec window.open() (et un autre onglet comprend une référence à celle-ci), soit elle a ouvert une fenêtre."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Le processus de rendu de la page située dans le cache amélioré a planté."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Le processus de rendu de la page située dans le cache amélioré a été interrompu."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Les pages qui ont demandé des autorisations pour des captures audio ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Les pages qui ont demandé des autorisations pour des capteurs ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Les pages qui ont demandé une synchronisation en arrière-plan ou des autorisations d'extraction ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Les pages qui ont demandé des autorisations MIDI ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Les pages qui ont demandé des autorisations pour des notifications ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Les pages qui ont demandé à accéder à l'espace de stockage ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Les pages qui ont demandé des autorisations pour des captures vidéo ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Seules les pages dont le schéma d'URL est HTTP/HTTPS peuvent être mises en cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "La page a été revendiquée par un service worker alors qu'elle se trouve dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Un service worker a tenté d'envoyer une propriété MessageEvent à la page située dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker a été désinscrit alors qu'une page se trouvait dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "La page a été exclue du cache amélioré, car un service worker a été activé."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome a redémarré et effacé les entrées du cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Les pages qui utilisent SharedWorker ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Les pages qui utilisent SpeechRecognizer ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Les pages qui utilisent SpeechSynthesis ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Une navigation a démarré sur un iFrame de la page, mais ne s'est pas terminée."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Les pages dont la sous-ressource contient cache-control:no-cache ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Les pages dont la sous-ressource contient cache-control:no-store ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "La page a dépassé la durée maximale autorisée dans le cache amélioré et a expiré."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Le délai d'enregistrement de la page dans le cache amélioré a été dépassé (probablement en raison de gestionnaires pagehide de longue durée)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Le frame principal de l'image contient un gestionnaire unload."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Le sous-frame de l'image contient un gestionnaire unload."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Le navigateur a modifié l'en-tête de forçage user-agent."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Les pages qui ont autorisé l'enregistrement audio ou vidéo ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Les pages qui utilisent WebDatabase ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Les pages qui utilisent WebHID ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Les pages qui utilisent WebLocks ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Les pages qui utilisent WebNfc ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Les pages qui utilisent WebOTPService ne sont actuellement pas éligibles à bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Les pages avec WebRTC ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Le cache amélioré est désactivé, car WebRTC a été utilisé."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Les pages qui utilisent WebShare ne sont actuellement pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Les pages avec WebSocket ne peuvent pas être incluses dans le cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Le cache amélioré est désactivé, car WebSocket a été utilisé."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Les pages avec WebTransport ne sont pas éligibles au cache amélioré."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Le cache amélioré est désactivé, car WebTransport a été utilisé."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Les pages qui utilisent WebXR ne sont actuellement pas éligibles au cache amélioré."}}