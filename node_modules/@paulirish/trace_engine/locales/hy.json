{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Լիազորումը չի կատարվի, եթե CORS Access-Control-Allow-Headers վերնագրում նշված լինի դերանշան (*)։"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Google Cast-ի կանխադրված ամբողջացումն անջատելու համար -internal-media-controls-overlay-cast-button ընտրիչի փոխարեն օգտագործեք disableRemotePlayback հատկանիշը։"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS-ի արժեք «slider-vertical»-ը ստանդարտացված չէ և կհեռացվի։"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Ռեսուրսների հարցումները, որոնց URL-ները պարունակում են հեռացված բացատի նշաններ (\\(n|r|t)) և փոքրի նշաններ (<), արգելափակվում են։ Այս ռեսուրսները բեռնելու համար հեռացրեք նոր տողի նշանները և կոդավորեք փոքրի նշանը URL-ի այնպիսի տեղերում, որտեղ, օրինակ, նշվում են հատկանիշների արժեքներ։"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Այս API-ը (chrome.loadTimes()) այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք ստանդարտ API՝ Navigation Timing 2։"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Այս API-ը (chrome.loadTimes()) այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք ստանդարտ API՝ Paint Timing։"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Այս API-ը (chrome.loadTimes()) այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք ստանդարտ API nextHopProtocol-ը Navigation Timing 2-ում։"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Քուքիները, որոնք պարունակում են \\(0|r|n) նիշը, չեն կրճատվում, այլ մերժվում են։"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Նույն աղբյուրի կանոնների թուլացումըdocument.domain-ի ընտրության հաշվին այլևս չի աջակցվում։ Ըստ կանխադրման՝ այս հնարավորությունը կանջատվի։ Աջակցման դադարեցման մասին այս նախազգուշացումը վերաբերում է այլ աղբյուրներից օգտագործման թույլտվությանը, որը միացվում է document.domain-ի միջոցով։"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Window.alert գործառույթի կանչը այլ աղբյուրների iframe պատուհաններից այլևս չի աջակցվում։ Հետագայում այս հնարավորությունը կհեռացվի։"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Window.confirm գործառույթի կանչը այլ աղբյուրների iframe պատուհաններից այլևս չի աջակցվում։ Հետագայում այս հնարավորությունը կհեռացվի։"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "SVGUseElement տարրի URL-ներն այլևս չեն աջակցվում և հետագայում կհեռացվեն։"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Այս մեթոդները (getCurrentPosition() և watchPosition()) այլևս չեն աշխատում ոչ անվտանգ աղբյուրներում։ Այս գործառույթն օգտագործելու համար ձեր հավելվածի համար ընտրեք անվտանգ աղբյուր, օրինակ՝ HTTPS-ը։ Մանրամասների համար անցեք https://goo.gle/chrome-insecure-origins էջ։"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Այս getCurrentPosition() և watchPosition() մեթոդներն այլևս չեն աջակցվում ոչ անվտանգ աղբյուրներում։ Այս գործառույթն օգտագործելու համար ձեր հավելվածի համար ընտրեք անվտանգ աղբյուր, օրինակ՝ HTTPS-ը։ Մանրամասների համար անցեք https://goo.gle/chrome-insecure-origins էջ։"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "Այս մեթոդը (getUserMedia()) այլևս չի աշխատում ոչ անվտանգ աղբյուրներում։ Այս գործառույթն օգտագործելու համար ձեր հավելվածի համար ընտրեք անվտանգ աղբյուր, օրինակ՝ HTTPS-ը։ Մանրամասների համար անցեք https://goo.gle/chrome-insecure-origins էջ։"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<h1> թեգ է գտնվել <article>, <aside>, <nav> կամ <section> տարրում, որի համար նշված չէ տառատեսակի չափսը։ Այս վերնագրի տեքստի չափսը շատ շուտով կփոխվի ընթացիկ դիտարկիչում։ Լրացուցիչ տեղեկությունների համար այցելեք https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 էջ։"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate-ն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք RTCPeerConnectionIceErrorEvent.address կամ RTCPeerConnectionIceErrorEvent.port։"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Մուտքի թվային տվյալներ ստանալու համար navigator.credentials.get() հարցման այս ձևաչափը հնացել է։ Նոր ձևաչափ օգտագործեք կանչի պարամետրերում։"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Վաճառողի երկիրը և կամայական տվյալները, որոնք նշված են canmakepayment SW իրադարձության մեջ, հնացել են և կհեռացվեն՝ topOrigin, paymentRequestOrigin, methodData, modifiers։"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Կայքը ենթառեսուրս է հայցել ցանցից, որի օգտագործման թույլտվություն կարելի է ստանալ միայն օգտատերերի ցանցի եզակի տեղակայման շնորհիվ։ Այս հարցումները հրապարակում են ներքին սարքերն ու սերվերները համացանցում, ինչն էլ բարձրացնում է միջկայքային հարցումների կեղծման (CSRF) և տեղեկությունների արտահոսքի վտանգը։ Այս ռիսկերը նվազեցնելու համար Chrome-ը դադարեցնում է ներքին ենթառեսուրսների հարցումների աջակցումը, երբ դրանք արվում են ոչ անվտանգ համատեքստերում։ Հետագայում նման հարցումները կարգելափակվեն։"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup() փոխանցված InterestGroups կառուցվածքի dailyUpdateUrl դաշտի անվանումը փոխվել է updateUrl-ի՝ ավելի ճշգրիտ կերպով վարքագիծն արտահայտելու համար։"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator API-ն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Intl.Segmenter API-ը։"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS-ը հնարավոր չէ բեռնել file: ձևաչափի URL-ներից, եթե դրանք չունեն .css ֆայլի ընդլայնում։"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Բնութագրի փոփոխության արդյունքում SourceBuffer.abort() մեթոդի օգտագործումը remove()-ի ասինխրոն ընդգրկույթի հեռացման համար այլևս չի աջակցվում։ Այդ հնարավորության աջակցումը հետագայում կհեռացվի։ Առաջնորդվեք updateend իրադարձությամբ։ Այս մեթոդը (abort()) նախատեսված է միայն մեդիա տվյալների ասինխրոն ավելացման կամ վերլուծիչի կարգավիճակի զրոյացման համար։"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Բնութագրի փոփոխության արդյունքում MediaSource.duration-ի համար ներկայացման առավելագույն ժամանակահատվածից ցածր արժեքի սահմանումը բուֆերի ցանկացած կոդավորված շրջանակի համար այլևս չի աջակցվում։ Սեղմված մեդիաֆայլերը բուֆերից ոչ բացահայտ կերպով հեռացնելու աջակցումը հետագայում կհեռացվի։ Դրա փոխարեն օգտագործեք remove(newDuration, oldDuration) բացահայտ գործառույթը բոլոր sourceBuffers տարրերի համար, որտեղ newDuration < oldDuration։"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI-ն կհայցի օգտագործման թույլտվություն, նույնիսկ եթե MIDIOptions-ում նշված չէ sysex պարամետրը։"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Notification API-ն այլևս չի կարելի օգտագործել ոչ անվտանգ աղբյուրներից։ Որպեսզի այն օգտագործեք, ձեր հավելվածի համար ընտրեք անվտանգ աղբյուր, օրինակ՝ HTTPS-ը։ Մանրամասների համար անցեք https://goo.gle/chrome-insecure-origins էջ։"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Այլ աղբյուրների iframe պատուհաններից այլևս հնարավոր չէ Notification API-ի օգտագործման թույլտվություն հայցել։ Հայցեք թույլտվություն վերին մակարդակի շրջանակից կամ նոր պատուհան բացեք։"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "CreateImageBitmap-ի imageOrientation: 'none' պարամետրը հնացած է։ Դրա փոխարեն createImageBitmap-ն օգտագործեք {imageOrientation: 'from-image'} պարամետրով։"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Ձեր գործընկերը տվյալներ է փոխանակում (D)TLS-ի հնացած տարբերակի միջոցով։ Հայտնեք նրան այս խնդիրը լուծելու անհրաժեշտության մասին։"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Եթե նշեք overflow: visible հատկությունը img, video և canvas պիտակների համար, դրանց վիզուալ բովանդակությունը կարող է ցուցադրվել այդ տարրերից դուրս։ Մանրամասներին ծանոթացեք այստեղ՝ https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md։"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments API-ն այլևս չի աջակցվում։ Օգտագործեք արդի API՝ վճարումների մշակման համար։"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Ձեր PaymentRequest API-ի կանչը շրջանցում է Content-Security-Policy (CSP) connect-src դիրեկտիվը։ Շրջանցման այդ մեթոդը հնացել է: Ավելացրեք վճարման եղանակի նույնացուցիչ PaymentRequest API-ից (supportedMethods դաշտում) ձեր CSP connect-src դիրեկտիվում։"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent-ն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք ստանդարտ navigator.storage։"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Այս տարրը (<source src>) <picture> գլխավոր տարրի հետ անվավեր է, ինչի հետևանքով անտեսվում է։ Դրա փոխարեն օգտագործեք <source srcset> տարրը։"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame մեթոդը կապված է որոշակի մատակարարի հետ։ Դրա փոխարեն օգտագործեք cancelAnimationFrame ստանդարտ մեթոդը։"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame մեթոդը կապված է որոշակի մատակարարի հետ։ Դրա փոխարեն օգտագործեք requestAnimationFrame ստանդարտ մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen մեթոդը հնացած է։ Դրա փոխարեն օգտագործեք Document.fullscreenElement մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Element.requestFullscreen() մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Element.requestFullscreen() մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Document.exitFullscreen() մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Document.exitFullscreen() մեթոդը։"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Document.fullscreenEnabled մեթոդը։"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Մենք դադարեցնում ենք chrome.privacy.websites.privacySandboxEnabled API-ի աջակցումը, թեև այն ակտիվ կմնա հետադարձ համատեղելիության համար մինչև M113-ի թողարկումը։ Փոխարենը կարող եք օգտագործել chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled և chrome.privacy.websites.adMeasurementEnabled API-ները։ Մանրամասների համար անցեք https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled էջ։"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement սահմանափակումը հեռացված է։ Դուք դրա համար նշել եք false արժեքը, որն ընկալվում է որպես չաջակցվող SDES key negotiation մեթոդն օգտագործելու փորձ։ Այս հնարավորությունը հեռացված է։ Դրա փոխարեն օգտագործեք DTLS key negotiation հաղորդակարգն աջակցող ծառայություն։"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement սահմանափակումը հեռացված է։ Դուք դրա համար նշել եք true արժեքը, որը որևէ բանի վրա չի ազդում։ Այդ պատճառով այն կարող եք հեռացնել։"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Հետզանգի վրա հիմնված getStats() մեթոդն այլևս չի աջակցվում և հետագայում հեռացվելու է։ Դրա փոխարեն օգտագործեք բնութագրերին համապատասխանող getStats() մեթոդը։"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() մեթոդն այլևս չի աջակցվում։ Դրա փոխարեն օգտագործեք Selection.modify() մեթոդը։"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Ենթառեսուրսների հարցումները, որոնց URL-ներում պարունակվում են մուտքի զետեղված տվյալներ (օր․՝ **********************/), արգելափակվում են։"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Այս տարբերակը (rtcpMuxPolicy) այլևս չի աջակցվում և հետագայում կհեռացվի։"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer տարրի համար պահանջվում է տարբեր աղբյուրների մեկուսացում։ Մանրամասների համար անցեք https://developer.chrome.com/blog/enabling-shared-array-buffer/ էջ։"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Առանց օգտատիրոջ ակտիվացման speechSynthesis.speak() մեթոդն այլևս չի աջակցվում, և այդ գործառույթը կհեռացվի։"}, "generated/Deprecation.ts | UnloadHandler": {"message": "Ապաբեռնման իրադարձության ունկնդիրներն այլևս չեն աջակցվում և կհեռացվեն։"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Ընդլայնումներում անհրաժեշտ է միացնել տարբեր աղբյուրների մեկուսացումը, որպեսզի շարունակեք օգտագործել SharedArrayBuffer օբյեկտը։ Մանրամասների համար անցեք https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ էջ։"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter հատկանիշը հնացել է։ Դրա փոխարեն օգտագործեք GPUAdapterInfo isFallbackAdapter հատկանիշը։"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16-ը չի աջակցվում JSON պատասխանի կողմից XMLHttpRequest-ում"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "XMLHttpRequest մեթոդի համաժամացված կանչերը հիմնական նամակագրությունում այլևս չեն աջակցվում, քանի որ դրանք բացասաբար են ազդում վերջնական օգտագործողների աշխատանքի հարմարավետության վրա։ Լրացուցիչ օգնության համար անցեք https://xhr.spec.whatwg.org/ էջ։"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Շարժապատկեր"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Դասավորության շեղումները լինում են այն ժամանակ, երբ տարրերը տեղաշարժվում են առանց օգտատիրոջ փոխազդեցության։ [Պարզեք դասավորության շեղումների պատճառները](https://web.dev/articles/optimize-cls), օրինակ՝ տարրերի ավելացում, հեռացում կամ դրանց տառատեսակների փոփոխում էջի բեռնման ժամանակ։"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Տառատեսակի հարցում"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "iframe-ի զետեղում"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Դասավորության շեղումների կլաստեր @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Չհաջողվեց հայտնաբերել դասավորության շեղումների պատճառները"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Դասավորության շեղումներ չկան"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Դասավորության շեղումների պատճառները"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Դասավորության շեղումների գլխավոր պատճառները"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Ամենավատ կլաստերը"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Դասավորության շեղումների վատագույն կլաստերը"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Քեշի կենսաժամանակը"}, "models/trace/insights/Cache.ts | description": {"message": "Քեշի պահման երկար ժամանակի շնորհիվ էջը կարող է ավելի արագ բեռնվել, երբ այն նորից այցելեք։ [Իմանալ ավելին](https://web.dev/uses-long-cache-ttl/)։"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Քեշավորման անարդյունավետ կանոններով հարցումներ չկան"}, "models/trace/insights/Cache.ts | others": {"message": "ևս {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Հարցում"}, "models/trace/insights/Cache.ts | title": {"message": "Ընտրեք քեշի պահման արդյունավետ ժամակաշրջան"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM-ի մեծ չափսի պատճառով ոճերի հաշվարկը կարող է դանդաղել, իսկ փաթեթի վերադասավորման ծախսերը՝ ավելանալ, ինչը կազդի էջի արձագանքման վրա։ DOM-ի մեծ չափսի պատճառով նաև ավելի շատ հիշողություն է օգտագործվում։ [Իմանալ, թե ինչպես խուսափել DOM-ի մեծ չափսից](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)։"}, "models/trace/insights/DOMSize.ts | element": {"message": "Տարր"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Ենթատարրերի մեծամասնությունը"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-ի խորությունը"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Վիճակագրություն"}, "models/trace/insights/DOMSize.ts | title": {"message": "Օպտիմալացնել DOM-ի չափսը"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Տարրերի ընդհանուր թիվը"}, "models/trace/insights/DOMSize.ts | value": {"message": "Արժեք"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Ձեր առաջին ցանցային հարցումը ամենակարևորն է։  Կրճատեք հապաղումը՝ խուսափելով վերաուղղորդումներից, արագացնելով սերվերի պատասխանը և օգտագործելով տեքստի սեղմումը։"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Եղել են վերաուղղորդումներ ({PH1} վերաուղղորդում +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Սերվերը դանդաղ է արձագանքել (փաստացի արագությունը՝ {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Սեղմում չի կիրառվել"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Խուսափում է վերաուղղորդումներից"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Սերվերը արագ է արձագանքել (փաստացի արագությունը՝ {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Կիրառում է տեքստի սեղմում"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Վերաուղղորդումներ"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Սերվերի արձագանքման ժամանակը"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Փաստաթղթի հարցման հապաղում"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Չսեղմված ֆայլի ներբեռնում"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Կրկնվող բայթեր"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Աղբյուր"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Հեռացրեք խոշոր, կրկնօրինակված JavaScript մոդուլները փաթեթներից՝ ցանցային ակտիվության կողմից սպառվող ավելորդ բայթերը նվազեցնելու համար։"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Կրկնվող JavaScript կոդ"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Խորհուրդ ենք տալիս [font-display](https://developer.chrome.com/blog/font-display) պարամետրի համար սահմանել swap կամ optional արժեքը, որպեսզի ամբողջ տեքստը հավասարապես տեսանելի լինի։ Դասավորության շեղումները նվազեցնելու համար կարելի է լրացուցիչ օպտիմալացնել swap արժեքը [տառատեսակի ցուցանիշի փոխարինումների](https://developer.chrome.com/blog/font-fallbacks) միջոցով։"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Տառատեսակ"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Օգտագործվող տառատեսակներ"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Վատնված ժամանակը"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(անանուն)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Շատ API-ներ, որոնք սովորաբար կարդում են դասավորության երկրաչափությունը, հարկադրում են արտապատկերման շարժիչին դադարեցնել սկրիպտների կատարումը՝ ոճը և դասավորությունը հաշվարկելու համար։ Իմացեք ավելին [հարկադրված վերադասավորման](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) և դրանց նվազեցման եղանակների մասին։"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Սթեքի ժամանակագրություն"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Հարկադրված վերադասավորում"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Գործառույթի ամենաժամանակատար կանչը"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Վերադասավորման ընդհանուր ժամանակը"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[առանց հատկանշման]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Պատկերների ներբեռնման ժամանակի կրճատումը կարող է բարելավել էջի բեռնման ժամանակը և LCP-ի արժեքը։ [Իմանալ ավելին պատկերի չափսի օպտիմալացման մասին](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (մոտ {PH2})։"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Օպտիմալացված պատկերներ չկան"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Օպտիմալացնել ֆայլի չափսը"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "ևս {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Բարելավեք պատկերների բեռնումը"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Եթե ավելացնեք պատկերի սեղմման գործակիցը, ֆայլի ներբեռնման չափսը կարող է փոքրանալ"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Պատկերների ժամանակակից ձևաչափի (WebP, AVIF) օգտագործումը կամ պատկերների սեղմման ավելացումը կարող է բարելավել այս պատկերի ներբեռնման չափսը"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Պատկերի այս ֆայլը գերազանցում է ({PH1}) ցուցադրված չափսերը ({PH2})։ Օգտագործեք ադապտիվ պատկերներ՝ պատկերի ներբեռնման չափսը փոքրացնելու համար"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF-երի փոխարեն վիդեո ձևաչափերի օգտագործումը կարող է բարելավել շարժանկարային բովանդակության ներբեռնման չափսը"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Սկսեք վերլուծությունն ամենաերկար փուլից։ [Հետաձգումները կարելի է կրճատել](https://web.dev/articles/optimize-inp#optimize_interactions)։ Մշակման տևողությունը կրճատելու համար [օպտիմալացրեք հիմնական հոսքի ծախսերը](https://web.dev/articles/optimize-long-tasks)։ Դրանք սովորաբար վերաբերում են JS-ին։"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Տևողություն"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Ներածման հետաձգում"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Փոխազդեցություններ չեն հայտնաբերվել"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Փուլ"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Ներկայացման հետաձգում"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Մշակման տևողությունը"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP-ն ըստ փուլի"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP ցուցանիշն օպտիմալացնելու համար LCP պատկերը [հայտնաբերելի](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) դարձրեք անմիջապես HTML կոդում և [մի օգտագործեք հետաձգված բեռնում](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Կիրառվեց fetchpriority=high հատկությունը"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "պետք է կիրառվի՝ fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "հետաձգված բեռնումը չի կիրառվել"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP պատկերը բեռնվել է մեկնարկի ամենավաղ կետից {PH1} հետո։"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP չի հայտնաբերվել"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP-ի աղբյուրը չի գտնվել, քանի որ LCP-ն պատկեր չէ"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Հարցումը հայտնաբերելի է սկզբնական փաստաթղթում"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP-ի հարցումների հայտնաբերում"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Յուրաքանչյուր [փուլի համար բարելավման ռազմավարություններ կան](https://web.dev/articles/optimize-lcp#lcp-breakdown)։ Ցանկալի է, որ LCP-ի ժամանակի մեծ մասը ծախսվի ռեսուրսների բեռնման, այլ ոչ թե հետաձգումների վրա։"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Տևողություն"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Տարրի արտապատկերման հետաձգում"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Դաշտ p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP չի հայտնաբերվել"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Փուլ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Ռեսուրսի բեռնման հետաձգում"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Ռեսուրսների բեռնման տևողությունը"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP-ն ըստ փուլի"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Սկրիպտ"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Վատնած բայթեր"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Պոլիֆիլները և փոխակերպումները թույլ են տալիս հնացած դիտարկիչներին օգտագործել JavaScript-ի նոր գործառույթներ։ Այնուամենայնիվ, շատ պոլիֆիլներ և փոխակերպումներ պետք չեն արդիական դիտարկիչների համար։ Եթե ձեզ անհրաժեշտ չէ աջակցել հնացած դիտարկիչները, խորհուրդ ենք տալիս փոխել JavaScript-ի կառուցման գործընթացը, որպեսզի [Baseline](https://web.dev/articles/baseline-and-polyfills) գործառույթները տրանսպիլյացիայի ենթարկելու կարիք չլինի։ [Ինչու կայքերի մեծամասնությունը կարող է օգտագործել ES6+ կոդն առանց տրանսպիլյացիայի](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript-ի հնացած կոդ"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2-ը և HTTP/3-ը շատ առավելություններ ունեն՝ համեմատած HTTP/1.1-ի հետ, օրինակ՝ մուլտիպլեքսավորումը։ [Իմանալ ավելին HTTP-ի ժամանակակից տարբերակի օգտագործման մասին](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)։"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1-ի օգտագործմամբ հարցումներ չկան"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Հաղորդակարգ"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Հարցում"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP-ի ժամանակակից տարբերակ"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Աղբյուրը"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Հարցում"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Աղբյուր"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Ժամանակ"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Մոտավոր LCP խնայողությունը"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Չօգտագործված նախնական միացում։ Համոզվեք, որ crossorigin հատկանիշը ճիշտ է օգտագործվում։"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Խուսափեք կրիտիկական հարցումների շղթաներից](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)՝ կրճատելով շղթաների երկարությունը, նվազեցնելով ռեսուրսների ներբեռնման չափսը կամ հետաձգելով ոչ անհրաժեշտ ռեսուրսների ներբեռնումը, որպեսզի էջի բեռնումը բարելավվի։"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Ավելացրեք [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) հուշումներն ամենակարևոր աղբյուրների համար, սակայն փորձեք օգտագործել ոչ ավել քան 4-ը։"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Նախնական միացման տաբերակներ"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Կրիտիկական ուղու առավելագույն հապաղում․"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Ցանցային կախվածության վրա ազդող առաջադրանքներ չկան"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Բացակայում են նախնական միացման համար լրացուցիչ աղբյուրներն ու լավ տարբերակները"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ոչ մի աղբյուր նախապես չի միացվել"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[Preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) հուշումները դիտարկիչին օգնում են կապ հաստատել էջի բեռնման նախնական փուլերում, ինչի արդյունքում աղբյուրի առաջին հարցման ընթացքում ժամանակի խնայողություն է կատարվում։ Հաջորդիվ ներկայացված են աղբյուրները, որոնց նախապես միացել է էջը։"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Նախապես միացված աղբյուրներ"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Ցանցային կախվածության ծառ"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Գտնվել են 4-ից ավելի preconnect միացումներ։ Դրանք պետք է օգտագործվեն չափավոր եղանակով և միայն նշանակալի աղբյուրների համար։"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Չօգտագործված նախնական միացում։ Օգտագործեք միայն preconnect հատվածն աղբյուրների համար, որոնց հարցում կարող է ուղարկել էջը։"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Խուսափեք կրիտիկական հարցումների շղթաներից՝ կրճատելով շղթաների երկարությունը, նվազեցնելով ռեսուրսների ներբեռնման չափսը կամ հետաձգելով ոչ անհրաժեշտ ռեսուրսների ներբեռնումը, որպեսզի էջի բեռնումը բարելավվի։"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Հարցումներն արգելափակում են էջի նախնական արտապատկերումը, ինչը կարող է առաջացնել LCP-ի հապաղում։ [Հետաձգումը կամ տեղադրումը](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) կարող է ազատել այս ցանցային հարցումների բացասական ազդեցությունից։"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Տևողություն"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Արտապատկերումն արգելափակելող հարցումներ չեն գտնվել"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Հարցում"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Արտապատկերումն արգելափակող հարցումներ"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Եթե ոճի վերահաշվարկի ծախսերը բարձր են մնում, դրանք կարող է նվազեցնել ընտրիչի օպտիմալացումը։ [Օպտիմալացրեք ընտրիչները՝](https://developer.chrome.com/docs/devtools/performance/selector-stats) կատարման երկար ժամանակով և դանդաղ ուղու բարձր տոկոսով։ Համադրման ծախսերը կարելի է նվազեցնել՝ օգտագործելով ավելի պարզ ընտրիչներ և նվազեցնելով դրանց քանակը, ինչպես նաև DOM-ի ավելի կոմպակտ կառուցվածքի հաշվին։"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Անցած ժամանակը"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS ընտրիչի տվյալներ չեն գտնվել։ Արդյունավետության վահանակի կարգավորումներում միացրեք CSS ընտրիչի վիճակագրությունը։"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Համընկնումներ գտնելու փորձեր"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Համընկնումների թիվը"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ընտրիչի ծախսեր"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Հիմնական ընտրիչներ"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Ընդամենը"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Հիմնական նամակագրության ժամանակը"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Երրորդ կողմ"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Տեղափոխված տվյալների ծավալը"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Երրորդ կողմի կոդը կարող է զգալիորեն ազդել բեռնման արդյունավետության վրա։ [Կրճատեք և հետաձգեք երրորդ կողմի կոդի բեռնումը](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), որպեսզի արագացնեք ձեր էջի բովանդակության ցուցադրումը։"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Երրորդ կողմեր չեն գտնվել"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Երրորդ կողմեր"}, "models/trace/insights/Viewport.ts | description": {"message": "Եթե բջջային սարքերի համար դիտման շրջանն օպտիմալացված չէ, էկրանին հպելիս հնարավոր է [մինչև 300 մվ տևողությամբ հապաղում](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)։"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Հպման հապաղում բջջային սարքում"}, "models/trace/insights/Viewport.ts | title": {"message": "Օպտիմալացնել դիտման շրջանը բջջային սարքերի համար"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Ամբողջությամբ հնարավոր է քեշավորել միայն այն էջերը, որոնք բեռնվել են GET հարցման միջոցով։"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Միայն 2XX կարգավիճակի կոդով էջերն է հնարավոր քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome-ը հայտնաբերել է JavaScript կոդը քեշում աշխատեցնելու փորձ։"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Էջերը, որոնք հայցել են AppBanner, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Ամբողջ էջի քեշավորումն անջատված է Chrome-ի փորձնական գործառույթների բաժնում։ Քեշն այս սարքում միացնելու համար անցեք chrome://flags/#back-forward-cache էջ։"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Ամբողջ էջի քեշավորումն անջատված է հրամանատողի միջոցով։"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Ամբողջ էջի քեշավորումն անջատված է անբավարար հիշողության պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Ամբողջ էջի քեշավորումը չի աջակցվում պատվիրակի կողմից։"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Ամբողջ էջի քեշավորումն անջատված է նախացուցադրման համակարգի համար։"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Էջը հնարավոր չէ քեշավորել, քանի որ այն ունի BroadcastChannel նմուշ՝ գրանցված ունկնդիրներով։"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Էջերը, որոնք ունեն cache-control:no-store վերնագիրը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Քեշը դիտավորյալ մաքրվել է։"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Էջը հեռացվել է քեշից, որպեսզի հնարավոր լինի քեշավորել մեկ այլ էջ։"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Փլագիններ պարունակող Էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Անորոշ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Էջերը, որոնք օգտագործում են FileChooser API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Էջերը, որոնք օգտագործում են File System Access API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Էջերը, որոնք օգտագործում են Media Device Dispatcher, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Մեդիա նվագարկիչը բովանդակություն էր նվագարկում ելքի ժամանակ։"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Էջերը, որոնք օգտագործում են MediaSession API-ը և որոնց համար ընտրված է նվագարկման կագավիճակ, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Էջերը, որոնք օգտագործում են MediaSession API-ը և որոնց համար կարգավորված են գործողությունների մշակիչներ, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Ամբողջ էջի քեշավորումն անջատված է էկրանի ընթերցիչի պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Էջերը, որոնք օգտագործում են SecurityHandler, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Էջերը, որոնք օգտագործում են Serial API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Էջերը, որոնք օգտագործում են WebAuthetication API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Էջերը, որոնք օգտագործում են WebBluetooth API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Էջերը, որոնք օգտագործում են WebUSB API-ը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Ամբողջ էջի քեշավորումն անջատված է, քանի որ էջում, որը Cache-Control: no-store է օգտագործում, քուքիներն անջատված են։"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Էջերը, որոնք օգտագործում են Dedicated Worker կամ Worklet, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Դուք դուրս եք եկել փաստաթղթից, նախքան այն կբեռնվեր։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Ելքի ժամանակ Հավելվածների ազդերիզը ցուցադրված է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Ելքի ժամանակ Chrome-ի Գաղտնաբառերի կառավարիչը ցուցադրված է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Ելքի ժամանակ DOM-ի թորումը դեռ ավարտված չի եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Ելքի ժամանակ DOM-ի թորիչի դիտակն աշխատելիս է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Ամբողջ էջի քեշավորումն անջատված է Messaging API օգտագործող .ընդլայնման պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Նախքան ամբողջ էջի քեշ մտնելը երկարաժամկետ միացումով ընդլայնումները պետք է ընդհատեն միացումը։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Երկարաժամկետ միացումով ընդլայնումները փորձել են հաղորդագրություններ ուղարկել շրջանակներին ամբողջ էջի քեշում։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Ամբողջ էջի քեշավորումն անջատված է ընդլայնումների պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Ելքի ժամանակ էջի համար ցուցադրված է եղել մոդալ երկխոսության պատուհան (օր․՝ ձևաթղթի վերաուղարկման կամ http գաղտնաբառի պատուհան)։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Ելքի ժամանակ օֆլայն էջը ցուցադրված է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Ելքի ժամանակ «Հիշողության սպառման դեպքում միջամտություն» գոտին ցուցադրված է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Ելքի ժամանակ թույլտվության հարցումներ են եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Ելքի ժամանակ ելնող տարրերի արգելափակիչն աշխատելիս է եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Ելքի ժամանակ Ապահով դիտարկման տվյալները ցուցադրված են եղել։"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Ապահով դիտարկումը հայտնաբերել է, որ այս էջը խախտում է պարունակում, և արգելափակել է ելնող պատուհանը։"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Service Worker սկրիպտն ակտիվացվել է, երբ էջը ամբողջությամբ քեշավորված էր։"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Ամբողջ էջի քեշավորումն անջատված է փաստաթղթի սխալի պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames օգտագործող էջերը հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Էջը հեռացվել է քեշից, որպեսզի հնարավոր լինի քեշավորել մեկ այլ էջ։"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Էջերը, որոնք տրամադրել են մեդիա հեռարձակման թույլտվություն, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Ներկառուցված բովանդակության որոշակի տեսակներ պարունակող էջերը (օրինակ՝ PDF ֆայլերը) դեռ չեն աջակցում ամբողջ էջի քեշավորումը։"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Էջերը, որոնք օգտագործում են IdleManager, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Բաց IndexedDB միացումով Էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Ամբողջ էջի քեշավորումն անջատված է IndexedDB իրադարձության պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Օգտագործվել են չաջակցվող API-ներ։"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Էջերը, որոնցում ընդլայնումների միջոցով տեղադրված է JavaScript, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Էջերը, որոնցում ընդլայնումների միջոցով տեղադրված է StyleSheet, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Ներքին սխալ։"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Ամբողջ էջի քեշավորումն անջատված է, քանի որ JavaScript ցանցային հարցումը Cache-Control: no-store գլխագրով ռեսուրս է ստացել։"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Ամբողջ էջի քեշավորումն անջատված է keep-alive հարցման պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Էջերը, որոնք օգտագործում են ստեղնաշարի կողպում, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Դուք դուրս եք եկել էջից, նախքան այն կբեռնվեր։"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Էջերը, որոնց հիմնական ռեսուրսում առկա է cache-control:no-cache վերնագիրը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Էջերը, որոնց հիմնական ռեսուրսում առկա է cache-control:no-store վերնագիրը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Չհաջողվեց մինչև ելքի չեղարկումը վերականգնել էջը ամբողջական քեշավորումից։"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Էջը հեռացվել է քեշից, քանի որ ակտիվ ցանցային կապի միջոցով ստացվել են չափից շատ տվյալներ։ Chrome-ը սահմանափակում է քեշավորված էջերին փոխանցվող տվյալների ծավալը։"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Էջերը, որոնք ունեն fetch() կամ XHR ուղարկված հարցումներ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Էջը հեռացվել է ամբողջական քեշավորումից, քանի որ ակտիվ ցանցային հարցումը կատարվել էր վերաուղղորդմամբ։"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Էջը հեռացվել է քեշից, քանի որ ցանցային կապը երկար ժամանակ բաց էր։ Chrome-ը սահմանափակում է քեշավորված էջերին տվյալների փոխանցման ժամանակը։"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Էջերը, որոնք չունեն պատասխանի վավեր վերնագիր, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Անցումը կատարվել է ոչ հիմնական շրջանակում։"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Էջերը, որոնցում կատարվում են ինդեքսավորված տվյալների շտեմարանների գործարքներ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Ակտիվ ցանցային հարցումով էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Ցանցային fetch հարցումով էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Ակտիվ ցանցային հարցումով էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR ակտիվ ցանցային հարցումով էջերը դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Էջերը, որոնք օգտագործում են PaymentManager, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Էջերը, որոնք օգտագործում են «Նկար նկարի մեջ» գործառույթը, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Էջերը, որոնցում ցուցադրված է տպման միջերես, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Էջը բացվել է window.open()-ի օգնությամբ, իսկ մեկ այլ ներդիր հղվում է դրան, կամ էջն է պատուհան բացել։"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Էջի արտապատկերման գործընթացը ամբողջական քեշում խափանված է։"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Էջի արտապատկերման գործընթացը ամբողջական քեշում դադարեցված է։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Էջերը, որոնք հայցել են ձայնագրման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Էջերը, որոնք հայցել են տվիչների օգտագործման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Էջերը, որոնք հայցել են ֆոնային համաժամացման կամ տվյալների բեռնման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Էջերը, որոնք հայցել են MIDI սարքերի օգտագործման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Էջերը, որոնք հայցել են ծանուցումների օգտագործման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Էջերը, որոնք հայցել են հիշողության օգտագործման թույլտվություն, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Էջերը, որոնք հայցել են տեսագրման թույլտվություններ, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Հնարավոր է քեշավորել միայն HTTP/HTTPS սխեմայով URL ունեցող էջերը։"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Էջը հայցվել է Service Worker սկրիպտի կողմից, երբ ամբողջությամբ քեշավորված էր։"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service Worker սկրիպտը փորձել է ուղարկել MessageEvent հատկանիշը ամբողջությամբ քեշավորված էջին։"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Service Worker-ի գրանցումը չեղարկվել է, երբ էջը ամբողջությամբ քեշավորված էր։"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Էջը հեռացվել է ամբողջական քեշավորումից Service Worker-ի ակտիվացման պատճառով։"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome-ը վերագործարկվել է։ Ամբողջ էջի քեշավորման բոլոր գրառումները մաքրվել են։"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Էջերը, որոնք օգտագործում են SharedWorker, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Էջերը, որոնք օգտագործում են SpeechRecognizer, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Էջերը, որոնք օգտագործում են SpeechSynthesis, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe պատուհանը էջում գործարկել է անցում, որը չի ավարտվել։"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Էջերը, որոնց ենթառեսուրսում առկա է cache-control:no-cache վերնագիրը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Էջերը, որոնց ենթառեսուրսում առկա է cache-control:no-store վերնագիրը, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Էջը գերազանցել է ամբողջական քեշում գտնվելու առավելագույն ժամանակը, և դրա ժամկետը սպառվել է։"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Էջը ամբողջական քեշում ավելացնելու ժամանակը սպառվել է (հնարավոր է՝ pagehide իրադարձությունների մշակիչները երկար են աշխատում)։"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Էջն ունի ապաբեռնման մշակիչ հիմնական շրջանակում։"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Էջն ունի ապաբեռնման մշակիչ ենթաշրջանակում։"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Դիտարկիչը փոխել է գործակալ ծրագրի փոխարինման վերնագիրը։"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Էջերը, որոնք տրամադրել են տեսագրման կամ ձայնագրման թույլտվություն, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Էջերը, որոնք օգտագործում են WebDatabase, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Էջերը, որոնք օգտագործում են WebHID, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Էջերը, որոնք օգտագործում են WebLocks, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Էջերը, որոնք օգտագործում են WebNFC, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Էջերը, որոնք օգտագործում են WebOTPService, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC-ով էջերին հասանելի չէ ամբողջ էջի քեշավորումը։"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Ամբողջ էջի քեշավորումն անջատված է, քանի որ WebRTC-ն է օգտագործվել։"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Էջերը, որոնք օգտագործում են WebShare, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Էջերը, որոնք օգտագործում են WebSocket միջերես, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Ամբողջ էջի քեշավորումն անջատված է, քանի որ WebSocket-ն է օգտագործվել։"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Էջերը, որոնք օգտագործում են WebTransport, հնարավոր չէ ամբողջությամբ քեշավորել։"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Ամբողջ էջի քեշավորումն անջատված է, քանի որ WebTransport-ն է օգտագործվել։"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Էջերը, որոնք օգտագործում են WebXR, դեռևս հնարավոր չէ ամբողջությամբ քեշավորել։"}}