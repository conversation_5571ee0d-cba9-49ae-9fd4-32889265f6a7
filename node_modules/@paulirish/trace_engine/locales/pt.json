{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "A autorização não será coberta pelo caractere curinga (*) no gerenciamento do CORS de Access-Control-Allow-Headers."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Use o atributo disableRemotePlayback para desativar a integração padrão do Google Cast em vez de usar o seletor -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "O valor de aparência CSS slider-vertical não está padronizado e será removido."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Solicitações de recursos com URLs que contêm caracteres \\(n|r|t) de espaço em branco removido e caracteres \"menor que\" (<) estão bloqueadas. Remova novas linhas e codifique caracteres \"menor que\" em lugares como valores de atributos de elementos para carregar esses recursos."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "O uso de chrome.loadTimes() foi descontinuado. Use a API padronizada Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "O uso de chrome.loadTimes() foi descontinuado. Use a API padronizada Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "O uso de chrome.loadTimes() foi descontinuado. Use a API padronizada nextHopProtocol na Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies contendo um caractere \\(0|r|n) serão rejeitados em vez de truncados."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "O relaxamento de políticas da mesma origem definindo document.domain foi descontinuado e será desativado por padrão. Este alerta é para um acesso entre origens que foi ativado ao configurar document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "O acionamento de window.alert de iframes entre origens foi descontinuado e será removido."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "O acionamento de window.confirm de iframes entre origens foi descontinuado e será removido."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Suporte para dados: os URLs em SVGUseElement foram descontinuados e serão removidos no futuro."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() e watchPosition() não funcionam mais em origens não seguras. Para usar esse recurso, considere trocar a origem do seu aplicativo para uma segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "O uso de getCurrentPosition() e watchPosition() foi descontinuado em origens não seguras. Para usar esse recurso, considere trocar a origem do seu aplicativo para uma segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() não funciona mais em origens não seguras. Para usar esse recurso, considere trocar a origem do seu aplicativo para uma segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Encontramos uma tag <h1> dentro de <article>, <aside>, <nav> ou <section> que não tem o tamanho da fonte especificado. O tamanho do texto do título vai mudar neste navegador em breve. Para mais informações, consulte https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 (página em inglês)."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "O uso de RTCPeerConnectionIceErrorEvent.hostCandidate foi descontinuado. Use RTCPeerConnectionIceErrorEvent.address ou RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Este formato de solicitação de credenciais digitais navigator.credentials.get() foi descontinuado. Atualize sua chamada usando o novo formato."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "A origem do comerciante e os dados arbitrários do evento do service worker canmakepayment foram descontinuados e vão ser removidos: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "O site solicitou um recurso secundário de uma rede que ele só conseguiu acessar devido à posição privilegiada na rede que os usuários dele têm. Essas solicitações expõem para a Internet dispositivos e servidores particulares, aumentando o risco de ataques de falsificação de solicitações entre sites (CSRF, na sigla em inglês) e/ou de vazamento de informações. Para reduzir os riscos, o Chrome descontinua solicitações para recursos secundários particulares quando iniciadas em contextos não seguros e vai começar a fazer o bloqueio delas."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "O campo dailyUpdateUrl de InterestGroups transmitido para joinAdInterestGroup() foi renomeado como updateUrl para refletir o comportamento com mais precisão."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "O uso de Intl.v8BreakIterator foi descontinuado. Use Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "O CSS não pode ser carregado usando URLs file:, a menos que eles terminem com uma extensão de arquivo .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "O uso de SourceBuffer.abort() para cancelar a remoção de intervalo assíncrono de remove() foi descontinuado devido a uma mudança na especificação. O suporte será removido futuramente. Como alternativa, você precisa detectar o evento updateend. O uso de abort() tem como objetivo cancelar um anexo de mídia assíncrono ou redefinir o estado do analisador."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "A definição de MediaSource.duration abaixo do carimbo de data/hora mais alto da apresentação de frames codificados em buffer foi descontinuada por causa de uma mudança na especificação. O suporte para remoção implícita de mídia truncada em buffer será removido no futuro. Faça uma remove(newDuration, oldDuration) explícita em todos os sourceBuffers, em que newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "A Web MIDI pede permissão de uso mesmo que o sysex não esteja especificado em MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "A API Notification não pode mais ser usada em origens não seguras. Considere trocar a origem do seu aplicativo para uma segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para ver mais detalhes."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "A permissão para a API Notification não pode mais ser solicitada em um iframe entre origens. Considere solicitar a permissão em um frame de nível superior ou abrir uma nova janela."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "A opção imageOrientation: 'none' em createImageBitmap foi descontinuada. Em vez dela, use createImageBitmap com a opção \"{imageOrientation: 'from-image'}\"."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Seu parceiro está negociando uma versão (D)TLS obsoleta. Fale com ele para que isso seja corrigido."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Especificar overflow: visible em tags img, video e canvas pode fazer com que elas produzam conteúdo visual fora dos limites do elemento. Consulte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md (em inglês) para saber mais."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "O uso de paymentManager.instruments foi descontinuado. Use uma instalação just-in-time para gerenciadores de pagamento."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Sua chamada PaymentRequest ignorou a diretiva connect-src da Política de Segurança de Conteúdo (CSP). Essa ação foi descontinuada. Adicione o identificador da forma de pagamento da API PaymentRequest (no campo supportedMethods) à diretiva connect-src da CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "O uso de StorageType.persistent foi descontinuado. Use o navigator.storage padronizado."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "O elemento <source src> com um pai <picture> é inválido e será ignorado. Use <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "O webkitCancelAnimationFrame é específico para fornecedores. Em vez dele, use o cancelAnimationFrame padrão."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "O webkitRequestAnimationFrame é específico para fornecedores. Em vez dele, use o requestAnimationFrame padrão."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "O uso de HTMLVideoElement.webkitDisplayingFullscreen foi descontinuado. Use Document.fullElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "O uso de HTMLVideoElement.webkitEnterFullScreen() foi descontinuado. Use Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "O uso de HTMLVideoElement.webkitEnterFullscreen() foi descontinuado. Use Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "O uso de HTMLVideoElement.webkitExitFullScreen() foi descontinuado. Use Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "O uso de HTMLVideoElement.webkitExitFullscreen() foi descontinuado. Use Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "O uso de HTMLVideoElement.webkitSupportsFullscreen foi descontinuado. Use Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Estamos descontinuando a API chrome.privacy.websites.privacySandboxEnabled, embora ela continue ativa para compatibilidade com versões anteriores até a versão M113. Em vez dela, use chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled e chrome.privacy.websites.adMeasurementEnabled. Consulte https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "A restrição DtlsSrtpKeyAgreement foi removida. Você especificou um valor false para essa restrição. Essa ação foi interpretada como uma tentativa de usar o método SDES key negotiation removido. Essa funcionalidade foi removida. Use um serviço que tenha suporte à DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "A restrição DtlsSrtpKeyAgreement foi removida. Você especificou um valor true para essa restrição. Essa ação não teve efeito, mas você pode remover essa restrição para manter a organização."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "O getStats() baseado em callback foi descontinuado e será removido. Use o getStats() que obedece às especificações."}, "generated/Deprecation.ts | RangeExpand": {"message": "O uso de Range.expand() foi descontinuado. Use Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "As solicitações de recursos secundários com URLs que contêm credenciais incorporadas (como **********************/) estão bloqueadas."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "A opção rtcpMuxPolicy foi descontinuada e será removida."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer requer isolamento entre origens. Consulte https://developer.chrome.com/blog/enabling-shared-array-buffer/ (link em inglês) para ver mais detalhes."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "A API speechSynthesis.speak() sem a ativação do usuário foi descontinuada e será removida."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Os listeners de eventos de descarregamento foram descontinuados e serão removidos."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "As extensões precisam ativar o isolamento entre origens para continuar usando o recurso SharedArrayBuffer. Consulte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/ (link em inglês)."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "O atributo isFallbackAdapter do GPUAdapter foi descontinuado. Use o atributo isFallbackAdapter do GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Não há suporte da resposta JSON para UTF-16 no XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "A XMLHttpRequest síncrona na linha de execução principal foi descontinuada por causa dos efeitos prejudiciais à experiência do usuário final. Para receber mais ajuda, acesse https://xhr.spec.whatwg.org/ (link em inglês)."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animação"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "As mudanças de layout ocorrem quando os elementos se movem sem qualquer interação do usuário. [Investigue as causas de mudanças no layout](https://web.dev/articles/optimize-cls), como elementos adicionados, removidos ou com fontes alteradas durante o carregamento da página."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Solicitação de fonte"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "iframe injetado"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Cluster de troca de layout em {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Não foi possível detectar as causas da troca de layout"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Nenhuma troca de layout"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Causas da troca de layout"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Principais causas da troca de layout"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Pior cluster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Pior cluster de troca de layout"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL do cache"}, "models/trace/insights/Cache.ts | description": {"message": "Um cache com ciclo de vida longo pode acelerar visitas repetidas à sua página. [<PERSON><PERSON> ma<PERSON>](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nenhum pedido com políticas de cache ineficazes"}, "models/trace/insights/Cache.ts | others": {"message": "<PERSON><PERSON> {<PERSON><PERSON>}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Solicitação"}, "models/trace/insights/Cache.ts | title": {"message": "Use ciclos de vida eficientes de cache"}, "models/trace/insights/DOMSize.ts | description": {"message": "Um DOM grande pode aumentar a duração dos cálculos de estilo e reflows de layout, o que afeta a capacidade de resposta da página. Um DOM grande também aumenta o uso da memória. [Aprenda a evitar que o tamanho do DOM seja grande demais](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Elemento"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Maioria das crianças"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Profundidade do DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Estatística"}, "models/trace/insights/DOMSize.ts | title": {"message": "Otimizar o tamanho do DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Total de elementos"}, "models/trace/insights/DOMSize.ts | value": {"message": "Valor"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "A primeira solicitação de rede é a mais importante.  Reduza a latência dela evitando redirecionamentos, garantindo uma resposta rápida do servidor e ativando a compactação de texto."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON><PERSON> redirecionamentos ({PH1}, mais de {PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "O servidor respondeu lentamente (observado: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nenhuma compactação aplicada"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Sem redirecionamentos"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "O servidor respondeu rapidamente (observado: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Compactação de texto aplicada"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redirecionamentos"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Tempo de resposta do servidor"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latência da solicitação de documentos"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Download sem compactação"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Bytes duplicados"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Origem"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Remover módulos JavaScript grandes e duplicados de pacotes para reduzir bytes desnecessários consumidos por atividades da rede."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript duplicado"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Defina [font-display](https://developer.chrome.com/blog/font-display) como swap ou optional para garantir que o texto fique visível de forma consistente. O atributo swap pode ser otimizado ainda mais para reduzir as trocas de layout com [substituições de métrica de fonte](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Fonte"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Exibição de fontes"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Tempo perdido"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(an<PERSON><PERSON><PERSON>)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Muitas APIs, que geralmente leem a geometria do layout, forçam o mecanismo de renderização a pausar a execução do script para calcular o estilo e o layout. Saiba mais sobre o [reflow forçado](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) e as mitigações dele."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stack trace"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Reflow forçado"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Chamada de função principal"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Tempo total de reflow"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[sem atribuição]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Reduzir o tempo de download das imagens pode melhorar o tempo de carregamento percebido da página e a LCP. [Saiba mais sobre como otimizar o tamanho da imagem](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Estimativa: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "<PERSON>enhuma imagem pode ser otimizada"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Tamanho de arquivo otimizado"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "<PERSON><PERSON> {<PERSON><PERSON>}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Melhorar a entrega de imagens"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Aumentar o fator de compactação da imagem pode aprimorar o tamanho de download dela."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Usar um formato de imagem moderno (WebP, AVIF) ou aumentar a compactação da imagem pode aprimorar o tamanho do download da imagem."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Este arquivo de imagem é maior do que precisa ser ({PH1}) para as dimensões exibidas ({PH2}). Use imagens responsivas para reduzir o tamanho do download da imagem."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Usar formatos de vídeo em vez de GIFs pode aprimorar o tamanho de download de conteúdo animado."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Comece a investigação pela fase mais longa. [Os atrasos podem ser minimizados](https://web.dev/articles/optimize-inp#optimize_interactions). Para reduzir a duração do processamento, [otimize os custos da linha de execução principal](https://web.dev/articles/optimize-long-tasks), geralmente JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Duração"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Latência na entrada"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nenhuma interação foi detectada"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Atraso na apresentação"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Duração do processamento"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP por fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Otimize a LCP tornando a imagem LCP [detectável](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) no HTML imediatamente e [evitando o carregamento lento](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Propriedade fetchpriority=high aplicada"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "A propriedade fetchpriority=high precisa ser aplicada"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "o carregamento lento não foi aplicado"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "A imagem da LCP carregou {PH1} depois do ponto de partida mais antigo."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nenhum LCP detectado"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nenhum recurso de LCP foi detectado porque o LCP não é uma imagem"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "A solicitação é detectável no documento inicial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Descoberta de solicitações de LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Cada [fase tem estratégias de melhoria específicas](https://web.dev/articles/optimize-lcp#lcp-breakdown). O ideal é que a maior parte do tempo de LCP seja gasto no carregamento dos recursos, não em atrasos."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Duração"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Atraso na renderização do elemento"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "P75 de campo"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nenhum LCP detectado"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Atraso no carregamento de recursos"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Duração do carregamento de recursos"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP por fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bytes desperdiçados"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfills e transformações permitem que navegadores mais antigos usem novos recursos do JavaScript. No entanto, muitos não são necessários para navegadores mais recentes. Considere mudar seu processo de criação do JavaScript para não transpilar os recursos [de referência](https://web.dev/articles/baseline-and-polyfills), a menos que você precise da compatibilidade com navegadores mais antigos. [Entenda por que a maioria dos sites pode implantar código ES6+ sem transpilação](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript legado"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 e HTTP/3 oferecem muitos benefícios em relação ao HTTP/1.1, como multiplexação. [Saiba mais sobre o uso de HTTP moderno](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Nenhuma solicitação usou HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocolo"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Solicitação"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP moderno"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origem"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Solicitação"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Fonte"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Economia estimada de LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Pré-conexão não usada. Confira se o atributo crossorigin está sendo usado corretamente."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Evite encadear solicitações críticas](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) reduzindo o tamanho das cadeias, o tamanho do download de recursos ou adiando o download de recursos desnecessários para melhorar o carregamento da página."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Adicione dicas de [pré-conex<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) às suas origens mais importantes, mas tente não usar mais que 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidatos à pré-conexão"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latência máxima do caminho crítico:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nenhuma tarefa de renderização foi afetada por dependências de rede"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nenhuma outra origem é uma boa candidata para a pré-conexão"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "nenhuma origem foi pré-conectada"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "As dicas de [pré-conexão](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ajudam o navegador a estabelecer uma conexão mais cedo durante o carregamento da página, poupando tempo quando a primeira solicitação para essa origem é feita. A página fez a pré-conexão com as seguintes origens:"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origens pré-conectadas"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Árvore de dependência da rede"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Foram encontradas mais de quatro conexões preconnect. Elas precisam ser usadas com moderação e somente para as origens mais importantes."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Pré-conexão não usada. Use preconnect apenas para origens que a página provavelmente vai solicitar."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Evite encadear solicitações críticas reduzindo o tamanho das cadeias, diminuindo o tamanho do download de recursos ou adiando o download de recursos desnecessários para melhorar o carregamento da página."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "As solicitações estão bloqueando a renderização inicial da página, o que pode atrasar a LCP. Essas solicitações de rede podem ser [deferidas ou colocadas inline](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) para que fiquem fora do caminho crítico."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Duração"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nenhuma solicitação de bloqueio de renderização para essa navegação"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Solicitação"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Renderizar solicitações de bloqueio"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Se os custos de \"Recalcular estilo\" permanecerem altos, a otimização do seletor poderá reduzir esse valor. [Otimize os seletores](https://developer.chrome.com/docs/devtools/performance/selector-stats) com muito tempo decorrido e porcentagem alta de caminhos lentos. Seletores mais simples, menos seletores, um DOM menor e um DOM com menos níveis reduzem os custos correspondentes."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Tempo decorrido"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nenhum dado de seletor de CSS foi encontrado. As estatísticas do seletor de CSS precisam ser ativadas nas configurações do painel de desempenho."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Tentativas de correspondência"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Número de correspondências"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Custos do seletor de CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Principais se<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Tempo na linha de execução principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Código de terceiros pode afetar significativamente a performance de carregamento. [Reduza e adie o carregamento de código de terceiros](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) para priorizar o conteúdo da sua página."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON><PERSON><PERSON> terceiro encontrado"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Te<PERSON><PERSON><PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "As interações por toque podem [atrasar em até 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) se a janela de visualização não for otimizada para dispositivos móveis."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Atraso de toque em dispositivo móvel"}, "models/trace/insights/Viewport.ts | title": {"message": "Otimizar janela de visualização para dispositivos móveis"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Apenas páginas carregadas por uma solicitação GET são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Apenas páginas com um código de status de 2XX podem ser adicionadas ao cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "O Chrome detectou uma tentativa de executar o JavaScript enquanto a página estava no cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "No momento, as páginas que solicitaram um AppBanner não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "O cache de avanço e retorno foi desativado por sinalizações. Acesse chrome://flags/#back-forward-cache para ativar localmente no dispositivo."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "O cache de avanço e retorno foi desativado pela linha de comando."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "O cache de avanço e retorno foi desativado devido à memória insuficiente."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "O delegado não oferece suporte ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "O cache de avanço e retorno foi desativado pelo pré-renderizador."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Não é possível adicionar a página ao cache porque ela tem uma instância BroadcastChannel com listeners registrados."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Não é possível adicionar páginas com um cabeçalho cache-control:no-store ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "O cache foi apagado intencionalmente."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "A página foi removida do cache para permitir que outra fosse adicionada."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "No momento, as páginas que contêm plug-ins não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Indefinido"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "As páginas que usam a API FileChooser não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "As páginas que usam a API File System Access não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "No momento, as páginas que usam o Media Device Dispatcher não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Um player de mídia estava ativo quando o usuário sa<PERSON> da página."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "As páginas que usam a API MediaSession e definiram um estado de reprodução não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "As páginas que usam a API MediaSession e definiram gerenciadores de ação não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "O cache de avanço e retorno foi desativado devido ao leitor de tela."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "As páginas que usam a classe SecurityHandler não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "As páginas que usam a API Serial não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "As páginas que usam a API WebAuthetication não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "As páginas que usam a API WebBluetooth não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "As páginas que usam a API WebUSB não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "O cache de avanço e retorno foi desativado porque os cookies também foram desativados em uma página que usa Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "No momento, as páginas que usam um worker ou worklet dedicados não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "O documento não concluiu o carregamento antes de o usuário sair dele."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "O banner de apps estava presente quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "O Gerenciador de senhas do Chrome estava presente quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A destilação do DOM estava em andamento quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "O leitor do destilador do DOM estava presente quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "O cache de avanço e retorno foi desativado porque as extensões usam uma API de mensagens."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Extensões com conexão de longa duração precisam encerrar a conexão antes de serem adicionadas ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Extensões com conexão de longa duração tentaram enviar mensagens a frames no cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "O cache de avanço e retorno foi desativado devido a extensões."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Uma caixa de diálogo modal (por exemplo, uma de senha HTTP ou reenvio de formulário) foi mostrada quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "A página off-line foi mostrada quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "A barra de intervenção de falta de memória estava presente quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Havia solicitações de permissão quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "O bloqueador de pop-ups estava presente quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Os detalhes do Navegação segura foram mostrados quando o usuário saiu da página."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "O Navegação segura considerou esta como página abusiva e bloqueou pop-ups."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Um service worker foi ativado enquanto a página estava no cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "O cache de avanço e retorno foi desativado devido a um erro no documento."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Páginas usando FencedFrames não podem ser armazenadas em bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "A página foi removida do cache para permitir que outra fosse adicionada."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "No momento, as páginas que concederam acesso ao streaming de mídia não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "No momento, as páginas com alguns tipos de conteúdo incorporado (por exemplo, PDFs) não estão qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "No momento, as páginas que usam a classe IdleManager não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "No momento, as páginas com uma conexão IndexedDB aberta não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "O cache de avanço e retorno foi desativado devido a um evento IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "APIs não qualificadas foram usadas."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "No momento, as páginas que tiveram uma injeção de JavaScript por extensões não estão qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "No momento, as páginas que tiveram uma injeção de StyleSheet por extensões não estão qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON>rro interno."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "O cache de avanço e retorno foi desativado porque algumas solicitações de rede JavaScript receberam recursos com o cabeçalho Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "O cache de avanço e retorno foi desativado devido a uma solicitação de sinal de atividade."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "No momento, as páginas que usam o bloqueio de teclado não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "A página não concluiu o carregamento antes de o usuário sair dela."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-cache ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-store ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "A navegação foi cancelada antes que a página pudesse ser restaurada usando o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "A página foi removida do cache porque uma conexão de rede ativa recebeu dados demais. O Chrome limita a quantidade de dados que uma página pode receber em cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "No momento, as páginas com fetch() ou XHR em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "A página foi removida do cache de avanço e retorno porque uma solicitação de rede ativa envolveu um redirecionamento."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "A página foi removida do cache porque uma conexão de rede ficou aberta por tempo demais. O Chrome limita a quantidade de tempo pelo qual uma página pode receber dados em cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Não é possível adicionar páginas que não têm um cabeçalho de resposta válido ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "A navegação aconteceu em um frame diferente do principal."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "No momento, as páginas com transações de banco de dados indexado em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "No momento, as páginas com uma solicitação de rede em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "No momento, as páginas com uma solicitação de busca de rede em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "No momento, as páginas com uma solicitação de rede em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "No momento, as páginas com uma solicitação XHR de rede em andamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "No momento, as páginas que usam o PaymentManager não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "No momento, as páginas que usam o picture-in-picture não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "No momento, as páginas que usam a interface de impressão não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "A página foi aberta usando \"window.open()\" e é referenciada por outra guia, ou a página abriu uma janela."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "O processo do renderizador da página no cache de avanço e retorno falhou."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "O processo do renderizador da página no cache de avanço e retorno foi encerrado."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "No momento, as páginas que solicitaram permissões de captura de áudio não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "No momento, as páginas que solicitaram permissões de sensor não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "No momento, as páginas que solicitaram a sincronização em segundo plano ou permissões de busca não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "No momento, as páginas que solicitaram permissões de MIDI não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "No momento, as páginas que solicitaram permissões de notificação não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "No momento, as páginas que solicitaram acesso ao armazenamento não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "No momento, as páginas que solicitaram permissões de captura de vídeo não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Apenas páginas com um esquema de URL HTTP/HTTPS podem ser adicionadas ao cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "A página foi reivindicada por um service worker enquanto estava no cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Um service worker tentou enviar um MessageEvent à página no cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "A inscrição do ServiceWorker foi cancelada enquanto a página estava no cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "A página foi removida do cache de avanço e retorno devido à ativação de um service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "O Chrome foi reiniciado e apagou as entradas do cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "No momento, as páginas que usam a interface SharedWorker não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "No momento, as páginas que usam a classe SpeechRecognizer não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "No momento, as páginas que usam a SpeechSynthesis não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Um iframe na página iniciou uma navegação que não foi concluída."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos secundários com cache-control:no-cache ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos secundários com cache-control:no-store ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "A página excedeu o tempo máximo no cache de avanço e retorno e expirou."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "A página expirou ao ser adicionada ao cache de avanço e retorno, provavelmente devido aos gerenciadores pagehide abertos há muito tempo."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "A página tem um gerenciador de descarregamento no frame principal."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "A página tem um gerenciador de descarregamento em um frame secundário."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "O navegador mudou o cabeçalho de substituição do user agent."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "No momento, as páginas que concederam acesso à gravação de vídeo ou áudio não estão qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "No momento, as páginas que usam WebDatabase não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "No momento, as páginas que usam WebHID não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "No momento, as páginas que usam WebLocks não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "No momento, as páginas que usam WebNfc não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "No momento, as páginas que usam a classe WebOTPService não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Não é possível adicionar páginas com WebRTC ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "O cache de avanço e retorno foi desativado porque o WebRTC foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "No momento, as páginas que usam WebShare não são qualificadas para o cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Não é possível adicionar páginas com WebSocket ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "O cache de avanço e retorno foi desativado porque o WebSocket foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Não é possível adicionar páginas com WebTransport ao cache de avanço e retorno."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "O cache de avanço e retorno foi desativado porque o WebTransport foi usado."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "No momento, as páginas que usam o WebXR não são qualificadas para o cache de avanço e retorno."}}