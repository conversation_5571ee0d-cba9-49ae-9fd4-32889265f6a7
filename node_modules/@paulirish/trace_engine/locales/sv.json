{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Auktorisering omfattas inte av jokertecknet (*) i CORS Access-Control-Allow-Headers-hantering."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Attributet disableRemotePlayback ska användas för att inaktivera standardintegreringen av Cast i stället för väljaren -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS-utseendevärdet slider-vertical är inte standardiserat och kommer att tas bort."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Resursförfrågningar vars webbadresser innehöll både de borttagna blankstegstecknen \\(n|r|t) och mindre än-tecknen (<) blockeras. Ta bort radbrytningstecken och koda mindre än-tecken från t.ex. värden för elementattribut om du vill läsa in dessa resurser."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() är utfasad. Använd det standardiserade API:et Navigation Timing 2 i stället."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() är utfasad. Använd i stället det standardiserade API:et Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() är utfasad. Använd det standardiserade API:et nextHopProtocol i Navigation Timing 2 i stället."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies som innehåller ett \\(0|r|n)-tecken avvisas i stället för att trunkeras."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Funktionen för att lätta på principen gällande samma ursprung genom att ange document.domain är utfasad och inaktiveras som standard. Denna varning om utfasning gäller åtkomst via korsursprung som aktiverades genom inställning av document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Aktivering av window.alert fr<PERSON>n iframes för korsursprung har fasats ut och kommer att tas bort."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Aktivering av window.confirm fr<PERSON>n iframes för korsursprung har fasats ut och kommer att tas bort."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Stöd för data: webbadresser i SVGUseElement har fasats ut och tas bort i framtiden."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() och watchPosition() fungerar inte längre i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() och watchPosition() är utfasade i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() fungerar inte längre i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "En <h1>-tagg hittades i en <article>, <aside>, <nav> eller <section> som inte har en angiven teckensnittsstorlek. Storleken på den här rubriken ändras i webbläsaren inom en snar framtid. Mer information finns på https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate är utfasad. Använd RTCPeerConnectionIceErrorEvent.address eller RTCPeerConnectionIceErrorEvent.port i stället."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Det här formatet för GET-beg<PERSON><PERSON> navigator.credentials.get() om digitala certifikat har fasats ut. Uppdatera anropet så att det nya formatet används."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Säljarursprunget och arbiträr data från tjänstefunktionens canmakepayment-händelse har fasats ut och tas bort: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Webbplatsen begärde en underresurs från ett nätverk som den enbart hade åtkomst till på grund av att användaren hade särskild nätverksbehörighet. Med dessa förfrågningar får enheter och servrar som inte är offentliga åtkomst till internet, vilket ökar risken för bedrägerier med begäran mellan webbplatser och/eller läckage av uppgifter. I syfte att minska dessa risker fasar Chrome ut förfrågningar till icke-offentliga underresurser som har startats i osäkra kontexter och kommer att blockera dem."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Fältet dailyUpdateUrl i InterestGroups som vidarebefordrats till joinAdInterestGroup() har döpts om till updateUrl för att bättre motsvara dess beteende."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator är utfasad. Använd Intl.Segmenter i stället."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS går inte att läsas in från file:-webbadresser om de inte slutar med ett .css-filnamnstillägg."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Användning av SourceBuffer.abort() i syfte att avbryta borttagning av asynkrona intervall med remove() har fasats ut på grund av en ändring av specifikation. Stödet tas bort i framtiden. Lyssna efter händelsen updateend i stället. abort() ska endast användas till att avbryta en asynkron mediebilaga eller återställa analyseringsläget."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Funktionen för att ställa in MediaSource.duration under den högsta visningstidsstämpeln för buffrade kodade ramar har fasats ut på grund av en ändring av specifikation. Stödet för implicit borttagning av trunkerad buffrad media tas bort i framtiden. Du ska i stället genomföra uttrycklig remove(newDuration, oldDuration) för alla sourceBuffers, om newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Webb-MIDI ber om behörighet om användning även om SysEx inte har angetts i MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Aviserings-API:et får inte längre användas från osäkra ursprung. Vi rekommenderar att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Behörighet för aviserings-API:et kan inte längre begäras via en iframe för korsursprung. Vi rekommenderar att du begär behörighet från en ram på toppnivå eller öppnar ett nytt fönster i stället."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Alternativet imageOrientation: 'none' i createImageBitmap har fasats ut. Använd createImageBitmap med alternativet {imageOrientation: 'from-image'} i stället."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partnern förhandlar med en föråldrad version av (D)TLS. Kontakta partnern om att åtgärda detta."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Om du anger overflow: visible för img-, video- och canvas-taggar kan det leda till att visuellt innehåll visas utanför elementets gränser. Läs mer på https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments är utfasat. Använd just-in-time-installation för betalningshanterare i stället."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest-anropet åsidosatte CSP-direktivet connect-src. Denna åsidosättning är utfasad. Lägg till betalningsmetodens identifierare från PaymentRequest API (i fältet supportedMethods) i CSP-direktivet connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent är utfasad. Använd navigator.storage i stället."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> med <picture> som överordnat element är ogiltigt och ignoreras därför. Använd <source srcset> i stället."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame är leverantörsspecifik. Använd standardmetoden cancelAnimationFrame i stället."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame är leverantörsspecifik. Använd standardmetoden requestAnimationFrame i stället"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen har fasats ut. Använd Docs.fullscreenElement i stället."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() har fasats ut. Använd Element.requestFullscreen() i stället."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() har fasats ut. Använd Element.requestFullscreen() i stället."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() har fasats ut. Använd Document.exitFullscreen() i stället."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() har fasats ut. Använd Document.exitFullscreen() i stället."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen har fasats ut. Använd Document.fullscreenEnabled i stället."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Vi fasar ut API:et chrome.privacy.websites.privacySandboxEnabled, men det förblir aktivt för bakåtkompatibilitet tills version M113 lanseras. Använd i stället chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled och chrome.privacy.websites.adMeasurementEnabled. Läs mer på https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Begränsningen DtlsSrtpKeyAgreement har tagits bort. Du har angett ett false-värde för denna begrä<PERSON>, vilket tolkas som ett försök att använda den borttagna SDES key negotiation-metoden. Denna funktion har tagits bort. Använd i stället en tjänst med stöd för DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Begränsningen DtlsSrtpKeyAgreement har tagits bort. Du har angett ett true-värde för denna begrä<PERSON>, vilket inte hade någon inverkan, men du kan ta bort begränsningen för att göra det tydligare."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Återanropsbaserade getStats() har utfasats ut och kommer att tas bort. Använd getStats() som uppfyller specifikationerna i stället."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() har fasats ut. Använd Selection.modify() i stället."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Förfrågningar från underresurser vars webbadresser innehåller inbäddade användaruppgifter (t.ex. **********************/) blockeras."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Alternativet rtcpMuxPolicy är utfasat och kommer att tas bort."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer kräver isolering för korsursprung. Läs mer på https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() utan användaraktivering har fasats ut och kommer att tas bort."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Borttagning av händelselyssnare har fasats ut och tas bort."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Tillägg måste isoleras för korsursprung om du vill fortsätta använda SharedArrayBuffer. Läs mer på https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Attributet GPUAdapter isFallbackAdapter har fasats ut. Använd i stället attributet GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 stöds inte av json-svar i XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synkron XMLHttpRequest i huvudtråden har fasats ut på grund av dess negativa effekt på slutanvändarens upplevelse. Besök https://xhr.spec.whatwg.org/ om du behöver hjälp."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animation"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Layoutförskjutningar uppstår när element flyttas utan användarinteraktion. [Undersök orsakerna till layoutförändringar](https://web.dev/articles/optimize-cls), till exempel om element har lagts till, tagits bort eller om teckensnittet ändras för dem vid sidhämtningen."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Teckensnittsbegäran"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Kluster med layoutförskjutningar på {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Det gick inte att identifiera några orsaker till layoutförskjutningar"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Inga layoutförskjutningar"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Orsaker till layoutförskjutningar"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "De vanligaste orsakerna till layoutförskjutningar"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Sämsta klustret"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Kluster med den sämsta layoutförskjutningen"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Lagringstid i cacheminnet"}, "models/trace/insights/Cache.ts | description": {"message": "Om filerna cachelagras under längre tid kan upprepade besök på sidan gå snabbare. [<PERSON><PERSON><PERSON> mer](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Inga förfrågningar med ineffektiva cachepolicyer"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} andra"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | title": {"message": "Använd effektiva cachelivslängder"}, "models/trace/insights/DOMSize.ts | description": {"message": "En stor DOM kan förlänga tiden för formatberäkningar och flödesomformningar av layouten, vilket påverkar sidans responsivitet. En stor DOM ökar även minnesanvändningen. [Läs om hur du undviker en för stor DOM-storlek](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Flest underordnade element"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-djup"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistik"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimera DOM-storlek"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Totalt antal element"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Den första nätverksbegäran är den viktigaste.  Minska latensen genom att undvika omdirigeringar (så att servern svarar snabbt) och aktivera textkomprimering."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "<PERSON>eh<PERSON>ll omdirigeringar ({PH1} omdirigeringar, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "<PERSON><PERSON> svarade långsamt ({PH1} har observerats)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Ingen komprimering har tillämpats"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Undviker omdirigeringar"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "<PERSON><PERSON> svarar snabbt ({PH1} har observerats)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Tillämpar textkomprimering"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Serversvarstid"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Fördröjning av dokumentbegäran"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Okomprimerad nedladdning"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Dubbletter av bytes"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON> bort stora, duplicerade JavaScript-moduler f<PERSON><PERSON><PERSON> paket så att färre onödiga byte skickas via nätverket."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Duplicerad JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Du kan göra så att texten alltid är synlig genom att ställa in [font-display](https://developer.chrome.com/blog/font-display) på swap eller optional. swap kan optimeras ytterligare för att minska layoutförskjutningar med [åsidosättanden av teckensnittsvärden](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Teckensnitt"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Teckensnittsvisning"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tid"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonym)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Många API:er, van<PERSON><PERSON><PERSON> när de läser layoutgeometri, tvingar renderingsmotorn att pausa skriptkörningen för att beräkna format och layout. Läs mer om [framtvingad flödesomformning](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) och hur det kan begränsas."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stackspårning"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Framtvingad flödesomformning"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Toppfunktionsanrop"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Total tid för flödesomformning"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[utan attribut]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Om du minskar nedladdningstiden för bilder kan du förbättra den upplevda hämtningstiden för sidan och LCP. [<PERSON><PERSON><PERSON> mer om att optimera bildstorlek](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (uppsk. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Inga bilder som kan optimeras"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimera <PERSON>"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} andra"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Förbättra bildleveransen"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Om du ökar bildkomprimeringsfaktorn kan du minska nedladdningsstorleken för bilden."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Du kan minska nedladdningsstorleken på den här bilden genom att använda ett modernt bildformat (WebP, AVIF) eller öka bildkomprimeringen."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Den här bildfilen är större än nödvändigt ({PH1}) för de visade måtten ({PH2}). Använd responsiva bilder för att minska nedladdningsstorleken för bilden."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Om du använder videoformat i stället för giffar kan du minska nedladdningsstorleken för animerat innehåll."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Börja undersöka med den längsta fasen. [Fördröjningar kan minimeras](https://web.dev/articles/optimize-inp#optimize_interactions). För att minska bearbetningstiden kan du [optimera kostnaderna för huvudtrådar](https://web.dev/articles/optimize-long-tasks), ofta JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Längd"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Inmatningsfördröjning"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Inga interaktioner har identifierats"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fas"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Presentations<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Behandlingstid"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP per fas"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimera LCP genom att göra LCP-bilden [synlig](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) från HTML direkt och [undvika uppskjuten inläsning](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high har tillämpats"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high bör <PERSON><PERSON>"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "uppskjuten inläsning tillämpas inte"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP-bilde<PERSON> l<PERSON> in {PH1} efter den tidigaste startpunkten."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Ingen LCP upptäcktes"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Ingen LCP-resurs har identifierats eftersom LCP inte är en bild"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Begäran kan hittas i det ursprungliga dokumentet"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Upptäcka LCP-begäran"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON>ar<PERSON> [fas har specifika förbättringsstrategier](https://web.dev/articles/optimize-lcp#lcp-breakdown). LCP-tiden bör helst användas till att läsa in resurser, inte till fördröjningar."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Längd"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Fördröjning av elementrendering"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Fält p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Ingen LCP upptäcktes"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fas"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Fördröjning av resursinläsning"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Inläsningstid för resurs"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP per fas"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "S<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Bortkastade bytes"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Med polyfill-koder och transformeringar går det att använda nya JavaScript-funktioner i äldre webbläsare. Många av dem är dock inte nödvändiga för moderna webbläsare. Överväg att ändra JavaScript-byggprocessen så att du inte transpilerar [Baseline](https://web.dev/articles/baseline-and-polyfills)-funktioner, förutom om du vet att du måste ha stöd för äldre webbläsare. [Läs mer om varför de flesta webbplatser kan implementera ES6+-kod utan transpilering](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Äldre JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 och HTTP/3 ger många fördelar jämfört med HTTP/1.1, till exempel multiplexering. [<PERSON><PERSON>s mer om hur du använder modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Inga begäranden använde HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protokoll"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Modern HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Ursprung"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Tid"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Beräknade LCP-besparingar"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Oanvänd föranslutning. Kontrollera att attributet crossorigin används korrekt."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Undvik att kedjekoppla kritiska begäranden](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) genom att göra kedjorna kortare, minska storleken på resurser som laddas ned eller skjut upp nedladdningen av onödiga resurser för att förbättra sidhämtningstiden."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "<PERSON>ä<PERSON> till signaler för [föranslutning](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) för dina viktigaste u<PERSON>p<PERSON>, men försök att använda högst fyra stycken."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidater för <PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Högsta latens för kritisk kedja:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Inga renderingsuppgifter påverkades av nätverksberoenden"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Inga ytterligare ursprung är bra kandidater för föran<PERSON>ning"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "inga källor har föran<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Signaler för [föranslutning](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) hj<PERSON><PERSON><PERSON> webbläsaren att upprätta en anslutning tidigare under sidhämtningen, vilket sparar tid när första begäran för det ursprunget görs. Följande är ursprungen som sidan föranslöts till."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Nätverksberoendeträd"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Fler än fyra anslutningar för preconnect hittades. Dessa ska användas sparsamt och bara med de viktigaste ursprungen."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Oanvänd föranslutning. Använd endast preconnect för ursprung som sidan sannolikt kommer att begära."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Undvik att kedjekoppla kritiska begäranden genom att göra kedjorna kortare, minska storleken på resurser som laddas ned eller skjuta upp nedladdningen av onödiga resurser för att förbättra sidhämtningstiden."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Begäranden blockerar sidans första rendering, vilket kan fördröja LCP. [Använd uppskjutning eller infogning](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) för att flytta bort dessa nätverksbegäranden från den kritiska vägen."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Längd"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Inga renderingsblockerande begäranden för denna navigering"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Begäranden om renderingsblockering"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Om kostnaderna för att beräkna om stilar förblir höga kan du minska dem genom att optimera väljare. [Optimera väljare](https://developer.chrome.com/docs/devtools/performance/selector-stats) med både hög förfluten tid och hög procentandel långsam sökväg. Enklare väljare, färre väljare, en mindre DOM och en grundare DOM minskar alla matchningskostnader."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tid"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Ingen CSS-väljardata hittades. Statistik för CSS-väljare måste aktiveras i inställningarna för resultatpanelen."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Matchningsförsök"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON>tal <PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Kostnader för CSS-väljare"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Vanligaste väljarna"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Totalt"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON> för hu<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Tredje part"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Överföringsstorlek"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Kod från tredje part kan påverka inläsningsprestandan betydligt. [Minska och skjut upp inläsningen av kod från tredje part](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) för att prioritera sidans innehåll."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Inga tredje parter hittades"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Tredje part"}, "models/trace/insights/Viewport.ts | description": {"message": "Tryckinteraktioner kan [f<PERSON><PERSON><PERSON><PERSON><PERSON> med upp till 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) om visningsområdet inte är optimerat för mobila enheter."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Fördr<PERSON><PERSON>ing vid tryck på mobil"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimera visningsområde för mobila enheter"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Bara sidor som lästs in via en GET-begäran kan placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Bara sidor med statuskoden 2XX kan cachelagras."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Ett försök att köra JavaScript i cacheminnet upptäcktes."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON>or som har begärt en AppBanner kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Vilocacheminnet har inaktiverats med hjälp av en flagga. Öppna chrome://flags/#back-forward-cache om du vill aktivera det lokalt på enheten."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Vilocacheminnet har inaktiverats från kommandoraden."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Vilocacheminnet har inaktiverats därför att minnet inte räckte till."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Vilocacheminnet stöds inte vid delegering."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Vilocacheminnet har inaktiverats för fö<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "<PERSON><PERSON> kan inte cachelagras eftersom den har en BroadcastChannel-instans med registrerade lyssnare."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Sidor med fältet cache-control:no-store i huvudet kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Cacheminnet rensades avsiktligen."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "<PERSON><PERSON> togs bort från cacheminnet så att en annan sida skulle kunna cachelagras."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "<PERSON>or som använder en plugin kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Odefinierad"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Sidor som använder FileChooser API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Sidor som använder File System Access API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Sidor som använder Media Device Dispatcher kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Uppspelning pågick i en mediespelare när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Sidor som använder MediaSession API och ställer in en uppspelningsstatus kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Sidor som använder MediaSession API och ställer in åtgärdshanterare kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Vilocacheminne har inaktiverats på grund av att skärmläsare används."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Sidor som använder SecurityHandler kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Sidor som använder Serial API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Sidor som använder WebAuthentication API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Sidor som använder WebBluetooth API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Sidor som använder WebUSB API kan inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Vilocacheminnet har inaktiverats eftersom cookies har inaktiverats på en sida där Cache-Control: no-store används."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Sidor som använder en dedikerad tjänstefunktion eller worklet kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Dokumentet lästes inte in helt innan användaren navigerade bort från det."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON> visades när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome <PERSON>tering kördes när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-destillering pågick när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer kördes när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Vilocacheminnet har inaktiverats på grund av att tillägg använder API:et för meddelanden."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Tillägg med långvarig anslutning ska koppla från innan lagras i vilocacheminne."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Tillägg med långvarig anslutning försökte skicka meddelanden till bildrutor i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Vilocacheminnet har inaktiverats på grund av till<PERSON>gg."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "En modal dialogruta, till exempel en återsändning av ett formulär eller en dialogruta för http-autentisering, visades när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Offlinesidan visades när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Fältet Out-Of-Memory Intervention visades när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Det fanns en aktiv begäran om behörighet när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Popupblockerare kördes när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Information från <PERSON> webbsökning visades när användaren navigerade bort från sidan."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Säker webbsökning identifierade den här sidan som olämplig och blockerade popupfönstret."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "En tjänstefunktion aktiverades medan sidan lagrades i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Vilocacheminnet har inaktiverats på grund av dokumentfel."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Det går inte att lagra sidor med FencedFrames i bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "<PERSON><PERSON> togs bort från cacheminnet så att en annan sida skulle kunna cachelagras."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Sidor med åtkomstbehörighet till en mediestream kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Sidor med vissa typer av inbäddat innehåll (t.ex. PDF-filer) kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Sidor som använder IdleManager kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Sidor med en öppen IndexedDB-anslutning kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Vilocacheminnet har inaktiverats på grund av en IndexedDB-händelse."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Otillåtna API:er användes."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Sidor med ett JavaScript som injicerats av ett tillägg kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Sidor med ett StyleSheet som injicerats av ett tillägg kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Internt fel."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Vilocacheminnet har inaktiverats eftersom en viss JavaScript-nätverksbegäran tog emot en resurs med rubriken Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Vilocacheminnet har inaktiverats på grund av en keepalive-begäran."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON> som använder tangentbordslås kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "<PERSON><PERSON> hade inte lästs in helt innan användaren navigerade bort från den."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Sidor vars huvudresurs har cache-control:no-cache kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Sidor vars huvudresurs har cache-control:no-store kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigeringen avbröts innan sidan kunde återställas från vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Sidan togs bort från cacheminnet eftersom för mycket data skickades till den via en aktiv nätverksanslutning. Det finns en gräns för hur mycket data en cachelagrad sida får ta emot i Chrome."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Sidor med utestående fetch() eller XHR kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Sidan togs bort från vilocacheminnet eftersom en aktiv nätverksbegäran medförde en omdirigering."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Sidan togs bort från cacheminnet eftersom en nätverksanslutning förblev öppen för länge. Det finns en gräns för hur länge en cachelagrad sida får ta emot data i Chrome."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "<PERSON>or utan giltigt svarshuvud kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigeringen gjordes i en annan ram än den överordnade ramen."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Sidor med pågående transaktioner i en indexerad databas kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Sidor med en utestående nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Sidor med en utestående hämtningsbegäran i nätverket kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Sidor med en utestående nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Sidor med en utestående XHR-nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Sidor som använder PaymentManager kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON>or som använder bild-i-bild kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Sidor där användargränssnittet för utskrift visas kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Sidan öppnades med window.open() och en annan flik refererar till den, eller också öppnade sidan ett fönster."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Renderingen av sidan i vilocacheminnet kraschade."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Renderingsprocessen för sidan i vilocacheminnet avslutades."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Sidor som har begärt behörighet att spela in ljud kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Sidor som har begärt sensorbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Sidor som har begärt synkronisering i bakgrunden eller hämtningsbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Sidor som har begärt MIDI-behörighet kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Sidor som har begärt aviseringsbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Sidor som har begärt åtkomst till lagringsutrymme kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Sidor som har begärt behörighet till videoinspelning kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Bara sidor med webbprotokollet HTTP eller HTTPS kan cachelagras."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "En tjänstefunktion gjorde anspråk på sidan medan den lagrades i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "En tjänstefunktion försökte skicka MessageEvent till sidan i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "En ServiceWorker avregistrerades medan sidan lagrades i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON> togs bort från vilocacheminnet därför att en tjänstefunktion aktiverades."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome har startats om och vilocacheminnet nollställts."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Sidor som använder SharedWorker kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Sidor som använder SpeechRecognizer kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "<PERSON><PERSON> som använder SpeechSynthesis kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "En iframe på sidan initierade en navigering som aldrig slutfördes."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Sidor som har en underresurs med cache-control:no-cache kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Sidor som har en underresurs med cache-control:no-store kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Sidan finns inte kvar eftersom tidsgränsen för lagring i vilocacheminnet överskreds."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Tidsgränsen överskreds när sidan skulle lagras i vilocacheminnet (troligen på grund av en pagehide-hanterare som kördes under lång tid)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Det finns en unload-hanterare i sidans överordnade ram."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "<PERSON><PERSON> har en unload-ha<PERSON>are i en underordnad ram."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Webbläsaren har ändrat fältet för åsidosättning av användaragent."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON>or med behörighet att spela in video eller ljud kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Sidor som använder WebDatabase kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Sidor som använder WebHID kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Sidor som använder WebLocks kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Sidor som använder WebNfc kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Sidor som använder WebOTPService kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Sidor med WebRTC kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Vilocacheminnet har inaktiverats eftersom WebRTC har använts."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Sidor som använder WebShare kan för närvarande inte placeras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Sidor med WebSocket kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Vilocacheminnet har inaktiverats eftersom WebSocket har använts."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Sidor med WebTransport kan inte lagras i vilocacheminnet."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Vilocacheminnet har inaktiverats eftersom WebTransport har använts."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Sidor som använder WebXR kan för närvarande inte placeras i vilocacheminnet."}}