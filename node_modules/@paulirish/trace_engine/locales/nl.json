{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Autorisatie wordt niet gedekt door het jokerteken (*) voor het gebruik van Access-Control-Allow-Headers in CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "In plaats van de kiezer -internal-media-controls-overlay-cast-button moet het kenmerk disableRemotePlayback worden gebruikt om de standaardintegratie van Cast uit te zetten."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "De CSS-weergavewaarde slider-vertical is niet gestandaard<PERSON>erd en wordt verwijderd."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Bronverzoeken waarvan de URL's verwijderde \\(n|r|t)-tekens voor witruimten en kleiner-dan-tekens (<) bevatten, worden geblokkeerd. Verwijder nieuwe-regelitems en codeer kleiner-dan-tekens, bijvoorbeeld in kenmerkwaarden voor elementen, om deze bronnen te laden."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() is beë<PERSON>igd, gebruik in plaats daarvan de gestandaardiseerde API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() is beë<PERSON>igd, gebruik in plaats daarvan de gestandaardiseerde API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() is be<PERSON><PERSON>ig<PERSON>, gebruik in plaats daarvan de gestandaardiseerde API: nextHopProtocol in Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookies die een \\(0|r|n)-teken bevatten, worden afgewezen in plaats van afgekapt."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Versoepeling van het same-origin-beleid door document.domain in te stellen is beëindigd en wordt standaard uitgezet. Deze beëindigingswaarschuwing is voor een cross-origin-toegang die is aangezet door document.domain in te stellen."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "De activering van window.alert via cross-origin iframes is beëindigd en wordt in de toekomst verwijderd."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "De activering van window.confirm van cross-origin iframes is beëindigd en wordt in de toekomst verwijderd."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Ondersteuning voor gegevens: URL's in GIFUseElement zijn beëindigd en worden in de toekomst verwijderd."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() en watchPosition() werken niet meer op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() en watchPosition() zijn beëindigd op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() werkt niet meer op niet-beveiligde oorsprongen. Als je deze functie wilt gebruiken, kun je overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "Er is een <h1>-tag gevonden in een <article>, <aside>, <nav> of <section> waarvoor geen lettergrootte is gespecificeerd. De grootte van deze koptekst wordt binnenkort gewijzigd in deze browser. Ga naar https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 voor meer informatie."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate is beëindigd. Gebruik in plaats daarvan RTCPeerConnectionIceErrorEvent.address of RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Deze indeling voor het verzoek navigator.credentials.get() voor digitale inloggegevens is beëindigd. Update de aanroep zodat de nieuwe indeling wordt gebruikt."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "De verkoperoorsprong en willekeurige gegevens van de canmakepayment-service worker-gebeurtenis zijn beëindigd en worden verwijderd: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "De website heeft een subbron bij een netwerk aangevraagd die alleen toegankelijk is vanwege de bevoorrechte netwerkpositie van de gebruikers. Deze verzoeken stellen niet-openbare apparaten en servers bloot aan internet, waardoor het risico op een CSRF-aanval (cross-site request forgery) en/of informatielekken toeneemt. Ter beperking van deze risico's beëindigt Chrome verzoeken naar niet-openbare subbronnen als deze worden gestart vanuit onbeveiligde omgevingen en gaat Chrome dergelijke verzoeken blokkeren."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "De naam van het veld dailyUpdateUrl van InterestGroups dat is doorgegeven aan joinAdInterestGroup(), is gewijzigd in updateUrl om het gedrag nauwkeuriger aan te geven."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator is beëindigd. Gebruik in plaats daarvan Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Css kan niet worden geladen via file:-URL's, tenzij ze eindigen op een .css-bestandsextensie."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Het gebruik van SourceBuffer.abort() om de verwijdering van het asynchrone bereik van remove() te annuleren, is beë<PERSON><PERSON>d vanwege wijzigingen in de specificaties. Ondersteuning wordt in de toekomst verwijderd. Gebruik in plaats daarvan een listener voor de gebeurtenis updateend. abort() is alleen bedoeld om een asynchrone mediatoevoeging te annuleren of om de status van de parser te resetten."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Het instellen van MediaSource.duration onder het hoogste presentatietijdstempel van gebufferde gecodeerde frames is be<PERSON><PERSON><PERSON><PERSON> vanwege wijzigingen in de specificaties. Ondersteuning voor impliciete verwijdering van afgebroken gebufferde media wordt in de toekomst verwijderd. In plaats daarvan moet je remove(newDuration, oldDuration) expliciet uitvoeren voor alle sourceBuffers, waarbij newDuration < oldDuration is."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI vraagt toestemming voor het gebruik van SysEx, zelfs als de SysEx niet is opgegeven in de MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "De Notification API mag niet meer worden gebruikt vanaf niet-beveiligde oorsprongen. Je kunt overwegen om een beveiligde oorsprong voor je app te gebruiken, zoals HTTPS. Zie https://goo.gle/chrome-insecure-origins voor meer informatie."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Rechten voor de Notification API kunnen niet meer worden aangevraagd bij een cross-origin iframe. Je kunt rechten aanvragen bij een frame op het hoofdniveau of in plaats daarvan een nieuw venster openen."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Optie imageOrientation: 'none' in createImageBitmap is beëindigd. Gebruik in plaats daarvan createImageBitmap met de optie {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Je partner g<PERSON><PERSON><PERSON>t een verouderde (D)TLS-versie. <PERSON><PERSON>m contact op met je partner om dit op te lossen."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Als je overflow: visible opgeeft voor img-, video- en canvas-tags, kunnen deze visuele content produceren buiten de elementgrenzen. Zie https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments is beëindigd. Gebruik in plaats daarvan just-in-time-installaties voor betalingshandlers."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Je aanroep van PaymentRequest heeft de instructie Content-Security-Policy (CSP) connect-src genegeerd. Deze negeeractie is beëindigd. Voeg de betaalmethode-ID van de PaymentRequest-API (in het veld supportedMethods) toe aan je CSP-instructie connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent is beëindigd. Gebruik in plaats daarvan de gestandaardiseerde navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> met een boven<PERSON>e <picture> is ongeldig en wordt daarom genegeerd. Gebruik in plaats daarvan <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimatieFrame is leverancierspecifiek. Gebruik in plaats daarvan het standaard cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame is leverancierspecifiek. Gebruik in plaats daarvan het standaard requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen is beëindigd. Gebruik in plaats daarvan Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() is beëindigd. Gebruik in plaats daarvan Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() is beëindigd. Gebruik in plaats daarvan Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() is beëindigd. Gebruik in plaats daarvan Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() is beëindigd. Gebruik in plaats daarvan Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen is beëindigd. Gebruik in plaats daarvan Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "De API chrome.privacy.websites.privacySandboxEnabled wordt be<PERSON><PERSON><PERSON><PERSON>, maar blijft actief voor compatibiliteit met eerdere versies tot release M113. G<PERSON>ruik in plaats daarvan chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled en chrome.privacy.websites.adMeasurementEnabled. Zie https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "De beperking DtlsSrtpKeyAgreement is verwijderd. Je hebt een false-waarde voor deze beperking opgegeven. Deze wordt niet geïnterpreteerd als een poging om de verwijderde SDES key negotiation-methode te gebruiken. Deze functionaliteit is verwijderd. Gebruik een service die in plaats daarvan DTLS key negotiation ondersteunt."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "De beperking DtlsSrtpKeyAgreement is verwijderd. Je hebt een true-waarde voor deze beperking opgegeven. Deze heeft geen effect en kan worden verwijderd om meer orde te scheppen."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "De op callback gebaseerde getStats() is beëindigd en wordt verwijderd. Gebruik in plaats daarvan de getStats() die aan de specificaties voldoet."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() is beëindigd. Gebruik in plaats daarvan Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Subbronverzoeken waarvan de URL's ingesloten inloggegevens bevatten (bijv. **********************/), worden geblokkeerd."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "De optie rtcpMuxPolicy is beëindigd en wordt verwijderd."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer vereist cross-origin-isolatie. Zie https://developer.chrome.com/blog/enabling-shared-array-buffer/ voor meer informatie."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() zonder gebruikersactivering is beëindigd en wordt verwijderd."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Unload-event-listeners zijn be<PERSON> en worden verwijderd."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensies moeten zijn a<PERSON>em<PERSON> voor cross-origin-isolatie om SharedArrayBuffer te blijven gebruiken. Zie https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Het GPUAdapter-kenmerk isFallbackAdapter is beëindigd. Gebruik in plaats daarvan het GPUAdapterInfo-kenmerk isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 wordt niet ondersteund door de reactie-json in XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchrone XMLHttpRequest op de primaire thread is be<PERSON><PERSON><PERSON>d vanwege het nadelige effect op de gebruikerservaring van de eindgebruiker. Check https://xhr.spec.whatwg.org/ voor meer hulp."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Indelingsverschuivingen komen voor als elementen bewegen zonder gebruikersinteractie. [Stel vast wat de oorzaak is van de indelingsverschuiving](https://web.dev/articles/optimize-cls), zoals elementen die worden toegevoegd of verwijderd, of het lettertype dat verandert terwijl de pagina wordt geladen."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Lettertypeverzoek"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>e"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Indelingsverschuivingscluster op {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Kan geen oor<PERSON>en van indelingsverschuivingen vinden"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON> indelingsverschuiving<PERSON>"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> van indelingsverschuivingen"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Belangrijkste oorzaken van indelingsverschuivingen"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Afbeeldingselement zonder formaat"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Slechtste cluster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Cluster met slechtste indelingsverschuiving"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "Cache-TTL"}, "models/trace/insights/Cache.ts | description": {"message": "<PERSON>en lange levensduur voor het cachegeheugen kan herhaalde bezoeken aan je pagina versnellen. [Meer informatie](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "<PERSON><PERSON> met in<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} andere items"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "V<PERSON>zoek"}, "models/trace/insights/Cache.ts | title": {"message": "Efficiënte levensduur voor het cachegeheugen gebruiken"}, "models/trace/insights/DOMSize.ts | description": {"message": "Een grote DOM kan de duur van stijlberekeningen en dynamische aanpassingen van de indeling verlengen, wat de responsiviteit van de pagina beïnvlo<PERSON>t. Een grote DOM vergroot ook het geheugengebruik. [Meer informatie over hoe je een overmatige DOM-grootte voorkomt](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Meeste onderliggende elementen"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM-diepte"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistiek"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM-grootte optimaliseren"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Totaal aantal elementen"}, "models/trace/insights/DOMSize.ts | value": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Je eerste netwerkverzoek is het belangrijkst.  <PERSON>erminder de vertraging door omleidingen te vermijden, een snelle serverreactie te garanderen en tekstcompressie aan te zetten."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Had omleidingen ({PH1} omleidingen, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Server reageerde traag ({PH1} waargenomen)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "<PERSON><PERSON> compressie toege<PERSON>t"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Vermijdt omleidingen"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Server reageert snel ({PH1} waargenomen)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Past tekstcompressie toe"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Omleidingen"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Serverreactietijd"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Vertrag<PERSON> van documentverzoek"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Niet-gecomprimeerde download"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Dubbele bytes"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> grote, dubbele JavaScript-modules uit bundels, zodat er minder onnodige bytes worden verbruikt door netwerkactiviteit."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Gedupliceerd JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Overweeg [font-display](https://developer.chrome.com/blog/font-display) in te stellen op swap of optional om te zorgen dat tekst consistent zichtbaar is. swap kan verder worden geoptimaliseerd om indelingsverschuivingen te verminderen met [overschrijvingen van lettertypestatistieken](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Lettertype"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Lettertypeweergave"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Verspilde tijd"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anoniem)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Veel API's, die meestal de indelingsgeometrie lezen, dwingen de weergave-engine om de scriptuitvoering te onderbreken om de stijl en indeling te berekenen. Meer informatie over [gedwongen dynamische aanpassing](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) en de bijbehorende beperkende maatregelen."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stacktrace"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Gedwongen dynamische aanpassing"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Topfunctieaanroep"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Totale tijd voor dynamische aanpassing"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[niet toe<PERSON><PERSON><PERSON>]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Als je de downloadtijd van afbeeldingen verkort, kunnen de waargenomen laadtijd van de pagina en LCP verbeteren. [Meer informatie over hoe je de afbeeldingsgrootte optimaliseert](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON> schatting {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Geen afbeeldingen die kunnen worden geoptimaliseerd"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Bestandsgrootte optimaliseren"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} andere items"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Afbeeldingslevering verbeteren"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "<PERSON>s je de afbeeldingscompressiefactor ver<PERSON><PERSON><PERSON>, kan dit de <PERSON><PERSON><PERSON> van deze afbeelding verbeteren."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Als je een moderne bestandsindeling (WebP, AVIF) gebruikt of de compressie van de afbeelding verhoogt, kan dit de downloadgrootte van deze afbeelding verbeteren."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Dit afbeeldingsbestand is groter dan nodig ({PH1}) voor de getoonde afmetingen ({PH2}). Gebruik responsieve afbeeldingen om de downloadgrootte van de afbeelding te verkleinen."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Als je video-indelingen gebruikt in plaats van gifjes, kan dit de downloadgrootte van geanimeerde content verbeteren."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Onderzoek eerst de langste fase. [Je kunt vertraging tot een minimum beperken](https://web.dev/articles/optimize-inp#optimize_interactions). [Optimaliseer de kosten van de hoofdthread](https://web.dev/articles/optimize-long-tasks) (vaak JS) om de verwerkingsduur te verkorten."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Invoervertraging"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "<PERSON>n <PERSON>ies gevonden"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Presentatievertraging"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Verwerkingsduur"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP per fase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimaliseer LCP door de LCP-afbeelding [vindbaar](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) te maken vanuit de HTML en [lazy loading te voorkomen](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high toegepast"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high moet worden toegepast"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "Lazy loading niet toegepast"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP-afbeelding is {PH1} na het vroegste beginpunt geladen."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Geen LCP gevonden"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Geen LCP-resource gevonden omdat de LCP geen afbeelding is"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Verzoek is vindbaar in het oorspronkelijke document"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP-verzoekdetectie"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON><PERSON> [fase heeft specifieke verbeteringsstrategieën](https://web.dev/articles/optimize-lcp#lcp-breakdown). Idealiter wordt het grootste deel van de LCP-tijd besteed aan het laden van de resources, niet aan vertragingen."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Vertraging bij element renderen"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Veld p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Geen LCP gevonden"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Vertraging bij bron laden"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "<PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to First Byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP per fase"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Verspilde bytes"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Met polyfills en transformaties kunnen oudere browsers nieuwe JavaScript-functies gebruiken. Voor moderne browsers is dit meestal niet nodig. Overweeg om je JavaScript-buildproces aan te passen zodat [Baseline](https://web.dev/articles/baseline-and-polyfills)-functies niet worden getranspileerd, tenzij je weet dat je oudere browsers moet ondersteunen. [Meer informatie over waarom de meeste sites ES6+-code kunnen implementeren zonder transpileren](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Verouderde JavaScript-code"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 en HTTP/3 bieden veel voordelen ten opzichte van HTTP/1.1, zoals multiplexing. [Meer informatie over het gebruik van moderne HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Geen verzoeken hebben HTTP/1.1 gebruikt"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "V<PERSON>zoek"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Moderne HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Oorsprong"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "V<PERSON>zoek"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Tijd"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Geschatte LCP-besparingen"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Niet-gebruikte preconnect. Ga na of het kenmerk crossorigin juist wordt gebruikt."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Vermijd het doorlinken van kritieke verzoeken](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) door de lengte van ketens te verkleinen, de downloadgrootte van resources te beperken of het downloaden van onnodige resources uit te stellen om de laadtijd van de pagina te verbeteren."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Voeg [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)-hints toe aan je belangrijkste oorsprongen, maar probeer er niet meer dan 4 te gebruiken."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Kandidaten voor preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON> latentie voor kritiek pad:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "<PERSON>n weergavetaken beïnvloed door netwerkafhankelijkheid"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Er zijn geen extra oorsprongen die goede kandidaten zijn voor verbinding vooraf (preconnect)"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "er zijn geen oorsprongen vooraf gekoppeld"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Met [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)-hints kan de browser eerder tijdens het laden van de pagina een verbinding tot stand brengen, waardoor er tijd wordt bespaard wanneer het eerste verzoek voor die oorsprong wordt gedaan. Dit zijn de oorsprongen waarmee de pagina een preconnect heeft gemaakt."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Vooraf gekoppelde oorsprongen"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Netwerkafhankelijkheidsstructuur"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "<PERSON>r zijn meer dan 4 preconnect-verbindingen gevonden. Deze moeten niet te veel worden gebruikt en alleen voor de belangrijkste oorsprongen."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Niet-gebruikte preconnect. Gebruik preconnect alleen voor oorsprongen die de pagina waarschijnlijk aanvraagt."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Vermijd het doorlinken van kritieke verzoeken door de lengte van ketens te verkleinen, de downloadgrootte van resources te beperken of het downloaden van onnodige resources uit te stellen om de laadtijd van de pagina te verbeteren."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Verzoeken blokkeren de eerste keer renderen van de pagina, waardoor LCP kan worden vertraagd. Door [verzoeken uit te stellen of in te sluiten](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) kun je deze netwerkverzoeken van de kritieke route verwijderen."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "<PERSON>n blokkeerverzoeken voor renderen van deze navigatie"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "V<PERSON>zoek"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Verzoeken voor renderblokkering"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Als de kosten van Stijl opnieuw berekenen hoog blijven, kun je ze verlagen met selectoroptimalisatie. [Optimaliseer de selectors](https://developer.chrome.com/docs/devtools/performance/selector-stats) met zowel een lange verstreken tijd als een hoog percentage langzame paden. Eenvoudigere selectors, minder selectors, een kleiner DOM en een minder diep DOM verlagen de kosten voor het zoeken naar overeenkomsten."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Verstreken tijd"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Er zijn geen css-kiezergegevens gevonden. Statistieken voor css-kiezers moeten worden aangezet in de instellingen van het prestatiedeelvenster."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Overeenkomstpogingen"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Aantal overeenkomsten"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "<PERSON><PERSON> van css-kiezer"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Topkiezers"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Totaal"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON><PERSON><PERSON> van prima<PERSON> thread"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "<PERSON><PERSON>"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Overdrachtsgrootte"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Code van derden kan van grote invloed zijn op de laadprestaties. [Verminder het laden van code van derden en stel dit uit](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) om prioriteit te geven aan de content van je pagina."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "<PERSON>n derden gevonden"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Derden"}, "models/trace/insights/Viewport.ts | description": {"message": "Tikinteracties kunnen [tot 300 ms vertraagd](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) worden als het kijkvenster niet is geoptimaliseerd voor mobiel."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Tikvertraging op mobiel"}, "models/trace/insights/Viewport.ts | title": {"message": "Kijkvenster optimaliseren voor mobiel"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "<PERSON>een pagina's die worden geladen via een GET-verzoek, komen in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Alleen pagina's met een statuscode 2XX kunnen worden gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome heeft een poging waargenomen om JavaScript uit te voeren in het cachegeheugen."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON>gin<PERSON>'s die een App<PERSON><PERSON>r hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Back-Forward Cache is uitgezet door flags. Ga naar chrome://flags/#back-forward-cache om het lokaal aan te zetten op dit apparaat."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Back-Forward <PERSON><PERSON> is uitgezet door de opdrachtregel."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Back-Forward <PERSON><PERSON> is uit<PERSON><PERSON>t vanwege onvoldoende geheugen."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Back-Forward <PERSON><PERSON> wordt niet ondersteund door de gemachtigde."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Back-Forward <PERSON><PERSON> is uitgezet voor prerenderer."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "De pagina kan niet worden gecachet omdat deze een BroadcastChannel-instantie met geregistreerde listeners bevat."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Pagina's met de header cache-control:no-store kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON> <PERSON><PERSON> is opzettelijk gewist."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "De pagina is verwijderd uit het cachegeheugen zodat een andere pagina kon worden gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "<PERSON><PERSON><PERSON>'s met plug-ins komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Ongedefinieerd"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Pagina's die de FileChooser API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Pagina's die de File System Access API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Pagina's die Media Device Dispatcher gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "<PERSON>en mediaspeler speelde tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Pagina's die de MediaSession API gebruiken en een afspeelstatus instellen, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Pagina's die de MediaSession API gebruiken en action-handlers instellen, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Back-Forward <PERSON><PERSON> is uit<PERSON><PERSON><PERSON> van<PERSON><PERSON> s<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Pagina's die SecurityHandler gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Pagina's die de Serial API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Pagina's die de WebAuthetication API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Pagina's die de WebBluetooth API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Pagina's die de WebUSB API gebruiken, komen niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Back-Forward <PERSON><PERSON> is uitgezet, omdat cookies zijn uitgezet op een pagina die Cache-Control: no-store gebruikt."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON>'s die een specifieke worker of worklet geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Het document was niet helemaal geladen voordat het werd gesloten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "App-banner was geopend tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome W<PERSON>twoordmanager was geopend tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-distillatie was bezig tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer was geopend tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Back-Forward <PERSON><PERSON> is uitgezet vanwege extensies die de API voor berichten gebruiken."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Extensies met een langdurige verbinding moeten de verbinding sluiten voordat Back-Forward <PERSON><PERSON> wordt geopend."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Extensies met een langdurige verbinding hebben gep<PERSON><PERSON> berichten te sturen naar frames in de Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Back-Forward <PERSON><PERSON> is uitgezet vanwege extensies."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON> dialoogven<PERSON>, zoals dialoogvenster voor formulier opnieuw indienen of HTTP-wachtwoord, is getoond voor de pagina tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "De offline pagina werd getoond tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Interventiebalk voor geheugen vol was geopend tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Er waren rechtenverzoeken tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Pop-up blocker was geopend tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Safe Browsing-g<PERSON><PERSON><PERSON> zijn getoond tijdens het sluiten."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing beschouwt deze pagina als misleidend en heeft de pop-up geblokkeerd"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Er is een service worker gea<PERSON><PERSON> te<PERSON><PERSON><PERSON> de pagina zich in Back-Forward <PERSON><PERSON> bevond."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Back-Forward <PERSON><PERSON> is uitge<PERSON>t vanwege een documentfout."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Pagina's die FencedFrames gebruiken, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "De pagina is verwijderd uit het cachegeheugen zodat een andere pagina kon worden gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Pagin<PERSON>'s die toegang tot mediastreams hebben gegeven, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "<PERSON><PERSON><PERSON>'s met bepaalde soorten ingesloten content (zoals pdf's) komen op dit moment niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "<PERSON><PERSON><PERSON>'s die IdleManager geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON>'s met een open IndexedDB-verbinding komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Back-Forward <PERSON><PERSON> is uitge<PERSON>t vanwege een IndexedDB-gebeurtenis."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "<PERSON><PERSON> zijn niet in aanmerking komende API's gebruikt."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Pagina's waarin <PERSON> is geïnjecteerd door extensies, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Pagina's waarin een StyleSheet is ge<PERSON>njecteerd door extensies, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Interne fout."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Back-Forward Cache is uit<PERSON><PERSON>t, omdat een JavaScript-netwerkverzoek een resource met Cache-Control: no-store-header heeft ontvangen."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Back-Forward <PERSON><PERSON> is uitge<PERSON>t vanwege een keepalive-verz<PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "<PERSON><PERSON><PERSON>'s die toetsenbordvergrendeling gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "De pagina was niet helemaal geladen voordat die werd gesloten."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "<PERSON>gin<PERSON>'s wa<PERSON><PERSON> de hoofdbro<PERSON> cache-control:no-cache bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Pagina's wa<PERSON><PERSON> de hoofdbron cache-control:no-store bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigatie is gean<PERSON><PERSON><PERSON> voordat de pagina kon worden hersteld vanuit Back-Forward <PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "De pagina is verwijderd uit het cachegeheugen omdat een actieve netwerkverbinding te veel gegevens heeft ontvangen. Chrome beperkt de hoeveelheid gegevens die een pagina kan ontvangen als deze is gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON>'s met inflight fetch() of XHR komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "De pagina is verwi<PERSON><PERSON><PERSON> uit Back-Forward <PERSON><PERSON> omdat bij een actief netwerkverzoek een omleiding betrokken was."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "De pagina is verwijderd uit het cachegeheugen omdat een netwerkverbinding te lang open was. Chrome beperkt hoelang een pagina gegevens kan ontvangen als deze is gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Pagina's die geen geldige reactiekop hebben, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigatie vond plaats in een ander frame dan het hoofdframe."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON><PERSON>'s met actieve geïndexeerde DB-transacties komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzo<PERSON> in behandeling komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzoek voor ophalen in behandeling komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON><PERSON>'s met een netwerkverzo<PERSON> in behandeling komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON><PERSON>'s met een X<PERSON>R-netwerkverzoek in behandeling komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "<PERSON><PERSON><PERSON>'s die Payment<PERSON><PERSON><PERSON> g<PERSON>n, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "<PERSON><PERSON><PERSON>'s die scherm-in-scherm gebruiken, komen momenteel niet in aanmerking voor Back-Forward C<PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Pagina's die UI voor afdrukken tonen, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "De pagina is geopend met window.open() en een ander tabblad bevat een verwijzing erna<PERSON>, of de pagina heeft een venster geopend."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Het proces van het weergaveprogramma voor de pagina in Back-Forward <PERSON><PERSON> is g<PERSON><PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Het proces van het weergaveprogramma voor de pagina in Back-Forward <PERSON><PERSON> is beë<PERSON>igd."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor audio-opnamen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON>gin<PERSON>'s die sensorrechten hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Pagina's die rechten voor synchronisatie of op<PERSON>n op de achtergrond hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON>'s die MIDI-rechten hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor meldingen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON>'s die opslagtoegang hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON>'s die rechten voor video-opnamen hebben a<PERSON>, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Alleen pagina's met HTTP/HTTPS als URL-schema kunnen worden gecachet."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "De pagina is geclaimd door een service worker te<PERSON><PERSON><PERSON><PERSON> de pagina zich in Back-Forward <PERSON><PERSON> bevindt."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Een service worker he<PERSON><PERSON> g<PERSON><PERSON><PERSON> de pagina in Back-Forward <PERSON><PERSON> een MessageEvent te sturen."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "De registratie van ServiceWorker is ongedaan gemaakt terwijl een pagina zich in Back-Forward <PERSON><PERSON> bevond."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "<PERSON> pagina is verwij<PERSON>d uit Back-Forward <PERSON><PERSON> van<PERSON> de <PERSON>ring van een service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome is opnieuw opgestart en heeft de Back-Forward Cache-items gewist."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "<PERSON>gin<PERSON>'s die SharedWorker gebruiken, komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "<PERSON><PERSON><PERSON>'s die SpeechRecognizer gebruiken, komen momenteel niet in aanmerking voor Back-Forward <PERSON><PERSON>."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "<PERSON><PERSON><PERSON>'s die SpeechSynthesis gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Een iframe op de pagina heeft navigatie gestart die niet is afgerond."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "<PERSON>gin<PERSON>'s wa<PERSON><PERSON> de subbron cache-control:no-cache bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Pagina's wa<PERSON><PERSON> de subbron cache-control:no-store bevat, kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "De pagina heeft de maximale tijd in Back-Forward <PERSON><PERSON> overs<PERSON>en en is verlopen."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Er is een time-out voor de pagina opgetreden tijdens het op<PERSON>an in Back-Forward Cache (waarschijnlijk vanwege langdurige pagehide-handlers)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "De pagina heeft een unload-handler in het hoofdframe."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "De pagina heeft een unload-handler in een subframe."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Browser heeft de override-header voor user-agents gewijzigd."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "<PERSON>gin<PERSON>'s die toegang hebben gegeven om video of audio op te nemen, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Pagina's die WebDatabase gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Pagina's die WebHID gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "<PERSON>gin<PERSON>'s die WebLocks geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Pagina's die WebNfc gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Pagina's die WebOTPService gebruiken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Pagina's met WebRTC kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Back-Forward C<PERSON> is uitgezet omdat WebRTC is gebruikt."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Pagina's die WebShare geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "<PERSON><PERSON><PERSON>'s met WebSocket kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Back-Forward <PERSON><PERSON> is uitgezet omdat WebSocket is gebruikt."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Pagina's met WebTransport kunnen niet worden opgeslagen in Back-Forward Cache."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Back-Forward <PERSON><PERSON> is uitgezet omdat WebTransport is gebru<PERSON>t."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Pagina's die WebXR geb<PERSON>iken, komen momenteel niet in aanmerking voor Back-Forward Cache."}}