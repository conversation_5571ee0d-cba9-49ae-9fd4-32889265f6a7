{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "สัญลักษณ์ไวลด์การ์ด (*) จะไม่ครอบคลุมการให้สิทธิ์ในการใช้งาน Access-Control-Allow-Headers สำหรับ CORS"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ควรใช้แอตทริบิวต์ disableRemotePlayback เพื่อปิดใช้การผสานรวมการแคสต์เริ่มต้นแทนการใช้ตัวเลือก -internal-media-controls-overlay-cast-button"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "ค่าลักษณะที่ปรากฏของ CSS ซึ่งได้แก่ slider-vertical ไม่เป็นมาตรฐานและจะถูกนำออก"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "คำขอทรัพยากรที่ URL มีทั้งอักขระ \\(n|r|t) แบบช่องว่างซึ่งนำออกไปแล้วและมีอักขระน้อยกว่า (<) ถูกบล็อก โปรดนำบรรทัดใหม่ออกและเข้ารหัสอักขระที่น้อยกว่าจากที่ต่างๆ อย่างเช่นค่าแอตทริบิวต์ขององค์ประกอบเพื่อโหลดทรัพยากรเหล่านี้"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() เลิกใช้งานแล้ว โปรดใช้ Navigation Timing 2 ซึ่งเป็น API แบบมาตรฐานแทน"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() เลิกใช้งานแล้ว โปรดใช้ Paint Timing ซึ่งเป็น API แบบมาตรฐานแทน"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() เลิกใช้งานแล้ว โปรดใช้ nextHopProtocol ใน Navigation Timing 2 ซึ่งเป็น API แบบมาตรฐานแทน"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "คุกกี้ที่มีอักขระ \\(0|r|n) จะถูกปฏิเสธแทนการตัดให้สั้นลง"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "การผ่อนปรนนโยบายต้นทางเดียวกันโดยการตั้งค่า document.domain เลิกใช้งานแล้วและจะปิดใช้โดยค่าเริ่มต้น คำเตือนการเลิกใช้งานนี้มีไว้สำหรับการเข้าถึงแบบข้ามต้นทางที่เปิดใช้โดยการตั้งค่า document.domain"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "การเรียกใช้ window.alert จาก iframe แบบข้ามต้นทางเลิกใช้งานแล้วและจะถูกนำออกในอนาคต"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "การเรียกใช้ window.confirm จาก iframe แบบข้ามต้นทางเลิกใช้งานแล้วและจะถูกนำออกในอนาคต"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "การรองรับข้อมูล: URL ใน SVGUseElement เลิกใช้งานแล้วและจะนำออกในอนาคต"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() และ watchPosition() ไม่ทำงานในต้นทางที่ไม่ปลอดภัยอีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() และ watchPosition() เลิกใช้งานแล้วในต้นทางที่ไม่ปลอดภัย คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() ทำงานในต้นทางที่ไม่ปลอดภัยไม่ได้อีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "พบแท็ก <h1> ภายใน <article>, <aside>, <nav> หรือ <section> ซึ่งไม่มีขนาดแบบอักษรที่ระบุ ขนาดของข้อความส่วนหัวนี้จะเปลี่ยนแปลงในเบราว์เซอร์นี้ในเร็วๆ นี้ ดูข้อมูลเพิ่มเติมได้ที่ https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate เลิกใช้งานแล้ว โปรดใช้ RTCPeerConnectionIceErrorEvent.address หรือ RTCPeerConnectionIceErrorEvent.port แทน"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "รูปแบบนี้สำหรับคำขอ navigator.credentials.get() ของข้อมูลเข้าสู่ระบบดิจิทัลเลิกใช้งานแล้ว โปรดอัปเดตการเรียกใช้เพื่อใช้รูปแบบใหม่"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "ต้นทางของผู้ขายและข้อมูลที่กําหนดเองจากเหตุการณ์ Service Worker canmakepayment ซึ่งได้แก่ topOrigin, paymentRequestOrigin, methodData, modifiers เลิกใช้งานแล้วและจะถูกนําออก"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "เว็บไซต์ขอทรัพยากรย่อยจากเครือข่ายที่เข้าถึงได้เท่านั้นเนื่องจากมีตำแหน่งเครือข่ายที่เป็นสิทธิ์เฉพาะของผู้ใช้ คำขอเหล่านี้จะเปิดเผยอุปกรณ์และเซิร์ฟเวอร์ที่ไม่เผยแพร่ต่อสาธารณะไปยังอินเทอร์เน็ต ซึ่งเพิ่มความเสี่ยงในการโจมตีโดยการปลอมแปลงคำขอแบบข้ามเว็บไซต์ (CSRF) และ/หรือข้อมูลรั่วไหล Chrome จะเลิกใช้งานคำขอไปยังทรัพยากรย่อยที่ไม่เผยแพร่ต่อสาธารณะเมื่อเริ่มต้นมาจากบริบทที่ไม่ปลอดภัยและจะเริ่มบล็อกคำขอดังกล่าวเพื่อลดความเสี่ยงเหล่านี้"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "ฟิลด์ dailyUpdateUrl ของ InterestGroups ที่ส่งไปยัง joinAdInterestGroup() ได้เปลี่ยนชื่อเป็น updateUrl เพื่อให้สะท้อนลักษณะการทำงานได้อย่างแม่นยำยิ่งขึ้น"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator เลิกใช้งานแล้ว โปรดใช้ Intl.Segmenter แทน"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "โหลด CSS จาก URL file: ไม่ได้ เว้นแต่จะลงท้ายด้วยนามสกุลไฟล์ .css"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "การใช้ SourceBuffer.abort() เพื่อล้มเลิกการนำช่วงที่ไม่พร้อมกันออกของ remove() เลิกใช้งานไปแล้ว เนื่องจากมีการเปลี่ยนแปลงข้อกำหนด ระบบจะนำการรองรับออกในอนาคต คุณควรให้ความสำคัญกับเหตุการณ์ updateend แทน abort() มีไว้เพื่อล้มเลิกการเพิ่มสื่อที่ไม่พร้อมกันหรือรีเซ็ตสถานะโปรแกรมแยกวิเคราะห์เท่านั้น"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "การตั้งค่า MediaSource.duration ต่ำกว่าการประทับเวลาการนำเสนอสูงสุดของเฟรมที่ใส่โค้ดที่บัฟเฟอร์เลิกใช้งานแล้วเนื่องจากการเปลี่ยนแปลงข้อกำหนด การรองรับการนำสื่อที่บัฟเฟอร์ที่มีการตัดให้สั้นลงออกแบบไม่เจาะจงจะถูกนำออกในอนาคต คุณควรดำเนินการ remove(newDuration, oldDuration) แบบเจาะจงแทนใน sourceBuffers ทั้งหมดที่ newDuration < oldDuration"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI จะขอสิทธิ์ในการใช้แม้ว่าจะไม่ได้ระบุ SysEx ใน MIDIOptions ก็ตาม"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "ต้นทางที่ไม่ปลอดภัยอาจไม่ใช้ Notification API อีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "iframe แบบข้ามต้นทางอาจไม่ขอสิทธิ์สำหรับ Notification API อีกต่อไป คุณควรพิจารณาขอสิทธิ์จากเฟรมระดับบนสุดหรือเปิดหน้าต่างใหม่แทน"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "ตัวเลือก imageOrientation: 'none' ใน createImageBitmap เลิกใช้งานแล้ว โปรดใช้ createImageBitmap กับตัวเลือก \"{imageOrientation: 'from-image'}\" แทน"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "พาร์ทเนอร์ของคุณต่อรอง (D)TLS เวอร์ชันที่ล้าสมัย โปรดตรวจสอบกับพาร์ทเนอร์เพื่อดำเนินการแก้ไข"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "การระบุ overflow: visible ในแท็ก img, video และ canvas อาจทำให้แท็กเหล่านี้สร้างเนื้อหาภาพนอกขอบเขตขององค์ประกอบ โปรดดู https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments เลิกใช้งานแล้ว โปรดใช้การติดตั้งแบบทันท่วงทีแทนสำหรับตัวแฮนเดิลการชำระเงิน"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "การเรียกใช้ PaymentRequest ได้ข้ามคำสั่งของนโยบายรักษาความปลอดภัยเนื้อหา (CSP) connect-src การข้ามนี้เลิกใช้งานแล้ว โปรดเพิ่มตัวระบุวิธีการชำระเงินจาก PaymentRequest API (ในช่อง supportedMethods) ลงในคำสั่งของ CSP connect-src"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent เลิกใช้งานแล้ว โปรดใช้ navigator.storage มาตรฐานแทน"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> ที่มีองค์ประกอบหลัก <picture> ไม่ถูกต้องและด้วยเหตุนี้ระบบจึงจะไม่สนใจ โปรดใช้ <source srcset> แทน"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame เจาะจงผู้ให้บริการ โปรดใช้ cancelAnimationFrame แบบมาตรฐานแทน"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame เจาะจงผู้ให้บริการ โปรดใช้ requestAnimationFrame แบบมาตรฐานแทน"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen เลิกใช้งานแล้ว โปรดใช้ Document.fullscreenElement แทน"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() เลิกใช้งานแล้ว โปรดใช้ Element.requestFullscreen() แทน"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() เลิกใช้งานแล้ว โปรดใช้ Element.requestFullscreen() แทน"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() เลิกใช้งานแล้ว โปรดใช้ Document.exitFullscreen() แทน"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() เลิกใช้งานแล้ว โปรดใช้ Document.exitFullscreen() แทน"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen เลิกใช้งานแล้ว โปรดใช้ Document.fullscreenEnabled แทน"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "เรากำลังจะเลิกใช้งาน API chrome.privacy.websites.privacySandboxEnabled แม้ว่า API นี้จะยังคงมีการใช้งานต่อไปเพื่อความเข้ากันได้แบบย้อนหลังจนกว่าจะเปิดตัวเวอร์ชัน M113 โปรดใช้ chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled และ chrome.privacy.websites.adMeasurementEnabled แทน ดู https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "ข้อจำกัด DtlsSrtpKeyAgreement ถูกนำออกแล้ว คุณได้ระบุค่า false สำหรับข้อจำกัดนี้ ซึ่งระบบตีความว่าเป็นการพยายามใช้เมธอด SDES key negotiation ที่นำออกไปแล้ว ฟังก์ชันการทำงานนี้ถูกนำออกแล้ว โปรดใช้บริการที่รองรับ DTLS key negotiation แทน"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "ข้อจำกัด DtlsSrtpKeyAgreement ถูกนำออกแล้ว คุณได้ระบุค่า true สำหรับข้อจำกัดนี้ซึ่งไม่มีผลกระทบ แต่สามารถนำข้อจำกัดนี้ออกได้เพื่อความเรียบร้อย"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "getStats() ที่อิงตาม Callback เลิกใช้งานแล้วและจะถูกนำออก โปรดใช้ getStats() ที่สอดคล้องกับข้อกำหนดแทน"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() เลิกใช้งานแล้ว โปรดใช้ Selection.modify() แทน"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "คำขอทรัพยากรย่อยที่ URL มีข้อมูลเข้าสู่ระบบที่ฝังไว้ (เช่น **********************/) ถูกบล็อก"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "ตัวเลือก rtcpMuxPolicy เลิกใช้งานแล้วและจะถูกนำออก"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer จะต้องใช้การแยกแบบข้ามต้นทาง ดูรายละเอียดเพิ่มเติมได้ที่ https://developer.chrome.com/blog/enabling-shared-array-buffer/"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "speechSynthesis.speak() แบบไม่ต้องมีการเปิดใช้งานของผู้ใช้เลิกใช้งานแล้วและจะถูกนำออก"}, "generated/Deprecation.ts | UnloadHandler": {"message": "เลิกใช้งาน Listener เหตุการณ์ที่ยกเลิกการโหลดแล้วและจะถูกนำออก"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "ส่วนขยายควรเลือกใช้การแยกแบบข้ามต้นทางเพื่อใช้ SharedArrayBuffer ต่อ ดู https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "เราเลิกใช้งานแอตทริบิวต์ isFallbackAdapter ของ GPUAdapter แล้ว โปรดใช้แอตทริบิวต์ isFallbackAdapter ของ GPUAdapterInfo แทน"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "JSON ของการตอบกลับไม่รองรับ UTF-16 ใน XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "XMLHttpRequest แบบพร้อมกันในชุดข้อความหลักเลิกใช้งานแล้วเนื่องจากมีผลกระทบที่เป็นอันตรายต่อประสบการณ์ของผู้ใช้ปลายทาง ดูความช่วยเหลือเพิ่มเติมได้ที่ https://xhr.spec.whatwg.org/"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ภาพเคลื่อนไหว"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "การเปลี่ยนแปลงเลย์เอาต์เกิดขึ้นเมื่อองค์ประกอบต่างๆ เคลื่อนที่โดยไม่มีการโต้ตอบของผู้ใช้ [ตรวจสอบสาเหตุของการเปลี่ยนแปลงเลย์เอาต์](https://web.dev/articles/optimize-cls) เช่น การเพิ่ม การนำออก หรือการเปลี่ยนแบบอักษรขององค์ประกอบต่างๆ เมื่อโหลดหน้าเว็บ"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "คำขอแบบอักษร"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "มีการแทรก iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "คลัสเตอร์การเปลี่ยนแปลงเลย์เอาต์ @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "ตรวจไม่พบสาเหตุของการเปลี่ยนแปลงเลย์เอาต์"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "ไม่มีการเปลี่ยนแปลงเลย์เอาต์"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "สาเหตุของการเปลี่ยนแปลงเลย์เอาต์"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "สาเหตุหลักๆ ของการเปลี่ยนแปลงเลย์เอาต์"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "คลัสเตอร์ที่แย่ที่สุด"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "คลัสเตอร์การเปลี่ยนแปลงเลย์เอาต์ที่แย่ที่สุด"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL ของแคช"}, "models/trace/insights/Cache.ts | description": {"message": "อายุการใช้งานแคชที่ยาวนานช่วยเพิ่มการเข้าชมหน้าเว็บซ้ำได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "ไม่มีคำขอที่มีนโยบายแคชที่ไม่มีประสิทธิภาพ"}, "models/trace/insights/Cache.ts | others": {"message": "อีก {PH1} รายการ"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "คำขอ"}, "models/trace/insights/Cache.ts | title": {"message": "ใช้อายุการใช้งานแคชที่มีประสิทธิภาพ"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM ที่มีขนาดใหญ่จะใช้เวลานานขึ้นในการคำนวณสไตล์และการจัดเรียงเลย์เอาต์ใหม่ ซึ่งส่งผลต่อการตอบสนองของหน้าเว็บ นอกจากนี้ DOM ที่มีขนาดใหญ่จะใช้หน่วยความจำเพิ่มขึ้นด้วย [ดูวิธีหลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "องค์ประกอบ"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "องค์ประกอบย่อยส่วนใหญ่"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "ความลึกของ DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "สถิติ"}, "models/trace/insights/DOMSize.ts | title": {"message": "ปรับขนาด DOM ให้เหมาะสม"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "องค์ประกอบทั้งหมด"}, "models/trace/insights/DOMSize.ts | value": {"message": "ค่า"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "คำขอเครือข่ายครั้งแรกเป็นสิ่งสำคัญที่สุด  ลดเวลาในการตอบสนองโดยหลีกเลี่ยงการเปลี่ยนเส้นทาง ตรวจสอบการตอบกลับของเซิร์ฟเวอร์ที่รวดเร็ว และเปิดใช้การบีบอัดข้อความ"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "มีการเปลี่ยนเส้นทาง (เปลี่ยนเส้นทาง {PH1} +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "เซิร์ฟเวอร์ตอบสนองช้า (สังเกต {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "ไม่ได้ใช้การบีบอัด"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "หลีกเลี่ยงการเปลี่ยนเส้นทาง"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "เซิร์ฟเวอร์ตอบสนองเร็ว (สังเกต {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "ใช้การบีบอัดข้อความ"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "เปลี่ยนเส้นทาง"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "เวลาในการตอบกลับของเซิร์ฟเวอร์"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "เวลาในการตอบสนองต่อคำขอเอกสาร"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "การดาวน์โหลดที่ไม่มีการบีบอัด"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "ไบต์ที่ซ้ำกัน"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "แหล่งที่มา"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "นำโมดูล JavaScript ขนาดใหญ่ที่ซ้ำกันออกจากแพ็กเกจเพื่อลดจำนวนไบต์ที่ไม่จำเป็นที่กิจกรรมเครือข่ายใช้"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript ที่ซ้ำกัน"}, "models/trace/insights/FontDisplay.ts | description": {"message": "ลองตั้งค่า [font-display](https://developer.chrome.com/blog/font-display) เป็น swap หรือ optional เพื่อให้ผู้ใช้มองเห็นข้อความได้เสมอ swap สามารถเพิ่มประสิทธิภาพเพิ่มเติมเพื่อลดการเปลี่ยนแปลงเลย์เอาต์ด้วย[การลบล้างเมตริกแบบอักษร](https://developer.chrome.com/blog/font-fallbacks)"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "แบบอักษร"}, "models/trace/insights/FontDisplay.ts | title": {"message": "การแสดงแบบอักษร"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "เวลาที่เสียไป"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(ไม่ระบุชื่อ)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "API หลายรายการ ซึ่งโดยปกติจะอ่านรูปทรงเรขาคณิตของเลย์เอาต์ บังคับให้เครื่องมือแสดงผลหยุดการทำงานของสคริปต์ชั่วคราวเพื่อคำนวณรูปแบบและเลย์เอาต์ ดูข้อมูลเพิ่มเติมเกี่ยวกับ[การบังคับให้จัดเรียงใหม่](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)และการลดผลกระทบ"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "สแต็กเทรซ"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "การบังคับให้จัดเรียงใหม่"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "การเรียกใช้ฟังก์ชันยอดนิยม"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "เวลาในการจัดเรียงใหม่ทั้งหมด"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ไม่มีการระบุแหล่งที่มา]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "การลดเวลาดาวน์โหลดรูปภาพจะช่วยปรับปรุงเวลาที่ใช้ในการโหลดที่รับรู้ของหน้าเว็บและ LCP ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการเพิ่มประสิทธิภาพขนาดรูปภาพ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (ประมาณ {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ไม่มีรูปภาพที่เพิ่มประสิทธิภาพได้"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "เพิ่มประสิทธิภาพขนาดไฟล์"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "อีก {PH1} รายการ"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "ปรับปรุงการนำส่งรูปภาพ"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "การเพิ่มค่าการบีบอัดรูปภาพสามารถปรับปรุงขนาดการดาวน์โหลดของรูปภาพนี้ได้"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "การใช้รูปแบบรูปภาพสมัยใหม่ (WebP, AVIF) หรือการเพิ่มการบีบอัดรูปภาพอาจช่วยปรับปรุงขนาดการดาวน์โหลดของรูปภาพนี้ได้"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ไฟล์ภาพนี้มีขนาดใหญ่เกินความจำเป็น ({PH1}) สำหรับมิติข้อมูลที่แสดง ({PH2}) โปรดใช้รูปภาพที่ปรับเปลี่ยนตามอุปกรณ์เพื่อลดขนาดการดาวน์โหลดรูปภาพ"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "การใช้รูปแบบวิดีโอแทน GIF จะช่วยปรับปรุงขนาดการดาวน์โหลดเนื้อหาที่เป็นภาพเคลื่อนไหวได้"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "เริ่มตรวจสอบด้วยระยะที่ยาวที่สุด [ลดการหน่วงเวลาให้น้อยที่สุด](https://web.dev/articles/optimize-inp#optimize_interactions) หากต้องการลดระยะเวลาในการประมวลผล ให้[เพิ่มประสิทธิภาพต้นทุนของเทรดหลัก](https://web.dev/articles/optimize-long-tasks) ซึ่งมักจะเป็น JS"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "ระยะเวลา"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "ความล่าช้าของอินพุต"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ไม่พบการโต้ตอบ"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ระยะ"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "ความล่าช้าของงานนำเสนอ"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "ระยะเวลาในการประมวลผล"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP ตามระยะ"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "เพิ่มประสิทธิภาพ LCP โดยทำให้รูปภาพ LCP [ค้นพบได้](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)จาก HTML ทันที และ[หลีกเลี่ยงการโหลดแบบ Lazy Loading](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "ใช้ลำดับความสำคัญในการดึงข้อมูล=สูงอยู่"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "ควรใช้ fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ไม่ได้ใช้การโหลดแบบ Lazy Loading"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "รูปภาพ LCP ใช้เวลาโหลด {PH1} หลังจากจุดเริ่มต้นแรกสุด"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "ไม่พบ LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "ไม่พบทรัพยากร LCP เนื่องจาก LCP ไม่ใช่รูปภาพ"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "คำขอสามารถค้นพบได้ในเอกสารเริ่มต้น"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "การค้นพบคำขอ LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "แต่ละ[ระยะมีกลยุทธ์การปรับปรุงเฉพาะ](https://web.dev/articles/optimize-lcp#lcp-breakdown) โดยปกติแล้ว เวลาส่วนใหญ่ของ LCP ควรใช้ไปกับการโหลดทรัพยากร ไม่ใช่การหน่วงเวลา"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "ระยะเวลา"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "ความล่าช้าในการแสดงผลองค์ประกอบ"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ช่อง p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "ไม่พบ LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ระยะ"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "ความล่าช้าของการโหลดทรัพยากร"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "ระยะเวลาในการโหลดทรัพยากร"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "เวลาที่ได้รับข้อมูลไบต์แรก"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP ตามระยะ"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "สคริปต์"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "ไบต์ที่เสียไป"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill และการเปลี่ยนรูปแบบช่วยให้เบราว์เซอร์เดิมใช้ฟีเจอร์ JavaScript ใหม่ได้ แต่ส่วนมากจะไม่จำเป็นสำหรับเบราว์เซอร์ที่ทันสมัย ลองแก้ไขกระบวนการบิลด์ JavaScript เพื่อไม่ให้ทำการ Transpile ฟีเจอร์ [Baseline](https://web.dev/articles/baseline-and-polyfills) เว้นแต่คุณจะรู้ว่าต้องรองรับเบราว์เซอร์เดิม [ดูสาเหตุที่เว็บไซต์ส่วนใหญ่สามารถติดตั้งใช้งานโค้ด ES6+ ได้โดยไม่ต้องทำการ Transpile](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript เดิม"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 และ HTTP/3 มีประโยชน์มากกว่า HTTP/1.1 หลายประการ เช่น ในด้านการมัลติเพล็กซ์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการใช้ HTTP ที่ทันสมัย](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "ไม่มีคำขอที่ใช้ HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "โปรโตคอล"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "คำขอ"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP ที่ทันสมัย"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "ต้นทาง"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "คำขอ"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "แหล่งที่มา"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "เวลา"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "การประหยัด LCP โดยประมาณ"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "การเชื่อมต่อล่วงหน้าที่ไม่ได้ใช้ โปรดตรวจสอบว่าใช้แอตทริบิวต์ crossorigin อย่างถูกต้องแล้ว"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[หลีกเลี่ยงการเชนคำขอที่สำคัญ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)โดยลดความยาวของเชน ลดขนาดการดาวน์โหลดของทรัพยากร หรือเลื่อนเวลาการดาวน์โหลดทรัพยากรที่ไม่จำเป็นเพื่อปรับปรุงการโหลดหน้าเว็บ"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "เพิ่มคำแนะนำ[การเชื่อมต่อล่วงหน้า](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)ไปยังต้นทางที่สำคัญที่สุด แต่พยายามใช้ไม่เกิน 4"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "ตัวเลือกการเชื่อมต่อล่วงหน้า"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "เวลาในการตอบสนองสูงสุดของเส้นทางที่สำคัญ:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "ไม่มีงานการแสดงผลที่ได้รับผลกระทบจากทรัพยากร Dependency ของเครือข่าย"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "ไม่มีตัวเลือกต้นทางเพิ่มเติมที่เหมาะสําหรับการเชื่อมต่อล่วงหน้า"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "ไม่ได้เชื่อมต่อกับต้นทางไว้ล่วงหน้า"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "คำแนะนำ[การเชื่อมต่อล่วงหน้า](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)ทำให้เบราว์เซอร์สร้างการเชื่อมต่อได้เร็วขึ้นเมื่อโหลดหน้าเว็บ ซึ่งจะช่วยประหยัดเวลาเมื่อมีการขอข้อมูลต้นทางครั้งแรก ต้นทางที่หน้าเว็บเชื่อมต่อไว้ล่วงหน้ามีดังนี้"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "ต้นทางที่เชื่อมต่อไว้ล่วงหน้า"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "แผนผังทรัพยากร Dependency ของเครือข่าย"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "พบการเชื่อมต่อ preconnect มากกว่า 4 รายการ ควรใช้การเชื่อมต่อเช่นนี้เท่าที่จำเป็นและใช้กับต้นทางที่สำคัญที่สุดเท่านั้น"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "การเชื่อมต่อล่วงหน้าที่ไม่ได้ใช้ ใช้ preconnect กับต้นทางที่หน้าเว็บมีแนวโน้มจะขอเท่านั้น"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "หลีกเลี่ยงการเชนคำขอที่สำคัญโดยลดความยาวของเชน ลดขนาดการดาวน์โหลดของทรัพยากร หรือเลื่อนเวลาการดาวน์โหลดทรัพยากรที่ไม่จำเป็นเพื่อปรับปรุงการโหลดหน้าเว็บ"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "คำขอกำลังบล็อกการแสดงผลเริ่มต้นของหน้าเว็บ ซึ่งอาจทำให้ LCP ล่าช้า [การเลื่อนเวลาหรือแทรกในหน้า](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)สามารถย้ายคำขอเครือข่ายเหล่านี้ออกจากเส้นทางที่สำคัญได้"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "ระยะเวลา"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ไม่มีคำขอบล็อกการแสดงผลสำหรับการนำทางนี้"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "คำขอ"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "แสดงผลคำขอบล็อก"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "หากต้นทุนในการคำนวณรูปแบบอีกครั้งยังคงสูงอยู่ การเพิ่มประสิทธิภาพตัวเลือกจะลดต้นทุนได้ [เพิ่มประสิทธิภาพตัวเลือก](https://developer.chrome.com/docs/devtools/performance/selector-stats)ด้วยทั้งเวลาที่ผ่านไปนานและ % ของเส้นทางที่ช้าที่สูง ตัวเลือกที่เรียบง่ายขึ้น, ตัวเลือกที่น้อยลง, DOM ที่เล็กลง และ DOM ที่ตื้นขึ้นจะช่วยลดต้นทุนการจับคู่ได้ทั้งหมด"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "เวลาที่ผ่านไป"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "ไม่พบข้อมูลตัวเลือก CSS ต้องเปิดใช้สถิติตัวเลือก CSS ในการตั้งค่าแผงประสิทธิภาพ"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ความพยายามจับคู่ที่ตรงกัน"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "จำนวนการจับคู่ที่ตรงกัน"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "ต้นทุนของตัวเลือก CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ตัวเลือกยอดนิยม"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "รวม"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "เวลาของเทรดหลัก"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "บุคคลที่สาม"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "ขนาดการโอน"}, "models/trace/insights/ThirdParties.ts | description": {"message": "โค้ดของบุคคลที่สามอาจส่งผลกระทบที่สำคัญต่อประสิทธิภาพการโหลด [ลดและเลื่อนการโหลดโค้ดของบุคคลที่สามออกไป](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)เพื่อจัดลำดับความสำคัญของเนื้อหาในหน้าเว็บ"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "ไม่พบบุคคลที่สาม"}, "models/trace/insights/ThirdParties.ts | title": {"message": "บุคคลที่สาม"}, "models/trace/insights/Viewport.ts | description": {"message": "การโต้ตอบด้วยการแตะอาจ[มีการหน่วงเวลาสูงสุด 300 มิลลิวินาที](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)หากวิวพอร์ตไม่เหมาะกับอุปกรณ์เคลื่อนที่"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "การหน่วงเวลาการแตะบนมือถือ"}, "models/trace/insights/Viewport.ts | title": {"message": "เพิ่มประสิทธิภาพวิวพอร์ตสำหรับอุปกรณ์เคลื่อนที่"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "เฉพาะหน้าที่โหลดผ่านคำขอ GET เท่านั้นที่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "เฉพาะหน้าที่มีรหัสสถานะ 2XX เท่านั้นที่แคชได้"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome ตรวจพบความพยายามที่จะเรียกใช้ JavaScript ขณะอยู่ในแคช"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "หน้าที่ขอ AppBanner ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "แคชย้อนหลังถูกปิดใช้งานจากการแจ้งว่าไม่เหมาะสม ไปที่ chrome://flags/#back-forward-cache เพื่อเปิดใช้งานในอุปกรณ์นี้โดยตรง"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "แคชย้อนหลังถูกปิดใช้งานโดยบรรทัดคำสั่ง"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "แคชย้อนหลังถูกปิดใช้งานเนื่องจากหน่วยความจำไม่พอ"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Delegate ไม่รองรับแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "แคชย้อนหลังถูกปิดใช้งานสำหรับตัวแสดงผลล่วงหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "แคชหน้านี้ไม่ได้เพราะมีอินสแตนซ์ BroadcastChannel ที่ Listener ลงทะเบียนไว้"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "หน้าที่มีส่วนหัว cache-control:no-store ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "มีการล้างแคชโดยตั้งใจ"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "หน้านี้ถูกนำออกจากแคชเพื่อให้แคชหน้าอื่นได้"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "หน้าที่มีปลั๊กอินยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "ไม่ระบุ"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "หน้าที่ใช้ FileChooser API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "หน้าที่ใช้ File System Access API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "หน้าที่ใช้ตัวส่งสำหรับอุปกรณ์สื่อไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "มีเดียเพลเยอร์กำลังเล่นอยู่ขณะที่มีการออกจากหน้าไป"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "หน้าที่ใช้ MediaSession API และมีการตั้งสถานะการเล่นไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "หน้าที่ใช้ MediaSession API และมีการตั้งตัวจัดการการทำงานไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "แคชย้อนหลังถูกปิดใช้เนื่องจากโปรแกรมอ่านหน้าจอ"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "หน้าที่ใช้ SecurityHandler ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "หน้าที่ใช้ Serial API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "หน้าที่ใช้ WebAuthetication API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "หน้าที่ใช้ WebBluetooth API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "หน้าที่ใช้ WebUSB API ไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Back/Forward Cache ถูกปิดใช้เนื่องจากมีการปิดใช้คุกกี้ในหน้าที่ใช้ Cache-Control: no-store"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "หน้าที่ใช้ Dedicated Worker หรือ Worklet ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "เอกสารโหลดไม่เสร็จก่อนที่จะมีการออกจากหน้าไป"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "พบแบนเนอร์แอปขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "พบเครื่องมือจัดการรหัสผ่านของ Chrome ขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "กำลังแยกเอลิเมนต์ DOM ขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "พบโปรแกรมดูเครื่องมือแยกเอลิเมนต์ DOM ขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "แคชย้อนหลังถูกปิดใช้เนื่องจากส่วนขยายที่ใช้ API การรับส่งข้อความ"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "ส่วนขยายที่มีการเชื่อมต่อระยะเวลานานควรปิดการเชื่อมต่อก่อนที่จะจัดเก็บไว้ในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "ส่วนขยายที่มีการเชื่อมต่อระยะเวลานานพยายามส่งข้อความถึงเฟรมในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "แคชย้อนหลังถูกปิดใช้เนื่องจากส่วนขยาย"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "กล่องโต้ตอบโมดัล เช่น การส่งแบบฟอร์มอีกครั้งหรือรหัสผ่าน HTTP แสดงขึ้นขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "หน้าแบบออฟไลน์แสดงขึ้นขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "พบแถบการแทรกแซง \"หน่วยความจำไม่เพียงพอ\" ขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "มีคำขอสิทธิ์ขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "พบตัวบล็อกป็อปอัปขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "รายละเอียด Google Safe Browsing แสดงขึ้นขณะที่มีการออกจากหน้า"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Google Safe Browsing ถือว่าหน้านี้มีการละเมิดและได้บล็อกป๊อปอัป"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "มีการเปิดใช้งาน Service Worker ขณะที่หน้าอยู่ในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "ปิดใช้ Back-Forward <PERSON><PERSON> เนื่องจากเอกสารมีข้อผิดพลาด"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "หน้าที่ใช้ FencedFrames จัดเก็บใน bfcache ไม่ได้"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "หน้านี้ถูกนำออกจากแคชเพื่อให้แคชหน้าอื่นได้"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "หน้าที่ให้สิทธิ์สตรีมสื่อยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "หน้าที่มีเนื้อหาบางชนิดฝังอยู่ (เช่น PDF) ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "หน้าที่ใช้ IdleManager ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "หน้าที่มีการเชื่อมต่อ IndexedDB แบบเปิดยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Back-Forward C<PERSON> ถูกปิดใช้เนื่องจากเหตุการณ์ IndexedDB"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "มีการใช้ API ที่ไม่มีสิทธิ์"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "หน้าที่มีการแทรก JavaScript ลงในส่วนขยายยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "หน้าที่มีการแทรกStyleSheetลงในส่วนขยายยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "ข้อผิดพลาดภายใน"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Back/Forward Cache ถูกปิดใช้เนื่องจากคำขอของเครือข่าย JavaScript บางรายการได้รับทรัพยากรที่มีส่วนหัว Cache-Control: no-store"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากคำขอ Keepalive"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "หน้าที่ใช้การล็อกแป้นพิมพ์ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "หน้านี้โหลดไม่เสร็จก่อนที่จะมีการออกจากหน้าไป"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "หน้าที่ทรัพยากรหลักของหน้ามี cache-control:no-cache ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "หน้าที่ทรัพยากรหลักของหน้ามี cache-control:no-store ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "มีการยกเลิกการนำทางก่อนที่จะสามารถกู้คืนหน้าจากแคชย้อนหลังได้"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "หน้าถูกนำออกจากแคชเพราะการเชื่อมต่อเครือข่ายที่ทำงานอยู่ได้รับข้อมูลมากเกินไป Chrome จำกัดปริมาณข้อมูลที่แต่ละหน้าสามารถรับได้ขณะที่มีการแคชหน้าไว้"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "หน้าที่คำขอ fetch() หรือ XHR กำลังทำงานยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "หน้าถูกนำออกจากแคชย้อนหลังเพราะคำขอเครือข่ายที่ใช้งานอยู่เกี่ยวข้องกับการเปลี่ยนเส้นทาง"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "หน้านี้ถูกนำออกจากแคชเพราะเปิดการเชื่อมต่อเครือข่ายไว้นานเกินไป Chrome จำกัดเวลาที่หน้าสามารถรับข้อมูลขณะแคช"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "หน้าที่ไม่มีส่วนหัวการตอบกลับที่ถูกต้องไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "มีการนำทางเกิดขึ้นในเฟรมอื่นนอกเหนือจากเฟรมหลัก"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "หน้าที่ธุรกรรมฐานข้อมูลได้รับการจัดทำดัชนีอย่างต่อเนื่องยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "หน้าที่คำขอเครือข่ายกำลังทำงานยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "หน้าที่คำขอดึงข้อมูลจากเครือข่ายกำลังทำงานยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "หน้าที่คำขอเครือข่ายกำลังทำงานยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "หน้าที่คำขอเครือข่าย XHR กำลังทำงานยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "หน้าที่ใช้ PaymentManager ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "หน้าที่ใช้การแสดงภาพซ้อนภาพยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "หน้าที่แสดง Printing UI ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "มีการเปิดหน้านี้โดยใช้ \"window.open()\" และมีการอ้างอิงจากแท็บอื่น หรือหน้านี้เปิดหน้าต่างขึ้นมา"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "การประมวลการแสดงผลสำหรับหน้านี้ในแคชย้อนหลังเกิดการขัดข้อง"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "การประมวลการแสดงผลสำหรับหน้านี้ในแคชย้อนหลังถูกตัด"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "หน้าที่มีการขอสิทธิ์บันทึกเสียงยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "หน้าที่ขอสิทธิ์เข้าถึงข้อมูลเซ็นเซอร์ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "หน้าที่มีการขอสิทธิ์เพื่อซิงค์เบื้องหลังหรือดึงข้อมูลยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "หน้าที่มีการขอสิทธิ์ MIDI ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "หน้าที่มีการขอสิทธิ์ส่งการแจ้งเตือนยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "หน้าที่มีการขอการเข้าถึงพื้นที่เก็บข้อมูลยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "หน้าที่มีการขอสิทธิ์บันทึกวิดีโอยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "เฉพาะหน้าที่รูปแบบ URL ของหน้าเป็น HTTP / HTTPS เท่านั้นที่สามารถแคชไว้ได้"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "มี Service Worker อ้างสิทธิ์หน้านี้ขณะอยู่ในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "มี Service Worker พยายามส่ง MessageEvent ให้หน้าที่อยู่ในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "มีการยกเลิกการลงทะเบียน ServiceWorker ขณะที่หน้าอยู่ในแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "หน้านี้ถูกนำออกจากแคชย้อนหลังเนื่องจากมีการเปิดใช้งาน Service Worker"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome รีสตาร์ทและล้างรายการแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "หน้าที่ใช้ SharedWorker ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "หน้าที่ใช้ SpeechRecognizer ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "หน้าที่ใช้ SpeechSynthesis ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe ในหน้าเริ่มการนำทางที่ไม่เสร็จสมบูรณ์"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "หน้าที่ทรัพยากรย่อยของหน้ามี cache-control:no-cache ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "หน้าที่ทรัพยากรย่อยของหน้ามี cache-control:no-store ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "หน้านี้อยู่ในแคชย้อนหลังเกินเวลาที่จำกัดและหมดอายุไปแล้ว"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "หมดเวลาเข้าถึงแคชย้อนหลังสำหรับหน้านี้ (น่าจะเป็นเพราะตัวจัดการสำหรับซ่อนหน้าทำงานเป็นเวลานาน)"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "หน้านี้มีตัวจัดการสำหรับยกเลิกการโหลดติดตั้งอยู่ในเฟรมหลัก"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "หน้านี้มีตัวจัดการสำหรับยกเลิกการโหลดในเฟรมย่อย"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "เบราว์เซอร์ได้เปลี่ยนส่วนหัวการลบล้างของ User Agent"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "หน้าที่ให้สิทธิ์เข้าถึงเพื่อบันทึกวิดีโอหรือเสียงยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "หน้าที่ใช้ WebDatabase ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "หน้าที่ใช้ WebHID ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "หน้าที่ใช้ WebLocks ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "หน้าที่ใช้ WebNfc ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "หน้าที่ใช้ WebOTPService ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "หน้าที่มี WebRTC ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Back-Forward Cache ถูกปิดเนื่องจากมีการใช้ WebRTC"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "หน้าที่ใช้ WebShare ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "หน้าที่มี WebSocket ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดเนื่องจากมีการใช้ WebSocket"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "หน้าที่มี WebTransport ไม่สามารถเข้าถึงแคชย้อนหลัง"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดเนื่องจากมีการใช้ WebTransport"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "หน้าที่ใช้ WebXR ยังไม่สามารถใช้ฟีเจอร์แคชย้อนหลัง"}}