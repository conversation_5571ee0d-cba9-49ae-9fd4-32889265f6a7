{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers 處理操作中的萬用字元符號 (*) 不包含授權。"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "應使用 disableRemotePlayback 屬性停用預設的投放整合功能，而非 -internal-media-controls-overlay-cast-button 選取器。"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS 外觀值 slider-vertical 並未標準化，因此將會移除。"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "系統將封鎖網址同時包含已移除的空白 \\(n|r|t) 字元和小於字元 (<) 的資源要求。請從元素屬性值等位置移除換行符號，並編碼小於字元，以便載入這些資源。"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() 已淘汰，請改用標準化 API：Navigation Timing 2。"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() 已淘汰，請改用標準化 API：Paint Timing。"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() 已淘汰，請改用標準化 API：Navigation Timing 2 中的 nextHopProtocol。"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "系統將會拒絕包含 \\(0|r|n) 字元的 Cookie，而非縮短。"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "透過設定 document.domain 啟用相同來源政策的功能已淘汰，並將根據預設移除。此淘汰警告是針對透過設定 document.domain 來啟用跨來源存取功能。"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "從跨來源 iframe 觸發 window.alert 的功能已淘汰，並將會在日後移除。"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "從跨來源 iframe 觸發 window.confirm 的功能已淘汰，並將會在日後移除。"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "SVGUseElement 不再支援「data: 網址」，並將於日後移除。"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() 和 watchPosition() 無法再在不安全來源上運作。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "不安全來源上的 getCurrentPosition() 和 watchPosition() 已淘汰。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() 無法再在不安全來源上運作。如要使用此功能，請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "在 <article>、<aside>、<nav> 或 <section> 中，系統發現有一個 <h1> 標籤未指定字型大小。不久後，此標題文字在此瀏覽器中的大小將有變動。詳情請參閱 https://developer.mozilla.org/zh-HK/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1。"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate 已淘汰。請改為使用 RTCPeerConnectionIceErrorEvent.address 或 RTCPeerConnectionIceErrorEvent.port。"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "數碼憑證的 navigator.credentials.get() 要求格式已淘汰，請更新調用以使用新格式。"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "「canmakepayment」Service Worker 事件的商家來源和任意資料目前已淘汰，並將在之後移除：topOrigin、paymentRequestOrigin、methodData、modifiers。"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "網站根據使用者具備權限的網絡位置，透過只能存取的網絡要求一個子資源。這些要求會向互聯網暴露非公開的裝置和伺服器，因而增加偽造跨網站要求 (CSRF) 攻擊和/或資料洩漏的風險。為降低風險，Chrome 會忽略不安全內容向非公開子資源發出的要求，並將開始封鎖這類要求。"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "已傳送至 joinAdInterestGroup() 的 InterestGroups dailyUpdateUrl 欄位已重新命名為 updateUrl，以便更準確地反映其行為。"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator 已淘汰。請改用 Intl.Segmenter。"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "除非 CSS 以 .css 副檔名作結，否則無法從 file: 網址載入。"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "由於規格變更，系統已淘汰使用 SourceBuffer.abort() 取消 remove() 的非同步範圍移除操作，日後亦將停止支援此功能。建議您改為監聽 updateend 事件。abort() 的用途只限於取消非同步媒體附加內容或重設剖析器狀態。"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "由於規格變更，系統已淘汰將 MediaSource.duration 設定為低於任何緩衝編碼頁框的最高顯示時間戳記。日後將停止支援對已縮短緩衝媒體的隱含移除操作。請改為在 newDuration < oldDuration 的所有 sourceBuffers 上執行明確 remove(newDuration, oldDuration)。"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "即使在 MIDIOptions 中未指定系統專用 (SysEx) 訊息，Web MIDI 亦會要求使用權限。"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "系統可能無法再使用不安全來源的 Notification API。請考慮將應用程式轉移至安全來源，例如 HTTPS。詳情請參閱 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "系統可能無法再透過跨來源 iframe 要求 Notification API。請考慮透過頂層頁框要求權限，或改為開啟新視窗。"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap 中的 imageOrientation: 'none' 選項已淘汰。請改用 createImageBitmap 配搭「{imageOrientation: 'from-image'}」選項。"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "您的合作夥伴正在交涉已過時的傳輸層安全標準 (TLS)/DTLS 版本。請向您的合作夥伴查詢，以便修正問題。"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "如果指定 img、video 和 canvas 標籤的「overflow: visible」，可能會導致這些標籤產生的視覺內容超出元素邊界。詳情請參閱 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "「paymentManager.instruments」已淘汰。請改用付款處理常式的即時安裝方法。"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "您的「PaymentRequest」調用已繞過內容安全政策 (CSP)「connect-src」指令，但目前已無法再繞過。請將 PaymentRequest API 的付款方法識別碼 (位於「supportedMethods」欄位) 新增至 CSP「connect-src」指令。"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent 已淘汰。請改為標準化的 navigator.storage。"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "具有 <picture> 父項的 <source src> 無效，因此系統會忽略。請改用「<source srcset>」。"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame 只限供應商使用，請改用標準的 cancelAnimationFrame。"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame 只限供應商使用，請改用標準 requestAnimationFrame。"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen 已淘汰，請改用 Document.fullscreenElement。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() 已淘汰，請改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() 已淘汰，請改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() 已淘汰，請改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() 已淘汰，請改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen 已淘汰，請改用 Document.fullscreenEnabled。"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "我們已淘汰此 API chrome.privacy.websites.privacySandboxEnabled，但為提供回溯兼容性，此 API 可持續使用至版本 M113。請改用 chrome.privacy.websites.topicsEnabled、chrome.privacy.websites.fledgeEnabled 和 chrome.privacy.websites.adMeasurementEnabled。詳情請參閱 https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "已移除 DtlsSrtpKeyAgreement 限制。系統將您為此限制指定的 false 值解讀為嘗試使用已移除的「SDES key negotiation」方法。此功能已移除，請改用支援「DTLS key negotiation」的方法。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "已移除 DtlsSrtpKeyAgreement 限制。您為此限制指定的 true 值已不再適用，不過您可將此限制移除以保持畫面整潔。"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "回呼式 getStats() 已淘汰，並將會移除。請改用合規的 getStats()。"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() 已淘汰，請改用 Selection.modify()。"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "系統將封鎖網址包含嵌入憑證 (例如 **********************/) 的子資源要求。"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy 選項已淘汰，並將會移除。"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer 會要求跨來源隔離。詳情請參閱 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "無需使用者啟用即可呼叫 speechSynthesis.speak() 的功能已淘汰，並將會移除。"}, "generated/Deprecation.ts | UnloadHandler": {"message": "卸載事件監聽器已淘汰，並將於日後移除。"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "擴充程式應選擇接受跨來源隔離功能，以繼續使用 SharedArrayBuffer。詳情請參閱 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter 屬性已淘汰，請改用 GPUAdapterInfo isFallbackAdapter 屬性。"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest 中的 JSON 回應不支援 UTF-16。"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主要執行緒上同步的 XMLHttpRequest 會對使用者體驗造成負面影響，因此已淘汰。如需進一步說明，請查看 https://xhr.spec.whatwg.org/。"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "動畫"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "當元素在無使用者互動下移動，就會出現版面配置轉移。請[調查版面配置轉移的原因](https://web.dev/articles/optimize-cls)，例如隨頁面載入有元素加入、移除或字型變更。"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "字型要求"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "已插入 iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "版面配置轉移叢集 @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "無法偵測任何版面配置轉移起因"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "沒有版面配置轉移"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "版面配置轉換起因"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "版面配置轉移的主要原因"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "最差的叢集"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "最差的版面配置轉移叢集"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "快取 TTL"}, "models/trace/insights/Cache.ts | description": {"message": "延長快取期限可加快重覆瀏覽頁面的速度。[瞭解詳情](https://web.dev/uses-long-cache-ttl/)。"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "沒有任何要求採用效率欠佳的快取政策"}, "models/trace/insights/Cache.ts | others": {"message": "和另外 {PH1} 個項目"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "要求"}, "models/trace/insights/Cache.ts | title": {"message": "使用有效的快取生命週期"}, "models/trace/insights/DOMSize.ts | description": {"message": "大型 DOM 可能會增加樣式運算和版面配置重排的時長，繼而影響頁面回應速度。大型 DOM 也會增加記憶體用量。[瞭解如何避免 DOM 過大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。"}, "models/trace/insights/DOMSize.ts | element": {"message": "元素"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "大部分子項"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM 深度"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "統計資料"}, "models/trace/insights/DOMSize.ts | title": {"message": "優化 DOM 大小"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "元素總計"}, "models/trace/insights/DOMSize.ts | value": {"message": "值"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "首個網絡要求至關重要。避免重新導向、確保伺服器快速回應，並啟用文字壓縮功能，都有助縮短延遲時間。"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "有重新導向 ({PH1} 次重新導向 +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "伺服器回應緩慢 (觀測結果：{PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "未套用任何壓縮"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "避免重新導向"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "伺服器回應迅速 (觀測結果：{PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "套用文字壓縮"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "重新導向"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "伺服器回應時間"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "文件要求延遲時間"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "未壓縮下載"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "重複的位元組數目"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "來源"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "從套件中移除重複的大型 JavaScript 模組，避免網絡活動耗用不必要的字節。"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "重複的 JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "建議將 [font-display](https://developer.chrome.com/blog/font-display) 設定為 swap 或 optional，確保文字能持續顯示。如要進一步優化 swap，你可[覆寫字型數據](https://developer.chrome.com/blog/font-fallbacks)來緩解版面配置轉換問題。"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "字型"}, "models/trace/insights/FontDisplay.ts | title": {"message": "字型顯示"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "浪費的時間"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(匿名)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "很多 API (通常讀取版面配置幾何圖形) 會強制輸出引擎暫停執行指令碼，以計算樣式和版面配置。進一步瞭解[強制重排](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)及其緩解措施。"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "堆疊追蹤"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "強制重排"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "最費時的函數調用"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "重排總時間"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[未歸因]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "縮短圖像下載時間，可縮減使用者感知的網頁載入時間，並提升 LCP 數據的表現。[進一步瞭解如何優化圖像大小](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (預估大小為 {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "沒有可優化的圖像"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "優化檔案大小"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "和另外 {PH1} 個項目"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "改善圖像放送"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "提高圖像壓縮系數可縮減此圖像的下載大小。"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "使用新式圖像格式 (WebP、AVIF) 或提高圖像壓縮系數，可縮減此圖像的下載大小"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "此圖像檔案的原始尺寸 ({PH2}) 大於所需顯示尺寸 ({PH1})。請使用回應式圖像來縮減圖像下載大小。"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "使用影片格式而非 GIF，可以縮減動畫內容的下載大小。"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "由最長的階段開始調查，[盡量縮短延遲時間](https://web.dev/articles/optimize-inp#optimize_interactions)。要減少處理時長，建議[優化主要執行緒成本](https://web.dev/articles/optimize-long-tasks) (通常為 JavaScript)。"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "時間長度"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "輸入延遲"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "未能偵測到互動"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "階段"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "簡報顯示延遲"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "處理時間長度"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "按階段劃分 INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "如要提升 LCP，請讓 LCP 圖片立即[顯示](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)在 HTML 中，[避免延遲載入](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "已套用 fetchpriority 的 high 屬性值"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "應套用 fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "未套用延遲載入"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP 圖片在最早的開始點 {PH1} 後載入。"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "未能偵測到 LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP 不是圖像，因此未偵測到 LCP 資源"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "要求內容可於初始文件中查看"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP 要求探索"}, "models/trace/insights/LCPPhases.ts | description": {"message": "每個[階段都有特定的改善策略](https://web.dev/articles/optimize-lcp#lcp-breakdown)。理想狀態下，大部分的 LCP 時間應該用於載入資源，而非延遲。"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "時間長度"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "元素輸出延遲"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "實際使用者的第 75 個百分位數"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "未能偵測到 LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "階段"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "資源載入延遲"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "資源載入時長"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "首個字節時間"}, "models/trace/insights/LCPPhases.ts | title": {"message": "按階段劃分 LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "指令碼"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "浪費的位元組數目"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill 和轉換可讓較舊版本的瀏覽器使用新版 JavaScript 的功能。不過，大部分並非新型瀏覽器的必要項目。除非你認為必須支援較舊版本的瀏覽器，否則請考慮將 JavaScript 建立流程改為不轉譯[基準](https://web.dev/articles/baseline-and-polyfills)功能。[瞭解為何大部分網站無需轉譯也能部署 ES6+ 程式碼](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "舊版 JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 和 HTTP/3 擁有許多 HTTP/1.1 沒有的優點，例如 multiplexing。[進一步瞭解如何使用新式 HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "沒有任何要求使用 HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "通訊協定"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "要求"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "新式 HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "來源"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "要求"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "來源"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "時間"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "估計節省的 LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "未使用預先連結。請檢查 crossorigin 屬性的設定是否正確。"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "如要[避免鏈結關鍵要求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)，你可以縮短鏈結長度、縮減下載資源的大小或延遲下載不必要資源，以提高頁面載入速度。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "請新增最重要來源的 [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 提示，但盡量不要超過 4 個。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "預先連結的候選來源"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "關鍵路徑延遲時間上限："}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "網絡依附組件沒有影響任何輸出工作"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "沒有其他適合預先連結的候選來源"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "沒有預先連結的來源"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[預先連結](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)提示可協助瀏覽器在頁面載入的較早階段建立連結，節省為該來源提出首項要求的時間。以下是頁面預先連結的來源。"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "預先連結的來源"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "網絡依附組件樹狀結構"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "找到超過 4 個preconnect連結。這些連結應盡量少用，並只用於最重要的來源。"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "未使用預先連結。請只為頁面可能要求的來源使用preconnect。"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "如要避免鏈結關鍵要求，你可以縮短鏈結長度、縮減下載資源的大小或延遲下載不必要資源，以提高頁面載入速度。"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "要求會封鎖頁面的初始輸出，可能會導致 LCP 延遲。[延遲或內嵌](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)可以將這些網絡要求移離關鍵路徑。"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "時間長度"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "此導覽沒有輸出封鎖要求"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "要求"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "輸出封鎖要求"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "如果「重新計算樣式」的成本仍然偏高，可以使用選取器優化功能降低成本。針對經過時間偏長及慢速路徑百分比偏高的[選取器作出優化](https://developer.chrome.com/docs/devtools/performance/selector-stats)，例如簡化選取器和減少數量，以及縮減 DOM 大小和結構，都有助降低配對成本。"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "經過時間"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "找不到任何 CSS 選取器資料。你需要在成效面板設定中，啟用 CSS 選取器統計資料。"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "嘗試配對次數"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "相符數量"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS 選取器成本"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "最花時間/心力的選取器"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "總計"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "主要執行緒的執行時間"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "第三方"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "傳輸大小"}, "models/trace/insights/ThirdParties.ts | description": {"message": "第三方程式碼可能會嚴重影響載入效能。建議[減少第三方程式碼和延遲載入](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)，優先載入網頁內容。"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "找不到第三方內容"}, "models/trace/insights/ThirdParties.ts | title": {"message": "第三方"}, "models/trace/insights/Viewport.ts | description": {"message": "如果不優化流動裝置的檢視區，輕按互動可能會[延遲最多 300 毫秒](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "流動裝置輕按延遲"}, "models/trace/insights/Viewport.ts | title": {"message": "優化流動裝置的檢視區"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "只有透過 GET 要求載入的網頁才符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "只有狀態碼為 2XX 的網頁才可快取。"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome 偵測到嘗試在快取中執行 JavaScript。"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "要求 AppBanner 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "標記已停用向前/返回快取。如要在此裝置上啟用該功能，請前往 chrome://flags/#back-forward-cache。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "指令列已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "由於記憶體不足，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "委派目標不支援向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "已為預先轉譯器停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "網頁有包含已註冊監聽器的 BroadcastChannel 例項，因此系統無法快取網頁。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "包含 cache-control:no-store 標題的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "有人刻意清除快取。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "為了讓系統可快取其他網頁，此網頁已從快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "包含外掛程式的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "未定義"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "使用 FileChooser API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "使用檔案系統存取 API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "使用媒體裝置調度工具的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "使用者離開網頁時，媒體播放器正在播放內容。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "使用 MediaSession API 並設定播放狀態的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "使用 MediaSession API 並設定動作處理常式的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "由於螢幕閱讀器的關係，系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "使用 SecurityHandler 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "使用序號 API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "使用 WebBluetooth API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "使用 WebUSB API 的網頁不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "由於使用 Cache-Control: no-store 的頁面已停用 Cookie，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "使用專屬 Worker 或 Worklet 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "文件未在使用者離開前完成載入。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "使用者離開網頁時，系統會顯示應用程式橫額。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "使用者離開網頁時，系統會顯示 Chrome 密碼管理工具。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "使用者離開網頁時，DOM distillation 正在處理中。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "使用者離開網頁時，系統會顯示 DOM Distiller Viewer。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "由於擴充程式使用訊息 API，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "在儲存至往向前/返回快取前，可持續連線的擴充程式應中斷連線。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "可持續連線的擴充程式嘗試在向前/返回快取中傳送訊息給頁框。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "由於擴充程式的關係，系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "使用者離開網頁時，系統會在該頁面顯示強制回應對話方塊，例如重新提交表格或 HTTP 密碼對話框。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "使用者離開網頁時，系統會顯示離線頁面。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "使用者離開網頁時，系統會顯示 Out-Of-Memory Intervention。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "使用者離開網頁時，系統會要求權限。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "使用者離開網頁時，系統會顯示彈出式視窗封鎖器。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "使用者離開網頁時，系統會顯示安全瀏覽詳細資料。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "安全瀏覽功能將此網頁視為濫用，並已封鎖彈出式視窗。"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Service Worker 已在網頁儲存於向前/返回快取時啟用。"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "由於發生文件錯誤，因此向前/返回快取已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "使用 FencedFrames 的頁面無法儲存在向前/返回快取之中。"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "為了讓系統可快取其他網頁，此網頁已從快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "授予媒體串流播放權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "有特定嵌入內容類型 (例如 PDF) 的頁面目前不支援向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "使用 IdleManager 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "包含開放式 IndexedDB 連線的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "由於發生 IndexedDB 事件，因此向前/返回快取已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "使用了不符合資格的 API。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "由擴充程式插入 JavaScript 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "由擴充程式插入 StyleSheet 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "內部錯誤。"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "由於部分 JavaScript 網絡要求收到含有「Cache-Control: no-store」標題的資源，因此向前/返回快取已停用。"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "由於有 Keepalive 要求，因此系統已停用向前/返回快取功能。"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "使用鍵盤鎖定的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "網頁在使用者離開前尚未完成載入。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "主要資源包含 cache-control:no-cache 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "主要資源包含 cache-control:no-store 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "瀏覽操作在網頁從向前/返回快取中還原前取消。"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "由於有效的網路連線收到太多資料，因此網頁已從快取中移除。Chrome 會限制網頁可在快取時接收的資料量。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "包含 in-flight fetch() 或 XHR 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "有效的網絡要求涉及重新導向，因此網頁已從往返快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "由於網絡連線開啟時間過長，因此網頁已從快取中移除。Chrome 會限制網頁可在快取時接收資料的時間長度。"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "沒有有效回應標題的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "導覽已在主頁框以外的頁框中執行。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "網頁包含已建立索引的資料庫所執行的進行中交易，目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "包含 in-flight 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "正在傳送擷取網絡要求的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "包含 in-flight 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "正在傳送 XHR 網絡要求的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "使用 PaymentManager 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "使用「畫中畫」的網頁目前不符合資格提供向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "顯示列印使用者介面的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "網頁已透過「window.open()」開啟且其他分頁含有該網頁的參照內容，或網頁已開啟視窗。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "向前/返回快取中網頁的轉譯器處理程序已當機。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "向前/返回快取中網頁的轉譯器處理程序已中斷。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "要求音訊擷取權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "已要求感應器權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "要求背景同步處理或擷取權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "要求 MIDI 權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "要求通知權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "要求儲存空間存取權的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "要求影片擷取權限的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "只能快取網址配置為 HTTP/HTTPS 的網頁。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "在網頁處於向前/返回快取時，由 Service Worker 聲明擁有權。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Service Worker 已嘗試向於向前/返回快取中的網頁傳送 MessageEvent。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Service Worker 已在網頁儲存於向前/返回快取時取消註冊。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "由於系統已啟用 Service Worker，因此網頁已從向前/返回快取中移除。"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome 已重新啟動，並清除向前/返回快取項目。"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "使用 SharedWorker 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "使用 SpeechRecognizer 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "使用 SpeechSynthesis 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "網頁上 iframe 啟動的導覽並未完成。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "子資源包含 cache-control:no-cache 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "子資源包含 cache-control:no-store 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "網頁已超出向前/返回快取中的時間上限，因此已過期。"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "將網頁儲存至向前/返回快取時已逾時 (可能是由於網頁隱藏事件處理常式的執行時間太長所致)。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "網頁的主頁框中有卸載處理常式。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "網頁在子頁框中含有卸載處理常式。"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "瀏覽器已變更用戶代理程式覆寫標題。"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "授權錄製影片或音效的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "使用 WebDatabase 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "使用 WebHID 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "使用 WebLocks 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "使用 WebNfc 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "使用 WebOTPService 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "使用 WebRTC 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "由於已使用 WebRTC，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "使用 WebShare 的網頁目前不符合向前/返回快取的資格。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "使用 WebSocket 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "由於已使用 WebSocket，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "使用 WebTransport 的網頁無法儲存至向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "由於已使用 WebTransport，因此系統已停用向前/返回快取。"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "使用 WebXR 的網頁目前不符合向前/返回快取的資格。"}}