{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Autorizarea nu va fi acoperită de simbolul pentru metacaracter (*) în cadrul gestionării Access-Control-Allow-Headers prin CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Pentru a dezactiva integrarea Cast prestabilită, trebuie folosit atributul disableRemotePlayback în locul selectorului -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Valoarea slider-vertical a aspectului CSS nu este standardizată și se va elimina."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Solicitările de resurse ale căror adrese URL conțineau atât caractere pentru spații \\(n|r|t), cât și semnul mai mic decât (<) sunt blocate. Pentru a înc<PERSON>rca resursele, elimină caracterele de rând nou și codifică semnul mai mic decât în locuri cum ar fi valorile atributelor elementelor."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() s-a învechit. Folosește API-ul standardizat Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() s-a învechit. Folosește API-ul standardizat Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() s-a învechit. Folosește API-ul standardizat nextHopProtocol din Navigation Timing 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Cookie-urile care conțin un caracter \\(0|r|n) vor fi respinse în loc de a fi trunchiate."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Relaxarea politicii pentru aceeași origine prin setarea document.domain este învechită și va fi dezactivată în mod prestabilit. Acest avertisment privind renunțarea la dezvoltare este asociat unei accesări cu origini diferite, care a fost activată din setarea document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Declanșarea window.alert din cadre iframe cu origini diferite s-a învechit și va fi eliminată pe viitor."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Declanșarea window.confirm din cadre iframe cu origini diferite s-a învechit și va fi eliminată pe viitor."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Compatibilitate cu datele: adresele URL din SVGUseElement sunt învechite și vor fi eliminate pe viitor."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() și watchPosition() nu mai funcționează cu origini nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() și watchPosition() sunt învechite, având origini nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() nu mai funcționează cu origini nesigure. Pentru a folosi această funcție, îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "S-a găsit o etichetă <h1> într-un element <article>, <aside>, <nav> sau <section> care nu are o dimensiune a fontului specificată. În viitorul apropiat, dimensiunea textului acestui titlu se va modifica în acest browser. Accesează https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 pentru mai multe informații."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate s-a învechit. Folosește RTCPeerConnectionIceErrorEvent.address sau RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Acest format pentru solicitarea navigator.credentials.get() pentru datele de conectare digitale este învechit. Actualizează apelul pentru a folosi noul format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Originea comerciantului și datele arbitrare din evenimentul service worker canmakepayment sunt învechite și vor fi eliminate: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Site-ul a solicitat o subresursă de la o rețea pe care a putut să o acceseze numai datorită poziției privilegiate în rețea a utilizatorilor săi. Aceste solicitări expun pe internet dispozitive și servere care nu sunt publice, crescând riscul de atacuri prin falsificarea solicitărilor de pe alt site (CSRF) și de scurgeri de informații. Pentru a reduce aceste riscuri, Chrome marchează drept învechite solicitările de la subresurse care nu sunt publice atunci când sunt inițiate din contexte nesigure și va începe să le blocheze."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Câmpul dailyUpdateUrl din InterestGroups transmis către joinAdInterestGroup() a fost redenumit updateUrl, astfel încât să reflecte mai exact comportamentul asociat."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator s-a învechit. Folosește Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Fișierul CSS nu se poate încărca din adrese URL file: dacă acestea nu se termină cu extensia de fișier .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Folosirea SourceBuffer.abort() pentru a abandona eliminarea intervalului asincron pentru remove() este învechită din cauza modificării specificațiilor. Pe viitor, aceasta nu va mai fi acceptată. Ar trebui să asculți evenimentul updateend. abort() are numai rolul de a abandona atașări media asincrone sau de a reseta starea parserului."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Setarea MediaSource.duration sub cel mai mare marcaj temporal de prezentare al oricăror cadre codificate din memoria temporară este învechită din cauza modificării specificațiilor. Eliminarea implicită a elementelor media trunchiate din memoria temporară va fi eliminată pe viitor. În locul acesteia, trebuie să rulezi remove(newDuration, oldDuration) pentru toate obiectele sourceBuffers, unde newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI va solicita o permisiune de folosit chiar dacă sysex nu este specificat în MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "API-ul Notification nu mai poate fi folosit din origini nesigure. Îți recomandăm să treci aplicația la o origine sigură, cum ar fi HTTPS. Consultă https://goo.gle/chrome-insecure-origins pentru detalii suplimentare."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Este posibil ca permisiunea pentru API-ul Notification să nu mai fie solicitată dintr-un iframe cu origini diferite. Îți recomandăm să soliciți permisiunea dintr-un cadru de nivel superior sau să deschizi o fereastră nouă."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opțiunea imageOrientation: 'none' din createImageBitmap este învechită. Folosește createImageBitmap cu opțiunea {imageOrientation: 'from-image'}."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Partenerul tău negociază o versiune (D)TLS învechită. Consultă-te cu partenerul pentru a remedia problema."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Dacă specifici overflow: visible pe etichetele imagine, video și pânză, este posibil să se producă conținut vizual în afara limitelor elementului. Accesează https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments s-a învechit. Folosește instalarea la timp pentru handlerele pentru plăți."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Apelul PaymentRequest a omis directiva Content-Security-Policy (CSP) connect-src. Această opțiune de ocolire este învechită. Adaugă identificatorul metodei de plată din API-ul PaymentRequest (în câmpul supportedMethods) la directiva CSP connect-src."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent s-a învechit. Folosește navigator.storage standardizat."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src> cu un element părinte <picture> nu este valid, deci va fi ignorat. Folosește <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Metoda webkitCancelAnimationFrame este specifică furnizorului. Folosește metoda standard cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame este specifică furnizorului. Folosește metoda standard requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen este învechit. Folosește Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() este învechit. Folosește Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() este învechit. Folosește Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() este învechit. Folosește Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() este învechit. Folosește Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen este învechit. Folosește Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Urmează să retragem API-ul chrome.privacy.websites.privacySandboxEnabled, dar acesta va rămâne activ pentru retrocompatibilitate până la lansarea M113. Folosește chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled și chrome.privacy.websites.adMeasurementEnabled. Consultă https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Restricția DtlsSrtpKeyAgreement a fost eliminată. Ai specificat o valoare false pentru această restricție, care este interpretată ca o încercare de a folosi metoda SDES key negotiation eliminată. Această funcție a fost eliminată. Folosește un serviciu care acceptă DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Restricția DtlsSrtpKeyAgreement a fost eliminată. Ai specificat o valoare true pentru această restricție, care nu a avut niciun efect, dar poți elimina restricția pentru un aspect ordonat."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Metoda callback-based getStats() este învechită și va fi eliminată. Folosește spec-compliant getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() este învechit. Folosește Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Solicitările de subresurse ale căror adrese URL conțin date de conectare încorporate (de ex., **********************/) sunt blocate."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Opțiunea rtcpMuxPolicy este învechită și va fi eliminată."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer va necesita blocarea accesului de la mai multe surse. Consultă https://developer.chrome.com/blog/enabling-shared-array-buffer/ pentru mai multe detalii."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Apelarea speechSynthesis.speak() fără activarea utilizatorului este învechită și va fi eliminată."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Funcțiile de procesare a evenimentelor unload sunt învechite și vor fi eliminate."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Extensiile trebuie să aibă blocarea accesului de la mai multe surse activată pentru a folosi în continuare SharedArrayBuffer. Consultă https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Atributul GPUAdapter isFallbackAdapter este învechit. Folosește atributul GPUAdapterInfo isFallbackAdapter."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Fișierul JSON de răspuns din XMLHttpRequest nu acceptă UTF-16"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Obiectul sincron XMLHttpRequest din firul principal este învechit din cauza efectelor sale negative asupra experienței utilizatorului final. Pentru mai mult ajutor, consultă https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animație"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Schimbările de aspect apar atunci când elementele se mișcă fără nicio interacțiune a utilizatorului. [Investighează cauzele schimbărilor de aspect](https://web.dev/articles/optimize-cls), cum ar fi adăugarea, eliminarea elementelor sau schimbarea fonturilor pe măsură ce se încarcă pagina."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Solicitare de font"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "S-a injectat un iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Grup de schimbări ale aspectului @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Nu s-au detectat factori care au cauzat schimbarea aspectului"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "<PERSON><PERSON><PERSON><PERSON> schimbări ale aspectului"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Principalii factori care au cauzat schimbarea aspectului"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Principalii factori care au cauzat schimbarea aspectului"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Cel mai slab grup"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Cel mai slab grup de schimbări ale aspectului"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL cache"}, "models/trace/insights/Cache.ts | description": {"message": "O durată lungă a memoriei cache poate grăbi accesările repetate ale paginii. [Află mai multe](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Nu există solicitări cu politici privind memoria cache ineficientă"}, "models/trace/insights/Cache.ts | others": {"message": "Încă {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Solicitare"}, "models/trace/insights/Cache.ts | title": {"message": "Folosește perioade eficiente ale memoriei cache"}, "models/trace/insights/DOMSize.ts | description": {"message": "Un DOM mare poate crește durata calculelor de stil și a rearanjărilor aspectului, afectând receptivitatea paginii. Un DOM mare va crește și folosirea memoriei. [Află cum să eviți o dimensiune DOM excesivă](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Majoritatea copiilor"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "Adâncimea DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Statistică"}, "models/trace/insights/DOMSize.ts | title": {"message": "Optimizează dimensiunea DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Numărul total de elemente"}, "models/trace/insights/DOMSize.ts | value": {"message": "Valoare"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Prima solicitare din rețea este cea mai importantă.  Redu latența acesteia evitând redirecționările, asigurând un răspuns rapid al serverului și activând comprimarea textului."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "A avut redirecționări ({PH1} redirecționări, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Serverul a răspuns lent (observat {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Nu s-a aplicat nicio compresie"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Evită redirecționările"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Serverul răspunde rapid (observat {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Se aplică comprimarea textului"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Redirecț<PERSON><PERSON><PERSON>"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Durata de răspuns de la server"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latența solicitării documentului"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Descărcare fără compresie"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Sursă"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Elimină modulele JavaScript dublate și mari din grupuri pentru a reduce consumul inutil de byți prin activitatea din rețea."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "JavaScript dublură"}, "models/trace/insights/FontDisplay.ts | description": {"message": "<PERSON><PERSON>i recomandăm să setezi [font-display](https://developer.chrome.com/blog/font-display) la swap sau optional pentru a te asigura că textul este vizibil în mod constant. swap poate fi optimizat în continuare pentru a reduce schimbările de aspect prin [modificări ale valorii fontului](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Font"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Afișarea fontului"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON> pier<PERSON>"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonim)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Multe API-uri, care citesc de obicei geometria aspectului, forțează motorul de redare să întrerupă executarea scriptului pentru a calcula stilul și aspectul. Află mai multe despre [rearanjarea forțată](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) și despre măsurile sale de atenuare."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>i"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> forțată"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Apelul funcției de top"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Durata totală de rearanjare"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[neatribuit]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Reducerea duratei de descărcare a imaginilor poate îmbunătăți durata de încărcare percepută a paginii și LCP. [Află mai multe despre optimizarea dimensiunii imaginii](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (est. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Nu există imagini care pot fi optimizate"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Optimizează dimensiunea fișierului"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Încă {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Îmbunătățește livrarea imaginilor"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Dacă mărești factorul de comprimare a imaginii, s-ar putea îmbunătăți dimensiunea de descărcare a imaginii."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Folosirea unui format de imagine modern (WebP, AVIF) sau creșterea comprimării imaginii ar putea îmbunătăți dimensiunea descărcării acestei imagini."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Fișierul imagine este mai mare decât trebuie ({PH1}) pentru dimensiunile afișate ({PH2}). Folosește imagini adaptabile pentru a reduce dimensiunea de descărcare a imaginii."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Folosirea formatelor video în locul GIF-urilor poate îmbunătăți dimensiunea descărcării conținutului animat."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Începe investigația cu cea mai lungă etapă. [Poți să minimizezi întârz<PERSON>le](https://web.dev/articles/optimize-inp#optimize_interactions). Pentru a reduce durata procesării, [optimizează costurile asociate cu firul principal](https://web.dev/articles/optimize-long-tasks), de obicei JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Întârziere la introducere"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Nu s-au detectat interacțiuni"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Fază"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Întârzier<PERSON> prezentării"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "<PERSON>rata procesării"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP în funcție de fază"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Optimizează LCP făcând imediat imaginea LCP [vizibilă](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) din HTML și [evitând încărcarea asincronă](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "Valoarea fetchpriority=high a fost aplicată"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Valoarea fetchpriority=high trebuie aplicată"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "încărcarea asincronă nu este aplicată"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Imaginea LCP s-a încărcat la {PH1} după primul punct de pornire."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Nu s-a detectat nicio valoare LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Nu s-au detectat resurse LCP deoarece LCP nu este o imagine"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Solicitarea poate fi găsită în documentul inițial"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Descoperirea solicitării LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Fiecare [etapă are strategii de îmbunătățire specifice](https://web.dev/articles/optimize-lcp#lcp-breakdown). În mod ideal, majoritatea timpului LCP ar trebui să fie petrecut cu încărcarea resurselor, nu cu înt<PERSON>."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Întârzierea la redarea elementului"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Câmpul p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Nu s-a detectat nicio valoare LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Fază"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Întârzierea la încărcarea resursei"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Durata de încărcare a resursei"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP în funcție de fază"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Codurile polyfill și transformările fac posibilă folosirea noilor funcții JavaScript în browserele vechi. Însă pentru browserele moderne, majoritatea nu sunt necesare. Poți să modifici procesul versiunii JavaScript astfel încât să nu transpileze funcțiile [Baseline](https://web.dev/articles/baseline-and-polyfills), cu excepția cazului în care știi că trebuie să accepti browserele mai vechi. [Află de ce majoritatea site-urilor pot implementa cod ES6+ fără transpilare](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "JavaScript vechi"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 și HTTP/3 oferă multe beneficii față de HTTP/1.1, cum ar fi multiplexingul. [Află mai multe despre folosirea protocolului HTTP modern](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "<PERSON><PERSON> solicitare nu a folosit HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Solicitare"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "HTTP modern"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origine"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Solicitare"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Sursă"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON>a"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Economii LCP est."}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Preconectare nefolosită. Verifică dacă folosești corect atributul crossorigin."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Evită să legi solicitări critice](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) reducând lungimea lanțurilor, reducând dimensiunea de descărcare a resurselor sau amânând descărcarea de resurse inutile pentru a îmbunătăți încărcarea paginilor."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Adaugă indicii de [preconectare](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) pentru cele mai importante origini, dar încearcă să nu folosești mai mult de patru."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Candidați pentru conectare prealabilă"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Latența maximă a căii critice:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Nicio activitate de redare nu a fost afectată de dependențele de rețea"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Nu există alte origini care să fie candidate bune pentru conectarea prealabilă"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "nu s-au conectat în prealabil origini"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Indiciile de [preconectare](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) ajută browserul să stabilească o conexiune mai devreme în timpul încărcării paginii, economisind timp la prima solicitare pentru acea origine. Următoarele sunt originile la care s-a conectat pagina în prealabil."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Origini conectate în prealabil"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Arborele de dependențe al rețelei"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "S-au găsit mai mult de patru conexiuni preconnect. Acestea trebuie folosite cu măsură și numai pentru cele mai importante origini."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Preconectare nefolosită. Folosește preconnect doar pentru originile pe care pagina le va solicita probabil."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Evită să legi solicitări critice reducând lungimea lanțurilor, reducând dimensiunea de descărcare a resurselor sau amânând descărcarea de resurse inutile pentru a îmbunătăți încărcarea paginilor."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Solicitările blochează redarea inițială a paginii, ceea ce poate întârzia LCP. [Amânarea sau încorporarea](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) poate să mute aceste solicitări de rețea în afara căii critice."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Nu există solicitări de blocare a redării pentru această navigare"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Solicitare"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Solicitări de blocare a redării"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "În cazul în care costurile pentru recalcularea stilului rămân ridicate, optimizarea selectorilor le poate scădea. [Optimizează selectorii](https://developer.chrome.com/docs/devtools/performance/selector-stats) cu un interval mare de timp scurs și un procentaj mare al căii lente. Selectorii mai simpli, numărul mai mic de selectori, un DOM mai mic și un DOM mai simplu vor scădea costurile de potrivire."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "<PERSON><PERSON> scurs"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Nu s-au găsit date despre selectorul CSS. Statisticile selectorului CSS trebuie activate în setările panoului de performanță."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Încercări de potrivire"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>rivir<PERSON>"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Costurile selectorului CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Selectori de top"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Total"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "<PERSON><PERSON> pentru firul principal"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Terță parte"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Dimensiunea transferului"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Codul terță parte poate influența semnificativ performanța de încărcare. [Redu și amână încărcarea codului terță parte](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) pentru a acorda prioritate conținutului din pagină."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Nu s-au găsit terțe părți"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Terț<PERSON> p<PERSON>"}, "models/trace/insights/Viewport.ts | description": {"message": "Interacțiunile prin atingere pot fi [întârziate cu până la 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) dacă aria vizibilă nu este optimizată pentru mobil."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Întârziere la atingerea pe mobil"}, "models/trace/insights/Viewport.ts | title": {"message": "Optimizează aria vizibilă pentru mobil"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Doar paginile încărcate prin intermediul unei solicitări GET sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Doar paginile cu codul de stare 2XX pot fi stocate în memoria cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome a detectat o încercare de executare JavaScript în cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "Paginile care au solicitat un AppBanner nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Memoria cache înainte/înapoi este dezactivată de funcțiile experimentale. Accesează chrome://flags/#back-forward-cache ca să o activezi local pe acest dispozitiv."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Memoria cache înainte/înapoi este dezactivată de linia de comandă."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza memoriei insuficiente."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Memoria cache înainte/înapoi nu este acceptată de delegat."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Memoria cache înainte/înapoi este dezactivată de programul de redare în avans."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Pagina nu poate fi stocată în memoria cache deoarece are o instanță BroadcastChannel cu ascultători înregistrați."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Paginile cu antetul cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Memoria cache a fost golită intenționat."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Pagina a fost eliminată din memoria cache pentru a permite memorarea altei pagini."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Paginile care conțin pluginuri nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Nedefinit"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Paginile care folosesc API-ul FileChooser nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Paginile care folosesc API-ul de acces la sistemul de fișiere nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Paginile care folosesc dispecerul pentru dispozitive media nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Un player media funcționa la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Paginile care folosesc API-ul MediaSession și setează o stare de redare nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Paginile care folosesc API-ul MediaSession și setează handlere de acțiune nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Memoria cache înainte-înapoi este dezactivată din cauza cititorului de ecran."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Paginile care folosesc SecurityHandler nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Paginile care folosesc API-ul Serial nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Paginile care folosesc API-ul WebAuthentication nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Paginile care folosesc API-ul WebBluetooth nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Paginile care folosesc API-ul WebUSB nu sunt eligibile pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Memoria cache înainte-înapoi este dezactivată deoarece cookie-urile sunt dezactivate pentru o pagină care folosește Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Paginile care folosesc un lucrător sau un worklet special nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Documentul nu s-a încărcat complet înainte de a ieși."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Bannerul de aplicație era prezent la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Managerul de parole Chrome era prezent la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Distilarea DOM era în curs în momentul ieșirii."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Vizualizatorul de distilare DOM era prezent la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza extensiilor care folosesc API de mesagerie."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Extensiile cu conexiune pe termen lung ar trebui să închidă conexiunea înainte să intre în memoria cache înainte/înapoi"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Extensiile cu conexiune pe termen lung au încercat să trimită mesaje către cadre din memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza extensiilor."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Un dialog modal, precum retrimiterea formularelor sau caseta de dialog pentru parola http, s-a afișat pentru pagină la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Pagina offline s-a afișat la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Bara de intervenție Memorie insuficientă era prezentă la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Au apărut solicitări de permisiune la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Funcția de blocare a ferestrelor pop-up era prezentă la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Detaliile Navigării sigure s-au afișat la ieșire."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Navigarea sigură a considerat această pagină abuzivă și a blocat elementul pop-up."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Un service worker s-a activat în timp ce pagina era în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Memoria cache înainte/înapoi este dezactivată din cauza unei erori de document."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Paginile care folosesc FencedFrames nu pot fi stocate în bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Pagina a fost eliminată din memoria cache pentru a permite memorarea altei pagini."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Paginile care au acordat acces pentru fluxul media nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Paginile care au anumite tipuri de conținut încorporat (de ex., PDF-uri) nu sunt eligibile momentan pentru memoria cache înainte-înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Paginile care folosesc IdleManager nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Paginile care au o conexiune IndexedDB deschisă nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Memoria cache înainte / înapoi este dezactivată din cauza unui eveniment IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "S-au folosit API-uri ineligibile."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Paginile în care se injectează JavaScript prin extensii nu sunt eligibile momentan pentru memoria cache înainte-înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Paginile în care se injectează StyleSheet prin extensii nu sunt eligibile momentan pentru memoria cache înainte-înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "<PERSON><PERSON><PERSON> internă."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Memoria cache înainte-înapoi este dezactivată deoarece unele solicitări din rețeaua JavaScript au primit o resursă cu antetul Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Memoria cache înainte-înapoi este dezactivată din cauza unei solicitări keep-alive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Paginile care folosesc blocarea tastaturii nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Pagina nu s-a încărcat complet înainte de a ieși."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Paginile a căror resursă principală are cache-control:no-cache nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Paginile a căror resursă principală are cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Navigarea a fost anulată înainte ca pagina să poată fi restabilită din memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Pagina a fost eliminată din cache pentru că o conexiune la rețea activă primea prea multe date. Chrome limitează volumul de date pe care îl poate primi o pagină în cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Paginile care au fetch() sau XHR în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Pagina a fost eliminată din memoria cache înainte/înapoi deoarece o solicitare pentru o rețea activă implica o redirecționare."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Pagina a fost eliminată din cache pentru că o conexiune la rețea a fost deschisă prea mult. Chrome limitează durata în care o pagină poate primi date în cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Paginile care nu au un antet de răspuns valid nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Navigarea a avut loc într-un cadru diferit de principalul."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Paginile cu tranzacții DB indexate în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Paginile cu o solicitare de rețea în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Paginile cu o solicitare de rețea de preluare în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Paginile cu o solicitare de rețea în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Paginile cu o solicitare de rețea XHR în curs nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Paginile care folosesc PaymentManager nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Paginile care folosesc modul picture-in-picture nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Paginile care afișează interfața de printare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Pagina s-a deschis folosind „window.open()” și altă filă are o referință la aceasta sau pagina a deschis o fereastră."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Procesul dispozitivului de redare pentru pagina din memoria cache înainte/înapoi s-a blocat."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Procesul dispozitivului de redare pentru pagina din memoria cache înainte/înapoi a fost oprit."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Paginile care au solicitat permisiuni de înregistrare audio nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Paginile care au permisiunile solicitate pentru senzori nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Paginile care au solicitat sincronizarea în fundal sau permisiuni de preluare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Paginile care au solicitat permisiuni MIDI nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Paginile care au solicitat permisiuni de notificare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Paginile care au solicitat acces la spațiul de stocare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Paginile care au solicitat permisiuni de înregistrare video nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Doar paginile a căror schemă URL este HTTP / HTTPS pot fi stocate în memoria cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Pagina a fost revendicată de un service worker în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Un service worker a încercat să trimită paginii din memoria cache înainte/înapoi o proprietate MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "S-a anulat înregistrarea ServiceWorker cât timp o pagină era în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Pagina a fost eliminată din memoria cache înainte/înapoi din cauza activării unui service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome a repornit și a șters intrările din memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Paginile care folosesc SharedWorker nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Paginile care folosesc SpeechRecognizer nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Paginile care folosesc SpeechSynthesis nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Un iframe de pe pagină a început o navigare care nu s-a terminat."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Paginile a căror subresursă are cache-control:no-cache nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Paginile a căror subresursă are cache-control:no-store nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Pagina a depășit timpul maxim în memoria cache înainte/înapoi și a expirat."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "A expirat intrarea paginii în memoria cache înainte/înapoi (probabil din cauza handlerelor pagehide pe termen lung)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Pagina are un handler de descărcare în cadrul principal."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Pagina are un handler de descărcare într-un subcadru."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Browserul a schimbat antetul de modificare user agent."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Paginile care au acordat acces pentru a înregistra conținut video sau audio nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Paginile care folosesc WebDatabase nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Paginile care folosesc WebHID nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Paginile care folosesc WebLocks nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Paginile care folosesc WebNfc nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Paginile care folosesc WebOTPService nu sunt eligibile momentan pentru bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Paginile cu WebRTC nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Memoria cache înainte-înapoi este dezactivată deoarece s-a folosit WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Paginile care folosesc WebShare nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Paginile cu WebSocket nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Memoria cache înainte-înapoi este dezactivată deoarece s-a folosit WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Paginile cu WebTransport nu pot intra în memoria cache înainte/înapoi."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Memoria cache înainte-înapoi este dezactivată deoarece s-a folosit WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Paginile care folosesc WebXR nu sunt eligibile momentan pentru memoria cache înainte/înapoi."}}