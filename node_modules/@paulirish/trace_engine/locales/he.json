{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "ההרשאה לא תיכלל עם השימוש בסמל של התו הכללי לחיפוש (*) בהגדרות לטיפול ב-Access-Control-Allow-Headers של CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "כדי להשבית את השילוב של הפעלת Cast שמוגדר כברירת מחדל, צריך להשתמש במאפיין disableRemotePlayback במקום בבורר -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "הערך של מראה ה-CSS‏ slider-vertical אינו סטנדרטי והוא יוסר."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "בקשות למשאבים שכתובות ה-URL שלהן מכילות גם תווי \\(n|r|t) של רווח לבן מחוק וגם תווי 'פחות מ-' (<) חסומות. כדי לטעון את המשאבים האלה, יש להסיר תווי 'פחות מ-' מקודדים ושורות חדשות ממקומות כמו ערכי מאפיינים של רכיבים."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "האפשרות chrome.loadTimes() הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: תזמון ניווט 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "האפשרות chrome.loadTimes() הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: תזמון המרת תמונה וקטורית למפת סיביות."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "האפשרות chrome.loadTimes() הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: nextHopProtocol בתזמון ניווט 2."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "קובצי cookie המכילים תו \\(0|r|n) יידחו במקום להיחתך."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "האפשרות למתן את מדיניות המקור הזהה על ידי הגדרת document.domain הוצאה משימוש, ותושבת כברירת מחדל. האזהרה הזו על הוצאה משימוש מתייחסת לגישה ממקורות שונים שהופעלה על ידי הגדרת document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "האפשרות להפעיל את הפונקציה window.alert דרך רכיבי iframe ממקורות שונים הוצאה משימוש ותוסר בעתיד."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "האפשרות להפעיל את הפונקציה window.confirm דרך רכיבי iframe ממקורות שונים הוצאה משימוש ותוסר בעתיד."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "התמיכה בהקצאת כתובות URL מסוג data:‎ ב-SVGUseElement הוצאה משימוש ותוסר בעתיד."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "האפשרויות getCurrentPosition() ו-watchPosition() לא פועלות יותר במקורות לא מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח, כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "האפשרויות getCurrentPosition() ו-watchPosition() הוצאו משימוש במקורות שאינם מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח, כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "האפשרות getUserMedia() לא פועלת יותר במקורות לא מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח, כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "נמצא תג <h1> בתוך <article>,‏ <aside>,‏ <nav> או <section>, שאין לו גודל גופן מוגדר. הגודל של הטקסט בכותרת הזו ישתנה בדפדפן הזה בעתיד הקרוב. מידע נוסף זמין בכתובת https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "האפשרות RTCPeerConnectionIceErrorEvent.hostCandidate הוצאה משימוש. במקומה יש להשתמש באפשרות RTCPeerConnectionIceErrorEvent.address או באפשרות RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "הפורמט הזה של הבקשה navigator.credentials.get()‎ לפרטי כניסה דיגיטליים יצא משימוש. צריך לעדכן את הקריאה כדי להשתמש בפורמט החדש."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "המקור של המוכר ונתונים שרירותיים מאירוע של קובץ השירות (service worker)‏ canmakepayment הוצאו משימוש ויוסרו: topOrigin‏, paymentRequestOrigin‏, methodData‏, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "האתר שלח בקשה למשאב משנה מרשת שיש לו גישה אליה רק בגלל שלמשתמשים שלו יש הרשאות למיקום הרשת. הבקשות האלה חושפות שרתים ומכשירים שלא גלויים לכולם לאינטרנט, ומגבירות את הסיכון למתקפת זיוף של בקשה בין אתרים (CSRF) או לדליפת מידע. כדי להפחית את הסיכונים האלה, מערכת Chrome מוציאה משימוש בקשות למשאבי משנה שאינם גלויים לכולם כשהן מגיעות מהקשרים לא מאובטחים, ובקרוב המערכת תתחיל לחסום אותן."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "שם השדה dailyUpdateUrl של InterestGroups שהועבר אל joinAdInterestGroup() שונה לשם updateUrl כדי לשקף את ההתנהגות בצורה מדויקת יותר."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "השיטה Intl.v8BreakIterator הוצאה משימוש. צריך להשתמש בשיטה Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "לא ניתן לטעון CSS מכתובות URL של file: אלא אם הן מסתיימות בסיומת הקובץ .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "האפשרות לבטל את הסרת הטווח האסינכרוני של remove() באמצעות SourceBuffer.abort() הוצאה משימוש עקב שינוי במפרט. התמיכה באפשרות הזו תוסר בעתיד. במקום זאת, כדאי להאזין לאירוע של updateend. ניתן להשתמש ב-abort() רק כדי לבטל צירוף מדיה אסינכרוני או לאפס מצב מנתח."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "האפשרות להגדיר את MediaSource.duration מתחת לערך הגבוה ביותר של חותמת הזמן להצגה של כל המסגרות המקודדות השמורות במאגר נתונים זמני הוצאה משימוש עקב שינוי במפרט. בעתיד תוסר התמיכה בהסרה מרומזת של מדיה חתוכה ששמורה במאגר נתונים זמני. במקו<PERSON> זאת, יש להשתמש באפשרות remove(newDuration, oldDuration) מפורשת בכל sourceBuffers, ולהגדיר newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "תתקבל מ-Web MIDI בקשת הרשאה לשימוש גם אם לא צוין sysex ב-MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "אי אפשר יותר להשתמש ב-Notification API ממקורות לא מאובטחים. כדאי להעביר את האפליקציה למקור מאובטח, כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "לא ניתן יותר לבקש הרשאה ל-Notification API מ-iframe ממקורות שונים. במקום זאת, כדאי לבקש הרשאה ממסגרת ברמה עליונה או לפתוח חלון חדש."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "האפשרות imageOrientation: 'none' ב-createImageBitmap הוצאה משימוש. ‫במקומה צריך להשתמש ב-createImageBitmap באפשרות '{imageOrientation: 'from-image'}'‎‎"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "השותף שלך מנהל משא ומתן על גרסת ‎(D)TLS מיושנת. עליך לפנות לשותף שלך כדי לתקן זאת."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "ציון של 'overflow: visible' בתגי img,‏ video ו-canvas עלול לגרום להופעת תוכן חזותי מחוץ לגבולות הרכיבים. פרטים נוספים זמינים בכתובת https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "הממשק paymentManager.instruments הוצא משימוש. במ<PERSON><PERSON><PERSON> זאת, אפשר להשתמש בהתקנה בזמן ריצה עבור רכיבי handler של תשלומים."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "הקריאה PaymentRequest עקפה את ההוראה connect-src של Content-Security-Policy ‏(CSP). המעקף הזה הוצא משימוש. יש להוסיף את מזהה אמצעי התשלום מ-API‎ PaymentRequest (בשדה supportedMethods) להנחיה connect-src של CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "האפשרות StorageType.persistent הוצאה משימוש. במקומה יש להשתמש באפשרות navigator.storage רגילה."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "רכיב <source src> עם הורה מסוג <picture> נחש<PERSON> ללא תקין, והמערכת מתעלמת ממנו. במקומו יש להשתמש במאפיין <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "הפונקציה webkitCancelAnimationFrame היא ספציפית לספק. במקומה צריך להשתמש בפונקציה הסטנדרטית cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "הפוקנציה webkitRequestAnimationFrame היא ספציפית לספק. במקומה צריך להשתמש בפונקציה הסטנדרטית requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitDisplayingFullscreen הוצא משימוש. במקומו צריך להשתמש ב-Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "ה-API‏ HTMLVideoElement.webkitEnterFullScreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Element.requestFullscreen()‎."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitEnterFullscreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Element.requestFullscreen()‎."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "ה-API‏ HTMLVideoElement.webkitExitFullScreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Document.exitFullscreen()‎."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitExitFullscreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Document.exitFullscreen()‎."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitSupportsFullscreen הוצא משימוש. במקומו צריך להשתמש ב-Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "אנחנו מוציאים משימוש את ה-API‏ chrome.privacy.websites.privacySandboxEnabled, אבל הוא ימשיך לפעול לצורך תאימות לאחור עד גרסה M113. במקומו צריך להשתמש ב-chrome.privacy.websites.topicsEnabled, ב-chrome.privacy.websites.fledgeEnabled וב-chrome.privacy.websites.adMeasurementEnabled. פרטים נוספים זמינים בכתובת https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "המגבלה DtlsSrtpKeyAgreement הוסרה. ציינת ערך false למגבלה הזו, והוא מפורש כניסיון להשתמש בשיטה SDES key negotiation שהוסרה. הפונקציונליות הזו הוסרה. במקומה יש להשתמש בשירות שתומך ב-DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "המגבלה DtlsSrtpKeyAgreement הוסרה. ציינת ערך true למגבלה הזו, ואין לו השפעה, אבל אפשר להסיר את המגבלה הזו כדי לשמור על סדר."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "הפונקציה getStats()‎ שמבוססת על קריאה חוזרת (callback) הוצאה משימוש ותוסר. במקומה אפשר להשתמש בפונקציה getStats()‎ שתואמת למפרט."}, "generated/Deprecation.ts | RangeExpand": {"message": "ה-API‏ Range.expand()‎ הוצא משימוש. במקומו צריך להשתמש ב-Selection.modify()‎."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "בקשות למשאבי משנה שכתובות ה-URL שלהן מכילות פרטי כניסה מוטמעים (למשל **********************/) חסומות."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "האפשרות rtcpMuxPolicy הוצאה משימוש ותוסר."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "תידרש חסימה לגישה מדומיינים אחרים עבור SharedArrayBuffer. פרטים נוספים זמינים בכתובת https://developer.chrome.com/blog/enabling-shared-array-buffer/‎."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "האפשרות של speechSynthesis.speak() ללא הפעלת משתמש הוצאה משימוש ותוסר."}, "generated/Deprecation.ts | UnloadHandler": {"message": "פונקציות event listener להסרת הנתונים שנטענו הוצאו משימוש ויוסרו."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "כדי להמשיך להשתמש ב-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, צריך לאשר חסימה לגישה מדומיינים אחרים בתוספים. הסבר זמין בכתובת https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/‎."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "המא<PERSON>יין isFallbackAdapter של GPUAdapter הוצא משימוש. צריך להשתמש במקומו במאפיין isFallbackAdapter של GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "אין תמיכה ב-UTF-16 ב-J<PERSON><PERSON> של התגובה ב-XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "האפשרות הסינכרונית XMLHttpRequest ב-thread הראשי הוצאה משימוש כי היא משפיעה לרעה על החוויה של משתמשי הקצה. ניתן לקבל עוד עזרה בכתובת https://xhr.spec.whatwg.org/‎."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "אנימציה"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "שינויים בפריסה מתרחשים כשרכיבים נעים ללא פעולה של המשתמש. [צריך לבדוק את הסיבות לשינויים בפריסה](https://web.dev/articles/optimize-cls), כמו רכיבים שנוספו או הוסרו, או גופנים שהשתנו בזמן טעינת הדף."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "ב<PERSON><PERSON><PERSON> גופן"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "החדרת iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "אשכ<PERSON>ל של שינוי הפריסה שמתחיל ב-{PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "לא זוהו גורמים לשינוי הפריסה"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "אין שינויים בפריסה"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "הגורמים לשינוי הפריסה"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "הגורמים המובילים לשינוי הפריסה"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "הא<PERSON><PERSON><PERSON>ל הגרוע ביותר"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "אש<PERSON><PERSON><PERSON> שינוי הפריסה הגרוע ביותר"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "אורך חיים (TTL) של מטמון"}, "models/trace/insights/Cache.ts | description": {"message": "מטמון בעל משך חיים ארוך יכול לאפשר לדף להיטען מהר יותר בביקורים חוזרים. [למידע נוסף](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "אין בקשות עם מדיניות מטמון לא יעילה"}, "models/trace/insights/Cache.ts | others": {"message": "‫{PH1} אחרים"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "בק<PERSON>ה"}, "models/trace/insights/Cache.ts | title": {"message": "שימוש בזמני אחסון יעילים במטמון"}, "models/trace/insights/DOMSize.ts | description": {"message": "נפח DOM גדול יכול להאריך את משך הזמן של חישובי סגנונות ולהוביל להזרמה חוזרת של פריסות, מה שמשפיע על הרספונסיביות של הדף. נפח DOM גדול מגביר את מידת השימוש בזיכרון. [כאן מוסבר איך להימנע מנפח DOM גדול מדי](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "רוב הר<PERSON><PERSON><PERSON>ים המשניים"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "עומק DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "נתון סטטיסטי"}, "models/trace/insights/DOMSize.ts | title": {"message": "אופטימיזציה של גודל DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "סה\"כ רכיבים"}, "models/trace/insights/DOMSize.ts | value": {"message": "ערך"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "בקשת הרשת הראשונה היא החשובה ביותר.  אפשר להפחית את זמן האחזור של הבקשה על ידי הימנעות מהפניות לכתובות URL אחרות, וכך להבטיח תגובה מהירה של השרת ולהפעיל דחיסת טקסט."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "היו הפניות אוטומטיות ({PH1} הפניות אוטומטיות, ‎+{PH2}‎)"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "השרת הגיב באיטיות (הערך שנמדד הוא {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "לא הוחלה דחיסת נתונים"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "בקשת המסמך לא מבצעת הפניות לכתובות אחרות"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "השרת מגיב במהירות (הערך שנמדד הוא {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "החלת דחיסת טקסט"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "הפניות לכתובות אחרות"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "<PERSON><PERSON><PERSON> תגובה של השרת"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "ז<PERSON>ן האחזור של בקשת מסמך"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "הורדה לא דחוסה"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "בייטים כפולים"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "אם קיימים מודולים משוכפלים גדולים בחבילות JavaScript, צריך להסיר אותם מהחבילות האלה כדי לצמצם צריכה של בייטים מיותרים על ידי הפעילות ברשת."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "‫JavaScript כפול"}, "models/trace/insights/FontDisplay.ts | description": {"message": "כדאי לשקול להגדיר את [font-display](https://developer.chrome.com/blog/font-display) לערך swap או optional כדי לוודא שהטקסט גלוי באופן עקבי. אפשר להמשיך לשפר את האופטימיזציה של swap כדי לצמצם את השינויים בפריסה באמצעות [שינויים של מדדי הגופן מברירות המחדל](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "גו<PERSON>ן"}, "models/trace/insights/FontDisplay.ts | title": {"message": "תצוגת גופן"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "ב<PERSON><PERSON><PERSON><PERSON>ן"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(אנונימית)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "הרבה ממשקי API, ב<PERSON><PERSON><PERSON><PERSON>ד כאלה שקוראים נתוני גיאומטריה של פריסה, גורמים למנוע הרינדור להשהות את ביצוע הסקריפט כדי לחשב את הסגנון והפריסה. מידע נוסף על [הזרמה חוזרת כפויה](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) ועל ההקלות שלה."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "ד<PERSON><PERSON> קריסות"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "אילוץ של הזרמה חוזרת"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "בק<PERSON>ה מובילה להפעלת פונקציה"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "הזמן הכולל של ההזרמה החוזרת"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ללא שיוך]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "צמצום זמן ההורדה של תמונות יכול לשפר את זמן הטעינה של הדף ואת ה-LCP. [מידע נוסף על אופטימיזציה של גודל התמונה](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "‫{PH1} (גודל משוער של {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "אין תמונות שאפשר לבצע אופטימיזציה שלהן"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "אופטימיזציה של גודל הקובץ"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "‫{PH1} אחרים"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "שיפור של שליחת התמונות"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "דחיסה יעילה יותר של התמונה יכולה להקטין את קובץ ההורדה שלה."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "שימוש בפורמט תמונה מודרני (WebP, ‏AVIF) או דחיסה יעילה יותר של התמונה יכולים להקטין את קובץ ההורדה שלה."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "קובץ התמונה הזה גדול יותר מהנדרש ({PH1}) למידות המוצגות שלו ({PH2}). כדאי להשתמש בתמונות רספונסיביות כדי להקטין את קובץ ההורדה של התמונה."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "שימוש בפורמטים של וידאו במקום ב-GIF יכול להקטין את קובץ ההורדה של תוכן אנימציה."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "כדאי להתחיל בחקירת השלב הארוך ביותר. [אפשר לקצר את משך הזמן שלוקח לשלוח את הבקשה](https://web.dev/articles/optimize-inp#optimize_interactions). כדי לקצר את משך הזמן של העיבוד, מומלץ [לבצע אופטימיזציה בעלויות של ה-Thread הראשי](https://web.dev/articles/optimize-long-tasks), לרוב JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "משך"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "השהיה לאחר קלט"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "לא זוהו אינטראקציות"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "שלב"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "השהיה של הצגת תגובה"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "משך העיבוד"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "‫INP לפי שלב"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "כדי לייעל את ה-LCP, צריך להפוך את תמונת ה-<PERSON><PERSON> ל[גלויה](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) ב-HTML באופן מיידי, ו[להימנע מטעינה מדורגת](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "אחזור בעדי<PERSON>ות גבוהה הוחל"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "צריך להחיל את fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "הטעינה המדורגת לא הוחלה"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "תמונת ה-<PERSON><PERSON> נטענה {PH1} אחרי נקודת ההתחלה המוקדמת ביותר."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "לא זוהה LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "לא זוהה משאב LCP כי ה-LCP הוא לא תמונה"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "הבק<PERSON>ה גלויה במסמך הראשוני"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "גילוי בקשות LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "[לכל שלב יש אסטרטגיות שיפור ספציפיות](https://web.dev/articles/optimize-lcp#lcp-breakdown). באופן אידיאלי, רוב זמן ה-LCP צריך להיות מוקדש לטעינה של המשאבים, ולא לעיכובים."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "משך"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "עיכוב בעי<PERSON><PERSON>ד הרכיב"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "שדה p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "לא זוהה LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "שלב"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "עיכוב בטעינת המשאבים"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "משך הטעינה של משאבים"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "המהירות שבה מגיע בייט התגובה הראשון (TTFB)"}, "models/trace/insights/LCPPhases.ts | title": {"message": "‫<PERSON><PERSON> לפי שלב"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "סקריפט"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "בייטים מבוזבזים"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "רכיבי Polyfill וטרנספורמציות מאפשרים לדפדפנים ישנים יותר להשתמש בתכונות JavaScript חדשות. עם זאת, רבים מהם לא נחוצים לדפדפנים מודרניים. כדאי לשקול לשנות את תהליך ה-build של JavaScript כדי לא לבצע טרנספילציה של [תכונות בסיסיות](https://web.dev/articles/baseline-and-polyfills), אלא אם ידוע לך שנדרשת תמיכה בדפדפנים ישנים יותר. [למה רוב האתרים יכולים לפרוס קוד ES6+‎ ללא טרנספילציה](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "‫JavaScript מדור קודם"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "ל-HTTP/2 ו-HTTP/3 יש הרבה יתרונות לעומת HTTP/1.1, כמו multiplexing. [מידע נוסף על השימוש ב-HTTP מודרני](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "לא נמצאו בקשות שנעשה בהן שימוש ב-HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "פרוטוקול"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "בק<PERSON>ה"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "‫HTTP מודרני"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "בק<PERSON>ה"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "חיס<PERSON><PERSON>ן משוער ב-LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "קישור מקדים שלא נוצל. צריך לוודא שהשימוש במאפיין crossorigin נעשה בצורה נכונה."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[כדאי להימנע משרשראות של בקשות קריטיות](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains). מומלץ לקצר את השרשראות, להקטין את גודל ההורדה של משאבים או לעכב את ההורדה של משאבים לא נחוצים כדי לשפר את מהירות טעינת הדף."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "אפשר להוסיף רמזים [לקישורים מקדימים](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) למקורות החשובים ביותר, אבל כדאי להשתמש ב-4 רמזים לכל היותר."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "מקורות מתאימים לקישור מקדים"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "<PERSON><PERSON><PERSON> אחז<PERSON><PERSON> מקסימלי בנתיב הקריטי:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "אין משימות רינדור שנפגעו בגלל תלות ברשת"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "אין מקורות נוספים שמתאימים לקישור מקדים"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "לא היו מקורות עם קישורים מקדימים"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "הרמזים [לקישורים מקדימים](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) עוזרים לדפדפן ליצור חיבור מהיר יותר במהלך טעינת הדף, ומאפשרים לשלוח מוקדם יותר את הבקשה הראשונה למקור הזה. אלה המקורות שהדף מתחבר אליהם מראש."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "מקורות עם קישור מקדים"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "עץ תלות ברשת"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "נמצאו יותר מארבעה קישורים preconnect. השימוש בקישורים מקדימים צריך להיות מצומצם מאוד ומוגבל למקורות החשובים ביותר."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "קישור מקדים שלא נוצל. צריך להשתמש ב-preconnect רק למקורות שהדף עשוי לבקש."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "כדי לטעון דפים מהר יותר, כדאי להימנע משרשראות של בקשות קריטיות. מומלץ לקצר את השרשראות, להקטין את נפח ההורדה של המשאבים או לדחות את ההורדה של משאבים לא נחוצים."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "יש בקשות שחוסמות את העיבוד הראשוני של הדף, מה שעלול לגרום לעיכוב של ה-LCP. [דחייה או שילוב בקוד](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) יכולים להוציא את בקשות הרשת האלה מהנתיב הקריטי."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "משך"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "אין בקשות עיבוד של חסימה לניווט הזה"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "בק<PERSON>ה"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "עיבוד של בקשות חסימה"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "אם העלויות של 'חישוב מחדש של הסגנון' עדיין גבוהות, אפשר להפחית אותן באמצעות אופטימיזציה של הסלקטור. כדאי לבצע [אופטימיזציה לסלקטורים](https://developer.chrome.com/docs/devtools/performance/selector-stats) עם זמן חולף ואחוז של נתיב איטי (slow-path) גבוהים. שימוש בסלקטורים פשוטים יותר, בפחות סלקטורים, ב-DOM קטן יותר וב-DOM נמוך יותר יפחית את עלויות ההתאמה."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "הזמן שחלף"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "לא נמצאו נתונים של סלקטור ב-CSS. צריך להפעיל את הנתונים הסטטיסטיים של הסלקטור ב-CSS בהגדרות של חלונית הביצועים."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "ניסיונות התאמה"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "ספירת התאמות"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "עלויות של סלקטור ב-CSS"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "הבוררים המובילים"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "סה\"כ"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "משך הזמן של השרשור הראשי"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "צ<PERSON> שלישי"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "גודל ההעברה"}, "models/trace/insights/ThirdParties.ts | description": {"message": "קוד של צד שלישי עשוי להשפיע בצורה משמעותית על ביצועי הטעינה. כדי לתעדף את תוכן הדף שלך, צריך [לצמצם ולדחות טעינת קוד של צד שלישי](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "לא נמצאו צדדים שלישיים"}, "models/trace/insights/ThirdParties.ts | title": {"message": "צדדים שלישיים"}, "models/trace/insights/Viewport.ts | description": {"message": "יכול להיות שאינטראקציות של לחיצה [יעוכבו בעד 300 אלפיות השנייה](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) אם אזור התצוגה לא עבר אופטימיזציה לנייד."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "עיכו<PERSON> בלחיצה על הנייד"}, "models/trace/insights/Viewport.ts | title": {"message": "אופטימיזציה של אזור התצוגה לנייד"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "רק דפים שהטעינה שלהם נעשית באמצעות בקשת GET מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "אפשר לשמור במטמון רק דפים עם קוד סטטוס של 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "מערכת Chrome זיהתה ניסיון להפעיל JavaScript בזמן שהדף היה במטמון."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "בשלב זה, דפים שנשלחה מהם בקשה ל-AppBanner לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת על ידי תכונות ניסיוניות. צריך לעבור לכתובת chrome://flags/#back-forward-cache כדי להפעיל אותו באופן מקומי במכשיר הזה."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "הפיצ'ר מטמון לדף הקודם/הבא הושבת על ידי שורת הפקודה."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת כי אין מספיק זיכרון."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' לא נתמך על ידי בעל גישה."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "הפיצ'ר מטמון לדף הקודם/הבא הושבת בשביל כלי עיבוד מקדים."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "אי אפשר לשמור את הדף במטמון כי יש בו מופע של BroadcastChannel עם אירועי listener רשומים."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שיש להם כותרת cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "המטמון נוקה באופן מכוון."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "הדף הוצא מהמטמון כדי לאפשר שמירה במטמון של דף אחר."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "בשלב זה, דפים שמכילים יישומי פלאגין לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "לא מוגדרת"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "דפים שנעשה בהם שימוש ב-FileChooser API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "דפים שנעשה בהם שימוש ב-File System Access API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "דפים שנעשה בהם שימוש ב-Dispatcher של מכשירים לאחסון מדיה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Media Player היה פעיל ביציאה מהדף."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "בשלב זה, דפים שנעשה בהם שימוש בממשק MediaSession API ושהוגדר בהם מצב הפעלה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "דפים שנעשה בהם שימוש ב-MediaSession API, אשר מגדירים מופעי handler של פעולות, לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת בגלל פעילות קורא המסך."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "דפים שנעשה בהם שימוש ב-SecurityHandler לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "דפים שמשתמשים ב-Serial API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "דפים שנעשה בהם שימוש ב-WebAuthetication API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "דפים שנעשה בהם שימוש ב-WebBluetooth API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "דפים שמשתמשים ב-WebUSB API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי קובצי cookie הושבתו בדף עם שימוש ב-Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-worker או ב-worklet ייעודיים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "טעינת המסמך לא הסתיימה לפני היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "הב<PERSON><PERSON><PERSON> של האפליקציה פעל במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "מנהל הסיסמאות של Chrome היה פעיל במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "התבצע זיקוק DOM במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "הכלי להצגת זיקוק ה-DOM פעל במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת עקב תוספים שמשתמשים ב-API לשליחת הודעות."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "תוספים עם חיבור לאורך זמן אמורים להיסגר לפני הכניסה למטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "תוספים עם חיבור לאורך זמן ניסו לשלוח הודעה למסגרות במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת בגלל תוספים."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "חלון עזר של תיבת דו-שיח, למשל שליחה מחדש של טופס או תיבת דו-שיח עם סיסמת http, הוצג בדף במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "הדף ללא החיבור לאינטרנט הוצג במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "סרגל ההתערבות לגבי חוסר בזי<PERSON><PERSON><PERSON><PERSON> פנוי פעל במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "נשלחו בקשות להרשאות במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "תוכנת החסימה לחלונות קופצים פעלה במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "פרטי הגלישה הבטוחה הוצגו במהלך היציאה."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "במסגרת השימוש ב'גלישה בטוחה' נקבע כי הדף הזה פוגעני והחלון הקופץ נחסם."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "קובץ שירות (service worker) הופעל בזמן שהדף היה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "המטמון לדף הקודם/הבא מושבת בגלל שגיאה במסמך."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "לא ניתן לשמור ב-bfcache דפים שנעשה בהם שימוש ב-FencedFrames."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "הדף הוצא מהמטמון כדי לאפשר שמירה במטמון של דף אחר."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "בשלב זה, ד<PERSON>ים שנתנו אישור גישה לסטרימינג של מדיה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "בשלב הזה, דפים שיש בהם סוגים מסוימים של תוכן מוטמע (למשל קובצי PDF) לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-IdleManager לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "בשלב זה, דפים שיש בהם חיבור IndexedDB פתוח לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה עקב אירוע מסוג IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "נעשה שימוש בממשקי API לא מתאימים."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "בשלב זה, דפים שמחדירים אליהם JavaScript באמצעות תוספים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "בשלב זה, דפים שמחדירים אליהם StyleSheet באמצעות תוספים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "שגיאה פנימית."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי בבקשות מסוימות של רשת JavaScript התקבל משאב עם הכותרת Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "הפיצ'ר 'מטמון לדף הקודם/הבא' הושבת עקב בקשה מסוג הודעת keep-alive."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "בשלב זה, דפים שנעשה בהם שימוש בנעילת מקלדת לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "טעינת הדף לא הסתיימה לפני היציאה מהפעולה."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב העיקרי שלהם מופיע cache-control:no-cache."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב העיקרי שלהם מופיע cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "הניווט בוטל לפני שנעשה לדף שחזור מהמטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "הדף הוצא מהמטמון כי חיבור פעיל לרשת קיבל יותר מדי נתונים. יש הגבלה של Chrome על כמות הנתונים שדף מסוים יכול לקבל כשהוא שמור במטמון."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "בשלב זה, דפים שמכילים XHR או פונקציית fetch()‎ פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "הדף הוצא מהמטמון לדף הקודם/הבא כי בקשה פעילה לרשת כללה הפניה אוטומטית."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "הדף הוצא מהמטמון כי החיבור לרשת היה פתוח יותר מדי זמן. יש הגבלה של Chrome בנוגע למשך הזמן שדף יכול לקבל נתונים כשהוא שמור במטמון."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שאין להם כותרת תגובה תקינה."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "הניווט התרחש במסגרת שאינה המסגרת הראשית."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "בשלב זה, דפים עם טרנזקציות פעילות של IndexedDB לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לאחזור לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "בשלב זה, דפים עם בקשת רשת XHR פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-PaymentManager לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "בשלב זה, דפים שבהם נעשה שימוש ב'תמונה בתוך תמונה' לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "בשלב זה, דפים שמוצגים בהם רכיבי ממשק משתמש של הדפסה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "הדף נפתח תוך שימוש ב-'window.open()' ולכרטיסייה נוספת יש הפניה לזה, או שנפתח חלון על ידי הדף."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "תהליך הרינדור קרס לדף הזה שבמטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "תהליך הרינדור הופסק לדף הזה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה להרשאות להקלטת אודיו לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה להרשאות חיישן לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה לסנכרון ברקע או להרשאות אחזור לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה להרשאות MIDI לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה להרשאות לגבי התראות לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "בשלב זה, דפים שהתקבלה מהם בקשת גישה לאחסון לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "בשלב זה, דפים שהתקבלה מהם בקשה להרשאות לצילום סרטון לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "אפשר לשמור במטמון רק דפים שהסכמה של כתובת ה-URL שלהם היא HTTP / HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "על הדף נתבעה בעלות על ידי קובץ שירות (service worker) בזמן שהוא נמצא במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "נעשה ניסיון של קובץ שירות (service worker) לשלוח MessageEvent לדף שנמצא במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "בוטל הרישום של ServiceWorker כשדף היה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "הדף הוצא מהמטמון לדף הקודם/הבא עקב הפעלה של קובץ שירות (service worker)."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome הופעל מחדש וניקה את הרשומות של המטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SharedWorker לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SpeechRecognizer לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SpeechSynthesis לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "החל ניווט מצד iframe בדף שלא הסתיים."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב המשנה שלהם מופיע cache-control:no-cache."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "לא ניתן להעביר למטמון לדף הקודם/הבא דפים שבמשאב המשנה שלהם מופיע cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "הדף חרג ממגבלת הזמן המקסימלית בפיצ'ר מטמון לדף הקודם/הבא והתוקף שלו פג."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "פג הזמן הקצוב של הדף להוספה למטמון לדף הקודם/הבא (ככל הנראה עקב מופעי handler לפעולות ממושכות של הסתרת דפים)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "לדף יש handler של הסרת הנתונים שנטענו במסגרת הראשית."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "לדף יש handler של הסרת הנתונים שנטענו במסגרת המשנית."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "הכותרת של השינוי מברירת המחדל של סוכן המשתמש שונתה על ידי הדפדפן."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "בשלב זה, דפים שנתנו הרשאת גישה לצילום סרטונים או להקלטת אודיו לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebDatabase לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebHID לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebLocks לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebNfc לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebOTPService לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "אי אפשר לשמור דפים עם WebRTC במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי נעשה שימוש ב-WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebShare לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "דפים עם WebSocket לא יכולים לעבור למטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי נעשה שימוש ב-WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "אי אפשר להוסיף דפים עם WebTransport למטמון לדף הקודם/הבא."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "התכונ<PERSON> 'מטמון לדף הקודם/הבא' הושבתה כי נעשה שימוש ב-WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebXR לא מתאימים לשמירה במטמון לדף הקודם/הבא."}}