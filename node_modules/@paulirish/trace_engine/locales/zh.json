{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "处理 CORS Access-Control-Allow-Headers 时，授权将不在通配符 (*) 的涵盖范围内。"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "若要停用默认 Cast 集成，应使用 disableRemotePlayback 属性，而非 -internal-media-controls-overlay-cast-button 选择器。"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS appearance 值 slider-vertical 未标准化，将会被移除。"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "如果对应的网址同时包含已移除的空白字符 \\(n|r|t) 和小于字符 (<)，资源请求会被屏蔽。请从元素属性值等位置移除换行符并编码小于字符，以便加载这些资源。"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Navigation Timing 2。"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Paint Timing。"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Navigation Timing 2 中的 nextHopProtocol。"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "包含 \\(0|r|n) 字符的 Cookie 将被拒，而不是被截断。"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "通过设置 document.domain 放宽同源政策的功能已被弃用，并将默认处于停用状态。此弃用警告针对的是通过设置 document.domain 启用的跨源访问。"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "从跨源 iframe 触发 window.alert 的功能已被弃用，日后将被移除。"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "从跨源 iframe 触发 window.confirm 的功能已被弃用，日后将被移除。"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "对 SVGUseElement 中 data: URL 的支持已被弃用，日后将被移除。"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() 和 watchPosition() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() 和 watchPosition() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "在 <article>、<aside>、<nav> 或 <section> 中发现了一个没有指定字体大小的 <h1> 标记。不久后，此标题文本在此浏览器中的大小将发生变化。如需了解详情，请参阅 https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1。"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate 已被弃用。请改用 RTCPeerConnectionIceErrorEvent.address 或 RTCPeerConnectionIceErrorEvent.port。"}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "这个使用 navigator.credentials.get() 方法来请求获取数字凭据的格式已被弃用，请更新您的调用并使用新格式。"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment Service Worker 事件中的商家源和任意数据已被弃用，并将被移除：topOrigin、paymentRequestOrigin、methodData、modifiers。"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "该网站向网络请求了一项子资源，而且仅仅因为其用户的特权网络位置而能够访问此项资源。此类请求会向互联网公开非公用设备和服务器，这会增加跨站请求伪造 (CSRF) 攻击和/或信息泄露的风险。为降低这类风险，Chrome 不再支持从非安全上下文发起针对非公用子资源的请求，并将开始阻止此类请求。"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "向 joinAdInterestGroup() 传递的 InterestGroups 所含 dailyUpdateUrl 字段已被重命名为 updateUrl，以便更准确地反映其行为。"}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator 已被弃用。请改用 Intl.Segmenter。"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "无法从 file: 网址加载 CSS，除非它们以 .css 文件扩展名结尾。"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "由于规范变更，使用 SourceBuffer.abort() 中止 remove() 的异步范围移除的功能已被弃用。日后我们会移除相应支持。您应改为监听 updateend 事件。abort() 只应用于中止异步媒体附加或重置解析状态。"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "由于规范变更，我们不再支持将 MediaSource.duration 设为低于任何缓冲编码帧的最高呈现时间戳。日后我们将不再支持隐式移除被截断的缓冲媒体。您应改为对 newDuration < oldDuration 的所有 sourceBuffers 执行显式 remove(newDuration, oldDuration)。"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "即使 MIDIOptions 中未指定 sysex，Web MIDI 也会请求获得使用许可。"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "无法再从不安全的源使用 Notification API。您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "无法再从跨源 iframe 中请求 Notification API 权限。您应考虑改为从顶级框架中请求权限，或者打开一个新窗口。"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap 中的 imageOrientation: 'none' 选项已被弃用。请改用带有 {imageOrientation: 'from-image'} 选项的 createImageBitmap。"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "您的合作伙伴正在协商某个已过时的 (D)TLS 版本。请与您的合作伙伴联系，以解决此问题。"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "为 img、video 和 canvas 标记指定 overflow: visible 可能会导致这些标记在元素边界之外生成视觉内容。请参阅 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments 已被弃用。请改用即时安装方式安装付款处理程序。"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "您的 PaymentRequest 调用已绕过内容安全政策 (CSP) connect-src 指令。此绕过方式已被弃用。请将 PaymentRequest API 中的付款方式标识符（在 supportedMethods 字段中）添加到 CSP connect-src 指令中。"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent 已被弃用。请改用标准化 navigator.storage。"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "带有 <picture> 父级的 <source src> 无效，因此会被忽略。请改用 <source srcset>。"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame 因供应商而异。请改用标准 cancelAnimationFrame。"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame 因供应商而异。请改用标准 requestAnimationFrame。"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen 已被弃用。请改用 Document.fullscreenElement。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen 已被弃用。请改用 Document.fullscreenEnabled。"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "我们即将弃用 API chrome.privacy.websites.privacySandboxEnabled，但为了保持向后兼容性，该 API 可持续使用到 M113 版。届时，请改用 chrome.privacy.websites.topicsEnabled、chrome.privacy.websites.fledgeEnabled 和 chrome.privacy.websites.adMeasurementEnabled。请参阅 https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "约束条件 DtlsSrtpKeyAgreement 已被移除。您已为此约束条件指定 false 值，系统会将这种情况解读为尝试使用已被移除的 SDES key negotiation 方法。此功能已被移除；请改用支持 DTLS key negotiation的服务。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "约束条件 DtlsSrtpKeyAgreement 已被移除。您已为此约束条件指定 true 值，这没有任何作用，但为整洁起见，您可以移除此约束条件。"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "基于回调的 getStats() 已被弃用，并将被移除。请改用符合规范的 getStats()。"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() 已被弃用。请改用 Selection.modify()。"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "如果对应的网址包含嵌入式凭据（例如 **********************/），子资源请求会被屏蔽。"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy 选项已被弃用，并将被移除。"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer 将要求进行跨域隔离。如需了解详情，请访问 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "无需用户激活的 speechSynthesis.speak() 已被弃用，并将被移除。"}, "generated/Deprecation.ts | UnloadHandler": {"message": "卸载事件监听器已被弃用，并且将被移除。"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "扩展程序应选择启用跨域隔离，以便继续使用 SharedArrayBuffer。请参阅 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter 属性已弃用，请改用 GPUAdapterInfo isFallbackAdapter 属性。"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest 中的响应 JSON 不再支持 UTF-16"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主线程上的同步 XMLHttpRequest 已被弃用，因为它会对最终用户的体验产生不利影响。如需更多帮助，请访问 https://xhr.spec.whatwg.org/。"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "动画"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "当元素在没有任何用户互动的情况下移动时，就会发生布局偏移。[调查布局偏移的原因](https://web.dev/articles/optimize-cls)，例如在网页加载时添加、移除元素或元素字体发生了变化。"}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "字体请求"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "注入的 iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "布局偏移集群开始于 {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "无法检测到任何布局偏移的根源"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "无布局偏移"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "布局偏移原因"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "导致布局偏移的首要原因"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "最糟糕的集群"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "最糟糕的布局偏移集群"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "缓存 TTL"}, "models/trace/insights/Cache.ts | description": {"message": "延长缓存生命周期可加快重访您网页的速度。[了解详情](https://web.dev/uses-long-cache-ttl/)。"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "没有任何请求使用效率低下的缓存政策"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} 项其他内容"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "请求"}, "models/trace/insights/Cache.ts | title": {"message": "使用高效的缓存生命周期"}, "models/trace/insights/DOMSize.ts | description": {"message": "大型 DOM 可能会增加样式计算和布局自动重排的用时，从而影响网页响应速度。大型 DOM 也会增加内存用量。[了解如何避免 DOM 规模过大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。"}, "models/trace/insights/DOMSize.ts | element": {"message": "元素"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "子级数量上限"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM 深度"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "统计信息"}, "models/trace/insights/DOMSize.ts | title": {"message": "优化 DOM 大小"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "元素总数"}, "models/trace/insights/DOMSize.ts | value": {"message": "值"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "您的第一个网络请求最为重要。您可通过避免重定向、确保服务器快速响应以及启用文本压缩，缩短其延迟时间。"}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "有重定向 ({PH1} 次重定向，+{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "服务器响应缓慢（观察结果：{PH1}）"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "未应用任何压缩"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "避免重定向"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "服务器响应迅速（观察结果：{PH1}）"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "应用文本压缩"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "重定向"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "服务器响应时间"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "文档请求延迟"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "未压缩下载"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "重复的字节数"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "来源"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "从软件包中移除重复的大型 JavaScript 模块，即可避免网络活动带来不必要的字节消耗。"}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "重复的 JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "建议您将 [font-display](https://developer.chrome.com/blog/font-display) 设为 swap 或 optional，确保文本始终可见。通过[替换字体指标](https://developer.chrome.com/blog/font-fallbacks)可进一步优化 swap，缓解布局偏移。"}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "字体"}, "models/trace/insights/FontDisplay.ts | title": {"message": "字体显示"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "浪费的时间"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "（匿名）"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "许多 API（通常用于读取布局几何图形信息）会强制渲染引擎暂停脚本执行，以便计算样式和布局。详细了解[强制自动重排](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)及其缓解措施。"}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "堆栈轨迹"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "强制自动重排"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "最耗时的函数调用"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "总自动重排时间"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[未归因]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "缩短图片下载时间可优化网页的感知加载时间和 LCP。[详细了解图片大小优化](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1}（预计 {PH2}）"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "没有可优化的图片"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "优化文件大小"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} 项其他内容"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "改进图片传送"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "提高图片压缩因子可以优化此图片的下载大小。"}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "使用现代图片格式（WebP、AVIF）或提高图片压缩率可以优化此图片的下载大小。"}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "此图片文件的大小 ({PH1}) 超过了所需显示大小 ({PH2})。请使用自适应图片来缩减图片下载大小。"}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "使用视频格式而非 GIF 格式可以优化动画内容的下载大小。"}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "请从持续时间最长的阶段开始检查。[几处延迟可降低到最小](https://web.dev/articles/optimize-inp#optimize_interactions)。如需缩短处理时长，请[优化主线程（通常是 JS）成本](https://web.dev/articles/optimize-long-tasks)。"}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "时长"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "输入延迟"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "未检测到任何互动"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "阶段"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "展示延迟时间"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "处理用时"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "按阶段划分的 INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "使 LCP 图像能够立即从 HTML 中[被发现](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)，并[避免延迟加载](https://web.dev/articles/lcp-lazy-loading)，以此优化 LCP"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "已应用 fetchpriority 的 high 属性值"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "应该应用 fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "未应用延迟加载"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP 图片会在最开始检测到后的 {PH1}之后加载完成。"}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "未检测到 LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "未检测到 LCP 资源，因为该 LCP 不是图片"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "可在初始文档中检测到请求"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "发现 LCP 请求"}, "models/trace/insights/LCPPhases.ts | description": {"message": "每个[阶段都有特定的改进策略](https://web.dev/articles/optimize-lcp#lcp-breakdown)。理想情况下，大部分 LCP 时间应该花在加载资源上，而不是浪费在延迟上。"}, "models/trace/insights/LCPPhases.ts | duration": {"message": "时长"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "元素渲染延迟"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "现场数据第 75 百分位"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "未检测到 LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "阶段"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "资源加载延迟"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "资源加载时长"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "第一字节时间 (Time to First Byte)"}, "models/trace/insights/LCPPhases.ts | title": {"message": "按阶段划分的 LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "脚本"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "浪费的字节数"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Polyfill 和 transform 让旧版浏览器能够使用新的 JavaScript 功能。不过，其中的很多函数对现代浏览器而言并不是必需的。请考虑修改 JavaScript 构建流程，以便不转译 [Baseline](https://web.dev/articles/baseline-and-polyfills) 功能，除非您知道必须支持旧版浏览器。[了解为什么大多数网站可以部署 ES6+ 代码而无需转译](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "旧版 JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 和 HTTP/3 拥有许多 HTTP/1.1 没有的优势，例如多路复用。[详细了解如何使用现代 HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "没有任何请求使用 HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "协议"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "请求"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "现代 HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "源"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "请求"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "来源"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "时间"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "预计 LCP 节省毫秒数"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "未使用的 preconnect。请检查并确保正确使用了 crossorigin 属性。"}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[避免链接关键请求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)，方法是缩短链长、缩减资源的下载文件大小，或者推迟下载不必要的资源，从而提高网页加载速度。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "为最重要的源添加 [preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 提示，但请尽量不要超过 4 个。"}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "预连接候选项"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "关键路径延迟时间上限："}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "没有任何渲染任务受到网络依赖项的影响"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "没有其他源是预连接的合适候选对象"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "未预连接任何源"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 提示可帮助浏览器在加载网页时尽早建立连接，从而在向该源发出第一个请求时节省时间。以下是该网页预先连接到的源。"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "预连接的源"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "网络依赖关系树"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "发现了超过 4 个 preconnect 连接。此类连接应尽量少用，且应仅用于指向最重要的源。"}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "未使用的 preconnect。请仅针对网页可能会请求的源使用 preconnect。"}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "避免链接关键请求，方法是缩短链长、缩减资源的下载文件大小，或者推迟下载不必要的资源，从而提高网页加载速度。"}, "models/trace/insights/RenderBlocking.ts | description": {"message": "请求正在屏蔽网页的初始渲染，这可能会延迟 LCP。[延迟或内嵌](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)可以将这些网络请求移出关键路径。"}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "时长"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "此导航没有渲染阻塞请求"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "请求"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "渲染屏蔽请求"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "如果“重新计算样式”的成本仍然很高，优化选择器可以降低此成本。请对用时较长及慢路径所占百分比较高的[选择器进行优化](https://developer.chrome.com/docs/devtools/performance/selector-stats)。选择器越简单、数量越少，DOM 的规模越小、结构越简单，越能降低匹配成本。"}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "用时"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "未找到任何 CSS 选择器数据。需要在“性能”面板设置中启用 CSS 选择器统计信息。"}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "尝试匹配次数"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "匹配数"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS 选择器成本"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "最耗时/耗力选择器"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "总计"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "主线程耗时"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "第三方"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "传输大小"}, "models/trace/insights/ThirdParties.ts | description": {"message": "第三方代码可能会显著影响加载性能。[请减少并推迟加载第三方代码](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)，以优先渲染您的网页内容。"}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "未找到第三方内容"}, "models/trace/insights/ThirdParties.ts | title": {"message": "第三方"}, "models/trace/insights/Viewport.ts | description": {"message": "如果视口未针对移动设备进行优化，点按互动可能会[延迟最多 300 毫秒](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。"}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "移动设备点按延迟"}, "models/trace/insights/Viewport.ts | title": {"message": "针对移动设备优化视口"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "只有通过 GET 请求进行加载的网页才能储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "只有状态代码为 2XX 的网页才能被缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome 检测到一项在储存于缓存期间执行 JavaScript 的意图。"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "已请求 AppBanner 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "往返缓存被相关 flag 停用了。请在此设备上访问 chrome://flags/#back-forward-cache 以从本地启用该功能。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "往返缓存已被命令行停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "因为内存不足，往返缓存已被停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "委托行为不支持往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "已针对预渲染程序停用往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "该网页无法缓存，因为它包含的 BroadcastChannel 实例具有已注册的监听器。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "含 cache-control:no-store 标头的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "缓存被刻意清除了。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "包含插件的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "未定义"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "使用 FileChooser API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "使用 File System Access API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "使用媒体设备调度程序的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "媒体播放器正在播放内容时用户就离开了网页。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "使用 MediaSession API 并设置了播放状态的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "使用 MediaSession API 并设置了操作处理程序的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "往返缓存已被停用，因为受屏幕阅读器影响。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "使用 SecurityHandler 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "使用 Serial API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "使用 WebBluetooth API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "使用 WebUSB API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "往返缓存已被停用，因为在使用 Cache-Control: no-store 的网页上 Cookie 处于停用状态。"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "使用专用 Worker 或 Worklet 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "该文档还未加载完毕时用户就离开了。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "用户离开网页时，系统显示了应用横幅。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "用户离开网页时，系统显示了 Chrome 密码管理工具。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "用户离开网页时，DOM 提取正在进行。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "用户离开网页时，系统显示了 DOM Distiller Viewer。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "往返缓存已被停用，因为扩展程序使用了 Messaging API。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "在进入往返缓存之前，采用长期有效连接的扩展程序应断开连接。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "采用长期有效连接的扩展程序试图将消息发送到往返缓存中的框架。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "往返缓存已被停用，因为受扩展程序影响。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "用户离开网页时，该网页上显示了模态对话框（例如表单重新提交）或 HTTP 密码对话框。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "用户离开网页时，系统显示了离线版网页。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "用户离开网页时，系统显示了 Out-Of-Memory Intervention 栏。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "用户离开网页时，系统显示了权限请求。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "用户离开网页时，系统显示了弹出式内容拦截器。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "用户离开网页时，系统显示了安全浏览详情。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "“安全浏览”功能认定该网页有滥用性质，因此拦截了弹出式窗口。"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "在该网页储存于往返缓存期间，有一个 Service Worker 被启用了。"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "往返缓存已被停用，因为文档出错了。"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "采用 FencedFrame 的网页无法存储在 bfcache 中。"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "已被授予媒体流访问权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "包含某些类型嵌入内容（例如 PDF）的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "使用 IdleManager 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "具备开放的 IndexedDB 连接的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "往返缓存已被停用，因为发生了 IndexedDB 事件。"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "使用了不符合条件的 API。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "已被扩展程序注入 JavaScript 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "已被扩展程序注入 StyleSheet 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "内部出错了。"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "往返缓存已被停用，因为部分 JavaScript 网络请求收到了包含 Cache-Control: no-store 标头的资源。"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "往返缓存已被停用，因为这是一项 keepalive 请求。"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "使用“键盘锁定”功能的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "该网页还未加载完毕时用户就离开了。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "主资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "主资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "该网页还没从往返缓存中恢复时导航就被取消了。"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "该网页被逐出了缓存，因为有一项使用中的网络连接收到了太多数据。Chrome 会限制网页在缓存期间可接收的数据量。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "包含传输中的 fetch() 或 XHR 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "该网页被逐出了往返缓存，因为有一项使用中的网络请求涉及了重定向。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "该网页被逐出了缓存，因为有一项网络连接处于开放状态的时间太长。Chrome 会限制网页在缓存期间可接收数据的时长。"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "不含有效响应标头的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "导航是在主框架之外的某个框架中发生的。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "正在针对已建立索引的数据库处理事务的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "包含传输中的 fetch() 网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "包含传输中的 XHR 网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "使用 PaymentManager 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "使用“画中画”功能的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "显示打印界面的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "该网页是使用“window.open()”打开的，而另一个标签页引用了该网页；或者，该网页打开了一个窗口。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "储存在往返缓存中的网页的渲染程序进程崩溃了。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "储存于往返缓存中的网页的渲染程序进程被终止了。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "已请求音频截取权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "已请求传感器使用权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "已请求后台同步或提取权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "已请求 MIDI 权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "已请求通知权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "已请求存储空间使用权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "已请求视频拍摄权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "只有网址架构为 HTTP / HTTPS 的网页才能被缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "在储存于往返缓存期间，该网页被一个 Service Worker 认领了。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "有一个 Service Worker 尝试向储存于往返缓存中的网页发送 MessageEvent。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "在网页储存于往返缓存期间，ServiceWorker 被取消注册了。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "该网页被逐出了往返缓存，因为有一个 Service Worker 被启用了。"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome 重启了，因而清除了往返缓存条目。"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "使用 SharedWorker 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "使用 SpeechRecognizer 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "使用 SpeechSynthesis 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "该网页上某个 iframe 发起的导航并未完成。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "子资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "子资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "该网页超出了往返缓存中的储存时长上限，因而已过期。"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "该网页在储存至往返缓存时超时了（可能是因为 pagehide 处理程序长时间运行）。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "该网页的主框架中含有一款卸载处理程序。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "该网页的子框架中含有一款卸载处理程序。"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "浏览器更改了用户代理替换标头。"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "已被授予视频/音频录制权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "使用 WebDatabase 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "使用 WebHID 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "使用 WebLocks 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "使用 WebNfc 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "使用 WebOTPService 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "使用 WebRTC 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "往返缓存已被停用，因为使用了 WebRTC。"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "使用 Webshare 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "使用 WebSocket 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "往返缓存已被停用，因为使用了 WebSocket。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "使用 WebTransport 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "往返缓存已被停用，因为使用了 WebTransport。"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "使用 WebXR 的网页目前无法储存至往返缓存。"}}