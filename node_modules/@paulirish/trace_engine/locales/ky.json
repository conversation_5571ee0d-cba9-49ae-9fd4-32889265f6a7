{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Авторизация CORS Access-Control-Allow-Headers иштетүүдө атайын символ (*) менен корголбойт."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Демейки Тышкы экранга чыгаруу интеграциясын -internal-media-controls-overlay-cast-button тандагычы эмес, disableRemotePlayback атрибуту аркылуу өчүрүү керек."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Төмөнкү CSS мааниси (slider-vertical) стандартташтырылган эмес жана алынып салынат."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "URL даректеринде өчүрүлгөн боштук \\(n|r|t) символдору бар жана < символдон аз булак сурамдары бөгөттөлдү. Бул булактарды жүктөө үчүн элементтердин атрибуттарынын маанилери сыяктуу жерлерден жаңы саптарды өчүрүп, азыраак символдорду коддоңуз."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() жактырылган жок, анын ордуна стандартташтырылган API'ни колдонуңуз: Өтүү убакыты 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() жактырылган жок, анын ордуна стандартташтырылган Paint Timing API'син колдонуңуз."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() жактырылган жок, анын ордуна стандартташтырылган API'ни колдонуңуз: nextHopProtocol (Өтүү убакыты 2)."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n) символу камтылган cookie файлдары кесилбестен, четке кагылат."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain параметри аркылуу бир булактын саясатын жеңилдетүү жактырылган жок жана демейки катары көрсөтүлөт. Жактырылбагандыгы тууралуу бул эскертүү document.domain параметри аркылуу иштетилген түрдүү булактарга кирүү мүмкүнчүлүгүнө тиешелүү."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Түрдүү булактардагы iframe'дерден window.alert элементин иштетүү аракети жактырылган жок жана кийинчерээк өчүрүлөт."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Түрдүү булактардагы iframe'дерден window.confirm элементин иштетүү аракети жактырылган жок жана кийинчерээк өчүрүлөт."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Маалыматты колдоо: SVGUseElement ичиндеги URL даректери эскирген жана келечекте өчүрүлөт."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() жана watchPosition() мындан ары кооптуу булактарда иштебейт. Бул функцияны колдонуу үчүн колдонмону HTTPS сыяктуу коопсуз булакка которушуңуз керек. Кеңири маалымат алуу үчүн https://goo.gle/chrome-insecure-origins баракчасын караңыз."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() жана watchPosition() кооптуу булактарда жактырылган жок. Бул функцияны колдонуу үчүн колдонмону HTTPS сыяктуу коопсуз булакка которушуңуз керек. Кеңири маалымат алуу үчүн https://goo.gle/chrome-insecure-origins баракчасын караңыз."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() мындан ары кооптуу булактарда иштебейт. Бул функцияны колдонуу үчүн колдонмону HTTPS сыяктуу коопсуз булакка которушуңуз керек. Кеңири маалымат алуу үчүн https://goo.gle/chrome-insecure-origins баракчасын караңыз."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav>, же <section> ичинде белгиленген шрифт өлчөмү жок <h1> теги табылды. Бул аталыштын текстинин көлөмү жакында бул серепчиде өзгөрөт. Кеңири маалымат алуу үчүн https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 дарегине өтүңүз."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate жактырылган жок. Анын ордуна төмөнкүнү колдонуңуз: RTCPeerConnectionIceErrorEvent.address же RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Санарип эсептик дайындарын алуу үчүн navigator.credentials.get() сурамынын бул форматы жоюлду. Жаңы форматты колдонуу үчүн чалууну жаңыртыңыз."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment кызмат иштеткичинин иш-чарасындагы арбитраждык маалымат жана сатуучунун теги жактырылган жок жана өчүрүлөт: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Вебсайт колдонуучуларынын тармактагы артыкчылыктуу позициясына байланыштуу гана кире алган тармактан кошумча булак суранды. Мындай сурамдар Интернетке коомдук эмес түзмөктөрдөн жана серверлерден кирип, башка сайттан жаcалма сурам (CSRF) менен чабуул жасоо жана/же маалыматтын ачыкталуу коркунучун жогорулатат. Мындай коркунучтарды азайтуу үчүн Chrome коомдук эмес кошумча булактарга кооптуу контексттерден жөнөтүлгөн сурамдарды жактырбай, бөгөттөй баштайт."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup() өткөрүлгөн InterestGroups дегендин dailyUpdateUrl талаасынын аталышыupdateUrl болуп өзгөртүлдү. Муну менен анын функциялары тагыраак колдонулат."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator жактырылган жок. Анын ордуна төмөнкүнү колдонуңуз: Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS'ти .css файл кеңейтүүсү менен бүтпөгөн file: URL даректеринен жүктөөгө болбойт."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Мүнөздөмөлөрү өзгөргөндүктөн, SourceBuffer.abort() аркылуу remove() шайкештирилбеген диапазонун өчүрүү жактырылган жок. Кийинчерээк колдоого алынбай калат. Анын ордуна төмөнкүнү угуңуз: updateend. abort() шайкештирилбеген медиа кошуу аракетин токтотуу же талдоочунун абалын баштапкы абалга келтирүүгө гана арналган."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Мүнөздөмөлөрү өзгөргөндүктөн, MediaSource.duration атрибутун буферленип коддолгон фреймдердин эң жогорку убакыт белгисинен ылдый коюу жактырылган жок. Кысылып буферлерген медианы жашыруун өчүрүү кийинчерээк колдоого алынбай калат. Анын ордуна бардык sourceBuffers (newDuration < oldDuration) атрибуттарында ачык-айкын remove(newDuration, oldDuration) аракетин аткарышыңыз керек."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "MIDIOptions ичинде sysex көрсөтүлбөсө да, Web MIDI аны колдонууга уруксат сурайт."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Мындан ары кооптуу булактардагы Notification API'син колдонууга болбойт. Колдонмону HTTPS сыяктуу коопсуз булакка которушуңуз керек. Кеңири маалымат алуу үчүн https://goo.gle/chrome-insecure-origins баракчасын караңыз."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Башка булактагы iframe'ден Notification API'сине уруксат суроого болбойт. Анын ордуна жогорку деңгээлдеги фреймден уруксат сурап же жаңы терезеден ачып көрүңүз."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap ичиндеги imageOrientation: 'none' параметри жактырылган жок. Анын ордуна createImageBitmap элементин '{imageOrientation: \"from-image\"}' параметри менен колдонуңуз."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Өнөктөшүңүз (D)TLS'тин эски версиясы тууралуу сүйлөшүүлөрдү жүргүзүп жатат. Аны оңдоо үчүн өнөктөшүңүз менен байланышыңыз."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Сүрөттүн, видеонун же холсттун тегдеринде overflow: visible көрсөтүү менен алардын визуалдык контенти элементтин чегинен ашып кетиши мүмкүн. Төмөнкүнү караңыз: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments жактырылган жок. Анын ордуна төлөмдөрдү иштеткичтер үчүн өз убагында орнотууну колдонуңуз."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest чалуусу Мазмундун коопсуздугу жөнүндө саясаттын (CSP) connect-src директивасын кыйгап өттү. Бул кыйгап өтүү жоюлган. PaymentRequest API'синен алынган төлөм ыкмасынын идентификаторун (supportedMethods талаасында) CSP connect-src директивасына кошуңуз."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent жактырылган жок. Анын ордуна стандарттуу navigator.storage элементин колдонуңуз."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Ата-эне элементи <picture> болгон <source src> жараксыз болгондуктан, четке кагылды. Анын ордуна төмөнкүнү колдонуңуз: <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame белгилүү бир кызмат көрсөтүүчү менен байланышкан. Анын ордуна стандарттуу cancelAnimationFrame ыкмасын колдонуңуз."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame белгилүү бир кызмат көрсөтүүчү менен байланышкан. Анын ордуна стандарттуу requestAnimationFrame ыкмасын колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen жактырылган жок. Анын ордуна Document.fullscreenElement элементин колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() жактырылган жок. Анын ордуна Element.requestFullscreen() элементин колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() жактырылган жок. Анын ордуна Element.requestFullscreen() элементин колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() жактырылган жок. Анын ордуна Document.exitFullscreen() объектин колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() жактырылган жок. Анын ордуна Document.exitFullscreen() объектин колдонуңуз."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen жактырылган жок. Анын ордуна Document.fullscreenEnabled объектин колдонуңуз."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "chrome.privacy.websites.privacySandboxEnabled API'син колдоого алууну токтотуп жатабыз. Аны М113 чыкканга чейин мурунку версияга шайкештигин камсыздоо үчүн колдонууга болот. Анын ордуна chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled жана chrome.privacy.websites.adMeasurementEnabled колдонуңуз. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled баракчасын караңыз."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement чектөөсү өчүрүлдү. Бул чектөө үчүн false маанисин көрсөттүңүз. Бул өчүрүлгөн SDES key negotiation ыкмасын колдонуу аракети катары чечмеленет. Бул функция өчүрүлгөн; анын ордуна DTLS key negotiation колдоого алынган кызматты пайдаланыңыз."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement чектөөсү өчүрүлдү. Бул чектөө үчүн true маанисин көрсөттүңүз. Анын эч кандай таасири жок, бирок тактык үчүн бул чектөөнү өчүрүп койсоңуз болот."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Кайра чалууга негизделген getStats() жактырылган жок жана өчүрүлөт. Анын ордуна мүнөздөмөлөргө ылайыктуу getStats() функциясын колдонуңуз."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() жактырылган жок. Анын ордуна Selection.modify() объектин колдонуңуз."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "URL даректеринде жалгаштырылган эсептик дайындар камтылган (мисалы, **********************/) кошумча булактардын сурамдары бөгөттөлдү."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy параметри жактырылган жок жана өчүрүлөт."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer түрдүү булактардагы изоляцияны талап кылат. Көбүрөөк маалымат алуу үчүн https://developer.chrome.com/blog/enabling-shared-array-buffer/ барагына баш багыңыз."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Колдонуучулар иштетпеген speechSynthesis.speak() жактырылган жок жана өчүрүлөт."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Чыгарып алынган окуяларды угуу блоктору эскирип калды жана алар өчүрүлөт."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer колдоно берүү үчүн кеңейтүүлөр түрдүү булактардагы изоляцияны тандашы керек. Төмөнкү баракты караңыз: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter атрибуту эскирген, анын ордуна GPUAdapterInfo isFallbackAdapter атрибутун колдонуңуз."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 XMLHttpRequest ичиндеги json сурамы тарабынан колдоого алынбайт"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Аяккы колдонуучу үчүн зыяндуу кесепеттери болгондуктан, негизги топтомдогу шайкештирилген XMLHttpRequest жактырылган жок. Көбүрөөк жардам алуу үчүн https://xhr.spec.whatwg.org/ баракчасын караңыз."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Анимация"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Элементтер колдонуучунун катышуусуз жылдырылганда калып жылып кетет. [Калыптар эмне себептен жылып келкенин иликтеңиз](https://web.dev/articles/optimize-cls), мисалы, кошулуп же өчүрүлгөн элементтер, барак жүктөлгөндө өзгөргөн ариптер."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Арип сурамы"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Киргизи<PERSON><PERSON>ен iframe терезеси"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Калыптын жылуу кластери @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Калыпты жылдырган тарап аныкталган жок"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Калып жылдырылган жок"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Калыптын жылып кетишинин себептери"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Калыптын жылып кетишинин негизги себептери"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Эң жаман кластер"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Эң жаман калыптын жылуу кластери"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL кеши"}, "models/trace/insights/Cache.ts | description": {"message": "Кештин узак иштөө мөөнөтү баракчаңызга кайталанма баш багууларды тездетет. [Кеңири маалымат](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Натыйжа<PERSON>ыз кеш эрежелери менен сурамдар жок"}, "models/trace/insights/Cache.ts | others": {"message": "Дагы {PH1}"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Сурам"}, "models/trace/insights/Cache.ts | title": {"message": "Кештин майнаптуу иштөө мөөнөтүн колдонуңуз"}, "models/trace/insights/DOMSize.ts | description": {"message": "Ири DOM стилди эсептөө жана макетти кайра жүктөө үчүн талап кылынган убакытты көбөйтүшү мүмкүн, бул барактын жооп берүүсүнө таасир этет. Ири DOM ошондой эле эстутумду колдонууну жогорулатат. [Ири DOM өлчөмүнүн алдын алуу жөнүндө кеңири маалымат алыңыз](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Элемент"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "Көпчүлүк балдар"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM тереңдиги"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Статистика"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM өлчөмүн оптималдаштыруу"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Жалпы элементтер"}, "models/trace/insights/DOMSize.ts | value": {"message": "Маа<PERSON>и"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Биринчи тармак сурамыңыз эң маанилүү.  Багыттоолорду алдын алуу, сервердин жообун тездетүү жана текстти кысуу аркылуу кечиктирүүнү азайтыңыз."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Багыттоолор болгон ({PH1} багыттоо +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Сервер жай жооп берди (ылдамдыгы: {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Кысуу колдонулган жок"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Багыттоолор жок"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Сервер тез жооп берет (ылдамдыгы: {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Текстти кысуу колдонулат"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Кайра багыттайт"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Сервердин жооп берүү убакыты"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Документти суроодо кечигүү"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Кысылбаган жүктөө"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Кай<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> байттар"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "Тармактын активдүүлүгү керектелүүчү керексиз байттарды азайтуу үчүн топтомдордон ири жана кайталанма JavaScript модулдарын өчүрүп салыңыз."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Кайталанган JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Текст такай көрүнүп турушу үчүн [font-display](https://developer.chrome.com/blog/font-display) арибин swap же optional кылып тууралаңыз. swap арибин дагы оптималдаштырып, [шрифт көрсөткүчтөрүн өзгөртүү менен калыптын жылыштарын](https://developer.chrome.com/blog/font-fallbacks) азайтса болот."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "<PERSON>р<PERSON><PERSON>"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Ариптердин көрүнүшү"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "Текке кеткен убакыт"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(жа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Адатта макет геометриясын окуган көптөгөн API'лер рендеринг кыймылдаткычын стилди жана макетти эсептөө үчүн скрипттин аткарылышын тындырууга алып келет. [Мажбурланган кайра түзүү](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) жана анын кесепеттерин азайтуу жөнүндө көбүрөөк маалымат."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Стекти трассирлөө"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Мажбу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кайра түзүү"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Жогорку функция чалуусу"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Жалпы кайра түзүү убактысы"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[атрибуту жок]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "Сүрөттөрдү жүктөө убактысын кыскартуу баракчанын жана LCP'дин жүктөө убактысын жакшыртат. [Сүрөттүн өлчөмүн оптималдаштыруу жөнүндө кеңири маалымат алыңыз](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (Болжолдуу {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Оптималдаштырылуучу сүрөттөр жок"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "Файлдын өлчөмүн оптималдаштыруу"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "Дагы {PH1}"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "Сүрөттүн көрсөтүлүшүн жакшыртуу"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Сүрөттү кысуу факторун жогорулатуу менен бул сүрөттүн жүктөп алуу өлчөмүн жакшыртууга болот."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Сүрөттүн жаңы форматын колдонуу (WebP, AVIF) же сүрөттүн кысылышын жогорулатуу менен сүрөттүн жүктөп алуу өлчөмүн жакшыртууга болот."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Бул сүрөт файлынын чен-өлчөмү ({PH1}) керек болгондон чоңураак ({PH2}). Сүрөттүн жүктөп алуу өлчөмүн кичирейтүү үчүн экранга туура келүүчү сүрөттөрдү колдонуңуз."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF'тердин ордуна видео форматтарды колдонуу менен анимацияланган контенттин жүктөп алуу өлчөмүн жакшыртууга болот."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Эң узун этапты талдоо менен баштаңыз. [Кечигүүлөрдү азайтса болот](https://web.dev/articles/optimize-inp#optimize_interactions). Иштетүү узактыгын кыскартуу үчүн [негизги агымдын чыгымдарын оптималдаштырыңыз](https://web.dev/articles/optimize-long-tasks). Бул көп учурда JS болот."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Узактыгы"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Киргизүүнүн кечигүүсү"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Аракеттешүүлөр аныкталган жок"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Эта<PERSON>"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Презентациянын кечигүүсү"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Иштетүүнүн узактыгы"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "Этап менен INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "LCP сүрөтүн HTML кодунан дароо [таба тургандай](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) оптималдаштырыңыз. [Керектелгенде жүктөөнү алдын алыңыз](https://web.dev/articles/lcp-lazy-loading)."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high applied"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high колдонулушу керек"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "керектелгенде жүктөө ылайыктуу эмес"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP сүрөтү эң эрте баштоо чекитинен кийин {PH1} жүктөлдү."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP аныкталган жок"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP сүрөт болбогондуктан, LCP булактары аныкталган жок"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Сурам баштапкы документте баарына көрүнүп турат"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP сурамын көрүү"}, "models/trace/insights/LCPPhases.ts | description": {"message": "Ар бир [этапта жакшыртуу боюнча белгилүү стратегиялар бар](https://web.dev/articles/optimize-lcp#lcp-breakdown). LCP элементтерин толук көрсөтүү үчүн талап кылынган убакыттын көбү кечиктирүүгө эмес, булактарды жүктөөгө жумшалышы керек."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Узактыгы"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Элементти көрсөтүүдө кечигүү"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "p75 талаасы"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP аныкталган жок"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Эта<PERSON>"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Булакты жүктөө кечигүүсү"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Булакты жүктөө узактыгы"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Биринчи байтка убакыт"}, "models/trace/insights/LCPPhases.ts | title": {"message": "Этап боюнча LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "Скрипт"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Коро<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> байттар"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Полифил жана трансформациялар эски серепчилерге JavaScript'тин жаңы функцияларын колдонууга мүмкүнчүлүк берет. Бирок, алардын көбү заманбап серепчилер үчүн зарыл эмес. Эски серепчилерди колдошуңуз керек болбосо, [Baseline](https://web.dev/articles/baseline-and-polyfills) функцияларын көчүрбөө үчүн JavaScript куруу процессиңизди өзгөртүп көрүңүз. [Эмне үчүн көпчүлүк сайттар ES6+ кодун көчүрбөстөн жайгаштыра аларын билип алыңыз](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Эски JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 жана HTTP/3 HTTP/1.1ге караганда мультиплекстөө сыяктуу көптөгөн артыкчылыктарды сунуштайт. [Заманбап HTTP колдонуу жөнүндө көбүрөөк билүү](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1 сурамдары колдонулган жок"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Протокол"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Сурам"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Заманбап HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Түпнуска"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Сурам"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Убакыт"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Est LCP үнөмдөөлөрү"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Колдонулбаган алдын ала туташуу. crossorigin атрибуту туура колдонулганын текшериңиз."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "Чынжырлардын узундугун кыскартуу, жүктөлүп алынган ресурстардын көлөмүн азайтуу же баракты жүктөөнү жакшыртуу үчүн керексиз ресурстарды жүктөөнү кийинкиге калтыруу аркылуу [олуттуу сурамдарды чынжырга байлабаңыз](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Эң маанилүү булактарыңызга [алдын ала туташтыруу](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) кыйытмаларын кошуңуз, бирок 4 азыраак кыйытма колдонууга аракет кылыңыз."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Талапкерлерди алдын ала туташтыруу"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Жолдун максималдуу критикалык кечигүүсү:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Тармактык көз карандылыктардын таасири тийбейт"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Бир дагы кошумча булак алдын ала туташтырууга ыңгайлуу эмес"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "булактар алдын ала туташтырылган эмес"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[алдын ала туташтыруу](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) кыйытмалары серепчиге баракты жүктөөдө байланышты эртерээк түзүүгө жардам берип, ошол булакка карата биринчи сурам аткарылганда убакытты үнөмдөйт. Төмөндө барак алдын ала туташтырылган булактар келтирилген."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Алдын ала туташтыр<PERSON><PERSON><PERSON><PERSON>н булактар"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Тармактык көз карандылык дарагы"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "4 ашуун preconnect туташуусу табылды. Аларды үнөмдөп жана эң маанилүү баштапкы булактарда гана колдонуу керек."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Колдонулбаган алдын ала туташуу. preconnect суралышы мүмкүн болгон барактардын булагында гана колдонуңуз."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "Чынжырлардын узундугун кыскартуу, жүктөлүп алынган ресурстардын көлөмүн азайтуу же баракты жүктөөнү жакшыртуу үчүн керексиз ресурстарды жүктөөнү кийинкиге калтыруу аркылуу олуттуу сурамдарды чынжырга байлабаңыз."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Сурамдар баракты алгачкы жүктөө аракетин бөгөттөп жатат. Мындай учурда LCP кечигиши мүмкүн. [Кечиктирүү же кыстаруу](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) бул тармак сурамдарынын терс таасирин четтетиши мүмкүн."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Узактыгы"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Бул навигация үчүн үлгүдөн алып тартууну бөгөттөө сурамдары жок"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Сурам"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Бөгөттөө өтүнүчтөрүн көрсөтүү"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Эгер Стилди кайра эсептөө чыгымдары жогору бойдон калса, тандагычты оптималдаштыруу аларды азайтышы мүмкүн. Узак аткаруу мөөнөтү жана жай жолдордун жогорку пайызы менен [тандагычтарды оптималдаштырыңыз](https://developer.chrome.com/docs/devtools/performance/selector-stats). Жөнөкөй же азыраак тандагычтар жана кичирээк DOM менен чыгымдарды азайтса болот."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Жал<PERSON>ы убакыт"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS тандагычынын маалыматы табылган жок. CSS тандагычынын статистикасын майнаптуулук панелинин параметрлеринен иштетүү керек."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Дал келүүлөрдү табуу аракеттери"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Дал келүүлөрдүн саны"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS тандагычтарынын чыгымы"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Мыкты тандагычтар"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Жа<PERSON><PERSON>ы"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Негизги топтомдун убакыты"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "Үчүнчү тарап"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Өткөрүлгөн маалыматтын өлчөмү"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Үчүнчү тараптын коду жүктөөнүн майнаптуулугуна олуттуу таасирин тийгизиши мүмкүн. Контентти тезирээк көрсөтүү үчүн [үчүнчү тараптын кодун жүктөөнү жайлатыңыз](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Үчүнчү тараптар табылган жок"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Үчүнчү тараптар"}, "models/trace/insights/Viewport.ts | description": {"message": "Эгер көрүнүш терезеси мобилдик түзмөк үчүн оптималдаштырылбаса, таптоо аракети [300 мсек. чейин кечигиши](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) мүмкүн."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Мобилдик түзмөктө таптоону кечиктирүү"}, "models/trace/insights/Viewport.ts | title": {"message": "Көрүнүш терезесин мобилдик түзмөк үчүн оптималдаштыруу"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET сурамы аркылуу жүктөлгөн барактарды гана кештөөгө болот."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "2XX абал коду бар барактарды гана кештөөгө болот."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome кештөө учурунда JavaScript'ти аткаруу аракетин аныктады."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner'ди суранган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "Баракты кештөө белгилөөлөр тарабынан өчүрүлдү. Бул түзмөктө иштетүү үчүн chrome://flags/#back-forward-cache бөлүмүнө баш багыңыз."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "Баракты кештөөнү буйрук сабы өчүрүп койгон."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Эстутум жетишсиз болгондуктан, баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Баракты кештөө өкүл тарабынан колдоого алынбайт."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Баракты кештөө алдын ала чагылдыргыч үчүн өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Катта<PERSON><PERSON><PERSON><PERSON> угуучулары бар BroadcastChannel үлгүсүнөн улам баракты кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store деген аталышы бар барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "Кеш атайын тазаланган."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Башка баракты кештөө үчүн бул барак кештен чыгарылды."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Плагиндерди камтыган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Media Device Dispatcher'ди колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "Башка жерге өтүү аракетинде медиа ойноткуч ойноп жаткан."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API'ни колдонгон жана ойнотуу абалы коюлган барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API'ни колдонгон жана аракет иштеткичтери коюлган барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Экрандагыны окугучтан улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler'ди колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API'ни колдонгон барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-storeколдонулган баракта cookie файлдары өчүрүлгөндүктөн, баракты кештөө өчүрүлгөн."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Дайындал<PERSON>ан жумушчуну же жумуш топтомун колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Документ чыгуу аракетине чейин жүктөлүп бүткөн жок."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "Баш жерге өтүү аракетинде колдонмонун көрнөк-жарнагы ачылып турду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Башка жерге өтүү аракетинде Chrome Сырсөздөрдү башкаргыч ачылып турду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Башка жерге өтүү аракетинде DOM дистилляция процесси жүрүп жатты."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "Башка жерге өтүү аракетинде DOM Distiller Viewer ачылып турду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Жазышуу API'син колдонгон кеңейтүүлөрдөн улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Баракты кештөөгө өтүүдөн мурун узак убакытка туташкан кеңейтүүлөр байланышты жабышы керек."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Узак убакытка туташкан кеңейтүүлөр баракты кештөөдө фреймдерге билдирүүлөрдү жөнөтүүгө аракет кылды."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Кеңейтүүлөрдөн улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Башка жерге өтүү аракетинде форманы кайра тапшыруу же http сырсөзүнүн диалогу сыяктуу модалдык диалог көрсөтүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Башке жерге өтүү аракетинде офлайн барак көрсөтүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "Башка жерге өтүү аракетинде Out-Of-Memory Intervention тилкеси ачылып турду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "Башка жерге өтүү аракетинде уруксат сурамдары чыкты."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "Башка жерге өтүү аракетинде калкып чыкма терезелерди бөгөттөгүч ачылып турду."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Башка жерге өтүү аракетинде Коопсуз серептөөнүн чоо-жайы көрсөтүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Коопсуз серептөө бул баракты орунсуз деп таап, калкып чыгуучу терезени бөгөттөдү."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Барак кештелип жатканда, кызмат иштеткичи иштетилди."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Документтеги катадан улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrames колдонулган барактарды төмөнкүдө сактоого болбойт: bfcache."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Башка баракты кештөө үчүн бул барак кештен чыгарылды."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Медианы алып ойнотууга уруксат берген барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Белгилүү бир жалгаштыр<PERSON><PERSON><PERSON><PERSON>н контенти (мисалы, PDF файлдары) бар барактар учурда кэштөөгө жарамдуу эмес."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager'ди колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "IndexedDB туташуусу ачык барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB иш-чарасынан улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "Жараксыз API'лер колдонулду."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "JavaScript кеңейтүүлөр тарабынан киргизилген барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "StyleSheet кеңейтүүлөр тарабынан киргизилген барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Ички ката."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Кандайдыр бир JavaScript тармак сурамы Cache-Control: no-storeаталышындыгы булакты алгандыктан, баракты кештөө функциясы өчүрүлгөн."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Байланышты текшерүүчү билдирүү сурамынан улам баракты кештөө өчүрүлдү."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Баскычтопту кулпулоону колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Барак башка жерге өтүү аракетине чейин жүктөлүп бүткөн жок."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Негизги булагында cache-control:no-cache бар барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Негизги булагында cache-control:no-store бар барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "Өтүү аракети барак кештөөдөн калыбына келтирилгенге чейин жокко чыгарылды."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Жигердүү тармакка туташуу өтө көп маалымат алгандыктан, барак кештен чыгарылды. Chrome барак кештелип жатканда ала турган маалыматты чектейт."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() же XHR мааниси менен барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Жигердүү тармак сурамында башка дарекке багыттама болгондуктан, барак кештөөдөн чыгарылды."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Тармакка туташуу көпкө чейин ачык тургандыктан, барак кештен чыгарылды. Chrome барак кештелип жатканда маалымат ала турган убакытты чектейт."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Жообунун аталышы жараксыз барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "Негизги фреймден тышкары фреймдеги элементтерге өтүү аракеттери."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Индекстелген DB транзакциялары бар баракты учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Жигердүү тармактык сурамы менен барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Жигердүү тармактык сурам менен барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Жигердүү тармактык сурамы менен барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Учурдагы XHR тармактык сурамы менен барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager'ди колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Cүрөттөгү сүрөт функциясын колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Printing UI көрсөтүлгөн барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "Барак \"window.open()\" аркылуу ачылган жана башка өтмөктө ага шилтеме бар, же болбосо баракта терезе ачылган."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Баракты кештөөдө үлгүдөн алып тартуу процессинде ката кетти."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Баракты кештөөдө үлгүдөн алып тартуу процесси үзгүлтүккө учурады."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Аудио жаздырууга уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Сенсорго уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Фондо шайкештирүү же алууга уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI'ге уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Билдирмеге уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Сактагычка кирүүгө уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Видео жаздырууга уруксат сураган барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "URL cхемасы HTTP/HTTPS болгон барактарды гана кештөөгө болот."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Кештелип жаткан барак кызмат иштеткичи тарабынан талап кылынды."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "Кызмат иштеткич кештелип жаткан баракка MessageEvent жөнөтүүгө аракет кылды."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Барак кештелип жатканда, ServiceWorker каттоодон чыкты."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Кызмат иштеткичи иштетилгендиктен, барак кештөөдөн чыгарылды."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome өчүрүлүп күйгүзүлүп, баракты кештөө жазууларын тазалады."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker'ни колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer'ди колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis'ти колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "Барактагы iframe аягына чыга элек өтүү аракетин баштады."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Кошумча булагында cache-control:no-cache бар барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Кошумча булагында cache-control:no-store бар барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Баракты кештөө убакыты чектен ашып, мөөнөтү бүттү."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Баракты кештөөнү күтүү мөөнөтү бүттү (баракты жашыруу иштеткичтери көпкө иштегенден болушу мүмкүн)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Негизги фреймде барактын чыгарып алынган иштеткичи бар."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Кошумча фреймде барактын чыгарып алынган иштеткичи бар."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Серепчи колдонуучу агентинин аталышын өзгөрттү."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Видео же аудио жаздырууга уруксат берген барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase'ди колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID'ни колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLocks'ту колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc'ни колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService'ты колдогон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC менен барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC колдонулгандыктан, баракты кештөө өчүрүлгөн."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare'ни колдонгон барактарды учурда кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket менен барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket колдонулгандыктан, баракты кештөө өчүрүлгөн."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport менен барактарды кештөөгө болбойт."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport колдонулгандыктан, баракты кештөө өчүрүлгөн."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR'ди колдонгон барактарды учурда кештөөгө болбойт."}}