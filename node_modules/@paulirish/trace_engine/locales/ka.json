{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS-ის მიერ Access-Control-Allow-Headers-ის დამუშავებისას ჩანაცვლების ნიშანი (*) არ მოიცავს ავტორიზაციას."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "ნაგულისხმევი Cast-ინტეგრაციის გასათიშად -internal-media-controls-overlay-cast-button ამომრჩევით სარგებლობის ნაცვლად გამოყენებული უნდა იყოს disableRemotePlayback ატრიბუტი."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "ვიზუალიზაციის CSS მნიშვნელობა slider-vertical არ არის სტანდარტიზებული და ამოიშლება."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "რესურსების მოთხოვნები, რომელთა URL-ებიც შეიცავდა როგორც შორისების გაუქმებულ \\(n|r|t) სიმბოლოებს, ისე ნაკლებობის სიმბოლოებს (<), დაბლოკილია. ეს რესურსები რომ ჩაიტვირთოს, გთხოვთ, ამოშალოთ ახალ ხაზზე გადატანები და დაურთეთ კოდირება ნაკლებობის სიმბოლოებს, მაგალითად, ელემენტების ატრიბუტების მნიშვნელობებში."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() მოძველდა. სანაცვლოდ გამოიყენეთ სტანდარტიზებული API: Navigation Timing 2."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() მოძველდა. სანაცვლოდ გამოიყენეთ სტანდარტიზებული API: Paint Timing."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() მოძველდა. სანაცვლოდ გამოიყენეთ სტანდარტიზებული API: „nextHopProtocol“ Navigation Timing 2-ში."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "ქუქი-ჩანაწერები, რომლებიც შეიცავს \\(0|r|n) სიმბოლოს, შემოკლების ნაცვლად უარყოფილი იქნება."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain-ის განსაზღვრის მეშვეობით ერთსა და იმავე წყაროსთან დაკავშირებული წესების შემსუბუქების შესაძლებლობა მოძველდა და ნაგულისხმევად გათიშული იქნება. მოძველების შესახებ ეს გაფრთხილება განკუთვნილია ჯვარედინი წარმოშობის წვდომისთვის, რომელიც ჩაირთო document.domain-ის განსაზღვრის მეშვეობით."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "window.alert-ის გაშვება iframe-ებიდან მოძველებულია და მალე ამოიშლება."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "window.confirm-ის გაშვება iframe-ებიდან მოძველებულია და მალე ამოიშლება."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "მონაცემთა მხარდაჭერა: SVGUseElement-ში URL-ების გამოყენების შესაძლებლობა მოძველებულია და მომავალში ის გაუქმდება."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() და watchPosition() აღარ მუშაობს დაუცველი წყაროების შემთხვევაში. ამ ფუნქციით სარგებლობისთვის გირჩევთ, თქვენი აპლიკაციისთვის გამოიყენოთ დაცული წყარო, როგორიცაა HTTPS. დეტალური ინფორმაციისთვის მოინახულეთ https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() და watchPosition() მოძველებულია დაუცველი წყაროების შემთხვევაში. ამ ფუნქციით სარგებლობისთვის გირჩევთ, თქვენი აპლიკაციისთვის გამოიყენოთ დაცული წყარო, როგორიცაა HTTPS. დეტალური ინფორმაციისთვის მოინახულეთ https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() აღარ მუშაობს დაუცველი წყაროების შემთხვევაში. ამ ფუნქციით სარგებლობისთვის გირჩევთ, თქვენი აპლიკაციისთვის გამოიყენოთ დაცული წყარო, როგორიცაა HTTPS. დეტალური ინფორმაციისთვის მოინახულეთ https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "მოძებნა <h1> თეგი <article>-ში, <aside>-ში, <nav>-ში ან <section>-ში, რომელსაც არ აქვს შრიფტის განსაზღვრული ზომა. ამ სათაურის ტექსტის ზონა შეიცვლება ამ ბრაუზერში ახლო მომავალში. დამატებითი ინფორმაციისთვის იხილეთ https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate მოძველდა. გთხოვთ, სანაცვლოდ, გამოიყენოთ RTCPeerConnectionIceErrorEvent.address ან RTCPeerConnectionIceErrorEvent.port."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "ეს ფორმატი ციფრული ავტორიზაციის მონაცემების ბრძანებისთვის „navigator.credentials.get()“ მოძველებულია. განაახლეთ თქვენი ბრძანება ახალი ფორმატით."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "მოვაჭრის წყაროსა და არბიტრარული მონაცემების გამოყენება canmakepayment სერვისის დამმუშავებლის მოვლენიდან მოძველდა და გაუქმდება: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "ვებსაიტმა მოითხოვა ქვერესურსი ქსელიდან, რომელზეც მხოლოდ წვდომა შეუძლია, ქსელში მისი მომხმარებლების მიერ პრივილეგირებული პოზიციის გამო. ეს მოთხოვნები ინტერნეტში ამჟღავნებს არასაჯარო მოწყობილობებსა და სერვერებს, რაც ზრდის საიტებსშორისი მოთხოვნის გაყალბებით (CSRF) თავდასხმის ან/და ინფორმაციის გაჟონვის რისკს. ამ რისკის შემცირების მიზნით, Chrome აუქმებს არასაჯარო ქვერესურსების მიმართ იმ მოთხოვნებს, რომლებიც ინიციირებულია არაუსაფრთხო კონტექსტებიდან, და დაიწყებს მათ დაბლოკვას."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "„InterestGroups“-ის ველს „dailyUpdateUrl“, რომელიც „joinAdInterestGroup()“-ს გადაეცემა, შეეცვალა სახელი „updateUrl“-ით, მის ქცევას უკეთ რომ აღწერდეს."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator მოძველდა. გთხოვთ, სანაცვლოდ, გამოიყენოთ Intl.Segmenter."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "CSS ვერ ჩაიტვირთება file: URL-ებიდან, თუ ისინი არ ბოლოვდება ფაილების .css გაფართოებით."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "remove()-ის ასინქრონული დიაპაზონის მოშორების შესაწყვეტად SourceBuffer.abort()-ის გამოყენების შესაძლებლობა მოძველდა სპეციფიკაციის ცვლილების გამო. აღნიშნული მომავალში აღარ იქნება მხარდაჭერილი. სანაცვლოდ უნდა მოისმინოთ updateend მოვლენა. abort()-ს მიზანია, შეწყვიტოს მხოლოდ ასინქრონული მედიის დამატება, ან გადააყენოთ სინტაქსიკური ანალიზის პროგრამის მდგომარეობა."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "MediaSource.duration-ის მნიშვნელობად ბუფერიზებული კოდირებული ფრეიმების პრეზენტაციის დროის ყველაზე მაღალ ანაბეჭდზე დაბალი რიცხვის განსაზღვრის შესაძლებლობა მოძველდა სპეციფიკაციის ცვლილების გამო. შემოკლებული ბუფერიზებული მედიის ფარული ამოშლა აღარ იქნება მხარდაჭერილი მომავალში. სანაცვლოდ, თქვენ მიერ უნდა შესრულდეს გამოკვეთილი remove(newDuration, oldDuration) ყველა sourceBuffers-ზე, სადაც newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI მოითხოვს გამოყენების ნებართვას იმ შემთხვევაშიც კი, თუ MIDIOptions-ში არ არის განსაზღვრული sysex."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "დაუცველი წყაროებიდან Notification API-ს გამოყენება ვეღარ მოხერხდება. გირჩევთ, თქვენი აპლიკაციისთვის გამოიყენოთ დაცული წყარო, როგორიცაა HTTPS. დეტალური ინფორმაციისთვის მოინახულეთ https://goo.gle/chrome-insecure-origins."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Notification API-სთვის ნებართვის მოთხოვნა ვეღარ მოხერხდება ჯვარედინი წარმოშობის iframe-იდან. გირჩევთ, მოითხოვოთ ნებართვა ზედა დონის ფრეიმიდან, ან სანაცვლოდ გახსნათ ახალი ფანჯარა."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "ვარიანტი imageOrientation: 'none' createImageBitmap-ში მოძველებულია. სანაცვლოდ გამოიყენეთ createImageBitmap {imageOrientation: 'from-image'} ვარიანტით."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "თქვენი პარტნიორი ათანხმებს (D)TLS-ის ხმარებიდან ამოღებულ ვერსიას. გთხოვთ, გაესაუბროთ თქვენს პარტნიორს ამ პრობლემის მოგვარების თაობაზე."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video და canvas თეგებისთვის „overflow: visible“-ის განსაზღვრით მათ მიერ, შესაძლოა, შეიქმნას ვიზუალური კონტენტი, რომელიც ელემენტების საზღვრებს სცდება. იხ. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments მოძველდა. გთხოვთ, სანაცვლოდ გამოიყენოთ JIT (just-in-time) ინსტალაცია გადახდის დამმუშავებლებისთვის."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "თქვენმა „PaymentRequest“ გამოძახებამ უგულებელყო Content-Security-Policy-ს (CSP) „connect-src“ დირექტივა. უგულებელყოფის ეს შესაძლებლობა მოძველდა. გთხოვთ, CSP-ში თქვენს „connect-src“ დირექტივას დაამატოთ გადახდის მეთოდის იდენტიფიკატორი PaymentRequest API-დან (ველში „supportedMethods“)."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent მოძველდა. გთხოვთ, სანაცვლოდ, გამოიყენოთ სტანდარტიზებული navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<source src>, რომლის მშობელიცაა <picture>, არასწორია და ამიტომ იგნორირებულია. გთხოვთ, სანაცვლოდ, გამოიყენოთ <source srcset>."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame მომწოდებლის მიხედვით სპეციფიკურია. გთხოვთ, სანაცვლოდ გამოიყენოთ სტანდარტული cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame მომწოდებლის მიხედვით სპეციფიკურია. გთხოვთ, სანაცვლოდ გამოიყენოთ სტანდარტული requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen მოძველებულია. სანაცვლოდ გამოიყენეთ Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() მოძველებულია. სანაცვლოდ გამოიყენეთ Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() მოძველებულია. სანაცვლოდ გამოიყენეთ Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() მოძველებულია. სანაცვლოდ გამოიყენეთ Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() მოძველებულია. სანაცვლოდ გამოიყენეთ Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen მოძველებულია. სანაცვლოდ გამოიყენეთ Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "API chrome.privacy.websites.privacySandboxEnabled-ს მოძველებულად ვაცხადებთ, თუმცა M113-ის გამოსვლამდე ის მოქმედი იქნება უკუთავსებადობისთვის. სანაცვლოდ გამოიყენეთ chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled და chrome.privacy.websites.adMeasurementEnabled. იხილეთ https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "შეზღუდვა DtlsSrtpKeyAgreement გაუქმებულია. ამ შეზღუდვისთვის მნიშვნელობად false მიუთითეთ, რაც ინტერპრეტირდება, როგორც გაუქმებული SDES key negotiation მეთოდის გამოყენების მცდელობა. ეს ფუნქციონალი გაუქმებულია, ამიტომ სანაცვლოდ გამოიყენეთ სერვისი, რომლის მიერაც მხარდაჭერილია DTLS key negotiation."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "შეზღუდვა DtlsSrtpKeyAgreement გაუქმებულია. ამ შეზღუდვისთვის მნიშვნელობად true მიუთითეთ, თუმცა ამას შედეგი არ ჰქონდა. სურვილისამებრ, მეტი მოწესრიგებულობისთვის, შეგიძლიათ ამოშალოთ ეს შეზღუდვა."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "უკუგამოძახების getStats() ფუნქცია მოძველებულია და მალე ამოიშლება. სანაცვლოდ გამოიყენეთ სპეციალურად თავსებადი getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() მოძველებულია. სანაცვლოდ გამოიყენეთ Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "ქვერესურსების მოთხოვნები, რომელთა URL-ებიც შეიცავს ავტორიზაციის ჩაშენებულ მონაცემებს (მაგ., **********************/), დაბლოკილია."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "ვარიანტი rtcpMuxPolicy მოძველდა და მომავალში გაუქმდება."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer-ს დასჭირდება ჯვარედინი წარმოშობის იზოლაცია. დეტალური ინფორმაციისთვის იხილეთ https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "მომხმარებლის აქტივაციის გარეშე speechSynthesis.speak()-ის გამოყენების შესაძლებლობა მოძველდა და მომავალში გაუქმდება."}, "generated/Deprecation.ts | UnloadHandler": {"message": "მოვლენათა მსმენელების განტვირთვის შესაძლებლობა მოძველდა და მალე გაუქმდება."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "გაფართოებებმა SharedArrayBuffer-ით სარგებლობა რომ გააგრძელონ, ისინი უნდა დაეთანხმონ ჯვარედინი წარმოშობის იზოლაციას. დეტალური ინფორმაციისთვის იხილეთ https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter ატრიბუტი მოძველებულია, მის ნაცვლად GPUAdapterInfo isFallbackAdapter ატრიბუტი გამოიყენეთ."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16 მხარდაუჭერელია response.json-ის მიერ XMLHttpRequest-ში"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "მთავარ ნაკადში სინქრონული XMLHttpRequest-ის გამოყენება მოძველდა საბოლოო მომხმარებლისთვის ხელმისაწვდომ ფუნქციონალზე უარყოფითი ზეგავლენის გამო. დამატებით ინფორმაციას გაეცანით აქ: https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "ანიმაცია"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "განლაგების ცვლილება ხდება ელემენტების მოძრაობისას, მომხმარებლის ინტერაქციის გარეშე. [გამოიკვლიეთ განლაგების ცვლილებების მიზეზები](https://web.dev/articles/optimize-cls), როგორიცაა, დამატებული თუ ამოშლილი ელემენტები, ან მათი შრიფტები, რომლებიც გვერდის ჩატვირთვის დროს იცვლება."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "შრიფტის მოთხოვნა"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "შეტანილი iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "განლაგების ცვლილების კლასტერი @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "განლაგების ცვლილების მიზეზების აღმოჩენა ვერ მოხერხდა"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "განლაგების ცვლილება არ არის"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "განლაგების ცვლილების მიზეზი"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "განლაგების წანაცვლების მთავარი მიზეზები"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "სურათის ზომაუცვლელი ელემენტი"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "ყველაზე ცუდი კლასტერი"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "უარესი განლაგების შეცვლის კლასტერი"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "ქეშირების TTL"}, "models/trace/insights/Cache.ts | description": {"message": "გრძელვადიანმა ქეშირებამ შეიძლება დააჩქაროს თქვენს გვერდზე განმეორებითი სტუმრობები. [შეიტყვეთ მეტი](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "ქეშირების არაეფექტური წესებით მოსარგებლე მოთხოვნები არ არის"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} სხვა"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "მოთხოვნა"}, "models/trace/insights/Cache.ts | title": {"message": "გამოიყენეთ ქეშირების ეფექტური დრო"}, "models/trace/insights/DOMSize.ts | description": {"message": "დიდმა DOM-მა შეიძლება გაზარდოს სტილის კალკულაციისა და განლაგების ხელახალი ფორმატირების ხანგრძლივობა, რაც გავლენას მოახდენს გვერდის ადაპტურობაზე. დიდმა DOM-მა შეიძლება გაზარდოს მეხსიერების გამოყენებაც. [შეიტყვეთ, როგორ აირიდოთ თავიდან DOM-ის ზომის გაზრდა](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "ელემენტი"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "დამოკიდებული ელემენტების უმეტესობა"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM სიღრმე"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "სტატისტიკა"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM ზომის ოპტიმიზაცია"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "სულ ელემენტები"}, "models/trace/insights/DOMSize.ts | value": {"message": "მნიშვნელობა"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "ქსელის პირველი მოთხოვნა ყველაზე მნიშვნელოვანია.  შეამცირეთ მისი რეაგირების დრო გადამისამართებების პრევენციით, სერვერის სწრაფი პასუხის უზრუნველყოფითა და ტექსტის შეკუმშვის აქტივაციით."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "იყო გადამისამართებები ({PH1} გადამისამართებები, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "სერვერის პასუხი იყო ნელი (დაფიქსირდა {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "შეკუმშვა არ გააქტიურდა"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "თავიდან ირიდებს გადამისამართებებს"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "სერვერი სწრაფად რეაგირებს (დაფიქსირდა {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "გაააქტიურებს ტექსტის შეკუმშვას"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "ამისამართებს"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "სერვერის რეაგირების დრო"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "დოკუმენტის მოთხოვნაზე რეაგირების დრო"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "შეუკუმშავი ჩამოტვირთვა"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "დუბლირებული ბაიტები"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "წყარო"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "ამოშალეთ JavaScript-ის დიდი, დუბლირებული მოდულები ნაკრებებიდან, რომ შეამციროთ ქსელის აქტივობის მიერ მოხმარებული არასაჭირო ბაიტები."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "დუბლირებული JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "გაითვალისწინეთ [font-display](https://developer.chrome.com/blog/font-display)-ის დაყენება swap-ზე ან optional-ზე, რომ უზრუნველყოთ ტექსტის მუდმივი ხილვადობა. swap-ის მეტად ოპტიმიზაცია შესაძლებელია, რათა შეამციროთ განლაგების ცვლილებები [შრიფტის მეტრიკის უგულებელყოფით](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "შრიფტი"}, "models/trace/insights/FontDisplay.ts | title": {"message": "შრიფტის ჩვენება"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "დახარჯული დრო"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(ანონიმური)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "ბევრი API, რომლებიც, როგორც წესი, კითხულობს განლაგების გეომეტრიას, აიძულებს რენდერის ძრავას, დააპაუზოს სკრიპტის შესრულება, რათა გამოითვალოს სტილი და განლაგება. შეიტყვეთ მეტი [ხელახალი დაფორმატების იძულებისა](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) და მისი შემსუბუქების შესახებ."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "სტეკის ტრასირება"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "იძულებითი ხელახალი დაფორმატება"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "ტოპ ფუნქციის გამოძახება"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "ხელახალი დაფორმატების ჯამური დრო"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[ატრიბუტების გარეშე]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "სურათების ჩამოტვირთვის დროის შემცირებამ შეიძლება გააუმჯობესოს გვერდისა და LCP-ს ჩატვირთვის წინასწარ განსაზღვრული დრო. [შეიტყვეთ მეტი სურათის ზომის ოპტიმიზაციის შესახებ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (დაახლ. {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "ოპტიმიზებადი სურათები არ არის"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "ფაილის ზომის ოპტიმიზება"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} სხვა"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "სურათის მიწოდების გაუმჯობესება"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "სურათის შეკუმშვის კოეფიციენტის გაზრდამ შეიძლება გააუმჯობესოს სურათის ჩამოსატვირთი ზომა."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "სურათის თანამედროვე ფორმატის (WebP, AVIF) გამოყენებამ ან სურათის შეკუმშვის გაზრდამ შეიძლება გააუმჯობესოს სურათის ჩამოსატვირთი ზომა."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "ამ სურათის ფაილი იმაზე დიდია ({PH1}), ვიდრე საჭიროა ნაჩვენები რეზოლუციისთვის ({PH2}). გამოიყენეთ ადაპტური სურათები, რომ შეამციროთ ფაილის ჩამოსატვირთი ზომა."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF-ის ნაცვლად ვიდეოს ფორმატების გამოყენებამ შეიძლება გააუმჯობესოს ანიმაციური კონტენტის ჩამოსატვირთი ზომა."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "დაიწყეთ გამოკვლევა ყველაზე გრძელი ფაზით. [დაყოვნებების მინიმიზაცია შესაძლებელია](https://web.dev/articles/optimize-inp#optimize_interactions). დამუშავების ხანგრძლივობის შესამცირებლად საჭიროა [ძირითადი ნაკადის ხარჯების შემცირება](https://web.dev/articles/optimize-long-tasks), ხშირად JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "ხანგრძლივობა"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "შეყვანის დაყოვნება"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "ინტერაქციები არ არის აღმოჩენილი"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "ფაზა"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "პრეზენტაციის დაყოვნება"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "დამუშავების ხანგრძლივობა"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP ფაზების მიხედვით"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "მოახდინეთ LCP-ს ოპტიმიზება, რისთვისაც LCP სურათი [აღმოჩენადი](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) უნდა გახადოთ HTML-იდან და თავიდან აირიდოთ [ასინქრონული ჩატვირთვა](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "გამოყენებულია fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "გამოყენებული უნდა იყოს fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "ასინქრონული ჩატვირთვის გამოყენება ვერ მოხერხდა"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP გამოსახულება ჩაიტვირთა უადრეს საწყის წერტილზე {PH1}-ით გვიან."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "LCP-ს აღმოჩენა ვერ მოხერხდა"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP-ს რესურსი არ აღმოჩნდა, რადგან LCP არ არის სურათი"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "მოთხოვნის აღმოჩენა შესაძლებელია თავდაპირველ დოკუმენტში"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP მოთხოვნის აღმოჩენა"}, "models/trace/insights/LCPPhases.ts | description": {"message": "თითოეულ [ფაზას აქვს გაუმჯობესების კონკრეტული სტრატეგია](https://web.dev/articles/optimize-lcp#lcp-breakdown). იდეალურ შემთხვევაში, LCP დროის უმეტესი ნაწილი უნდა დაიხარჯოს რესურსების ჩატვირთვაზე, დაყოვნების გარეშე."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "ხანგრძლივობა"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "ელემენტის რენდერის დაყოვნება"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "ველი p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "LCP-ს აღმოჩენა ვერ მოხერხდა"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "ფაზა"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "რესურსების ჩატვირთვის შეყოვნება"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "რესურსების ჩატვირთვის ხანგრძლივობა"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "დრო პირველ ბაიტამდე"}, "models/trace/insights/LCPPhases.ts | title": {"message": "ფრაზის LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "სცენარი"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "გახარჯული ბაიტები"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "პოლიფილებისა და გარდაქმნების საშუალებით მოძველებული ბრაუზერები JavaScript-ის ახალ ფუნქციებს იყენებს. მიუხედავად ამისა, ბევრი მათგანი არ არის საჭირო თანამედროვე ბრაუზერებისთვის. განიხილეთ JavaScript-ის აწყობის პროცესის მოდიფიცირება, რათა არ მოხდეს [საბაზისო](https://web.dev/articles/baseline-and-polyfills) ფუნქციების კოდების ტრანსპილირება, თუ არ იცით, რომ მოძველებულ ბრაუზერებს უნდა დაუჭიროთ მხარი. [შეიტყვეთ, რატომ შეუძლია საიტების უმეტესობას ES6+ კოდის ჩასმა ტრანსპილირების გარეშე](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "მოძველებული JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 და HTTP/3 გთავაზობთ მრავალ უპირატესობას, ვიდრე HTTP/1.1, მაგალითად, მულტიპლექსირებას. [შეიტყვეთ მეტი თანამედროვე HTTP-ს გამოყენების შესახებ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "არცერთი მოთხოვნა არ იყენებს HTTP/1.1-ს"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "პროტოკოლი"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "მოთხოვნა"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "თანამედროვე HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "წყარო"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "მოთხოვნა"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "წყარო"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "დრო"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Est LCP დაზოგვა"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "გამოუყენებელი წინასწარი დაკავშირება. შეამოწმეთ, სწორად არის თუ არა crossorigin ატრიბუტი გამოყენებული."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[თავიდან აირიდეთ კრიტიკული მოთხოვნები](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) ჯაჭვების სიგრძის შემცირებით, რესურსების ჩამოტვირთვის ზომის შემცირებით ან არასასურველი რესურსების ჩამოტვირთვის გადადებით, რათა ხელი შეუწყოთ გვერდის დატვირთვის გაუმჯობესებას."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "დაამატეთ [წინასწარი დაკავშირების](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) მინიშნებები ყველაზე მნიშვნელოვან წყაროებს, მაგრამ სცადეთ, რომ 4-ზე მეტი არ გამოიყენოთ."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "წინასწარი დაკავშირების კანდიდატები"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "მდებარეობის რეაგირების მაქსიმალური კრიტიკული დრო:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "რენდერის ამოცანებზე გავლენას არ ახდენს ქსელის სქემა"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "დამატებითი წყაროები არ არის კარგი კანდიდატები წინასწარი დაკავშირებისთვის"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "წყაროები არ არის წინასწარ დაკავშირებული"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[წინასწარი დაკავშირების](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) მინიშნებები ეხმარება ბრაუზერს, ჩამოაყალიბოს დაკავშირება გვერდის ჩატვირთვის ადრეულ ეტაპზე, რაც დაზოგავს დროს, როდესაც ამ წყაროსთვის პირველი მოთხოვნა შესრულდება. ქვემოთ მოცემულია ის წყაროები, რომლებთანაც გვერდი წინასწარ არის დაკავშირებული."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "წინასწარ დაკავშირებული წყაროები"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "ქსელის დამოკიდებულების სქემა"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "ნაპოვნია 4-ზე მეტი preconnect კავშირი. ისინი უნდა გამოიყენონ ეკონომიურად და მხოლოდ ყველაზე მნიშვნელოვან წყაროებში."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "გამოუყენებელი წინასწარი დაკავშირება. preconnect მხოლოდ იმ წყაროებისთვის გამოიყენეთ, რომლებსაც გვერდი, სავარაუდოდ, მოითხოვს."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "თავიდან აირიდეთ კრიტიკული მოთხოვნები ჯაჭვების სიგრძის შემცირებით, რესურსების ჩამოტვირთვის ზომის შემცირებით ან არასასურველი რესურსების ჩამოტვირთვის გადადებით, რათა ხელი შეუწყოთ გვერდის დატვირთვის გაუმჯობესებას."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "მოთხოვნები ბლოკავს გვერდის საწყის რენდერს, რამაც შეიძლება დააყოვნოს LCP. [გაწევამ ან ჩაშენებამ](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) შეიძლება ეს მოთხოვნები კრიტიკული მდებარეობიდან გადაიტანოს."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "ხანგრძლივობა"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "ამ ნავიგაციისთვის რენდერის დაბლოკვის მოთხოვნები არ არის"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "მოთხოვნა"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "რენდერის დაბლოკვის მოთხოვნები"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "თუ სტილის გადათვლის ხარჯი მაღალი დარჩება, ამომრჩევის ოპტიმიზაცია შეამცირებს მას. [მოახდინეთ სელექტორების ოპტიმიზაცია](https://developer.chrome.com/docs/devtools/performance/selector-stats) დარჩენილი დროისა და შენელებული გადასვლის %-ით. დამთხვევის ხარჯებს შეამცირებს უფრო მარტივი ამომრჩევები, ნაკლები ამომრჩევი, უფრო პატარა DOM და უფრო ცარიელი DOM."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "გასული დრო"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS ამომრჩევის მონაცემები არ მოიძებნა. საჭიროა ეფექტურობის პანელის პარამეტრებიდან CSS ამომრჩევის სტატისტიკის ჩართვა."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "დამთხვევის მცდელობები"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "შესატყვისი ელემენტების რაოდენობა"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS ამომრჩევის ხარჯები"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "ტოპ სელექტორები"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "სულ"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "ძირითადი ნაკადის დრო"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "მე-3 მხარე"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "გადაცემის მოცულობა"}, "models/trace/insights/ThirdParties.ts | description": {"message": "მე-3 მხარის კოდებმა შეიძლება მნიშვნელოვნად იმოქმედოს ჩატვირთვაზე. [შეამცირეთ და გადადეთ მე-3 მხარის კოდების ჩატვირთვა](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/), რომ პრიორიტეტი მიანიჭოთ თქვენი გვერდის კონტენტს."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "მესამე მხარეები არ მოიძებნა"}, "models/trace/insights/ThirdParties.ts | title": {"message": "მე-3 მხარეები"}, "models/trace/insights/Viewport.ts | description": {"message": "შეხების ინტერაქცია შეიძლება [დაგვიანდეს 300 მწმ-ით](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/), თუ ხედის ფანჯარა არ არის ოპტიმიზებული მობილურისთვის."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "მობილური შეხების დაყოვნება"}, "models/trace/insights/Viewport.ts | title": {"message": "მობილურის ხედის ფანჯრის ოპტიმიზაცია"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "მხოლოდ GET მოთხოვნის მეშვეობით ჩატვირთული გვერდები შეძლებენ უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "შესაძლებელია მხოლოდ იმ გვერდების ქეშირება, რომელთაც გააჩნიათ სტატუსის კოდი 2XX."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome-მა ქეშში მყოფ გვერდზე JavaScript-ის შესრულების მცდელობა აღმოაჩინა."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "გვერდები, რომლებმაც მოითხოვეს AppBanner, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "უკან-წინ გადასვლის ქეში გათიშულია ალმის სასარგებლოდ. ამ მოწყობილობაზე ადგილობრივად მის ჩასართავად გადადით მისამართზე chrome://flags/#back-forward-cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "უკან-წინ გადასვლის ქეში გათიშულია ბრძანებათა სტრიქონის მიერ."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "არასაკმარისი მეხსიერების გამო უკან-წინ გადასვლის ქეში გათიშულია."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "უკან-წინ გადასვლის ქეში მხარდაჭერილი არ არის მინდობილი პირის მიერ."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "უკან-წინ გადასვლის ქეში გათიშულია წინასწარი ჩამტვირთველისთვის."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "გვერდის ქეშირება შეუძლებელია, რადგან მას აქვს BroadcastChannel-ის ეგზემპლარი რეგისტრირებული მსმენელისთვის."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "გვერდები, რომელთაც აქვთ cache-control:no-store სათაური, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "ქეში საგანგებოდ გაიწმინდა."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "გვერდი წაიშალა ქეშიდან, სხვა გვერდის ქეშირების მიზნით."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "გვერდები, რომელთაც აქვს დანამატები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "განუსაზღვრელი"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "გვერდები, რომლებიც იყენებს FileChooser API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "გვერდები, რომლებიც იყენებს File System Access API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "გვერდები, რომლებიც იყენებს Media Device Dispatcher-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "მედიადამკვრელი უკრავდა სხვა გვერდზე გადასვლისას."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "გვერდები, რომლებიც იყენებს MediaSession API-ს და რომლებსაც არჩეული აქვს დაკვრის მდგომარეობა, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "გვერდები, რომლებიც იყენებს MediaSession API-ს და დაყენებული აქვს მოქმედების დამმუშავებლები, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "უკან-წინ გადასვლის ქეში გათიშულია ეკრანის წამკითხველის გამო."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "გვერდები, რომლებიც იყენებს SecurityHandler-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "გვერდები, რომლებიც იყენებს Serial API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "გვერდები, რომლებიც იყენებს WebAuthetication API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "გვერდები, რომლებიც იყენებს WebBluetooth API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "გვერდები, რომლებიც იყენებს WebUSB API-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "უკან-წინ გადასვლის ქეში გამორთულია, რადგან ქუქი-ჩანაწერები გამორთულია იმ გვერდზე, რომელიც იყენებს „Cache-Control: no-store“-ს."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "გვერდები, რომლებიც იყენებს Dedicated Worker-ს ან Worklet-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "გვერდის ჩატვირთვა არ დასრულებულა მისგან გამოსვლამდე."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო აპის ბანერი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო Chrome პაროლების მმართველი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "სხვაგან გადასვლისას მიმდინარეობდა DOM დისტილაცია."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო DOM დისტილატორის მნახველი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "უკან-წინ გადასვლის ქეში გათიშულია, რადგან გაფართოებები იყენებს შეტყობინებების მიმოცვლის API-ს."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "უკან-წინ გადასვლის ქეშში შესვლამდე გაფართოებებმა, რომელთა კავშირიც დიდი ხნის განმავლობაში იყო აქტიური, უნდა დახურონ კავშირი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "გაფართოებებმა, რომელთა კავშირიც დიდი ხნის განმავლობაში იყო აქტიური, ცადეთ შეტყობინებების გაგზავნა ფრეიმებისთვის უკან-წინ გადასვლის ქეშში."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "უკან-წინ გადასვლის ქეში გათიშულია გაფართოებების გამო."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "სხვაგან გადასვლისას ნაჩვენები იყო მოდალური დიალოგი, მაგ., ფორმის ხელახლა გადაგზავნა ან HTTPS პაროლთან დაკავშირებული დიალოგი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "სხვაგან გადასვლისას ნაჩვენები იყო ხაზგარეშე გვერდი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო მეხსიერების ამოწურვასთან დაკავშირებული ჩარევის ზოლი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო ნებართვების მოთხოვნები."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "სხვაგან გადასვლისას წარმოდგენილი იყო ამომხტარი ფანჯრების მბლოკავი."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "სხვაგან გადასვლისას ნაჩვენები იყო Safe Browsing-ის დეტალები."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing-მა ეს გვერდი არასათანადოდ მიიჩნია და ამომხტარი ფანჯარა დაბლოკა."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "სერვისის დამმუშავებელი გააქტიურდა გვერდის უკან-წინ გადასვლის ქეშშში ყოფნისას."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "უკან-წინ გადასვლის ქეში გათიშულია დოკუმენტის შეცდომის გამო."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "გვერდები, რომლებიც იყენებს FencedFrames-ს, ვერ შეინახება bfcache-ში."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "გვერდი წაიშალა ქეშიდან, სხვა გვერდის ქეშირების მიზნით."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "გვერდები, რომლებმაც მედია ნაკადზე წვდომა მიიღო, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "გვერდებზე, რომლებიც შეიცავს გარკვეული ტიპის ჩაშენებულ კონტენტს (მაგ.: PDF-ები), ამჟამად არ ვრცელდება უკან-წინ გადასვლის ქეშირება."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "გვერდები, რომლებიც იყენებს IdleManager-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "გვერდები, რომელთაც აქვს ღია IndexedDB კავშირი, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "უკან-წინ გადასვლის ქეში გათიშულია IndexedDB მოვლენის გამო."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "გამოყენებულ იქნა შეუსაბამო API-ები."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "გვერდები, რომლებშიც JavaScript ინერგება გაფართოებების მიხედვით, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "გვერდები, რომლებშიც StyleSheet ინერგება გაფართოებების მიხედვით, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "შიდა შეცდომა."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "უკან-წინ გადასვლის ქეში გამორთულია, რადგან JavaScript-ის ქსელის ზოგიერთმა მოთხოვნამ მიიღო რესურსი სათაურით „Cache-Control: no-store“."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "უკან-წინ გადასვლის ქეში გათიშულია კავშირის შენარჩუნების მოთხოვნის გამო."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "გვერდები, რომლებიც იყენებს კლავიატურის ჩაკეტვის შესაძლებლობას, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "გვერდის ჩატვირთვა არ დასრულებულა მისგან გამოსვლამდე."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "გვერდები, რომელთა მთავარ რესურსს აქვს cache-control:no-cache, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "გვერდები, რომელთა მთავარ წყაროს გააჩნია cache-control:no-store, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "ნავიგაცია გაუქმდა უკან-წინ გადასვლის ქეშიდან გვერდის აღდგენამდე."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "გვერდი ამოღებულია ქეშიდან, რადგან ქსელთან მოქმედმა კავშირმა ძალიან ბევრი მონაცემი მიიღო. Chrome ზღუდავს მონაცემთა რაოდენობას, რომლის მიღებაც ქეშირებულ გვერდს შეუძლია."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch-ზე ან XHR-ზე მოთხოვნის მქონე გვერდები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "გვერდი წაიშალა უკან-წინ გადასვლის ქეშიდან, რადგან ქსელის აქტიური მოთხოვნა საჭიროებდა გადამისამართებას."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "გვერდი წაიშალა ქეშიდან, რადგან ქსელთან დაკავშირება დიდხანს გაგრძელდა. Chrome ზღუდავს დროს, რომლის განმავლობაშიც გვერდმა შეიძლება მიიღოს მონაცემები ქეშირების დროს."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "გვერდები, რომელთაც არ აქვს პასუხის მოქმედი სათაური, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "ნავიგაცია განხორციელდა არამთავარ ფრეიმში."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "გვერდი, რომელსაც აქვს DB-ს მიმდინარე ინდექსირებული ტრანსაქციები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "გვერდები, რომლებმაც ქსელზე მოთხოვნა გააგზავნა, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "fetch ქსელზე მოთხოვნის მქონე გვერდები ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "გვერდები, რომლებმაც ქსელზე მოთხოვნა გააგზავნა, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR ქსელში გაგზავნილი მოთხოვნების მქონე გვერდები ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "გვერდები, რომლებიც იყენებს PaymentManager-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "გვერდები, რომლებიც იყენებს ფუნქციას „ეკრანი ეკრანში“, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "გვერდები, რომლებზეც აისახება ბეჭდვის ინტერფეისი, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "გვერდი გაიხსნა „window.open()“-ის გამოყენებით, ხოლო სხვა ჩანართს აქვს მასზე მითითება, ან გვერდის მეშვეობით გაიხსნა ფანჯარა."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "უკან-წინ გადასვლის ქეშში გვერდის დამუშავების პროცესში ის გაიჭედა."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "უკან-წინ გადასვლის ქეშში ამ გვერდის დამუშავების პროცესი დასრულდა."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "გვერდები, რომლებმაც მოითხოვა აუდიოს აღბეჭდვის ნებართვები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "გვერდები, რომლებმაც მოითხოვეს სენსორის ნებართვები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "გვერდები, რომლებმაც მოითხოვა ფონურ რეჟიმში სინქრონიზაცია ან მონაცემთა მიღება, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "გვერდები, რომლებმაც მოითხოვა MIDI-ნებართვები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "გვერდები, რომლებმაც მოითხოვა MIDI-შეტყობინებები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "გვერდები, რომლებმაც მოითხოვა მეხსიერებაზე წვდომა, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "გვერდები, რომლებმაც მოითხოვა ვიდეოს აღბეჭდვის ნებართვები, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "მხოლოდ იმ გვერდების ქეშირება არის შესაძლებელი, რომელთა URL სქემაც არის: HTTP / HTTPS."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "ამ გვერდზე პრეტენზია გამოაცხადა სერვისის დამმუშავებელმა, ის კი უკან-წინ გადასვლის ქეშშია დამატებული."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "სერვისის დამმუშავებელი შეეცადა, გვერდი უკან-წინ გადასვლის ქეშში გაეგზავნა MessageEvent."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "ServiceWorker-ის რეგისტრაცია გაუქმდა გვერდის უკან-წინ გადასვლის ქეშში ყოფნისას."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "გვერდი ამოღებულია უკან-წინ გადასვლის ქეშიდან, სერვისის დამმუშავებლის გააქტიურების გამო."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome გადაიტვირთა და უკან-წინ გადასვლის ქეშის ჩანაწერები წაშალა."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "გვერდები, რომლებიც იყენებს SharedWorker-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "გვერდები, რომლებიც იყენებს SpeechRecognizer-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "გვერდები, რომლებიც იყენებს SpeechSynthesis-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "iframe-მა დაიწყო ნავიგაცია გვერდზე, რომელიც ჯერ ვერ დასრულდა."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "გვერდები, რომელთა ქვერესურსს აქვს cache-control:no-cache, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "გვერდები, რომელთა ქვერესურსსაც აქვს cache-control:no-store, ვერ დაემატება უკან-წინ გადასვლის ქეშში."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "გვერდმა გადააჭარბა უკან-წინ გადასვლის ქეშში ყოფნის ზღვრულ დროს და მოქმედების ვადა ამოეწურა."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "უკან-წინ გადასვლის ქეშში გვერდის დამატებისას მოლოდინის დრო ამოიწურა (სავარაუდოდ, გვერდის დამალვის დამმუშავებლების ხანგრძლივი მუშაობის გამო)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "გვერდს მთავარ ფრეიმში გააჩნია განტვირთვის დამმუშავებელი."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "გვერდს ქვეფრეიმში აქვს განტვირთვის დამმუშავებელი."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "ბრაუზერმა შეცვალა User Agent-ის უგულებელყოფის სათაური."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "გვერდები, რომელთაც მინიჭებული აქვს წვდომა ვიდეოს ან აუდიოს ჩაწერაზე, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "გვერდები, რომლებიც იყენებს WebDatabase-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "გვერდები, რომლებიც იყენებს WebHID-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "გვერდები, რომლებიც იყენებს WebLocks-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "გვერდები, რომლებიც იყენებს WebNfc-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "გვერდები, რომლებიც იყენებს WebOTPService-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC-ის მქონე გვერდების უკან-წინ გადასვლის ქეშში დამატება ვერ მოხერხდება."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "უკან-წინ გადასვლის ქეში გათიშულია, რადგან გამოყენებული იყო WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "გვერდები, რომლებიც იყენებს WebShare-ს, ამჟამად ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket-ის გვერდების უკან-წინ გადასვლის ქეშში დამატება შეუძლებელია."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "უკან-წინ გადასვლის ქეში გათიშულია, რადგან გამოყენებული იყო WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport-ის მქონე გვერდები ვერ მოხვდება უკან-წინ გადასვლის ქეშში."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "უკან-წინ გადასვლის ქეში გათიშულია, რადგან გამოყენებული იყო WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "გვერდები, რომლებიც იყენებს WebXR-ს, ვერ შეძლებს უკან-წინ გადასვლის ქეშის გამოყენებას."}}