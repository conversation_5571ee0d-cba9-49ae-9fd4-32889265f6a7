{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "CORS Access-Control-Allow-Headers 처리 시 와일드 카드 기호(*)를 사용하면 승인되지 않습니다."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "기본 Cast 통합을 사용 중지하려면 -internal-media-controls-overlay-cast-button 선택기 대신 disableRemotePlayback 속성을 사용해야 합니다."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS 모양 값(slider-vertical)이 표준화되지 않았으며 삭제될 예정입니다."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "삭제된 공백 \\(n|r|t)자 및 글자수 제한(<)이 URL에 포함된 리소스 요청이 차단되었습니다. 이러한 리소스를 로드하려면 엘리먼트 속성 값과 같은 위치에서 줄바꿈을 삭제하고 글자수 제한을 인코딩하세요."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes()는 지원 중단되었습니다. 대신 표준화된 API인 Navigation Timing 2를 사용하세요."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes()는 사용 중지되었습니다. 대신 표준화된 API인 Paint Timing을 사용하세요."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes()는 지원 중단되었습니다. 대신 nextHopProtocol이 추가된 표준화된 API인 Navigation Timing 2를 사용하세요."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "\\(0|r|n)자를 포함하는 쿠키는 잘리는 대신 거부됩니다."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "document.domain 설정을 통한 동일 출처 정책의 해제는 지원 중단되었으며 기본적으로 사용 중지될 예정입니다. 이 지원 중단 경고는 document.domain 설정에 의해 사용 설정된 교차 출처 액세스에 적용됩니다."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "교차 출처 iframe에서의 window.alert 트리거는 지원 중단되었으며 앞으로 삭제될 예정입니다."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "교차 출처 iframe에서의 window.confirm 트리거는 지원 중단되었으며 앞으로 삭제될 예정입니다."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "데이터 지원: SVGUseElement의 URL은 지원 중단되며 향후 삭제될 예정입니다."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() 및 watchPosition()은 더 이상 안전하지 않은 출처에서 작동하지 않습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "안전하지 않은 출처의 getCurrentPosition() 및 watchPosition()은 지원 중단되었습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia()는 더 이상 안전하지 않은 출처에서 작동하지 않습니다. 이 기능을 사용하려면 애플리케이션을 HTTPS와 같은 안전한 출처로 전환하는 것을 고려해야 합니다. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "<article>, <aside>, <nav> 또는 <section> 내에 지정된 글꼴 크기가 없는 <h1> 태그가 있습니다. 이 브라우저에서 이 제목 텍스트의 크기가 조만간 변경될 예정입니다. 자세한 내용은 다음을 참고하세요. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate는 지원 중단되었습니다. 대신 RTCPeerConnectionIceErrorEvent.address 또는 RTCPeerConnectionIceErrorEvent.port를 사용하세요."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "디지털 사용자 인증 정보에 대한 navigator.credentials.get() 요청의 이 형식은 지원 중단되었으므로 호출을 업데이트하여 새 형식을 사용하세요."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment 서비스 워커 이벤트의 판매자 원본 및 임의 데이터인 topOrigin, paymentRequestOrigin, methodData, modifiers가 지원 중단되었으며 삭제될 예정입니다."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "웹사이트에서 사용자 권한이 지정된 네트워크 위치로 인해 해당 웹사이트만 액세스 가능한 네트워크의 하위 리소스를 요청했습니다. 이러한 요청은 비공개 기기 및 서버를 인터넷에 노출시켜 크로스 사이트 요청 위조(CSRF) 공격 또는 정보 유출의 위험을 높입니다. 위험을 완화하기 위해 Chrome은 안전하지 않은 컨텍스트에서 시작된 비공개 하위 리소스에 대한 요청을 사용 중지하고 차단하기 시작합니다."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "joinAdInterestGroup()에 전달된 InterestGroups의 dailyUpdateUrl 필드 이름이 동작을 더 정확하게 반영하도록 updateUrl로 변경되었습니다."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Intl.v8BreakIterator는 지원 중단되었습니다. 대신 Intl.Segmenter를 사용하세요."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": ".css 파일 확장자로 끝나지 않는 CSS는 file: URL에서 로드할 수 없습니다."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "사양 변경으로 인해 SourceBuffer.abort()를 사용하여 remove() 비동기 범위 삭제를 취소하는 기능의 지원이 중단됩니다. 지원은 향후 삭제될 예정입니다. 대신 updateend 이벤트를 수신 대기해야 합니다. abort()는 비동기 미디어 추가 항목을 취소하거나 파서 상태를 초기화하는 데만 사용하도록 만들어졌습니다."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "사양 변경으로 인해, MediaSource.duration을 버퍼링된 코드 프레임의 최고 타임스탬프보다 낮게 설정하는 기능이 지원 중단되었습니다. 버퍼링된 미디어의 잘린 부분을 암시적으로 삭제하는 기능에 대한 지원은 앞으로 삭제될 예정입니다. newDuration < oldDuration의 경우 대신 모든 sourceBuffers에서 명시적 remove(newDuration, oldDuration)을 수행해야 합니다."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "SysEx가 MIDIOptions에 지정되어 있지 않은 경우에도 사용할 수 있도록 웹 MIDI가 권한을 요청합니다."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "안전하지 않은 출처의 Notification API는 더 이상 사용할 수 없습니다. 애플리케이션을 HTTPS와 같이 안전한 출처로 전환해 보세요. 자세한 내용은 다음 페이지를 참고하세요. https://goo.gle/chrome-insecure-origins"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "더 이상 교차 출처 iframe에서 Notification API 권한을 요청할 수 없습니다. 최상위 프레임에서 권한을 요청하거나 대신 새로운 창을 여는 방법을 고려해야 합니다."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap의 imageOrientation: 'none' 옵션은 지원 중단됩니다. 대신 '{imageOrientation: 'from-image'}' 옵션으로 createImageBitmap을 사용하시기 바랍니다."}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "파트너가 지원 중단된 (D)TLS 버전을 협상합니다. 파트너에게 확인하여 이를 해결하세요."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "img, video, canvas 태그에 overflow: visible을(를) 지정하면 요소 경계 외부에 시각적 콘텐츠가 생성될 수 있습니다. 다음을 참고하세요. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments 지원이 중단됩니다. 대신 결제 핸들러에 적시 설치를 사용하세요."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "PaymentRequest 호출이 콘텐츠 보안 정책(CSP) connect-src 지시어를 우회했습니다. 이 우회 지원이 중단됩니다. PaymentRequest API(supportedMethods 입력란)의 결제 수단 식별자를 CSP connect-src 지시어에 추가하세요."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent는 지원 중단되었습니다. 대신 표준화된 navigator.storage를 사용하세요."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "<picture> 상위 요소를 지닌 <source src>가 잘못되었으므로 무시됩니다. 대신 <source srcset>를 사용하세요."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame은 공급업체별로 다릅니다. 표준 cancelAnimationFrame을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame은 공급업체별로 다릅니다. 표준 requestAnimationFrame을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen은 지원 중단되었습니다. 대신 Document.fullscreenElement를 사용하세요."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen()은 지원 중단되었습니다. Element.requestFullscreen()을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen()은 지원 중단되었습니다. Element.requestFullscreen()을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen()은 지원 중단되었습니다. Document.exitFullscreen()을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen()은 지원 중단되었습니다. Document.exitFullscreen()을 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen은 지원 중단되었습니다. 대신 Document.fullscreenEnabled를 사용하세요."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "chrome.privacy.websites.privacySandboxEnabled API는 지원 중단되지만 M113 버전이 출시될 때까지 이전 버전과의 호환성을 위해 활성 상태로 유지됩니다. 대신 chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, chrome.privacy.websites.adMeasurementEnabled API를 사용하세요. 자세한 내용은 다음 페이지를 참고하세요. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "DtlsSrtpKeyAgreement 제약 조건은 삭제되었습니다. 이 제약 조건에 false 값을 지정했으며 이는 삭제된 SDES key negotiation 메서드를 사용하기 위한 시도로 해석될 수 있습니다. 해당 기능은 삭제되었습니다. 대신 DTLS key negotiation을 지원하는 서비스를 사용하세요."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "DtlsSrtpKeyAgreement 제약 조건은 삭제되었습니다. 이 제약 조건에 true 값을 지정했으며 이는 아무런 영향을 미치지 않습니다. 정돈을 위해 이 제약 조건을 삭제할 수 있습니다."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "콜백 기반 getStats()는 지원 중단되었으며 삭제될 예정입니다. 대신 사양을 준수하는 getStats()를 사용하세요."}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand()는 지원 중단되었습니다. Selection.modify()를 대신 사용하시기 바랍니다."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "삽입된 사용자 인증 정보(예: **********************/)가 URL에 포함된 하위 리소스 요청이 차단되었습니다."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy 옵션은 지원 중단되었으며 삭제될 예정입니다."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer에서는 교차 출처 분리를 요구합니다. 자세한 내용은 다음 페이지를 참고하세요. https://developer.chrome.com/blog/enabling-shared-array-buffer/"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "사용자 활성화를 포함하지 않은 speechSynthesis.speak()는 지원 중단되었으며 삭제될 예정입니다."}, "generated/Deprecation.ts | UnloadHandler": {"message": "로드 취소 이벤트 리스너는 지원 중단되었으며 삭제될 예정입니다."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "SharedArrayBuffer를 계속 사용하려면 확장 프로그램에서 교차 출처 분리를 사용해야 합니다. 다음 페이지를 참고하세요. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/"}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "GPUAdapter isFallbackAdapter 속성은 지원 중단되었습니다. 대신 GPUAdapterInfo isFallbackAdapter 속성을 사용하세요."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "UTF-16은 XMLHttpRequest의 response.json에서 지원되지 않습니다."}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "최종 사용자 환경에 부정적인 영향을 미치므로 기본 스레드의 동기식 XMLHttpRequest이(가) 지원 중단되었습니다. 추가 지원이 필요한 경우 다음 페이지를 참고하세요. https://xhr.spec.whatwg.org/"}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "애니메이션"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "레이아웃 변경은 사용자 상호작용 없이 요소가 이동할 때 발생합니다. 페이지가 로드될 때 요소가 추가되거나 삭제되거나 글꼴이 변경되는 등 [레이아웃이 변경되는 이유를 조사](https://web.dev/articles/optimize-cls)하세요."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "글꼴 요청"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "삽입된 iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "레이아웃 변경 클러스터 @{PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "레이아웃 변경 원인을 찾을 수 없습니다."}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "레이아웃 변경 없음"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "레이아웃 변경 원인"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "상위 레이아웃 변경 원인"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "최악의 클러스터"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "최악의 레이아웃 변경 클러스터"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "캐시 TTL"}, "models/trace/insights/Cache.ts | description": {"message": "캐시 수명이 길면 페이지를 반복해서 방문하는 속도가 빨라질 수 있습니다. [자세히 알아보기](https://web.dev/uses-long-cache-ttl/)"}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "비효율적인 캐시 정책을 사용하는 요청 없음"}, "models/trace/insights/Cache.ts | others": {"message": "그 외 {PH1}개"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "요청"}, "models/trace/insights/Cache.ts | title": {"message": "효율적인 캐시 수명 사용"}, "models/trace/insights/DOMSize.ts | description": {"message": "DOM이 크면 스타일 계산 및 레이아웃 리플로우 시간이 길어져 페이지 반응성에 영향을 미칠 수 있습니다. DOM이 크면 메모리 사용량도 늘어납니다. [과도한 DOM 크기를 방지하는 방법 알아보기](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "models/trace/insights/DOMSize.ts | element": {"message": "요소"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "대부분의 하위 요소"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "DOM 깊이"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "통계"}, "models/trace/insights/DOMSize.ts | title": {"message": "DOM 크기 최적화"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "총 요소 수"}, "models/trace/insights/DOMSize.ts | value": {"message": "값"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "첫 번째 네트워크 요청이 가장 중요합니다.  리디렉션을 방지하고 서버 응답 속도를 높이며 텍스트 압축을 사용 설정하여 지연 시간을 줄이세요."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "리디렉션 있음(리디렉션 {PH1}개, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "서버 응답이 느림({PH1} 관측됨)"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "적용된 압축 없음"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "리디렉션 방지"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "서버 응답이 빠름({PH1} 관측됨)"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "텍스트 압축 적용"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "리디렉션"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "서버 응답 시간"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "문서 요청 지연 시간"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "비압축 다운로드"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "중복된 바이트"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "소스"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "번들에서 크고 중복된 JavaScript 모듈을 삭제하여 네트워크 활동이 소비하는 불필요한 바이트 수를 줄입니다."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "중복된 JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "[font-display](https://developer.chrome.com/blog/font-display)를 swap 또는 optional로 설정하여 텍스트가 일관되게 표시되도록 하세요. swap은 [글꼴 측정항목 재정의](https://developer.chrome.com/blog/font-fallbacks)를 통해 레이아웃 변화를 완화하도록 추가로 최적화할 수 있습니다."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "글꼴"}, "models/trace/insights/FontDisplay.ts | title": {"message": "글꼴 표시"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "낭비된 시간"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(익명)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "많은 API가 일반적으로 레이아웃 도형을 읽으며, 스타일과 레이아웃을 계산하기 위해 렌더링 엔진이 스크립트 실행을 일시중지하도록 강제합니다. [강제 리플로우](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) 및 완화에 관해 자세히 알아보세요."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "스택 트레이스"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "강제 실행된 리플로우"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "상위 함수 호출"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "총 리플로우 시간"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[출처 불명]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "이미지의 다운로드 시간을 줄이면 페이지의 체감 로드 시간과 LCP를 개선할 수 있습니다. [이미지 크기 최적화에 대해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1}(예상: {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "최적화할 수 있는 이미지 없음"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "파일 크기 최적화"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "그 외 {PH1}개"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "이미지 전송 개선"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "이미지 압축률을 높이면 이 이미지의 다운로드 크기가 개선될 수 있습니다."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "최신 이미지 형식(WebP, AVIF)을 사용하거나 이미지 압축률을 높이면 이 이미지의 다운로드 크기를 개선할 수 있습니다."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "이 이미지 파일은 표시된 크기({PH2})에 비해 크기가 너무 큽니다({PH1}). 반응형 이미지를 사용하여 이미지 다운로드 크기를 줄이세요."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "GIF 대신 동영상 형식을 사용하면 애니메이션 콘텐츠의 다운로드 크기를 개선할 수 있습니다."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "가장 긴 단계부터 조사를 시작하세요. [지연을 최소화할 수 있습니다](https://web.dev/articles/optimize-inp#optimize_interactions). 처리 시간을 줄이려면 [주 스레드 비용(JS인 경우가 많음)을 최적화](https://web.dev/articles/optimize-long-tasks)하세요."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "기간"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "입력 지연"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "상호작용이 감지되지 않음"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "단계"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "표시 지연"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "처리 기간"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "단계별 INP"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "HTML에서 LCP 이미지를 즉시 [검색 가능](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)하게 만들고 [지연 로드](https://web.dev/articles/lcp-lazy-loading)를 방지하여 LCP를 최적화합니다."}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "fetchpriority=high 적용됨"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "fetchpriority=high가 적용되어야 합니다."}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "지연 로드가 적용되지 않음"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "LCP 이미지가 가장 이른 시작 시간 {PH1} 후에 로드되었습니다."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "감지된 LCP 없음"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "LCP가 이미지가 아니기 때문에 LCP 리소스가 감지되지 않았습니다."}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "초기 문서에서 요청을 검색할 수 있습니다"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "LCP 요청 탐색"}, "models/trace/insights/LCPPhases.ts | description": {"message": "각 [단계에는 해당 단계에 맞는 개선 전략이 있습니다](https://web.dev/articles/optimize-lcp#lcp-breakdown). 이상적으로는 대부분의 LCP 시간이 지연이 아닌 리소스 로드에 소요되어야 합니다."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "기간"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "요소 렌더링 지연"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "실사용자 75번째 백분위수"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "감지된 LCP 없음"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "단계"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "리소스 로드 지연"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "리소스 로드 시간"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "첫 바이트까지의 시간"}, "models/trace/insights/LCPPhases.ts | title": {"message": "단계별 LCP"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "스크립트"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "낭비된 바이트"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "폴리필 및 변환을 통해 이전 브라우저에서 새로운 JavaScript 기능을 사용할 수 있습니다. 하지만 최신 브라우저에서는 대부분 필요하지 않습니다. 이전 브라우저를 지원해야 하는 경우가 아니라면 [Baseline](https://web.dev/articles/baseline-and-polyfills) 기능을 트랜스파일하지 않도록 JavaScript 빌드 프로세스를 수정해 보세요. [대부분의 사이트에서 트랜스파일링 없이 ES6+ 코드를 배포할 수 있는 이유 알아보기](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "레거시 JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "HTTP/2 및 HTTP/3은 HTTP/1.1에 비해 다중화와 같은 많은 이점을 제공합니다. [최신 HTTP 사용에 관해 자세히 알아보기](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "HTTP/1.1을 사용한 요청 없음"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "프로토콜"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "요청"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "최신 HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "출처"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "요청"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "소스"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "시간"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "예상 LCP 절감 시간"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "미사용 연결입니다. crossorigin 속성이 올바르게 사용되었는지 확인하세요."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "체인의 길이를 줄이고, 리소스의 다운로드 크기를 줄이거나 불필요한 리소스의 다운로드를 지연하여 페이지 로드 속도를 개선하는 방식으로 [크리티컬 요청 체이닝을 방지](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)합니다."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "가장 중요한 출처에 [사전 연결](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 힌트를 추가하되 4개 이하로 사용하세요."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "후보 사전 연결"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "최대 중요 경로 지연 시간:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "네트워크 종속 항목의 영향을 받는 렌더링 작업이 없습니다."}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "사전 연결에 적합한 추가 출처가 없습니다."}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "사전 연결된 출처가 없습니다."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "[사전 연결](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) 힌트는 브라우저가 페이지 로드 초기에 연결을 설정하도록 지원하여 해당 출처에 대한 첫 번째 요청이 이루어질 때 시간을 절약합니다. 다음은 페이지가 사전 연결된 출처입니다."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "사전 연결된 출처"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "네트워크 종속 항목 트리"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "5개가 넘는 preconnect 연결이 발견되었습니다. 사전 연결은 가장 중요한 출처에 한해 최소한으로만 사용해야 합니다."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "미사용 연결입니다. 페이지에서 요청할 가능성이 있는 출처에만 preconnect를 사용합니다."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "체인의 길이를 줄이고, 리소스의 다운로드 크기를 줄이거나 불필요한 리소스의 다운로드를 지연하여 페이지 로드 속도를 개선하는 방식으로 크리티컬 요청 체이닝을 방지합니다."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "요청으로 인해 페이지의 초기 렌더링이 차단되어 LCP가 지연될 수 있습니다. [지연 또는 인라인 처리](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources)하면 이러한 네트워크 요청이 주요 경로에서 벗어날 수 있습니다."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "기간"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "이 탐색에 대한 렌더링 차단 요청 없음"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "요청"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "렌더링 차단 요청"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "스타일 재계산 비용이 여전히 높다면 선택자 최적화를 통해 비용을 줄일 수 있습니다. 경과 시간이 길고 느린 경로 비율이 높은 경우 [선택자를 최적화](https://developer.chrome.com/docs/devtools/performance/selector-stats)하세요. 선택자가 간단하고 선택자 수가 적으며 DOM이 작고 DOM이 얕으면 일치 비용이 줄어듭니다."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "경과 시간"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "CSS 선택자 데이터를 찾을 수 없습니다. 성능 패널 설정에서 CSS 선택자 통계를 사용 설정해야 합니다."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "일치 시도"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "일치 항목 수"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "CSS 선택자 비용"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "상위 선택기"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "합계"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "기본 스레드 시간"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "서드 파티"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "전송 크기"}, "models/trace/insights/ThirdParties.ts | description": {"message": "서드 파티 코드는 로드 성능에 크게 영향을 미칠 수 있습니다. [서드 파티 코드의 로드를 줄이고 연기](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/)하여 페이지 콘텐츠에 우선순위를 지정하세요."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "서드 파티를 찾을 수 없음"}, "models/trace/insights/ThirdParties.ts | title": {"message": "서드 파티"}, "models/trace/insights/Viewport.ts | description": {"message": "표시 영역이 모바일에 최적화되어 있지 않으면 탭 상호작용이 [최대 300밀리초 지연될 수 있습니다](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "모바일 탭 지연"}, "models/trace/insights/Viewport.ts | title": {"message": "모바일 표시 영역 최적화"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "GET 요청을 통해 로드된 페이지만 뒤로-앞으로 캐시를 사용할 수 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "상태 코드가 2XX인 페이지만 캐시될 수 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome이 캐시된 페이지에서 자바스크립트 실행 시도를 감지했습니다."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "AppBanner를 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "플래그에서 뒤로-앞으로 캐시가 사용 중지되었습니다. chrome://flags/#back-forward-cache로 이동하여 뒤로-앞으로 캐시를 사용 설정하세요. 캐시는 기기의 로컬에 저장됩니다."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "명령줄에 의해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "메모리가 충분하지 못하여 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "권한을 위임받은 사용자가 뒤로-앞으로 캐시를 허용하지 않습니다."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "프리렌더러에 의해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "페이지에 등록된 리스너가 포함된 BroadcastChannel 인스턴스가 있어 페이지를 캐시할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "cache-control:no-store 헤더가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "캐시가 의도적으로 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "다른 페이지를 캐시하기 위해 페이지가 캐시에서 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "플러그인을 포함하고 있는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "정의되지 않음"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "FileChooser API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "File System Access API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "미디어 기기 디스패처를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "페이지에서 나갈 때 미디어 플레이어가 재생 중이었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "MediaSession API를 사용하며 재생 상태를 설정한 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "MediaSession API를 사용하며 작업 핸들러를 설정한 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "스크린 리더로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "SecurityHandler를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Serial API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "WebAuthetication API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "WebBluetooth API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "WebUSB API를 사용하는 페이지에서는 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Cache-Control: no-store를 사용하는 페이지에서 쿠키가 사용 중지되므로 뒤로-앞으로 캐시가 사용 중지됩니다."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "전용 작업자 또는 Worklet을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "사용자가 문서에서 나갈 때까지 문서 로드가 완료되지 않았습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "페이지에서 나갈 때 앱 배너가 표시되어 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "페이지에서 나갈 때 Chrome 비밀번호 관리자가 표시되어 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "페이지에서 나갈 때 DOM 디스틸레이션이 진행 중이었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "페이지에서 나갈 때 DOM Distiller 뷰어가 표시되어 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "messaging API를 사용하는 확장 프로그램으로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "장시간 연결이 포함된 확장 프로그램은 연결을 해제해야 뒤로/앞으로 캐시에 저장될 수 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "장기 연결이 포함된 확장 프로그램에서 뒤로/앞으로 캐시 내 프레임으로 메시지를 전송하려 했습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "확장 프로그램으로 인해 뒤로/앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "페이지에서 나갈 때 양식 다시 제출과 같은 모달 대화상자 또는 http 인증 대화상자가 표시되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "페이지에서 나갈 때 오프라인 페이지가 표시되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "페이지에서 나갈 때 메모리 부족 알림 표시줄이 표시되어 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "페이지에서 나갈 때 권한 요청이 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "페이지에서 나갈 때 팝업 차단기가 표시되어 있었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "페이지에서 나갈 때 세이프 브라우징 세부정보가 표시되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "세이프 브라우징에서 이 페이지를 악성으로 간주하여 팝업을 차단했습니다."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 서비스 워커가 활성화되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "문서 오류로 인해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "FencedFrame을 사용하는 페이지는 bfcache에 저장할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "다른 페이지를 캐시하기 위해 페이지가 캐시에서 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "미디어 스트림 액세스 권한을 부여한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "특정 유형의 삽입 콘텐츠(예: PDF)가 있는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "IdleManager를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "IndexedDB가 연결된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "IndexedDB 이벤트로 인해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "사용 불가능한 API가 사용되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "확장 프로그램에 의해 JavaScript가 삽입된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "확장 프로그램에 의해 StyleSheet이(가) 삽입된 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "내부 오류입니다."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "일부 JavaScript 네트워크 요청에서 Cache-Control: no-store 헤더가 있는 리소스를 수신했으므로 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "연결 유지 요청으로 인해 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "키보드 잠금을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "사용자가 페이지에서 나갈 때까지 페이지 로드가 완료되지 않았습니다."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "기본 리소스에 cache-control:no-cache가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "기본 리소스에 cache-control:no-store가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "뒤로-앞으로 캐시에서 페이지를 복원하기 전에 탐색이 취소되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "활성 네트워크에 연결된 동안 페이지에서 지나치게 많은 데이터를 수신하여 페이지가 캐시에서 삭제되었습니다. Chrome은 캐시된 페이지에서 수신할 수 있는 데이터의 양을 제한합니다."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "fetch() 또는 XHR이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "활성 네트워크 요청에 리디렉션이 발생하여 페이지가 뒤로-앞으로 캐시에서 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "네트워크 연결 시간이 너무 길어 페이지가 캐시에서 삭제되었습니다. Chrome은 캐시된 페이지에서 데이터를 수신할 수 있는 시간을 제한합니다."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "유효한 응답 헤드가 없는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "기본 프레임이 아닌 다른 프레임에서 탐색이 이루어졌습니다."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "indexedDB 트랜잭션이 진행 중인 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "가져오기 네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "XHR 네트워크 요청이 발생한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "PaymentManager를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "PIP 모드를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "인쇄 UI를 표시하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "페이지가 'window.open()'을(를) 사용하여 열렸고 다른 탭에 해당 페이지 참조가 있거나 페이지가 새 창에서 열렸습니다."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "뒤로-앞으로 캐시에 저장된 페이지의 렌더기 프로세스가 다운되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "뒤로-앞으로 캐시에 저장된 페이지의 렌더기 프로세스가 중단되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "오디오 캡처 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "센서 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "백그라운드 동기화 또는 가져오기 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "MIDI 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "알림 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "저장용량 액세스를 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "동영상 캡처 권한을 요청한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "URL 스킴이 HTTP/HTTPS인 페이지만 캐시될 수 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 서비스 워커에 의해 사용되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "서비스 워커가 뒤로-앞으로 캐시에 추가된 페이지에 MessageEvent 속성을 전송하려고 했습니다."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "페이지가 뒤로-앞으로 캐시에 저장된 상태에서 ServiceWorker가 등록 취소되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "서비스 워커 활성화로 인하여 페이지가 뒤로-앞으로 캐시에서 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome이 다시 시작되면서 뒤로-앞으로 캐시 항목이 삭제되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "SharedWorker를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "SpeechRecognizer를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "SpeechSynthesis를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "페이지의 iframe이 탐색을 시작했으나 완료되지 않았습니다."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "하위 리소스에 cache-control:no-cache가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "하위 리소스에 cache-control:no-store가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "페이지가 뒤로-앞으로 캐시에서 최대 시간을 초과하여 만료되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "페이지의 뒤로-앞으로 캐시 시작이 타임아웃되었습니다(페이지 숨김 핸들러의 장기간 실행 때문일 가능성이 큼)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "페이지 기본 프레임에 로드 취소 핸들러가 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "페이지 하위 프레임에 로드 취소 핸들러가 있습니다."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "브라우저에서 사용자 에이전트 재정의 헤더를 변경했습니다."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "동영상 녹화 또는 오디오 녹음 액세스를 부여한 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "WebDatabase를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "WebHID를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "WebLock을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "WebNfc를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "WebOTPService를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "WebRTC가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "WebRTC가 사용되었기 때문에 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "WebShare를 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "WebSocket이 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "WebSocket이 사용되었기 때문에 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "WebTransport가 있는 페이지에서는 뒤로-앞으로 캐시를 시작할 수 없습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "WebTransport가 사용되었기 때문에 뒤로-앞으로 캐시가 사용 중지되었습니다."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "WebXR을 사용하는 페이지에서는 현재 뒤로-앞으로 캐시를 사용할 수 없습니다."}}