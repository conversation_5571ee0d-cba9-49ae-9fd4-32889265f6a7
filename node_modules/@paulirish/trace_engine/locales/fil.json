{"generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "Hindi sasaklawin ang pagpapahintulot ng simbolong wildcard (*) sa pangangasiwa ng Access-Control-Allow-Headers sa CORS."}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Dapat gamitin ang attribute na disableRemotePlayback para i-disable ang default na integration ng Cast sa halip na gamitin ang selector na -internal-media-controls-overlay-cast-button."}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "Hindi naka-standardize ang value ng hitsura sa CSS na slider-vertical at aalisin ito."}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "Naka-block ang mga kahiligan sa resource na may mga URL na naglalaman ng mga inalis na whitespace \\(n|r|t) character at less-than character (<). Pakialis ang mga newline at i-encode ang mga less-than character mula sa mga lugar tulad ng mga value na attribute ng element para ma-load ang mga resource na ito."}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "Hindi na ginagamit ang chrome.loadTimes(), standardized na API: Navigation Timing 2 na lang ang gamitin."}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Hindi na ginagamit ang chrome.loadTimes(), standardized na API: Paint Timing na lang ang gamitin."}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Hindi na ginagamit ang chrome.loadTimes(), standardized na API: nextHopProtocol sa Navigation Timing 2 na lang ang gamitin."}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "Tatanggihan sa halip na puputulin ang cookies na naglalaman ng \\(0|r|n) character."}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "Hindi na ginagamit, at madi-disable nang default ang pagpapahinga ng patakaran sa iisang origin sa pamamagitan ng pagtatakda sa document.domain. Ang babalang ito sa paghinto sa paggamit ay para sa isang cross-origin na access na na-enable sa pamamagitan ng pagtatakda sa document.domain."}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "Hindi na ginagamit ang pag-trigger ng window.alert mula sa mga cross origin na iframe at aalisin na ito sa hinaharap."}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "Hindi na ginagamit ang pag-trigger ng window.confirm mula sa mga cross origin na iframe at aalisin na ito sa hinaharap."}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "Suporta para sa data: Hindi na ginagamit ang mga URL sa SVGUseElement at aalisin na ito sa hinaharap."}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "Hindi na gumagana ang getCurrentPosition() at watchPosition() sa mga hindi secure na origin. Para magamit ang feature na ito, dapat mong isaalang-alang ang paglipat ng iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Hindi na ginagamit ang getCurrentPosition() at watchPosition() sa mga hindi secure na origin. Para magamit ang feature na ito, dapat mong isaalang-alang ang paglipat ng iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "Hindi na gumagana ang getUserMedia() sa mga hindi secure na origin. Para magamit ang feature na ito, dapat mong isaalang-alang ang paglipat ng iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "generated/Deprecation.ts | H1UserAgentFontSizeInSection": {"message": "May nakitang tag na <h1> sa <article>, <aside>, <nav>, o <section> na walang tinukoy na laki ng font. Magbabago ang laki ng text ng heading na ito sa browser na ito sa nalalapit na hinaharap. Tingnan ang https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Heading_Elements#specifying_a_uniform_font_size_for_h1 para sa higit pang impormasyon."}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "Hindi na ginagamit ang RTCPeerConnectionIceErrorEvent.hostCandidate. RTCPeerConnectionIceErrorEvent.address o RTCPeerConnectionIceErrorEvent.port na lang ang gamitin."}, "generated/Deprecation.ts | IdentityDigitalCredentials": {"message": "Hindi na ginagamit ang format na ito para sa request na navigator.credentials.get() para sa mga digital na kredensyal, paki-update ang iyong call para gamitin ang bagong format."}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "Ang origin ng merchant at arbitrary na data mula sa canmakepayment na event ng service worker ay hindi na ginagamit at aalisin: topOrigin, paymentRequestOrigin, methodData, modifiers."}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "Humiling ang website ng subresource mula sa isang network na maa-access lang nito dahil sa privileged na posisyon ng network ng mga user nito. Inilalantad ng mga kahilingang ito ang mga hindi pampublikong device at server sa internet, na nagpapataas sa panganib ng cross-site request forgery (CSRF) na pag-atake, at/o pag-leak ng impormasyon. Para mapigilan ang mga panganib na ito, hindi na gagamitin ng Chrome ang mga kahilingan sa mga hindi pampublikong subresource kapag sinimulan mula sa mga hindi secure na konteksto, at magsisimula itong i-block ang mga iyon."}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "Na-rename sa updateUrl ang field ng dailyUpdateUrl ng InterestGroups na ipinasa sa joinAdInterestGroup(), para mas tumpak na maipakita ang gawi nito."}, "generated/Deprecation.ts | IntlV8BreakIterator": {"message": "Hindi na ginagamit ang Intl.v8BreakIterator. Intl.Segmenter na lang ang gamitin."}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "Hindi ma-load ang CSS mula sa mga URL ng file: maliban kung nagtatapos ang mga ito sa file extension na .css."}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "Hindi na ginagamit ang paggamit ng SourceBuffer.abort() para i-abort ang asynchronous na pag-aalis ng sakop ng remove() dahil sa pagbabago ng detalye. Aalisin ang suporta sa hinaharap. Dapat mong pakinggan na lang ang updateend. Nilalayon lang ng abort() na i-abort ang asynchronous na pagdaragdag ng media o i-reset ang status ng pang-parse."}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "Hindi na ginagamit ang pagtatakda ng MediaSource.duration sa ibaba ng pinakamataas na timestamp ng presentation ng anumang naka-buffer at naka-code na frame dahil sa pagbabago ng detalye. Aalisin ang suporta para sa direktang pag-aalis ng naputol at naka-buffer na media sa hinaharap. Sa halip, dapat kang magsagawa ng direktang remove(newDuration, oldDuration) sa lahat ng sourceBuffers, kung saan newDuration < oldDuration."}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "Hihingi ng pahintulot ang Web MIDI sa paggamit kahit na hindi tinukoy ang sysex sa MIDIOptions."}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "Posibleng hindi na magamit ang Notification API mula sa mga hindi secure na origin. Dapat mong isaalang-alang ang paglipat ng iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "Posibleng hindi na humiling ng pahintulot para sa Notification API mula sa isang cross-origin na iframe. Dapat mong isaalang-alang ang paghiling ng pahintulot mula sa isang top-level na frame o sa halip ay magbukas ng bagong window."}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Hindi na ginagamit ang opsyong imageOrientation: 'none' sa createImageBitmap. Gamitin na lang ang createImageBitmap na may opsyong '{imageOrientation: 'from-image'}.'"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "Nakikipagkasundo ang iyong partner sa isang lumang bersyon ng (D)TLS. Sumangguni sa iyong partner para maayos ito."}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "Kapag tinukoy ang overflow: visible sa mga tag ng img, video at canvas, posibleng gumawa ang mga ito ng visual na content sa labas ng mga hangganan ng element. Tingnan ang https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "generated/Deprecation.ts | PaymentInstruments": {"message": "Hindi na ginagamit ang paymentManager.instruments. Gumamit na lang ng just-in-time na pag-install para sa mga tagapangasiwa ng pagbabayad."}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "Na-bypass ng iyong call na PaymentRequest ang direktibang connect-src ng Content-Security-Policy (CSP). Hindi na ginagamit ang pag-bypass na ito. Magdagdag ng identifier ng paraan ng pagbabayad mula sa PaymentRequest API (sa field na supportedMethods) sa iyong direktibang connect-src ng CSP."}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "Hindi na ginagamit ang StorageType.persistent. Pakigamit na lang ang standardized na navigator.storage."}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "Invalid at binabalewala ang <source src> na may parent na <picture>. <source srcset> na lang ang gamitin."}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "Partikular sa vendor ang webkitCancelAnimationFrame. Pakigamit na lang ang karaniwang cancelAnimationFrame."}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "Partikular sa vendor ang webkitRequestAnimationFrame. Pakigamit na lang ang karaniwang requestAnimationFrame."}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitDisplayingFullscreen. Pakigamit na lang ang Document.fullscreenElement."}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitEnterFullScreen(). Pakigamit na lang ang Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitEnterFullscreen(). Pakigamit na lang ang Element.requestFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitExitFullScreen(). Pakigamit na lang ang Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitExitFullscreen(). Pakigamit na lang ang Document.exitFullscreen()."}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitSupportsFullscreen. Pakigamit na lang ang Document.fullscreenEnabled."}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "Hindi na namin gagamitin ang API na chrome.privacy.websites.privacySandboxEnabled, pero mananatili itong aktibo para sa backward compatibility hanggang sa pag-release ng M113. Sa halip, pakigamit ang chrome.privacy.websites.topicsEnabled, chrome.privacy.websites.fledgeEnabled, at chrome.privacy.websites.adMeasurementEnabled. Tingnan ang https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "Inalis ang paghihigpit na DtlsSrtpKeyAgreement. Tinukoy mo ang isang false na value para sa paghihigpit na ito, na itinuturing na pagsubok na gamitin ang inalis na paraan ng SDES key negotiation. Inalis ang functionality na ito; isang serbisyong sumusuporta sa DTLS key negotiation na lang ang gamitin."}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "Inalis ang paghihigpit na DtlsSrtpKeyAgreement. Tinukoy mo ang isang true na value para sa paghihigpit na ito, na walang epekto, pero puwede mong alisin ang paghihigpit na ito para sa kaayusan."}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Hindi na ginagamit at aalisin na ang nakabatay sa callback na getStats(). Gamitin na lang ang sumusunod sa spec na getStats()."}, "generated/Deprecation.ts | RangeExpand": {"message": "Hindi na ginagamit ang Range.expand(). Pakigamit na lang ang Selection.modify()."}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "Naka-block ang mga kahilingan sa subresource na may mga URL na naglalaman ng mga naka-embed na kredensyal (hal., **********************/)."}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "Hindi na ginagamit at aalisin ang opsyong rtcpMuxPolicy."}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "Mangangailangan ang SharedArrayBuffer ng pag-isolate ng cross-origin. Tingnan ang https://developer.chrome.com/blog/enabling-shared-array-buffer/ para sa higit pang detalye."}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "Hindi na ginagamit at aalisin ang speechSynthesis.speak() nang walang pag-activate ng user."}, "generated/Deprecation.ts | UnloadHandler": {"message": "Hindi na ginagamit at aalisin na ang mag-unload ng mga event listener."}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Dapat mag-opt in ang mga extension sa mga pag-isolate ng cross-origin para patuloy na magamit ang SharedArrayBuffer. Tingnan ang https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "generated/Deprecation.ts | WebGPUAdapterIsFallbackAdapter": {"message": "Hindi na ginagamit ang attribute na isFallbackAdapter ng GPUAdapter, sa halip ay gamitin ang attribute na isFallbackAdapter ng GPUAdapterInfo."}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "Hindi sinusuportahan ang UTF-16 ng sagot na json sa XMLHttpRequest"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Hindi na ginagamit ang synchronous na XMLHttpRequest sa pangunahing thread dahil sa mga nakakapinsalang epekto nito sa experience ng end user. Para sa higit pang tulong, tingnan ang https://xhr.spec.whatwg.org/."}, "models/trace/insights/CLSCulprits.ts | animation": {"message": "Animation"}, "models/trace/insights/CLSCulprits.ts | description": {"message": "Nangyayari ang mga pagbabago sa layout kapag gumagalaw ang mga element nang walang anumang interaction ng user. [Siyasatin ang mga dahilan ng pagbabago sa layout](https://web.dev/articles/optimize-cls), tulad ng mga pagdaragdag ng mga element, pag-alis, o pagbabago ng mga font nito kapag naglo-load ang page."}, "models/trace/insights/CLSCulprits.ts | fontRequest": {"message": "Request sa font"}, "models/trace/insights/CLSCulprits.ts | injectedIframe": {"message": "Na-inject na iframe"}, "models/trace/insights/CLSCulprits.ts | layoutShiftCluster": {"message": "Cluster ng pagbabago sa layout @ {PH1}"}, "models/trace/insights/CLSCulprits.ts | noCulprits": {"message": "Walang ma-detect na anumang dahilan ng pagbabago sa layout"}, "models/trace/insights/CLSCulprits.ts | noLayoutShifts": {"message": "Walang pagbabago sa layout"}, "models/trace/insights/CLSCulprits.ts | title": {"message": "Mga dahilan ng pagbabago sa layout"}, "models/trace/insights/CLSCulprits.ts | topCulprits": {"message": "Mga nangungunang dahilan ng pagbabago sa layout"}, "models/trace/insights/CLSCulprits.ts | unsizedImage": {"message": "Unsized image element"}, "models/trace/insights/CLSCulprits.ts | worstCluster": {"message": "Pinakahindi magandang cluster"}, "models/trace/insights/CLSCulprits.ts | worstLayoutShiftCluster": {"message": "Pinakamalalang cluster ng pagbabago sa layout"}, "models/trace/insights/Cache.ts | cacheTTL": {"message": "TTL ng Cache"}, "models/trace/insights/Cache.ts | description": {"message": "Puwedeng mapabilis ng mahabang lifetime ng cache ang mga umuulit na pagbisita sa iyong page. [Matuto pa](https://web.dev/uses-long-cache-ttl/)."}, "models/trace/insights/Cache.ts | noRequestsToCache": {"message": "Walang request na may mga hindi mahusay na patakaran sa cache"}, "models/trace/insights/Cache.ts | others": {"message": "{PH1} pa"}, "models/trace/insights/Cache.ts | requestColumn": {"message": "Request"}, "models/trace/insights/Cache.ts | title": {"message": "Gumamit ng mahuhusay na lifetime ng cache"}, "models/trace/insights/DOMSize.ts | description": {"message": "<PERSON><PERSON>g malaki ang DOM, puwedeng tumaas ang tagal ng pagkalkula ng istilo at mga reflow ng layout, na nakakaapekto sa pagiging responsive ng page. Madaragdagan din ng malaking DOM ang paggamit ng memory. [Alamin kung paano iwasan ang masyadong malaking DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "models/trace/insights/DOMSize.ts | element": {"message": "Element"}, "models/trace/insights/DOMSize.ts | maxChildren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> child"}, "models/trace/insights/DOMSize.ts | maxDOMDepth": {"message": "<PERSON><PERSON> ng DOM"}, "models/trace/insights/DOMSize.ts | statistic": {"message": "Istatistika"}, "models/trace/insights/DOMSize.ts | title": {"message": "I-optimize ang laki ng DOM"}, "models/trace/insights/DOMSize.ts | totalElements": {"message": "Kabuuang element"}, "models/trace/insights/DOMSize.ts | value": {"message": "Value"}, "models/trace/insights/DocumentLatency.ts | description": {"message": "Pinakamahalaga ang iyong unang request sa network.  Bawasan ang latency nito sa pamamagitan ng pag-iwas sa mga pag-redirect, na tumitiyak sa mabilis na pagtugon ng server, at nagbibigay-daan sa pag-compress ng text."}, "models/trace/insights/DocumentLatency.ts | failedRedirects": {"message": "Nagkaroon ng mga pag-redirect ({PH1} (na) pag-redirect, +{PH2})"}, "models/trace/insights/DocumentLatency.ts | failedServerResponseTime": {"message": "Mabagal na tumutugon ang server (naobserbahan ang {PH1})"}, "models/trace/insights/DocumentLatency.ts | failedTextCompression": {"message": "Wala inilapat na pag-compress"}, "models/trace/insights/DocumentLatency.ts | passingRedirects": {"message": "Umiiwas sa mga pag-redirect"}, "models/trace/insights/DocumentLatency.ts | passingServerResponseTime": {"message": "Mabilis na tumutugon ang server (naobserbahan ang {PH1})"}, "models/trace/insights/DocumentLatency.ts | passingTextCompression": {"message": "Naglalapat ng pag-compress ng text"}, "models/trace/insights/DocumentLatency.ts | redirectsLabel": {"message": "Mga pag-redirect"}, "models/trace/insights/DocumentLatency.ts | serverResponseTimeLabel": {"message": "Tagal ng pagsagot ng server"}, "models/trace/insights/DocumentLatency.ts | title": {"message": "Latency ng pag-request ng dokumento"}, "models/trace/insights/DocumentLatency.ts | uncompressedDownload": {"message": "Hindi naka-compress na pag-download"}, "models/trace/insights/DuplicatedJavaScript.ts | columnDuplicatedBytes": {"message": "Mga na-duplicate na byte"}, "models/trace/insights/DuplicatedJavaScript.ts | columnSource": {"message": "Source"}, "models/trace/insights/DuplicatedJavaScript.ts | description": {"message": "<PERSON><PERSON> ang mga malaki at duplicate na module ng JavaScript sa mga bundle para mabawasan ang mga hindi kinakailangang byte na nakokonsumo ng aktibidad ng network."}, "models/trace/insights/DuplicatedJavaScript.ts | title": {"message": "Na-duplicate na JavaScript"}, "models/trace/insights/FontDisplay.ts | description": {"message": "Pag-isipang itakda ang [font-display](https://developer.chrome.com/blog/font-display) sa swap o optional para tiyaking patuloy na makikita ang text. Puwede pang i-optimize ang swap para mabawasan ang mga pagbabago sa layout gamit ang [mga override sa sukatan ng font](https://developer.chrome.com/blog/font-fallbacks)."}, "models/trace/insights/FontDisplay.ts | fontColumn": {"message": "Font"}, "models/trace/insights/FontDisplay.ts | title": {"message": "Font display"}, "models/trace/insights/FontDisplay.ts | wastedTimeColumn": {"message": "<PERSON><PERSON>ang na oras"}, "models/trace/insights/ForcedReflow.ts | anonymous": {"message": "(anonymous)"}, "models/trace/insights/ForcedReflow.ts | description": {"message": "Pinipilit ng maraming API, na kadalasang nagbabasa ng geometry ng layout, ang rendering engine na i-pause ang pag-execute ng script para makalkula nito ang istilo at layout. Matuto pa tungkol sa [sapilitang reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) at ang mga pag-mitigate nito."}, "models/trace/insights/ForcedReflow.ts | relatedStackTrace": {"message": "Stack trace"}, "models/trace/insights/ForcedReflow.ts | title": {"message": "Sapilitang reflow"}, "models/trace/insights/ForcedReflow.ts | topTimeConsumingFunctionCall": {"message": "Nangungunang function call"}, "models/trace/insights/ForcedReflow.ts | totalReflowTime": {"message": "Ka<PERSON>uang tagal ng pag-reflow"}, "models/trace/insights/ForcedReflow.ts | unattributed": {"message": "[hindi na-attribute]"}, "models/trace/insights/ImageDelivery.ts | description": {"message": "<PERSON><PERSON><PERSON> binawasan ang tagal ng pag-download ng mga larawan, posibleng mapahusay ang natukoy na oras ng pag-load ng page at LCP. [Matuto pa tungkol sa pag-optimize ng laki ng larawan](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "models/trace/insights/ImageDelivery.ts | estimatedSavings": {"message": "{PH1} (<PERSON><PERSON><PERSON> {PH2})"}, "models/trace/insights/ImageDelivery.ts | noOptimizableImages": {"message": "Walang mao-optimize na larawan"}, "models/trace/insights/ImageDelivery.ts | optimizeFile": {"message": "I-optimize ang laki ng file"}, "models/trace/insights/ImageDelivery.ts | others": {"message": "{PH1} pa"}, "models/trace/insights/ImageDelivery.ts | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang paghahatid ng larawan"}, "models/trace/insights/ImageDelivery.ts | useCompression": {"message": "Ka<PERSON>g tinaasan ang salik ng pag-compress ng larawan, posibleng mapahusay ang laki ng download ng larawang ito."}, "models/trace/insights/ImageDelivery.ts | useModernFormat": {"message": "Kapag gumamit ng modernong format ng imahe (WebP, AVIF) o kapag tinaasan ang pag-compress ng larawan, posibleng mapahusay ang laki ng download ng larawang ito."}, "models/trace/insights/ImageDelivery.ts | useResponsiveSize": {"message": "Ang image file na ito ay mas malaki kaysa sa dapat nitong laki ({PH1}) para sa mga ipinapakitang dimensyon nito ({PH2}). Gumamit ng mga responsive na larawan para bawasan ang laki ng download ng larawan."}, "models/trace/insights/ImageDelivery.ts | useVideoFormat": {"message": "Kapag gumamit ng mga format ng video sa halip na mga GIF, posibleng mapahusay ang laki ng download ng animated na content."}, "models/trace/insights/InteractionToNextPaint.ts | description": {"message": "Simulan ang pagsisiyasat sa pinakamahabang phase. [<PERSON><PERSON><PERSON><PERSON> mabawasan ang mga pagkaantala](https://web.dev/articles/optimize-inp#optimize_interactions). Para pabilisin ang pagpoproseso, [i-optimize ang mga gastos sa pangunahing thread](https://web.dev/articles/optimize-long-tasks), na madalas na JS."}, "models/trace/insights/InteractionToNextPaint.ts | duration": {"message": "Tagal"}, "models/trace/insights/InteractionToNextPaint.ts | inputDelay": {"message": "Pagkaantala ng input"}, "models/trace/insights/InteractionToNextPaint.ts | noInteractions": {"message": "Walang na-detect na interaction"}, "models/trace/insights/InteractionToNextPaint.ts | phase": {"message": "Phase"}, "models/trace/insights/InteractionToNextPaint.ts | presentationDelay": {"message": "Pagkaantala ng presentation"}, "models/trace/insights/InteractionToNextPaint.ts | processingDuration": {"message": "Tagal ng pagpoproseso"}, "models/trace/insights/InteractionToNextPaint.ts | title": {"message": "INP ayon sa phase"}, "models/trace/insights/LCPDiscovery.ts | description": {"message": "Gawing [nahahanap](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) kaagad ang larawan para sa LCP mula sa HTML para ma-optimize ang LCP, at [maiwasan ang mabagal na pag-load](https://web.dev/articles/lcp-lazy-loading)"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityApplied": {"message": "In-apply ang fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | fetchPriorityShouldBeApplied": {"message": "Dapat i-apply ang fetchpriority=high"}, "models/trace/insights/LCPDiscovery.ts | lazyLoadNotApplied": {"message": "hindi na-apply ang mabagal na pag-load"}, "models/trace/insights/LCPDiscovery.ts | lcpLoadDelay": {"message": "Nag-load ang LCP na larawan {PH1} pagkatapos ng pinakaunang punto ng pagsisimula."}, "models/trace/insights/LCPDiscovery.ts | noLcp": {"message": "Walang na-detect na LCP"}, "models/trace/insights/LCPDiscovery.ts | noLcpResource": {"message": "Walang na-detect na resource ng LCP dahil hindi larawan ang LCP"}, "models/trace/insights/LCPDiscovery.ts | requestDiscoverable": {"message": "Nahahanap ang request sa paunang dokumento"}, "models/trace/insights/LCPDiscovery.ts | title": {"message": "Pagtuklas sa request ng LCP"}, "models/trace/insights/LCPPhases.ts | description": {"message": "<PERSON> bawat [phase ay may mga partikular na strategy sa pagpapahusay](https://web.dev/articles/optimize-lcp#lcp-breakdown). <PERSON>am kung gugugulin sa pag-load ng mga resource ang malaking bahagi ng tagal ng LCP, hindi sa mga pagkaantala."}, "models/trace/insights/LCPPhases.ts | duration": {"message": "Tagal"}, "models/trace/insights/LCPPhases.ts | elementRenderDelay": {"message": "Pagkaantala sa pag-render ng element"}, "models/trace/insights/LCPPhases.ts | fieldDuration": {"message": "Field p75"}, "models/trace/insights/LCPPhases.ts | noLcp": {"message": "Walang na-detect na LCP"}, "models/trace/insights/LCPPhases.ts | phase": {"message": "Phase"}, "models/trace/insights/LCPPhases.ts | resourceLoadDelay": {"message": "Pagkaantala sa pag-load ng resource"}, "models/trace/insights/LCPPhases.ts | resourceLoadDuration": {"message": "Tagal ng pag-load ng resource"}, "models/trace/insights/LCPPhases.ts | timeToFirstByte": {"message": "Time to first byte"}, "models/trace/insights/LCPPhases.ts | title": {"message": "LCP ayon sa bahagi"}, "models/trace/insights/LegacyJavaScript.ts | columnScript": {"message": "<PERSON><PERSON><PERSON>"}, "models/trace/insights/LegacyJavaScript.ts | columnWastedBytes": {"message": "Mga nasayang na byte"}, "models/trace/insights/LegacyJavaScript.ts | description": {"message": "Nagbibigay-daan ang mga polyfill at transform na magamit ng mga mas lumang browser ang mga bagong feature ng JavaScript. <PERSON><PERSON><PERSON><PERSON>, marami ang hindi kinakailangan para sa mga modernong browser. Pag-isipang baguhin ang iyong proseso ng pagbuo sa JavaScript para hindi ma-transpile ang mga [Baseline](https://web.dev/articles/baseline-and-polyfills) na feature, maliban kung alam mong dapat mong masuportahan ang mga mas lumang browser. [Alamin kung bakit puwedeng mag-deploy ang karamihan ng mga site ng ES6+ na code nang hindi nagta-transpile](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)"}, "models/trace/insights/LegacyJavaScript.ts | title": {"message": "Legacy na JavaScript"}, "models/trace/insights/ModernHTTP.ts | description": {"message": "Nag-aalok ang HTTP/2 at HTTP/3 ng maraming benepisyo kumpara sa HTTP/1.1, tulad ng multiplexing. [Matuto pa tungkol sa paggamit ng modernong HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "models/trace/insights/ModernHTTP.ts | noOldProtocolRequests": {"message": "Walang request na gumamit ng HTTP/1.1"}, "models/trace/insights/ModernHTTP.ts | protocol": {"message": "Protocol"}, "models/trace/insights/ModernHTTP.ts | request": {"message": "Request"}, "models/trace/insights/ModernHTTP.ts | title": {"message": "Modernong HTTP"}, "models/trace/insights/NetworkDependencyTree.ts | columnOrigin": {"message": "Origin"}, "models/trace/insights/NetworkDependencyTree.ts | columnRequest": {"message": "Request"}, "models/trace/insights/NetworkDependencyTree.ts | columnSource": {"message": "Source"}, "models/trace/insights/NetworkDependencyTree.ts | columnTime": {"message": "Or<PERSON>"}, "models/trace/insights/NetworkDependencyTree.ts | columnWastedMs": {"message": "Mga tinatayang pagtitipid sa LCP"}, "models/trace/insights/NetworkDependencyTree.ts | crossoriginWarning": {"message": "Hindi nagamit na pag-preconnect. Tingnan kung ginagamit nang maayos ang attribute na crossorigin."}, "models/trace/insights/NetworkDependencyTree.ts | description": {"message": "[Iwas<PERSON> ang pag-chain ng mahahalagang request](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) sa pamamagitan ng pagbabawas sa haba ng mga chain, pagbabawas sa laki ng mga dina-download na resource, o pagpapaliban sa pag-download ng mga hindi kinakailangang resource para mapabilis ang pag-load ng page."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableDescription": {"message": "Magdagdag ng mga hint sa [pag-preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) sa mga pinakamahalagang origin mo, pero subukang gumamit ng hindi lalampas sa 4."}, "models/trace/insights/NetworkDependencyTree.ts | estSavingTableTitle": {"message": "Mag-preconnect ng mga candidate"}, "models/trace/insights/NetworkDependencyTree.ts | maxCriticalPathLatency": {"message": "Maximum na latency ng critical path:"}, "models/trace/insights/NetworkDependencyTree.ts | noNetworkDependencyTree": {"message": "Walang gawain sa pag-render na naapektuhan ng mga dependency ng network"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectCandidates": {"message": "Walang karagdagang origin ang magandang candidate para sa pag-preconnect"}, "models/trace/insights/NetworkDependencyTree.ts | noPreconnectOrigins": {"message": "walang na-preconnect na origin"}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableDescription": {"message": "Nakakatulong ang mga hint sa [pag-preconnect](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/) sa browser na bumuo ng koneksyon nang mas maaga sa pag-load ng page, kung saan may matitipid na oras kapag ginawa ang unang request para sa origin na iyon. Ang sumusunod ay mga origin kung saan naka-preconnect ang page."}, "models/trace/insights/NetworkDependencyTree.ts | preconnectOriginsTableTitle": {"message": "Mga na-preconnect na origin"}, "models/trace/insights/NetworkDependencyTree.ts | title": {"message": "Dependency tree ng network"}, "models/trace/insights/NetworkDependencyTree.ts | tooManyPreconnectLinksWarning": {"message": "Mahigit 4 na koneksyong preconnect ang nakita. Bihira lang dapat gamitin ang mga ito at tanging sa mga pinakamahalagang origin lang."}, "models/trace/insights/NetworkDependencyTree.ts | unusedWarning": {"message": "Hindi nagamit na pag-preconnect. Gamitin lang ang preconnect para sa mga origin na malamang na i-request ng page."}, "models/trace/insights/NetworkDependencyTree.ts | warningDescription": {"message": "<PERSON>wasan ang pag-chain ng mahahalagang request sa pamamagitan ng pagbabawas sa haba ng mga chain, pagbabawas sa laki ng mga dina-download na resource, o pagpapaliban sa pag-download ng mga hindi kinakailangang resource para mapabilis ang pag-load ng page."}, "models/trace/insights/RenderBlocking.ts | description": {"message": "Bina-block ng mga request ang unang pag-render ng page, na posibleng makaantala sa LCP. Puwedeng alisin ng [pagpapaliban o pag-inline](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) ang mga request sa network na ito sa critical path."}, "models/trace/insights/RenderBlocking.ts | duration": {"message": "Tagal"}, "models/trace/insights/RenderBlocking.ts | noRenderBlocking": {"message": "Walang request na nagba-block ng pag-render para sa navigation na ito"}, "models/trace/insights/RenderBlocking.ts | renderBlockingRequest": {"message": "Request"}, "models/trace/insights/RenderBlocking.ts | title": {"message": "Mag-render ng mga request sa pag-block"}, "models/trace/insights/SlowCSSSelector.ts | description": {"message": "Kung mananatiling mataas ang mga gastos sa Kalkulahin Ulit ang <PERSON>tilo, mapapababa ito ng pag-optimize ng selector. [I-optimize ang mga selector](https://developer.chrome.com/docs/devtools/performance/selector-stats) gamit ang matagal na lumipas na oras at mataas na % ng slow-path. Mapapaliit ng mga mas simpleng selector, mga mas kaunting selector, mas maliit na DOM, at mas mababaw na DOM ang mga tumutugmang gastos."}, "models/trace/insights/SlowCSSSelector.ts | elapsed": {"message": "Lumipas na oras"}, "models/trace/insights/SlowCSSSelector.ts | enableSelectorData": {"message": "Walang nakitang data ng CSS selector. Kailangang i-enable ang mga istatistika ng CSS selector sa mga setting ng panel ng performance."}, "models/trace/insights/SlowCSSSelector.ts | matchAttempts": {"message": "Mga pagsubok sa pagtutugma"}, "models/trace/insights/SlowCSSSelector.ts | matchCount": {"message": "Bilang ng tugma"}, "models/trace/insights/SlowCSSSelector.ts | title": {"message": "Mga gastos sa CSS Selector"}, "models/trace/insights/SlowCSSSelector.ts | topSelectors": {"message": "Mga nangungunang selector"}, "models/trace/insights/SlowCSSSelector.ts | total": {"message": "Kabu<PERSON>"}, "models/trace/insights/ThirdParties.ts | columnMainThreadTime": {"message": "Oras sa pangunahing thread"}, "models/trace/insights/ThirdParties.ts | columnThirdParty": {"message": "3rd party"}, "models/trace/insights/ThirdParties.ts | columnTransferSize": {"message": "Laki ng paglipat"}, "models/trace/insights/ThirdParties.ts | description": {"message": "Puwedeng lubos na makaapekto ang code ng 3rd party sa performance ng pag-load. [<PERSON><PERSON><PERSON> at ipagpaliban ang pag-load ng code ng 3rd party](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) para isapriyoridad ang content ng iyong page."}, "models/trace/insights/ThirdParties.ts | noThirdParties": {"message": "Walang nakitang third party"}, "models/trace/insights/ThirdParties.ts | title": {"message": "Mga 3rd party"}, "models/trace/insights/Viewport.ts | description": {"message": "Posibleng [maantala nang hanggang 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) ang mga interaction sa pag-tap kung hindi na-optimize para sa mobile ang viewport."}, "models/trace/insights/Viewport.ts | mobileTapDelayLabel": {"message": "Pagkaantala sa pag-tap sa mobile"}, "models/trace/insights/Viewport.ts | title": {"message": "I-optimize ang viewport para sa mobile"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "Ang mga page lang na nag-load sa pamamagitan ng kahilingan gamit ang GET ang kwalipikado para sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "Ang mga page lang na may status code na 2XX ang puwedeng i-cache."}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Naka-detect ang Chrome ng pagsubok na i-execute ang JavaScript habang nasa cache."}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "<PERSON><PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng AppBanner."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "<PERSON><PERSON>-disable ng mga pag-flag ang back/forward cache. Bumisita sa chrome://flags/#back-forward-cache para lokal itong i-enable sa device na ito."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON>-disable ng command line ang back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "Naka-disable ang back/forward cache dahil sa hindi sapat na memory."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "Hindi sinusuportahan ng pag-delegate ang back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "Naka-disable ang back/forward cache para sa prerenderer."}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "Hindi puwedeng i-cache ang page dahil mayroon itong instance na BroadcastChannel na may mga nakarehistrong listener."}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may header na cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "<PERSON><PERSON> na-clear ang cache."}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "Inalis sa cache ang page para mapayagang ma-cache ang isa pang page."}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na naglalaman ng mga plugin."}, "panels/application/components/BackForwardCacheStrings.ts | contentDiscarded": {"message": "Hindi tinukoy"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng FileChooser API."}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng File System Access API."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Dispatcher ng Media Device."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "May nagpe-play na media player noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng MediaSession API at nagtakda ng status ng pag-playback."}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng MediaSession API at nagtakda ng mga tagapangasiwa ng pagkilos."}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "Naka-disable ang back/forward cache dahil sa screen reader."}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SecurityHandler."}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Serial API."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebAuthentication API."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebBluetooth API."}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebUSB API."}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "Naka-disable ang back/forward cache dahil naka-disable ang cookies sa isang page na gumagamit ng Cache-Control: no-store."}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng nakalaang worker o worklet."}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "Hindi natapos mag-load ang dokumento bago nag-navigate paalis dito."}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "May App Banner noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "May Password Manager ng Chrome noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Isinasagawa ang DOM distillation noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "May DOM Distiller Viewer noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "Naka-disable ang back/forward cache dahil gumagamit ang mga extension ng messaging API."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "Dapat isara ng mga extension na may long-lived na koneksyon ang koneksyon bago pumasok sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "Sinubukang magpadala ng mga extension na may long-lived na koneksyon ng mga mensahe sa mga frame sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "Naka-disable ang back/forward cache dahil sa mga extension."}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "Ipinakita ang modal dialog, gaya ng dialog para sa pagsusumite ulit ng form o http password, para sa page noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "Ipinakita ang offline na page noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "May Out-Of-Memory Intervention bar noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "May mga kahilingan sa pahintulot noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "May popup blocker noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "Ipinakita ang mga detalye ng Ligtas na Pag-browse noong nag-navigate paalis."}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Tinukoy ng Ligtas na Pag-browse ang page na ito bilang mapang-abuso at nag-block ito ng popup."}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "May service worker na na-activate habang nasa back/forward cache ang page."}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "Naka-disable ang back/forward cache dahil sa isang error sa dokumento."}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "Hindi puwedeng i-store sa bfcache ang mga page na gumagamit ng FencedFrames."}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "Inalis sa cache ang page para mapayagang ma-cache ang isa pang page."}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagbigay ng access sa pag-stream ng media."}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page na may ilang partikular na uri ng naka-embed na content (hal. mga PDF)."}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng IdleManager."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na may bukas na IndexedDBConnection."}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "Naka-disable ang back/forward cache dahil sa event na IndexedDB."}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "May ginamit na mga hindi kwalipikadong API."}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page kung saan na-inject ng mga extension ang JavaScript."}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page kung saan na-inject ng mga extension ang StyleSheet."}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "Internal na error."}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "Naka-disable ang back/forward cache dahil nakatanggap ang ilang request sa network ng JavaScript ng resource na may Cache-Control: no-store na header."}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "Naka-disable ang back/forward cache dahil sa isang keepalive na kahilingan."}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mag page na gumagamit ng KeyboardLock."}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "Hindi natapos mag-load ang page bago nag-navigate paalis dito."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may pangunahing resource na may cache-control:no-cache."}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may pangunahing resource na may cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON> ang pag-navigate bago ma-restore ang page mula sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "Inalis sa cache ang page dahil may aktibong koneksyon ng network na nakatanggap ng masyadong maraming data. Nililimitahan ng Chrome ang dami ng data na puwedeng matanggap ng page habang naka-cache."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON><PERSON> hindi kwalipikado para sa back/forward cache ang mga page na may inflight na fetch() o XHR)."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "Inalis sa back/forward cache ang page dahil may kaugnay na pag-redirect ang isang kahilingan sa aktibong network."}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "Inalis sa cache ang page dahil masyadong matagal na nakabukas ang isang koneksyon ng network. Nililimitahan ng Chrome ang haba ng oras na puwedeng makatanggap ng data ang isa page habang naka-cache."}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na walang valid na header ng sagot."}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "<PERSON><PERSON><PERSON> ang pag-navigate sa frame bukod pa sa pangunahing frame."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang page na may mga kasalukuyang naka-index na transaksyon sa DB."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na kahilingan sa network."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na may in-flight na kahilingan sa fetch network."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na kahilingan sa network."}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na kahilingan sa network na XHR."}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng PaymentManager."}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Picture-in-Picture."}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagpapakita ng UI sa Pag-print."}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "<PERSON><PERSON><PERSON> ang page gamit ang 'window.open()' at may ibang tab na tumutukoy rito, o may window na binuksan ang page."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "Nag-crash ang proseso ng pag-render para sa page sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "Inihinto ang proseso ng pag-render para sa page na nasa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa pag-capture ng audio."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa sensor."}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa pag-sync o pag-fetch ng background."}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa MIDI."}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa mga notification."}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng access sa storage."}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na humiling ng mga pahintulot sa pag-capture ng video."}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "Ang mga page lang na may scheme ng URL na HTTP / HTTPS ang puwedeng i-cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "Na-claim ng service worker ang page habang nasa back/forward cache ito."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "May service worker na sumubok na magpadala ng MessageEvent sa page na nasa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "Na-unregister ang ServiceWorker habang nasa back/forward cache ang isang page."}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "Inalis sa back/forward cache ang page dahil sa pag-activate ng service worker."}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Ni-restart at na-clear ng Chrome ang mga entry sa back/forward cache."}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "Kasal<PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SharedWorker."}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SpeechRecognizer."}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SpeechSynthesis."}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "May iframe sa page na nagsimula ng pag-navigate na hindi natapos."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may subresource na may cache-control:no-cache."}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may subresource na may cache-control:no-store."}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "Lumampas ang page sa maximum na oras sa back/forward cache at nag-expire na ito."}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "Nag-time out ang page habang pumapasok sa back/forward cache (malamang na dahil sa mga matagal na tumatakbong tagapangasiwa ng pagehide)."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "Ang page ay may tagapangasiwa ng pag-unload sa pangunahing frame."}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "Ang page ay may tagapangasiwa ng pag-unload sa sub frame."}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "Binago ng browser ang header ng override ng user agent."}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagbigay ng access sa pag-record ng video o audio."}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebDatabase."}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebHID."}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebLocks."}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebNfc."}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "Kasalukuyang hindi kwalipikado para sa bfcache ang mga page na gumagamit ng WebOTPService."}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "Naka-disable ang back/forward cache dahil ginamit ang WebRTC."}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebShare."}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "Naka-disable ang back/forward cache dahil ginamit ang WebSocket."}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "Naka-disable ang back/forward cache dahil ginamit ang WebTransport."}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebXR."}}