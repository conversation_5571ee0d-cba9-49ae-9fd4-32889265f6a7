"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTTP_REQUEST_METHOD_VALUE_POST = exports.HTTP_REQUEST_METHOD_VALUE_PATCH = exports.HTTP_REQUEST_METHOD_VALUE_OPTIONS = exports.HTTP_REQUEST_METHOD_VALUE_HEAD = exports.HTTP_REQUEST_METHOD_VALUE_GET = exports.HTTP_REQUEST_METHOD_VALUE_DELETE = exports.HTTP_REQUEST_METHOD_VALUE_CONNECT = exports.HTTP_REQUEST_METHOD_VALUE_OTHER = exports.ATTR_HTTP_REQUEST_METHOD = exports.ATTR_HTTP_REQUEST_HEADER = exports.ATTR_EXCEPTION_TYPE = exports.ATTR_EXCEPTION_STACKTRACE = exports.ATTR_EXCEPTION_MESSAGE = exports.ATTR_EXCEPTION_ESCAPED = exports.ERROR_TYPE_VALUE_OTHER = exports.ATTR_ERROR_TYPE = exports.ATTR_CLIENT_PORT = exports.ATTR_CLIENT_ADDRESS = exports.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = exports.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = exports.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = exports.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = exports.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = exports.ATTR_ASPNETCORE_RATE_LIMITING_POLICY = exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = exports.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = exports.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = exports.ATTR_TELEMETRY_SDK_VERSION = exports.ATTR_TELEMETRY_SDK_NAME = exports.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = exports.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = exports.TELEMETRY_SDK_LANGUAGE_VALUE_RUST = exports.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = exports.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = exports.TELEMETRY_SDK_LANGUAGE_VALUE_PHP = exports.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = exports.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = exports.TELEMETRY_SDK_LANGUAGE_VALUE_GO = exports.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = exports.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = exports.TELEMETRY_SDK_LANGUAGE_VALUE_CPP = exports.ATTR_TELEMETRY_SDK_LANGUAGE = exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = exports.ATTR_ASPNETCORE_RATE_LIMITING_RESULT = void 0;
exports.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = exports.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = exports.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = exports.ATTR_SIGNALR_CONNECTION_STATUS = exports.ATTR_SERVICE_VERSION = exports.ATTR_SERVICE_NAME = exports.ATTR_SERVER_PORT = exports.ATTR_SERVER_ADDRESS = exports.ATTR_OTEL_STATUS_DESCRIPTION = exports.OTEL_STATUS_CODE_VALUE_OK = exports.OTEL_STATUS_CODE_VALUE_ERROR = exports.ATTR_OTEL_STATUS_CODE = exports.ATTR_OTEL_SCOPE_VERSION = exports.ATTR_OTEL_SCOPE_NAME = exports.NETWORK_TYPE_VALUE_IPV6 = exports.NETWORK_TYPE_VALUE_IPV4 = exports.ATTR_NETWORK_TYPE = exports.NETWORK_TRANSPORT_VALUE_UNIX = exports.NETWORK_TRANSPORT_VALUE_UDP = exports.NETWORK_TRANSPORT_VALUE_TCP = exports.NETWORK_TRANSPORT_VALUE_QUIC = exports.NETWORK_TRANSPORT_VALUE_PIPE = exports.ATTR_NETWORK_TRANSPORT = exports.ATTR_NETWORK_PROTOCOL_VERSION = exports.ATTR_NETWORK_PROTOCOL_NAME = exports.ATTR_NETWORK_PEER_PORT = exports.ATTR_NETWORK_PEER_ADDRESS = exports.ATTR_NETWORK_LOCAL_PORT = exports.ATTR_NETWORK_LOCAL_ADDRESS = exports.JVM_THREAD_STATE_VALUE_WAITING = exports.JVM_THREAD_STATE_VALUE_TIMED_WAITING = exports.JVM_THREAD_STATE_VALUE_TERMINATED = exports.JVM_THREAD_STATE_VALUE_RUNNABLE = exports.JVM_THREAD_STATE_VALUE_NEW = exports.JVM_THREAD_STATE_VALUE_BLOCKED = exports.ATTR_JVM_THREAD_STATE = exports.ATTR_JVM_THREAD_DAEMON = exports.JVM_MEMORY_TYPE_VALUE_NON_HEAP = exports.JVM_MEMORY_TYPE_VALUE_HEAP = exports.ATTR_JVM_MEMORY_TYPE = exports.ATTR_JVM_MEMORY_POOL_NAME = exports.ATTR_JVM_GC_NAME = exports.ATTR_JVM_GC_ACTION = exports.ATTR_HTTP_ROUTE = exports.ATTR_HTTP_RESPONSE_STATUS_CODE = exports.ATTR_HTTP_RESPONSE_HEADER = exports.ATTR_HTTP_REQUEST_RESEND_COUNT = exports.ATTR_HTTP_REQUEST_METHOD_ORIGINAL = exports.HTTP_REQUEST_METHOD_VALUE_TRACE = exports.HTTP_REQUEST_METHOD_VALUE_PUT = void 0;
exports.ATTR_USER_AGENT_ORIGINAL = exports.ATTR_URL_SCHEME = exports.ATTR_URL_QUERY = exports.ATTR_URL_PATH = exports.ATTR_URL_FULL = exports.ATTR_URL_FRAGMENT = exports.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = exports.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = exports.SIGNALR_TRANSPORT_VALUE_LONG_POLLING = exports.ATTR_SIGNALR_TRANSPORT = void 0;
//----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/registry/stable/attributes.ts.j2
//----------------------------------------------------------------------------------------------------------
/**
 * Rate-limiting result, shows whether the lease was acquired or contains a rejection reason
 *
 * @example acquired
 * @example request_canceled
 */
exports.ATTR_ASPNETCORE_RATE_LIMITING_RESULT = 'aspnetcore.rate_limiting.result';
/**
  * Enum value "acquired" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.
  */
exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = "acquired";
/**
  * Enum value "endpoint_limiter" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.
  */
exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = "endpoint_limiter";
/**
  * Enum value "global_limiter" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.
  */
exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = "global_limiter";
/**
  * Enum value "request_canceled" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.
  */
exports.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = "request_canceled";
/**
 * The language of the telemetry SDK.
 */
exports.ATTR_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';
/**
  * Enum value "cpp" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_CPP = "cpp";
/**
  * Enum value "dotnet" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = "dotnet";
/**
  * Enum value "erlang" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = "erlang";
/**
  * Enum value "go" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_GO = "go";
/**
  * Enum value "java" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = "java";
/**
  * Enum value "nodejs" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = "nodejs";
/**
  * Enum value "php" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_PHP = "php";
/**
  * Enum value "python" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = "python";
/**
  * Enum value "ruby" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = "ruby";
/**
  * Enum value "rust" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_RUST = "rust";
/**
  * Enum value "swift" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = "swift";
/**
  * Enum value "webjs" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.
  */
exports.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = "webjs";
/**
 * The name of the telemetry SDK as defined above.
 *
 * @example opentelemetry
 *
 * @note The OpenTelemetry SDK **MUST** set the `telemetry.sdk.name` attribute to `opentelemetry`.
 * If another SDK, like a fork or a vendor-provided implementation, is used, this SDK **MUST** set the
 * `telemetry.sdk.name` attribute to the fully-qualified class or module name of this SDK's main entry point
 * or another suitable identifier depending on the language.
 * The identifier `opentelemetry` is reserved and **MUST NOT** be used in this case.
 * All custom identifiers **SHOULD** be stable across different versions of an implementation.
 */
exports.ATTR_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';
/**
 * The version string of the telemetry SDK.
 *
 * @example 1.2.3
 */
exports.ATTR_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';
/**
 * Full type name of the [`IExceptionHandler`](https://learn.microsoft.com/dotnet/api/microsoft.aspnetcore.diagnostics.iexceptionhandler) implementation that handled the exception.
 *
 * @example Contoso.MyHandler
 */
exports.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = 'aspnetcore.diagnostics.handler.type';
/**
 * ASP.NET Core exception middleware handling result
 *
 * @example handled
 * @example unhandled
 */
exports.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = 'aspnetcore.diagnostics.exception.result';
/**
  * Enum value "aborted" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.
  */
exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = "aborted";
/**
  * Enum value "handled" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.
  */
exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = "handled";
/**
  * Enum value "skipped" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.
  */
exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = "skipped";
/**
  * Enum value "unhandled" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.
  */
exports.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = "unhandled";
/**
 * Rate limiting policy name.
 *
 * @example fixed
 * @example sliding
 * @example token
 */
exports.ATTR_ASPNETCORE_RATE_LIMITING_POLICY = 'aspnetcore.rate_limiting.policy';
/**
 * Flag indicating if request was handled by the application pipeline.
 *
 * @example true
 */
exports.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = 'aspnetcore.request.is_unhandled';
/**
 * A value that indicates whether the matched route is a fallback route.
 *
 * @example true
 */
exports.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = 'aspnetcore.routing.is_fallback';
/**
 * Match result - success or failure
 *
 * @example success
 * @example failure
 */
exports.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = 'aspnetcore.routing.match_status';
/**
  * Enum value "failure" for attribute {@link ATTR_ASPNETCORE_ROUTING_MATCH_STATUS}.
  */
exports.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = "failure";
/**
  * Enum value "success" for attribute {@link ATTR_ASPNETCORE_ROUTING_MATCH_STATUS}.
  */
exports.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = "success";
/**
 * Client address - domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.
 *
 * @example client.example.com
 * @example *********
 * @example /tmp/my.sock
 *
 * @note When observed from the server side, and when communicating through an intermediary, `client.address` **SHOULD** represent the client address behind any intermediaries,  for example proxies, if it's available.
 */
exports.ATTR_CLIENT_ADDRESS = 'client.address';
/**
 * Client port number.
 *
 * @example 65123
 *
 * @note When observed from the server side, and when communicating through an intermediary, `client.port` **SHOULD** represent the client port behind any intermediaries,  for example proxies, if it's available.
 */
exports.ATTR_CLIENT_PORT = 'client.port';
/**
 * Describes a class of error the operation ended with.
 *
 * @example timeout
 * @example java.net.UnknownHostException
 * @example server_certificate_invalid
 * @example 500
 *
 * @note The `error.type` **SHOULD** be predictable, and **SHOULD** have low cardinality.
 *
 * When `error.type` is set to a type (e.g., an exception type), its
 * canonical class name identifying the type within the artifact **SHOULD** be used.
 *
 * Instrumentations **SHOULD** document the list of errors they report.
 *
 * The cardinality of `error.type` within one instrumentation library **SHOULD** be low.
 * Telemetry consumers that aggregate data from multiple instrumentation libraries and applications
 * should be prepared for `error.type` to have high cardinality at query time when no
 * additional filters are applied.
 *
 * If the operation has completed successfully, instrumentations **SHOULD NOT** set `error.type`.
 *
 * If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),
 * it's **RECOMMENDED** to:
 *
 *   - Use a domain-specific attribute
 *   - Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.
 */
exports.ATTR_ERROR_TYPE = 'error.type';
/**
  * Enum value "_OTHER" for attribute {@link ATTR_ERROR_TYPE}.
  */
exports.ERROR_TYPE_VALUE_OTHER = "_OTHER";
/**
 * **SHOULD** be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.
 *
 * @note An exception is considered to have escaped (or left) the scope of a span,
 * if that span is ended while the exception is still logically "in flight".
 * This may be actually "in flight" in some languages (e.g. if the exception
 * is passed to a Context manager's `__exit__` method in Python) but will
 * usually be caught at the point of recording the exception in most languages.
 *
 * It is usually not possible to determine at the point where an exception is thrown
 * whether it will escape the scope of a span.
 * However, it is trivial to know that an exception
 * will escape, if one checks for an active exception just before ending the span,
 * as done in the [example for recording span exceptions](https://opentelemetry.io/docs/specs/semconv/exceptions/exceptions-spans/#recording-an-exception).
 *
 * It follows that an exception may still escape the scope of the span
 * even if the `exception.escaped` attribute was not set or set to false,
 * since the event might have been recorded at a time where it was not
 * clear whether the exception will escape.
 */
exports.ATTR_EXCEPTION_ESCAPED = 'exception.escaped';
/**
 * The exception message.
 *
 * @example Division by zero
 * @example Can't convert 'int' object to str implicitly
 */
exports.ATTR_EXCEPTION_MESSAGE = 'exception.message';
/**
 * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.
 *
 * @example "Exception in thread "main" java.lang.RuntimeException: Test exception\\n at com.example.GenerateTrace.methodB(GenerateTrace.java:13)\\n at com.example.GenerateTrace.methodA(GenerateTrace.java:9)\\n at com.example.GenerateTrace.main(GenerateTrace.java:5)\\n"
 */
exports.ATTR_EXCEPTION_STACKTRACE = 'exception.stacktrace';
/**
 * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.
 *
 * @example java.net.ConnectException
 * @example OSError
 */
exports.ATTR_EXCEPTION_TYPE = 'exception.type';
/**
 * HTTP request headers, `<key>` being the normalized HTTP Header name (lowercase), the value being the header values.
 *
 * @example http.request.header.content-type=["application/json"]
 * @example http.request.header.x-forwarded-for=["*******", "*******"]
 *
 * @note Instrumentations **SHOULD** require an explicit configuration of which headers are to be captured. Including all request headers can be a security risk - explicit configuration helps avoid leaking sensitive information.
 * The `User-Agent` header is already captured in the `user_agent.original` attribute. Users **MAY** explicitly configure instrumentations to capture them even though it is not recommended.
 * The attribute value **MUST** consist of either multiple header values as an array of strings or a single-item array containing a possibly comma-concatenated string, depending on the way the HTTP library provides access to headers.
 */
const ATTR_HTTP_REQUEST_HEADER = (key) => `http.request.header.${key}`;
exports.ATTR_HTTP_REQUEST_HEADER = ATTR_HTTP_REQUEST_HEADER;
/**
 * HTTP request method.
 *
 * @example GET
 * @example POST
 * @example HEAD
 *
 * @note HTTP request method value **SHOULD** be "known" to the instrumentation.
 * By default, this convention defines "known" methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)
 * and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).
 *
 * If the HTTP request method is not known to instrumentation, it **MUST** set the `http.request.method` attribute to `_OTHER`.
 *
 * If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it **MUST** provide a way to override
 * the list of known HTTP methods. If this override is done via environment variable, then the environment variable **MUST** be named
 * OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods
 * (this list **MUST** be a full override of the default known method, it is not a list of known methods in addition to the defaults).
 *
 * HTTP method names are case-sensitive and `http.request.method` attribute value **MUST** match a known HTTP method name exactly.
 * Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, **SHOULD** populate a canonical equivalent.
 * Tracing instrumentations that do so, **MUST** also set `http.request.method_original` to the original value.
 */
exports.ATTR_HTTP_REQUEST_METHOD = 'http.request.method';
/**
  * Enum value "_OTHER" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_OTHER = "_OTHER";
/**
  * Enum value "CONNECT" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_CONNECT = "CONNECT";
/**
  * Enum value "DELETE" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_DELETE = "DELETE";
/**
  * Enum value "GET" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_GET = "GET";
/**
  * Enum value "HEAD" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_HEAD = "HEAD";
/**
  * Enum value "OPTIONS" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_OPTIONS = "OPTIONS";
/**
  * Enum value "PATCH" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_PATCH = "PATCH";
/**
  * Enum value "POST" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_POST = "POST";
/**
  * Enum value "PUT" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_PUT = "PUT";
/**
  * Enum value "TRACE" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.
  */
exports.HTTP_REQUEST_METHOD_VALUE_TRACE = "TRACE";
/**
 * Original HTTP method sent by the client in the request line.
 *
 * @example GeT
 * @example ACL
 * @example foo
 */
exports.ATTR_HTTP_REQUEST_METHOD_ORIGINAL = 'http.request.method_original';
/**
 * The ordinal number of request resending attempt (for any reason, including redirects).
 *
 * @example 3
 *
 * @note The resend count **SHOULD** be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).
 */
exports.ATTR_HTTP_REQUEST_RESEND_COUNT = 'http.request.resend_count';
/**
 * HTTP response headers, `<key>` being the normalized HTTP Header name (lowercase), the value being the header values.
 *
 * @example http.response.header.content-type=["application/json"]
 * @example http.response.header.my-custom-header=["abc", "def"]
 *
 * @note Instrumentations **SHOULD** require an explicit configuration of which headers are to be captured. Including all response headers can be a security risk - explicit configuration helps avoid leaking sensitive information.
 * Users **MAY** explicitly configure instrumentations to capture them even though it is not recommended.
 * The attribute value **MUST** consist of either multiple header values as an array of strings or a single-item array containing a possibly comma-concatenated string, depending on the way the HTTP library provides access to headers.
 */
const ATTR_HTTP_RESPONSE_HEADER = (key) => `http.response.header.${key}`;
exports.ATTR_HTTP_RESPONSE_HEADER = ATTR_HTTP_RESPONSE_HEADER;
/**
 * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).
 *
 * @example 200
 */
exports.ATTR_HTTP_RESPONSE_STATUS_CODE = 'http.response.status_code';
/**
 * The matched route, that is, the path template in the format used by the respective server framework.
 *
 * @example /users/:userID?
 * @example {controller}/{action}/{id?}
 *
 * @note **MUST NOT** be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.
 * **SHOULD** include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.
 */
exports.ATTR_HTTP_ROUTE = 'http.route';
/**
 * Name of the garbage collector action.
 *
 * @example end of minor GC
 * @example end of major GC
 *
 * @note Garbage collector action is generally obtained via [GarbageCollectionNotificationInfo#getGcAction()](https://docs.oracle.com/en/java/javase/11/docs/api/jdk.management/com/sun/management/GarbageCollectionNotificationInfo.html#getGcAction()).
 */
exports.ATTR_JVM_GC_ACTION = 'jvm.gc.action';
/**
 * Name of the garbage collector.
 *
 * @example G1 Young Generation
 * @example G1 Old Generation
 *
 * @note Garbage collector name is generally obtained via [GarbageCollectionNotificationInfo#getGcName()](https://docs.oracle.com/en/java/javase/11/docs/api/jdk.management/com/sun/management/GarbageCollectionNotificationInfo.html#getGcName()).
 */
exports.ATTR_JVM_GC_NAME = 'jvm.gc.name';
/**
 * Name of the memory pool.
 *
 * @example G1 Old Gen
 * @example G1 Eden space
 * @example G1 Survivor Space
 *
 * @note Pool names are generally obtained via [MemoryPoolMXBean#getName()](https://docs.oracle.com/en/java/javase/11/docs/api/java.management/java/lang/management/MemoryPoolMXBean.html#getName()).
 */
exports.ATTR_JVM_MEMORY_POOL_NAME = 'jvm.memory.pool.name';
/**
 * The type of memory.
 *
 * @example heap
 * @example non_heap
 */
exports.ATTR_JVM_MEMORY_TYPE = 'jvm.memory.type';
/**
  * Enum value "heap" for attribute {@link ATTR_JVM_MEMORY_TYPE}.
  */
exports.JVM_MEMORY_TYPE_VALUE_HEAP = "heap";
/**
  * Enum value "non_heap" for attribute {@link ATTR_JVM_MEMORY_TYPE}.
  */
exports.JVM_MEMORY_TYPE_VALUE_NON_HEAP = "non_heap";
/**
 * Whether the thread is daemon or not.
 */
exports.ATTR_JVM_THREAD_DAEMON = 'jvm.thread.daemon';
/**
 * State of the thread.
 *
 * @example runnable
 * @example blocked
 */
exports.ATTR_JVM_THREAD_STATE = 'jvm.thread.state';
/**
  * Enum value "blocked" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_BLOCKED = "blocked";
/**
  * Enum value "new" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_NEW = "new";
/**
  * Enum value "runnable" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_RUNNABLE = "runnable";
/**
  * Enum value "terminated" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_TERMINATED = "terminated";
/**
  * Enum value "timed_waiting" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_TIMED_WAITING = "timed_waiting";
/**
  * Enum value "waiting" for attribute {@link ATTR_JVM_THREAD_STATE}.
  */
exports.JVM_THREAD_STATE_VALUE_WAITING = "waiting";
/**
 * Local address of the network connection - IP address or Unix domain socket name.
 *
 * @example *********
 * @example /tmp/my.sock
 */
exports.ATTR_NETWORK_LOCAL_ADDRESS = 'network.local.address';
/**
 * Local port number of the network connection.
 *
 * @example 65123
 */
exports.ATTR_NETWORK_LOCAL_PORT = 'network.local.port';
/**
 * Peer address of the network connection - IP address or Unix domain socket name.
 *
 * @example *********
 * @example /tmp/my.sock
 */
exports.ATTR_NETWORK_PEER_ADDRESS = 'network.peer.address';
/**
 * Peer port number of the network connection.
 *
 * @example 65123
 */
exports.ATTR_NETWORK_PEER_PORT = 'network.peer.port';
/**
 * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.
 *
 * @example amqp
 * @example http
 * @example mqtt
 *
 * @note The value **SHOULD** be normalized to lowercase.
 */
exports.ATTR_NETWORK_PROTOCOL_NAME = 'network.protocol.name';
/**
 * The actual version of the protocol used for network communication.
 *
 * @example 1.1
 * @example 2
 *
 * @note If protocol version is subject to negotiation (for example using [ALPN](https://www.rfc-editor.org/rfc/rfc7301.html)), this attribute **SHOULD** be set to the negotiated version. If the actual protocol version is not known, this attribute **SHOULD NOT** be set.
 */
exports.ATTR_NETWORK_PROTOCOL_VERSION = 'network.protocol.version';
/**
 * [OSI transport layer](https://osi-model.com/transport-layer/) or [inter-process communication method](https://wikipedia.org/wiki/Inter-process_communication).
 *
 * @example tcp
 * @example udp
 *
 * @note The value **SHOULD** be normalized to lowercase.
 *
 * Consider always setting the transport when setting a port number, since
 * a port number is ambiguous without knowing the transport. For example
 * different processes could be listening on TCP port 12345 and UDP port 12345.
 */
exports.ATTR_NETWORK_TRANSPORT = 'network.transport';
/**
  * Enum value "pipe" for attribute {@link ATTR_NETWORK_TRANSPORT}.
  */
exports.NETWORK_TRANSPORT_VALUE_PIPE = "pipe";
/**
  * Enum value "quic" for attribute {@link ATTR_NETWORK_TRANSPORT}.
  */
exports.NETWORK_TRANSPORT_VALUE_QUIC = "quic";
/**
  * Enum value "tcp" for attribute {@link ATTR_NETWORK_TRANSPORT}.
  */
exports.NETWORK_TRANSPORT_VALUE_TCP = "tcp";
/**
  * Enum value "udp" for attribute {@link ATTR_NETWORK_TRANSPORT}.
  */
exports.NETWORK_TRANSPORT_VALUE_UDP = "udp";
/**
  * Enum value "unix" for attribute {@link ATTR_NETWORK_TRANSPORT}.
  */
exports.NETWORK_TRANSPORT_VALUE_UNIX = "unix";
/**
 * [OSI network layer](https://osi-model.com/network-layer/) or non-OSI equivalent.
 *
 * @example ipv4
 * @example ipv6
 *
 * @note The value **SHOULD** be normalized to lowercase.
 */
exports.ATTR_NETWORK_TYPE = 'network.type';
/**
  * Enum value "ipv4" for attribute {@link ATTR_NETWORK_TYPE}.
  */
exports.NETWORK_TYPE_VALUE_IPV4 = "ipv4";
/**
  * Enum value "ipv6" for attribute {@link ATTR_NETWORK_TYPE}.
  */
exports.NETWORK_TYPE_VALUE_IPV6 = "ipv6";
/**
 * The name of the instrumentation scope - (`InstrumentationScope.Name` in OTLP).
 *
 * @example io.opentelemetry.contrib.mongodb
 */
exports.ATTR_OTEL_SCOPE_NAME = 'otel.scope.name';
/**
 * The version of the instrumentation scope - (`InstrumentationScope.Version` in OTLP).
 *
 * @example 1.0.0
 */
exports.ATTR_OTEL_SCOPE_VERSION = 'otel.scope.version';
/**
 * Name of the code, either "OK" or "ERROR". **MUST NOT** be set if the status code is UNSET.
 */
exports.ATTR_OTEL_STATUS_CODE = 'otel.status_code';
/**
  * Enum value "ERROR" for attribute {@link ATTR_OTEL_STATUS_CODE}.
  */
exports.OTEL_STATUS_CODE_VALUE_ERROR = "ERROR";
/**
  * Enum value "OK" for attribute {@link ATTR_OTEL_STATUS_CODE}.
  */
exports.OTEL_STATUS_CODE_VALUE_OK = "OK";
/**
 * Description of the Status if it has a value, otherwise not set.
 *
 * @example resource not found
 */
exports.ATTR_OTEL_STATUS_DESCRIPTION = 'otel.status_description';
/**
 * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.
 *
 * @example example.com
 * @example *********
 * @example /tmp/my.sock
 *
 * @note When observed from the client side, and when communicating through an intermediary, `server.address` **SHOULD** represent the server address behind any intermediaries, for example proxies, if it's available.
 */
exports.ATTR_SERVER_ADDRESS = 'server.address';
/**
 * Server port number.
 *
 * @example 80
 * @example 8080
 * @example 443
 *
 * @note When observed from the client side, and when communicating through an intermediary, `server.port` **SHOULD** represent the server port behind any intermediaries, for example proxies, if it's available.
 */
exports.ATTR_SERVER_PORT = 'server.port';
/**
 * Logical name of the service.
 *
 * @example shoppingcart
 *
 * @note **MUST** be the same for all instances of horizontally scaled services. If the value was not specified, SDKs **MUST** fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value **MUST** be set to `unknown_service`.
 */
exports.ATTR_SERVICE_NAME = 'service.name';
/**
 * The version string of the service API or implementation. The format is not defined by these conventions.
 *
 * @example 2.0.0
 * @example a01dbef8a
 */
exports.ATTR_SERVICE_VERSION = 'service.version';
/**
 * SignalR HTTP connection closure status.
 *
 * @example app_shutdown
 * @example timeout
 */
exports.ATTR_SIGNALR_CONNECTION_STATUS = 'signalr.connection.status';
/**
  * Enum value "app_shutdown" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.
  */
exports.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = "app_shutdown";
/**
  * Enum value "normal_closure" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.
  */
exports.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = "normal_closure";
/**
  * Enum value "timeout" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.
  */
exports.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = "timeout";
/**
 * [SignalR transport type](https://github.com/dotnet/aspnetcore/blob/main/src/SignalR/docs/specs/TransportProtocols.md)
 *
 * @example web_sockets
 * @example long_polling
 */
exports.ATTR_SIGNALR_TRANSPORT = 'signalr.transport';
/**
  * Enum value "long_polling" for attribute {@link ATTR_SIGNALR_TRANSPORT}.
  */
exports.SIGNALR_TRANSPORT_VALUE_LONG_POLLING = "long_polling";
/**
  * Enum value "server_sent_events" for attribute {@link ATTR_SIGNALR_TRANSPORT}.
  */
exports.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = "server_sent_events";
/**
  * Enum value "web_sockets" for attribute {@link ATTR_SIGNALR_TRANSPORT}.
  */
exports.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = "web_sockets";
/**
 * The [URI fragment](https://www.rfc-editor.org/rfc/rfc3986#section-3.5) component
 *
 * @example SemConv
 */
exports.ATTR_URL_FRAGMENT = 'url.fragment';
/**
 * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986)
 *
 * @example https://www.foo.bar/search?q=OpenTelemetry#SemConv
 * @example //localhost
 *
 * @note For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it **SHOULD** be included nevertheless.
 * `url.full` **MUST NOT** contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password **SHOULD** be redacted and attribute's value **SHOULD** be `https://REDACTED:<EMAIL>/`.
 * `url.full` **SHOULD** capture the absolute URL when it is available (or can be reconstructed). Sensitive content provided in `url.full` **SHOULD** be scrubbed when instrumentations can identify it.
 */
exports.ATTR_URL_FULL = 'url.full';
/**
 * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component
 *
 * @example /search
 *
 * @note Sensitive content provided in `url.path` **SHOULD** be scrubbed when instrumentations can identify it.
 */
exports.ATTR_URL_PATH = 'url.path';
/**
 * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component
 *
 * @example q=OpenTelemetry
 *
 * @note Sensitive content provided in `url.query` **SHOULD** be scrubbed when instrumentations can identify it.
 */
exports.ATTR_URL_QUERY = 'url.query';
/**
 * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.
 *
 * @example https
 * @example ftp
 * @example telnet
 */
exports.ATTR_URL_SCHEME = 'url.scheme';
/**
 * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.
 *
 * @example CERN-LineMode/2.15 libwww/2.17b3
 * @example Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1
 * @example YourApp/1.0.0 grpc-java-okhttp/1.27.2
 */
exports.ATTR_USER_AGENT_ORIGINAL = 'user_agent.original';
//# sourceMappingURL=stable_attributes.js.map