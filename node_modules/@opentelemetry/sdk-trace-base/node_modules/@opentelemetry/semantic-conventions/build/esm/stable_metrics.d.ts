/**
 * Number of exceptions caught by exception handling middleware.
 *
 * @note Meter name: `Microsoft.AspNetCore.Diagnostics`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS: "aspnetcore.diagnostics.exceptions";
/**
 * Number of requests that are currently active on the server that hold a rate limiting lease.
 *
 * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES: "aspnetcore.rate_limiting.active_request_leases";
/**
 * Number of requests that are currently queued, waiting to acquire a rate limiting lease.
 *
 * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS: "aspnetcore.rate_limiting.queued_requests";
/**
 * The time the request spent in a queue waiting to acquire a rate limiting lease.
 *
 * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE: "aspnetcore.rate_limiting.request.time_in_queue";
/**
 * The duration of rate limiting lease held by requests on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION: "aspnetcore.rate_limiting.request_lease.duration";
/**
 * Number of requests that tried to acquire a rate limiting lease.
 *
 * @note Requests could be:
 *
 *   - Rejected by global or endpoint rate limiting policies
 *   - Canceled while waiting for the lease.
 *
 * Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS: "aspnetcore.rate_limiting.requests";
/**
 * Number of requests that were attempted to be matched to an endpoint.
 *
 * @note Meter name: `Microsoft.AspNetCore.Routing`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS: "aspnetcore.routing.match_attempts";
/**
 * Duration of HTTP client requests.
 */
export declare const METRIC_HTTP_CLIENT_REQUEST_DURATION: "http.client.request.duration";
/**
 * Duration of HTTP server requests.
 */
export declare const METRIC_HTTP_SERVER_REQUEST_DURATION: "http.server.request.duration";
/**
 * Number of classes currently loaded.
 */
export declare const METRIC_JVM_CLASS_COUNT: "jvm.class.count";
/**
 * Number of classes loaded since JVM start.
 */
export declare const METRIC_JVM_CLASS_LOADED: "jvm.class.loaded";
/**
 * Number of classes unloaded since JVM start.
 */
export declare const METRIC_JVM_CLASS_UNLOADED: "jvm.class.unloaded";
/**
 * Number of processors available to the Java virtual machine.
 */
export declare const METRIC_JVM_CPU_COUNT: "jvm.cpu.count";
/**
 * Recent CPU utilization for the process as reported by the JVM.
 *
 * @note The value range is [0.0,1.0]. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/jdk.management/com/sun/management/OperatingSystemMXBean.html#getProcessCpuLoad()).
 */
export declare const METRIC_JVM_CPU_RECENT_UTILIZATION: "jvm.cpu.recent_utilization";
/**
 * CPU time used by the process as reported by the JVM.
 */
export declare const METRIC_JVM_CPU_TIME: "jvm.cpu.time";
/**
 * Duration of JVM garbage collection actions.
 */
export declare const METRIC_JVM_GC_DURATION: "jvm.gc.duration";
/**
 * Measure of memory committed.
 */
export declare const METRIC_JVM_MEMORY_COMMITTED: "jvm.memory.committed";
/**
 * Measure of max obtainable memory.
 */
export declare const METRIC_JVM_MEMORY_LIMIT: "jvm.memory.limit";
/**
 * Measure of memory used.
 */
export declare const METRIC_JVM_MEMORY_USED: "jvm.memory.used";
/**
 * Measure of memory used, as measured after the most recent garbage collection event on this pool.
 */
export declare const METRIC_JVM_MEMORY_USED_AFTER_LAST_GC: "jvm.memory.used_after_last_gc";
/**
 * Number of executing platform threads.
 */
export declare const METRIC_JVM_THREAD_COUNT: "jvm.thread.count";
/**
 * Number of connections that are currently active on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_ACTIVE_CONNECTIONS: "kestrel.active_connections";
/**
 * Number of TLS handshakes that are currently in progress on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES: "kestrel.active_tls_handshakes";
/**
 * The duration of connections on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_CONNECTION_DURATION: "kestrel.connection.duration";
/**
 * Number of connections that are currently queued and are waiting to start.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_QUEUED_CONNECTIONS: "kestrel.queued_connections";
/**
 * Number of HTTP requests on multiplexed connections (HTTP/2 and HTTP/3) that are currently queued and are waiting to start.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_QUEUED_REQUESTS: "kestrel.queued_requests";
/**
 * Number of connections rejected by the server.
 *
 * @note Connections are rejected when the currently active count exceeds the value configured with `MaxConcurrentConnections`.
 * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_REJECTED_CONNECTIONS: "kestrel.rejected_connections";
/**
 * The duration of TLS handshakes on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_TLS_HANDSHAKE_DURATION: "kestrel.tls_handshake.duration";
/**
 * Number of connections that are currently upgraded (WebSockets). .
 *
 * @note The counter only tracks HTTP/1.1 connections.
 *
 * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_KESTREL_UPGRADED_CONNECTIONS: "kestrel.upgraded_connections";
/**
 * Number of connections that are currently active on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS: "signalr.server.active_connections";
/**
 * The duration of connections on the server.
 *
 * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0
 */
export declare const METRIC_SIGNALR_SERVER_CONNECTION_DURATION: "signalr.server.connection.duration";
//# sourceMappingURL=stable_metrics.d.ts.map