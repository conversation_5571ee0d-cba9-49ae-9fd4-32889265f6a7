{"version": 3, "file": "SemanticResourceAttributes.js", "sourceRoot": "", "sources": ["../../../src/resource/SemanticResourceAttributes.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,4GAA4G;AAC5G,iHAAiH;AACjH,4GAA4G;AAE5G,4GAA4G;AAC5G,iDAAiD;AACjD,4GAA4G;AAE5G,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAE1D;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAEjE;;;;GAIG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAE/E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAE3E;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAEjE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAE3E;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAE7E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAEnD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAE/E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAEnE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAEnD;;;;;;;;;;;;;;;;;;;EAmBE;AACF,MAAM,CAAC,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAE/C;;;;;;;;;;;;;;EAcE;AACF,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAE3D;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAE/C;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAEnD;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAEnD;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAEnD;;;;GAIG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAEjE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAEjE;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAE/C;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAE/C;;;;GAIG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,cAAc,CAAC;AAErD;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,+BAA+B,CAAC;AAElC;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAEnE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAE7E;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAE3E;;;;GAIG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAE7E;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAqd3E;;;GAGG;AACH,MAAM,CAAC,IAAM,0BAA0B;AACrC,aAAa,CAAC,cAAc,CAA6B;IACvD,kBAAkB;IAClB,oBAAoB;IACpB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;IAClB,yBAAyB;IACzB,uBAAuB;IACvB,sBAAsB;IACtB,oBAAoB;IACpB,uBAAuB;IACvB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,kBAAkB;IAClB,gBAAgB;IAChB,qBAAqB;IACrB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,aAAa;IACb,2BAA2B;IAC3B,qBAAqB;IACrB,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,iBAAiB;IACjB,sBAAsB;IACtB,oBAAoB;IACpB,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,2BAA2B;IAC3B,2BAA2B;IAC3B,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,iBAAiB;IACjB,wBAAwB;IACxB,2BAA2B;IAC3B,+BAA+B;IAC/B,gBAAgB;IAChB,qBAAqB;IACrB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IACtB,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,kBAAkB;IAClB,qBAAqB;IACrB,yBAAyB;CAC1B,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAC1C,IAAM,6BAA6B,GAAG,OAAO,CAAC;AAC9C,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAE1C;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAsBnE;;;GAGG;AACH,MAAM,CAAC,IAAM,mBAAmB;AAC9B,aAAa,CAAC,cAAc,CAAsB;IAChD,qCAAqC;IACrC,2BAA2B;IAC3B,6BAA6B;IAC7B,2BAA2B;CAC5B,CAAC,CAAC;AAEL;;;;;;gHAMgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,wCAAwC,GAAG,kBAAkB,CAAC;AACpE,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,kCAAkC,GAAG,YAAY,CAAC;AACxD,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,gCAAgC,GAAG,UAAU,CAAC;AACpD,IAAM,iDAAiD,GACrD,2BAA2B,CAAC;AAC9B,IAAM,iCAAiC,GAAG,WAAW,CAAC;AACtD,IAAM,uCAAuC,GAAG,iBAAiB,CAAC;AAClE,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,0CAA0C,GAAG,oBAAoB,CAAC;AACxE,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,2CAA2C,GAAG,qBAAqB,CAAC;AAC1E,IAAM,sCAAsC,GAAG,gBAAgB,CAAC;AAEhE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAE3E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAE3E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AAE3E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAErC;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAEhD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAE7E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAEpD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAE/E;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,sCAAsC,GACjD,0CAA0C,CAAC;AAE7C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAEhD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAE9C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AA+DzC;;;GAGG;AACH,MAAM,CAAC,IAAM,mBAAmB;AAC9B,aAAa,CAAC,cAAc,CAAsB;IAChD,yCAAyC;IACzC,wCAAwC;IACxC,+BAA+B;IAC/B,+BAA+B;IAC/B,+BAA+B;IAC/B,kCAAkC;IAClC,6CAA6C;IAC7C,gCAAgC;IAChC,iDAAiD;IACjD,iCAAiC;IACjC,uCAAuC;IACvC,yCAAyC;IACzC,0CAA0C;IAC1C,qCAAqC;IACrC,6CAA6C;IAC7C,2CAA2C;IAC3C,sCAAsC;CACvC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,8BAA8B,GAAG,KAAK,CAAC;AAC7C,IAAM,kCAAkC,GAAG,SAAS,CAAC;AAErD;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAgBrC;;;GAGG;AACH,MAAM,CAAC,IAAM,sBAAsB;AACjC,aAAa,CAAC,cAAc,CAAyB;IACnD,8BAA8B;IAC9B,kCAAkC;CACnC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,sBAAsB,GAAG,KAAK,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AA+BzD;;;GAGG;AACH,MAAM,CAAC,IAAM,cAAc;AACzB,aAAa,CAAC,cAAc,CAAiB;IAC3C,wBAAwB;IACxB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,wBAAwB;IACxB,sBAAsB;CACvB,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,sBAAsB,GAAG,OAAO,CAAC;AACvC,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,6BAA6B,GAAG,cAAc,CAAC;AACrD,IAAM,qBAAqB,GAAG,MAAM,CAAC;AACrC,IAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,qBAAqB,GAAG,MAAM,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,oBAAoB,CAAC;AAErD;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AA2CvD;;;GAGG;AACH,MAAM,CAAC,IAAM,YAAY;AACvB,aAAa,CAAC,cAAc,CAAe;IACzC,wBAAwB;IACxB,sBAAsB;IACtB,uBAAuB;IACvB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,6BAA6B;IAC7B,qBAAqB;IACrB,oBAAoB;IACpB,wBAAwB;IACxB,qBAAqB;CACtB,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,oCAAoC,GAAG,OAAO,CAAC;AAErD;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAEtC;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAEtC;;;;GAIG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAwCvC;;;GAGG;AACH,MAAM,CAAC,IAAM,0BAA0B;AACrC,aAAa,CAAC,cAAc,CAA6B;IACvD,kCAAkC;IAClC,qCAAqC;IACrC,qCAAqC;IACrC,iCAAiC;IACjC,mCAAmC;IACnC,qCAAqC;IACrC,kCAAkC;IAClC,qCAAqC;IACrC,mCAAmC;IACnC,oCAAoC;CACrC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createConstMap } from '../internal/utils';\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n//----------------------------------------------------------------------------------------------------------\n// Constant values for SemanticResourceAttributes\n//----------------------------------------------------------------------------------------------------------\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUD_PROVIDER = 'cloud.provider';\nconst TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';\nconst TMP_CLOUD_REGION = 'cloud.region';\nconst TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';\nconst TMP_CLOUD_PLATFORM = 'cloud.platform';\nconst TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';\nconst TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';\nconst TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';\nconst TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';\nconst TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';\nconst TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';\nconst TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';\nconst TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';\nconst TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';\nconst TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';\nconst TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';\nconst TMP_CONTAINER_NAME = 'container.name';\nconst TMP_CONTAINER_ID = 'container.id';\nconst TMP_CONTAINER_RUNTIME = 'container.runtime';\nconst TMP_CONTAINER_IMAGE_NAME = 'container.image.name';\nconst TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';\nconst TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';\nconst TMP_DEVICE_ID = 'device.id';\nconst TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';\nconst TMP_DEVICE_MODEL_NAME = 'device.model.name';\nconst TMP_FAAS_NAME = 'faas.name';\nconst TMP_FAAS_ID = 'faas.id';\nconst TMP_FAAS_VERSION = 'faas.version';\nconst TMP_FAAS_INSTANCE = 'faas.instance';\nconst TMP_FAAS_MAX_MEMORY = 'faas.max_memory';\nconst TMP_HOST_ID = 'host.id';\nconst TMP_HOST_NAME = 'host.name';\nconst TMP_HOST_TYPE = 'host.type';\nconst TMP_HOST_ARCH = 'host.arch';\nconst TMP_HOST_IMAGE_NAME = 'host.image.name';\nconst TMP_HOST_IMAGE_ID = 'host.image.id';\nconst TMP_HOST_IMAGE_VERSION = 'host.image.version';\nconst TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';\nconst TMP_K8S_NODE_NAME = 'k8s.node.name';\nconst TMP_K8S_NODE_UID = 'k8s.node.uid';\nconst TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';\nconst TMP_K8S_POD_UID = 'k8s.pod.uid';\nconst TMP_K8S_POD_NAME = 'k8s.pod.name';\nconst TMP_K8S_CONTAINER_NAME = 'k8s.container.name';\nconst TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';\nconst TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';\nconst TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';\nconst TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';\nconst TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';\nconst TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';\nconst TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';\nconst TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';\nconst TMP_K8S_JOB_UID = 'k8s.job.uid';\nconst TMP_K8S_JOB_NAME = 'k8s.job.name';\nconst TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';\nconst TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';\nconst TMP_OS_TYPE = 'os.type';\nconst TMP_OS_DESCRIPTION = 'os.description';\nconst TMP_OS_NAME = 'os.name';\nconst TMP_OS_VERSION = 'os.version';\nconst TMP_PROCESS_PID = 'process.pid';\nconst TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';\nconst TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';\nconst TMP_PROCESS_COMMAND = 'process.command';\nconst TMP_PROCESS_COMMAND_LINE = 'process.command_line';\nconst TMP_PROCESS_COMMAND_ARGS = 'process.command_args';\nconst TMP_PROCESS_OWNER = 'process.owner';\nconst TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';\nconst TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';\nconst TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';\nconst TMP_SERVICE_NAME = 'service.name';\nconst TMP_SERVICE_NAMESPACE = 'service.namespace';\nconst TMP_SERVICE_INSTANCE_ID = 'service.instance.id';\nconst TMP_SERVICE_VERSION = 'service.version';\nconst TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';\nconst TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';\nconst TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';\nconst TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';\nconst TMP_WEBENGINE_NAME = 'webengine.name';\nconst TMP_WEBENGINE_VERSION = 'webengine.version';\nconst TMP_WEBENGINE_DESCRIPTION = 'webengine.description';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use ATTR_CLOUD_PROVIDER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @deprecated Use ATTR_CLOUD_ACCOUNT_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;\n\n/**\n * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n *\n * @deprecated Use ATTR_CLOUD_REGION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n *\n * @deprecated Use ATTR_CLOUD_AVAILABILITY_ZONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use ATTR_CLOUD_PLATFORM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;\n\n/**\n * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CONTAINER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;\n\n/**\n * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use ATTR_AWS_ECS_LAUNCHTYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;\n\n/**\n * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;\n\n/**\n * The task definition family this task definition is a member of.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_FAMILY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;\n\n/**\n * The revision for this task definition.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_REVISION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;\n\n/**\n * The ARN of an EKS cluster.\n *\n * @deprecated Use ATTR_AWS_EKS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;\n\n/**\n * The name(s) of the AWS log group(s) an application is writing to.\n *\n * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;\n\n/**\n * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n *\n * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;\n\n/**\n * The name(s) of the AWS log stream(s) an application is writing to.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;\n\n/**\n * The ARN(s) of the AWS log stream(s).\n *\n * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;\n\n/**\n * Container name.\n *\n * @deprecated Use ATTR_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n *\n * @deprecated Use ATTR_CONTAINER_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;\n\n/**\n * The container runtime managing this container.\n *\n * @deprecated Use ATTR_CONTAINER_RUNTIME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;\n\n/**\n * Name of the image the container was built on.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;\n\n/**\n * Container image tag.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_TAGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;\n\n/**\n * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n *\n * @deprecated Use ATTR_DEPLOYMENT_ENVIRONMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;\n\n/**\n * A unique identifier representing the device.\n *\n * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n *\n * @deprecated Use ATTR_DEVICE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;\n\n/**\n * The model identifier for the device.\n *\n * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_IDENTIFIER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;\n\n/**\n * The marketing name for the device model.\n *\n * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;\n\n/**\n * The name of the single function that this runtime instance executes.\n *\n * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n *\n * @deprecated Use ATTR_FAAS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;\n\n/**\n* The unique ID of the single function that this runtime instance executes.\n*\n* Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n*\n* @deprecated Use ATTR_CLOUD_RESOURCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;\n\n/**\n* The immutable version of the function being executed.\n*\n* Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n*\n* @deprecated Use ATTR_FAAS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;\n\n/**\n * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n *\n * Note: * **AWS Lambda:** Use the (full) log stream name.\n *\n * @deprecated Use ATTR_FAAS_INSTANCE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;\n\n/**\n * The amount of memory available to the serverless function in MiB.\n *\n * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n *\n * @deprecated Use ATTR_FAAS_MAX_MEMORY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n *\n * @deprecated Use ATTR_HOST_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ID = TMP_HOST_ID;\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @deprecated Use ATTR_HOST_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @deprecated Use ATTR_HOST_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use ATTR_HOST_ARCH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;\n\n/**\n * Name of the VM image or OS install the host was instantiated from.\n *\n * @deprecated Use ATTR_HOST_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;\n\n/**\n * VM image ID. For Cloud, this value is from the provider.\n *\n * @deprecated Use ATTR_HOST_IMAGE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;\n\n/**\n * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n *\n * @deprecated Use ATTR_HOST_IMAGE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;\n\n/**\n * The name of the cluster.\n *\n * @deprecated Use ATTR_K8S_CLUSTER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;\n\n/**\n * The name of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;\n\n/**\n * The UID of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;\n\n/**\n * The name of the namespace that the pod is running in.\n *\n * @deprecated Use ATTR_K8S_NAMESPACE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;\n\n/**\n * The UID of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;\n\n/**\n * The name of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;\n\n/**\n * The name of the Container in a Pod template.\n *\n * @deprecated Use ATTR_K8S_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;\n\n/**\n * The UID of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;\n\n/**\n * The name of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;\n\n/**\n * The UID of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;\n\n/**\n * The name of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;\n\n/**\n * The UID of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;\n\n/**\n * The name of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;\n\n/**\n * The UID of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;\n\n/**\n * The name of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;\n\n/**\n * The UID of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;\n\n/**\n * The name of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;\n\n/**\n * The UID of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;\n\n/**\n * The name of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;\n\n/**\n * The operating system type.\n *\n * @deprecated Use ATTR_OS_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;\n\n/**\n * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n *\n * @deprecated Use ATTR_OS_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;\n\n/**\n * Human readable operating system name.\n *\n * @deprecated Use ATTR_OS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_NAME = TMP_OS_NAME;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n *\n * @deprecated Use ATTR_OS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;\n\n/**\n * Process identifier (PID).\n *\n * @deprecated Use ATTR_PROCESS_PID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;\n\n/**\n * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;\n\n/**\n * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_PATH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;\n\n/**\n * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;\n\n/**\n * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_LINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;\n\n/**\n * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_ARGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;\n\n/**\n * The username of the user that owns the process.\n *\n * @deprecated Use ATTR_PROCESS_OWNER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;\n\n/**\n * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;\n\n/**\n * The version of the runtime of this process, as returned by the runtime without modification.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;\n\n/**\n * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION =\n  TMP_PROCESS_RUNTIME_DESCRIPTION;\n\n/**\n * Logical name of the service.\n *\n * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n *\n * @deprecated Use ATTR_SERVICE_NAME.\n */\nexport const SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;\n\n/**\n * A namespace for `service.name`.\n *\n * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @deprecated Use ATTR_SERVICE_NAMESPACE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;\n\n/**\n * The string ID of the service instance.\n *\n * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n *\n * @deprecated Use ATTR_SERVICE_INSTANCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;\n\n/**\n * The version string of the service API or implementation.\n *\n * @deprecated Use ATTR_SERVICE_VERSION.\n */\nexport const SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;\n\n/**\n * The name of the telemetry SDK as defined above.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_NAME.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_LANGUAGE.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;\n\n/**\n * The version string of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_VERSION.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;\n\n/**\n * The version string of the auto instrumentation agent, if used.\n *\n * @deprecated Use ATTR_TELEMETRY_DISTRO_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;\n\n/**\n * The name of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;\n\n/**\n * The version of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;\n\n/**\n * Additional description of the web engine (e.g. detailed version and edition information).\n *\n * @deprecated Use ATTR_WEBENGINE_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;\n\n/**\n * Definition of available values for SemanticResourceAttributes\n * This type is used for backward compatibility, you should use the individual exported\n * constants SemanticResourceAttributes_XXXXX rather than the exported constant map. As any single reference\n * to a constant map value will result in all strings being included into your bundle.\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification.\n */\nexport type SemanticResourceAttributes = {\n  /**\n   * Name of the cloud provider.\n   */\n  CLOUD_PROVIDER: 'cloud.provider';\n\n  /**\n   * The cloud account ID the resource is assigned to.\n   */\n  CLOUD_ACCOUNT_ID: 'cloud.account.id';\n\n  /**\n   * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n   */\n  CLOUD_REGION: 'cloud.region';\n\n  /**\n   * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n   *\n   * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n   */\n  CLOUD_AVAILABILITY_ZONE: 'cloud.availability_zone';\n\n  /**\n   * The cloud platform in use.\n   *\n   * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n   */\n  CLOUD_PLATFORM: 'cloud.platform';\n\n  /**\n   * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n   */\n  AWS_ECS_CONTAINER_ARN: 'aws.ecs.container.arn';\n\n  /**\n   * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n   */\n  AWS_ECS_CLUSTER_ARN: 'aws.ecs.cluster.arn';\n\n  /**\n   * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n   */\n  AWS_ECS_LAUNCHTYPE: 'aws.ecs.launchtype';\n\n  /**\n   * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n   */\n  AWS_ECS_TASK_ARN: 'aws.ecs.task.arn';\n\n  /**\n   * The task definition family this task definition is a member of.\n   */\n  AWS_ECS_TASK_FAMILY: 'aws.ecs.task.family';\n\n  /**\n   * The revision for this task definition.\n   */\n  AWS_ECS_TASK_REVISION: 'aws.ecs.task.revision';\n\n  /**\n   * The ARN of an EKS cluster.\n   */\n  AWS_EKS_CLUSTER_ARN: 'aws.eks.cluster.arn';\n\n  /**\n   * The name(s) of the AWS log group(s) an application is writing to.\n   *\n   * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n   */\n  AWS_LOG_GROUP_NAMES: 'aws.log.group.names';\n\n  /**\n   * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n   *\n   * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n   */\n  AWS_LOG_GROUP_ARNS: 'aws.log.group.arns';\n\n  /**\n   * The name(s) of the AWS log stream(s) an application is writing to.\n   */\n  AWS_LOG_STREAM_NAMES: 'aws.log.stream.names';\n\n  /**\n   * The ARN(s) of the AWS log stream(s).\n   *\n   * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n   */\n  AWS_LOG_STREAM_ARNS: 'aws.log.stream.arns';\n\n  /**\n   * Container name.\n   */\n  CONTAINER_NAME: 'container.name';\n\n  /**\n   * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n   */\n  CONTAINER_ID: 'container.id';\n\n  /**\n   * The container runtime managing this container.\n   */\n  CONTAINER_RUNTIME: 'container.runtime';\n\n  /**\n   * Name of the image the container was built on.\n   */\n  CONTAINER_IMAGE_NAME: 'container.image.name';\n\n  /**\n   * Container image tag.\n   */\n  CONTAINER_IMAGE_TAG: 'container.image.tag';\n\n  /**\n   * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n   */\n  DEPLOYMENT_ENVIRONMENT: 'deployment.environment';\n\n  /**\n   * A unique identifier representing the device.\n   *\n   * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n   */\n  DEVICE_ID: 'device.id';\n\n  /**\n   * The model identifier for the device.\n   *\n   * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n   */\n  DEVICE_MODEL_IDENTIFIER: 'device.model.identifier';\n\n  /**\n   * The marketing name for the device model.\n   *\n   * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n   */\n  DEVICE_MODEL_NAME: 'device.model.name';\n\n  /**\n   * The name of the single function that this runtime instance executes.\n   *\n   * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n   */\n  FAAS_NAME: 'faas.name';\n\n  /**\n  * The unique ID of the single function that this runtime instance executes.\n  *\n  * Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n  */\n  FAAS_ID: 'faas.id';\n\n  /**\n  * The immutable version of the function being executed.\n  *\n  * Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n  */\n  FAAS_VERSION: 'faas.version';\n\n  /**\n   * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n   *\n   * Note: * **AWS Lambda:** Use the (full) log stream name.\n   */\n  FAAS_INSTANCE: 'faas.instance';\n\n  /**\n   * The amount of memory available to the serverless function in MiB.\n   *\n   * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n   */\n  FAAS_MAX_MEMORY: 'faas.max_memory';\n\n  /**\n   * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n   */\n  HOST_ID: 'host.id';\n\n  /**\n   * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n   */\n  HOST_NAME: 'host.name';\n\n  /**\n   * Type of host. For Cloud, this must be the machine type.\n   */\n  HOST_TYPE: 'host.type';\n\n  /**\n   * The CPU architecture the host system is running on.\n   */\n  HOST_ARCH: 'host.arch';\n\n  /**\n   * Name of the VM image or OS install the host was instantiated from.\n   */\n  HOST_IMAGE_NAME: 'host.image.name';\n\n  /**\n   * VM image ID. For Cloud, this value is from the provider.\n   */\n  HOST_IMAGE_ID: 'host.image.id';\n\n  /**\n   * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n   */\n  HOST_IMAGE_VERSION: 'host.image.version';\n\n  /**\n   * The name of the cluster.\n   */\n  K8S_CLUSTER_NAME: 'k8s.cluster.name';\n\n  /**\n   * The name of the Node.\n   */\n  K8S_NODE_NAME: 'k8s.node.name';\n\n  /**\n   * The UID of the Node.\n   */\n  K8S_NODE_UID: 'k8s.node.uid';\n\n  /**\n   * The name of the namespace that the pod is running in.\n   */\n  K8S_NAMESPACE_NAME: 'k8s.namespace.name';\n\n  /**\n   * The UID of the Pod.\n   */\n  K8S_POD_UID: 'k8s.pod.uid';\n\n  /**\n   * The name of the Pod.\n   */\n  K8S_POD_NAME: 'k8s.pod.name';\n\n  /**\n   * The name of the Container in a Pod template.\n   */\n  K8S_CONTAINER_NAME: 'k8s.container.name';\n\n  /**\n   * The UID of the ReplicaSet.\n   */\n  K8S_REPLICASET_UID: 'k8s.replicaset.uid';\n\n  /**\n   * The name of the ReplicaSet.\n   */\n  K8S_REPLICASET_NAME: 'k8s.replicaset.name';\n\n  /**\n   * The UID of the Deployment.\n   */\n  K8S_DEPLOYMENT_UID: 'k8s.deployment.uid';\n\n  /**\n   * The name of the Deployment.\n   */\n  K8S_DEPLOYMENT_NAME: 'k8s.deployment.name';\n\n  /**\n   * The UID of the StatefulSet.\n   */\n  K8S_STATEFULSET_UID: 'k8s.statefulset.uid';\n\n  /**\n   * The name of the StatefulSet.\n   */\n  K8S_STATEFULSET_NAME: 'k8s.statefulset.name';\n\n  /**\n   * The UID of the DaemonSet.\n   */\n  K8S_DAEMONSET_UID: 'k8s.daemonset.uid';\n\n  /**\n   * The name of the DaemonSet.\n   */\n  K8S_DAEMONSET_NAME: 'k8s.daemonset.name';\n\n  /**\n   * The UID of the Job.\n   */\n  K8S_JOB_UID: 'k8s.job.uid';\n\n  /**\n   * The name of the Job.\n   */\n  K8S_JOB_NAME: 'k8s.job.name';\n\n  /**\n   * The UID of the CronJob.\n   */\n  K8S_CRONJOB_UID: 'k8s.cronjob.uid';\n\n  /**\n   * The name of the CronJob.\n   */\n  K8S_CRONJOB_NAME: 'k8s.cronjob.name';\n\n  /**\n   * The operating system type.\n   */\n  OS_TYPE: 'os.type';\n\n  /**\n   * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n   */\n  OS_DESCRIPTION: 'os.description';\n\n  /**\n   * Human readable operating system name.\n   */\n  OS_NAME: 'os.name';\n\n  /**\n   * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n   */\n  OS_VERSION: 'os.version';\n\n  /**\n   * Process identifier (PID).\n   */\n  PROCESS_PID: 'process.pid';\n\n  /**\n   * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_NAME: 'process.executable.name';\n\n  /**\n   * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_PATH: 'process.executable.path';\n\n  /**\n   * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n   */\n  PROCESS_COMMAND: 'process.command';\n\n  /**\n   * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n   */\n  PROCESS_COMMAND_LINE: 'process.command_line';\n\n  /**\n   * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n   */\n  PROCESS_COMMAND_ARGS: 'process.command_args';\n\n  /**\n   * The username of the user that owns the process.\n   */\n  PROCESS_OWNER: 'process.owner';\n\n  /**\n   * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n   */\n  PROCESS_RUNTIME_NAME: 'process.runtime.name';\n\n  /**\n   * The version of the runtime of this process, as returned by the runtime without modification.\n   */\n  PROCESS_RUNTIME_VERSION: 'process.runtime.version';\n\n  /**\n   * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n   */\n  PROCESS_RUNTIME_DESCRIPTION: 'process.runtime.description';\n\n  /**\n   * Logical name of the service.\n   *\n   * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n   */\n  SERVICE_NAME: 'service.name';\n\n  /**\n   * A namespace for `service.name`.\n   *\n   * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n   */\n  SERVICE_NAMESPACE: 'service.namespace';\n\n  /**\n   * The string ID of the service instance.\n   *\n   * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n   */\n  SERVICE_INSTANCE_ID: 'service.instance.id';\n\n  /**\n   * The version string of the service API or implementation.\n   */\n  SERVICE_VERSION: 'service.version';\n\n  /**\n   * The name of the telemetry SDK as defined above.\n   */\n  TELEMETRY_SDK_NAME: 'telemetry.sdk.name';\n\n  /**\n   * The language of the telemetry SDK.\n   */\n  TELEMETRY_SDK_LANGUAGE: 'telemetry.sdk.language';\n\n  /**\n   * The version string of the telemetry SDK.\n   */\n  TELEMETRY_SDK_VERSION: 'telemetry.sdk.version';\n\n  /**\n   * The version string of the auto instrumentation agent, if used.\n   */\n  TELEMETRY_AUTO_VERSION: 'telemetry.auto.version';\n\n  /**\n   * The name of the web engine.\n   */\n  WEBENGINE_NAME: 'webengine.name';\n\n  /**\n   * The version of the web engine.\n   */\n  WEBENGINE_VERSION: 'webengine.version';\n\n  /**\n   * Additional description of the web engine (e.g. detailed version and edition information).\n   */\n  WEBENGINE_DESCRIPTION: 'webengine.description';\n};\n\n/**\n * Create exported Value Map for SemanticResourceAttributes values\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification\n */\nexport const SemanticResourceAttributes: SemanticResourceAttributes =\n  /*#__PURE__*/ createConstMap<SemanticResourceAttributes>([\n    TMP_CLOUD_PROVIDER,\n    TMP_CLOUD_ACCOUNT_ID,\n    TMP_CLOUD_REGION,\n    TMP_CLOUD_AVAILABILITY_ZONE,\n    TMP_CLOUD_PLATFORM,\n    TMP_AWS_ECS_CONTAINER_ARN,\n    TMP_AWS_ECS_CLUSTER_ARN,\n    TMP_AWS_ECS_LAUNCHTYPE,\n    TMP_AWS_ECS_TASK_ARN,\n    TMP_AWS_ECS_TASK_FAMILY,\n    TMP_AWS_ECS_TASK_REVISION,\n    TMP_AWS_EKS_CLUSTER_ARN,\n    TMP_AWS_LOG_GROUP_NAMES,\n    TMP_AWS_LOG_GROUP_ARNS,\n    TMP_AWS_LOG_STREAM_NAMES,\n    TMP_AWS_LOG_STREAM_ARNS,\n    TMP_CONTAINER_NAME,\n    TMP_CONTAINER_ID,\n    TMP_CONTAINER_RUNTIME,\n    TMP_CONTAINER_IMAGE_NAME,\n    TMP_CONTAINER_IMAGE_TAG,\n    TMP_DEPLOYMENT_ENVIRONMENT,\n    TMP_DEVICE_ID,\n    TMP_DEVICE_MODEL_IDENTIFIER,\n    TMP_DEVICE_MODEL_NAME,\n    TMP_FAAS_NAME,\n    TMP_FAAS_ID,\n    TMP_FAAS_VERSION,\n    TMP_FAAS_INSTANCE,\n    TMP_FAAS_MAX_MEMORY,\n    TMP_HOST_ID,\n    TMP_HOST_NAME,\n    TMP_HOST_TYPE,\n    TMP_HOST_ARCH,\n    TMP_HOST_IMAGE_NAME,\n    TMP_HOST_IMAGE_ID,\n    TMP_HOST_IMAGE_VERSION,\n    TMP_K8S_CLUSTER_NAME,\n    TMP_K8S_NODE_NAME,\n    TMP_K8S_NODE_UID,\n    TMP_K8S_NAMESPACE_NAME,\n    TMP_K8S_POD_UID,\n    TMP_K8S_POD_NAME,\n    TMP_K8S_CONTAINER_NAME,\n    TMP_K8S_REPLICASET_UID,\n    TMP_K8S_REPLICASET_NAME,\n    TMP_K8S_DEPLOYMENT_UID,\n    TMP_K8S_DEPLOYMENT_NAME,\n    TMP_K8S_STATEFULSET_UID,\n    TMP_K8S_STATEFULSET_NAME,\n    TMP_K8S_DAEMONSET_UID,\n    TMP_K8S_DAEMONSET_NAME,\n    TMP_K8S_JOB_UID,\n    TMP_K8S_JOB_NAME,\n    TMP_K8S_CRONJOB_UID,\n    TMP_K8S_CRONJOB_NAME,\n    TMP_OS_TYPE,\n    TMP_OS_DESCRIPTION,\n    TMP_OS_NAME,\n    TMP_OS_VERSION,\n    TMP_PROCESS_PID,\n    TMP_PROCESS_EXECUTABLE_NAME,\n    TMP_PROCESS_EXECUTABLE_PATH,\n    TMP_PROCESS_COMMAND,\n    TMP_PROCESS_COMMAND_LINE,\n    TMP_PROCESS_COMMAND_ARGS,\n    TMP_PROCESS_OWNER,\n    TMP_PROCESS_RUNTIME_NAME,\n    TMP_PROCESS_RUNTIME_VERSION,\n    TMP_PROCESS_RUNTIME_DESCRIPTION,\n    TMP_SERVICE_NAME,\n    TMP_SERVICE_NAMESPACE,\n    TMP_SERVICE_INSTANCE_ID,\n    TMP_SERVICE_VERSION,\n    TMP_TELEMETRY_SDK_NAME,\n    TMP_TELEMETRY_SDK_LANGUAGE,\n    TMP_TELEMETRY_SDK_VERSION,\n    TMP_TELEMETRY_AUTO_VERSION,\n    TMP_WEBENGINE_NAME,\n    TMP_WEBENGINE_VERSION,\n    TMP_WEBENGINE_DESCRIPTION,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';\nconst TMP_CLOUDPROVIDERVALUES_AWS = 'aws';\nconst TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';\nconst TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_ALIBABA_CLOUD =\n  TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AZURE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_GCP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;\n\n/**\n * Identifies the Values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport type CloudProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud';\n\n  /** Amazon Web Services. */\n  AWS: 'aws';\n\n  /** Microsoft Azure. */\n  AZURE: 'azure';\n\n  /** Google Cloud Platform. */\n  GCP: 'gcp';\n};\n\n/**\n * The constant map of values for CloudProviderValues.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport const CloudProviderValues: CloudProviderValues =\n  /*#__PURE__*/ createConstMap<CloudProviderValues>([\n    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n    TMP_CLOUDPROVIDERVALUES_AWS,\n    TMP_CLOUDPROVIDERVALUES_AZURE,\n    TMP_CLOUDPROVIDERVALUES_GCP,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';\nconst TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  'azure_container_instances';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';\nconst TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';\nconst TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';\nconst TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_LAMBDA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_LAMBDA =\n  TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK =\n  TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_VM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_AKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_APP_SERVICE =\n  TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_RUN =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_APP_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;\n\n/**\n * Identifies the Values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport type CloudPlatformValues = {\n  /** Alibaba Cloud Elastic Compute Service. */\n  ALIBABA_CLOUD_ECS: 'alibaba_cloud_ecs';\n\n  /** Alibaba Cloud Function Compute. */\n  ALIBABA_CLOUD_FC: 'alibaba_cloud_fc';\n\n  /** AWS Elastic Compute Cloud. */\n  AWS_EC2: 'aws_ec2';\n\n  /** AWS Elastic Container Service. */\n  AWS_ECS: 'aws_ecs';\n\n  /** AWS Elastic Kubernetes Service. */\n  AWS_EKS: 'aws_eks';\n\n  /** AWS Lambda. */\n  AWS_LAMBDA: 'aws_lambda';\n\n  /** AWS Elastic Beanstalk. */\n  AWS_ELASTIC_BEANSTALK: 'aws_elastic_beanstalk';\n\n  /** Azure Virtual Machines. */\n  AZURE_VM: 'azure_vm';\n\n  /** Azure Container Instances. */\n  AZURE_CONTAINER_INSTANCES: 'azure_container_instances';\n\n  /** Azure Kubernetes Service. */\n  AZURE_AKS: 'azure_aks';\n\n  /** Azure Functions. */\n  AZURE_FUNCTIONS: 'azure_functions';\n\n  /** Azure App Service. */\n  AZURE_APP_SERVICE: 'azure_app_service';\n\n  /** Google Cloud Compute Engine (GCE). */\n  GCP_COMPUTE_ENGINE: 'gcp_compute_engine';\n\n  /** Google Cloud Run. */\n  GCP_CLOUD_RUN: 'gcp_cloud_run';\n\n  /** Google Cloud Kubernetes Engine (GKE). */\n  GCP_KUBERNETES_ENGINE: 'gcp_kubernetes_engine';\n\n  /** Google Cloud Functions (GCF). */\n  GCP_CLOUD_FUNCTIONS: 'gcp_cloud_functions';\n\n  /** Google Cloud App Engine (GAE). */\n  GCP_APP_ENGINE: 'gcp_app_engine';\n};\n\n/**\n * The constant map of values for CloudPlatformValues.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport const CloudPlatformValues: CloudPlatformValues =\n  /*#__PURE__*/ createConstMap<CloudPlatformValues>([\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,\n    TMP_CLOUDPLATFORMVALUES_AWS_EC2,\n    TMP_CLOUDPLATFORMVALUES_AWS_ECS,\n    TMP_CLOUDPLATFORMVALUES_AWS_EKS,\n    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,\n    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,\n    TMP_CLOUDPLATFORMVALUES_AZURE_VM,\n    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,\n    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,\n    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,\n    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';\nconst TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_FARGATE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_FARGATE =\n  TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;\n\n/**\n * Identifies the Values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport type AwsEcsLaunchtypeValues = {\n  /** ec2. */\n  EC2: 'ec2';\n\n  /** fargate. */\n  FARGATE: 'fargate';\n};\n\n/**\n * The constant map of values for AwsEcsLaunchtypeValues.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport const AwsEcsLaunchtypeValues: AwsEcsLaunchtypeValues =\n  /*#__PURE__*/ createConstMap<AwsEcsLaunchtypeValues>([\n    TMP_AWSECSLAUNCHTYPEVALUES_EC2,\n    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_HOSTARCHVALUES_AMD64 = 'amd64';\nconst TMP_HOSTARCHVALUES_ARM32 = 'arm32';\nconst TMP_HOSTARCHVALUES_ARM64 = 'arm64';\nconst TMP_HOSTARCHVALUES_IA64 = 'ia64';\nconst TMP_HOSTARCHVALUES_PPC32 = 'ppc32';\nconst TMP_HOSTARCHVALUES_PPC64 = 'ppc64';\nconst TMP_HOSTARCHVALUES_X86 = 'x86';\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_AMD64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_IA64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_X86 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;\n\n/**\n * Identifies the Values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport type HostArchValues = {\n  /** AMD64. */\n  AMD64: 'amd64';\n\n  /** ARM32. */\n  ARM32: 'arm32';\n\n  /** ARM64. */\n  ARM64: 'arm64';\n\n  /** Itanium. */\n  IA64: 'ia64';\n\n  /** 32-bit PowerPC. */\n  PPC32: 'ppc32';\n\n  /** 64-bit PowerPC. */\n  PPC64: 'ppc64';\n\n  /** 32-bit x86. */\n  X86: 'x86';\n};\n\n/**\n * The constant map of values for HostArchValues.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport const HostArchValues: HostArchValues =\n  /*#__PURE__*/ createConstMap<HostArchValues>([\n    TMP_HOSTARCHVALUES_AMD64,\n    TMP_HOSTARCHVALUES_ARM32,\n    TMP_HOSTARCHVALUES_ARM64,\n    TMP_HOSTARCHVALUES_IA64,\n    TMP_HOSTARCHVALUES_PPC32,\n    TMP_HOSTARCHVALUES_PPC64,\n    TMP_HOSTARCHVALUES_X86,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for OsTypeValues enum definition\n *\n * The operating system type.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_OSTYPEVALUES_WINDOWS = 'windows';\nconst TMP_OSTYPEVALUES_LINUX = 'linux';\nconst TMP_OSTYPEVALUES_DARWIN = 'darwin';\nconst TMP_OSTYPEVALUES_FREEBSD = 'freebsd';\nconst TMP_OSTYPEVALUES_NETBSD = 'netbsd';\nconst TMP_OSTYPEVALUES_OPENBSD = 'openbsd';\nconst TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';\nconst TMP_OSTYPEVALUES_HPUX = 'hpux';\nconst TMP_OSTYPEVALUES_AIX = 'aix';\nconst TMP_OSTYPEVALUES_SOLARIS = 'solaris';\nconst TMP_OSTYPEVALUES_Z_OS = 'z_os';\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_WINDOWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_LINUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DARWIN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_FREEBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_NETBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_OPENBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DRAGONFLYBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_HPUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_AIX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_SOLARIS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_Z_OS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;\n\n/**\n * Identifies the Values for OsTypeValues enum definition\n *\n * The operating system type.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport type OsTypeValues = {\n  /** Microsoft Windows. */\n  WINDOWS: 'windows';\n\n  /** Linux. */\n  LINUX: 'linux';\n\n  /** Apple Darwin. */\n  DARWIN: 'darwin';\n\n  /** FreeBSD. */\n  FREEBSD: 'freebsd';\n\n  /** NetBSD. */\n  NETBSD: 'netbsd';\n\n  /** OpenBSD. */\n  OPENBSD: 'openbsd';\n\n  /** DragonFly BSD. */\n  DRAGONFLYBSD: 'dragonflybsd';\n\n  /** HP-UX (Hewlett Packard Unix). */\n  HPUX: 'hpux';\n\n  /** AIX (Advanced Interactive eXecutive). */\n  AIX: 'aix';\n\n  /** Oracle Solaris. */\n  SOLARIS: 'solaris';\n\n  /** IBM z/OS. */\n  Z_OS: 'z_os';\n};\n\n/**\n * The constant map of values for OsTypeValues.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport const OsTypeValues: OsTypeValues =\n  /*#__PURE__*/ createConstMap<OsTypeValues>([\n    TMP_OSTYPEVALUES_WINDOWS,\n    TMP_OSTYPEVALUES_LINUX,\n    TMP_OSTYPEVALUES_DARWIN,\n    TMP_OSTYPEVALUES_FREEBSD,\n    TMP_OSTYPEVALUES_NETBSD,\n    TMP_OSTYPEVALUES_OPENBSD,\n    TMP_OSTYPEVALUES_DRAGONFLYBSD,\n    TMP_OSTYPEVALUES_HPUX,\n    TMP_OSTYPEVALUES_AIX,\n    TMP_OSTYPEVALUES_SOLARIS,\n    TMP_OSTYPEVALUES_Z_OS,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_CPP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_CPP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_DOTNET =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_ERLANG =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_GO.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_JAVA.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_JAVA =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_NODEJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PHP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PHP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PYTHON =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_RUBY.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_RUBY =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_WEBJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;\n\n/**\n * Identifies the Values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport type TelemetrySdkLanguageValues = {\n  /** cpp. */\n  CPP: 'cpp';\n\n  /** dotnet. */\n  DOTNET: 'dotnet';\n\n  /** erlang. */\n  ERLANG: 'erlang';\n\n  /** go. */\n  GO: 'go';\n\n  /** java. */\n  JAVA: 'java';\n\n  /** nodejs. */\n  NODEJS: 'nodejs';\n\n  /** php. */\n  PHP: 'php';\n\n  /** python. */\n  PYTHON: 'python';\n\n  /** ruby. */\n  RUBY: 'ruby';\n\n  /** webjs. */\n  WEBJS: 'webjs';\n};\n\n/**\n * The constant map of values for TelemetrySdkLanguageValues.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport const TelemetrySdkLanguageValues: TelemetrySdkLanguageValues =\n  /*#__PURE__*/ createConstMap<TelemetrySdkLanguageValues>([\n    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS,\n  ]);\n"]}