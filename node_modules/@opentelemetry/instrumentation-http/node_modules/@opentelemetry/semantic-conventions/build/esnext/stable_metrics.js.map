{"version": 3, "file": "stable_metrics.js", "sourceRoot": "", "sources": ["../../src/stable_metrics.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,4GAA4G;AAC5G,2GAA2G;AAC3G,4GAA4G;AAE5G;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,qDAAqD,GAAG,gDAAyD,CAAC;AAE/H;;;;GAIG;AACH,MAAM,CAAC,MAAM,+CAA+C,GAAG,0CAAmD,CAAC;AAEnH;;;;GAIG;AACH,MAAM,CAAC,MAAM,qDAAqD,GAAG,gDAAyD,CAAC;AAE/H;;;;GAIG;AACH,MAAM,CAAC,MAAM,sDAAsD,GAAG,iDAA0D,CAAC;AAEjI;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;GAEG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;GAEG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,cAAuB,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,yCAAyC,GAAG,oCAA6C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * Number of exceptions caught by exception handling middleware.\n * \n * @note Meter name: `Microsoft.AspNetCore.Diagnostics`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS = 'aspnetcore.diagnostics.exceptions' as const;\n\n/**\n * Number of requests that are currently active on the server that hold a rate limiting lease.\n * \n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES = 'aspnetcore.rate_limiting.active_request_leases' as const;\n\n/**\n * Number of requests that are currently queued, waiting to acquire a rate limiting lease.\n * \n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS = 'aspnetcore.rate_limiting.queued_requests' as const;\n\n/**\n * The time the request spent in a queue waiting to acquire a rate limiting lease.\n * \n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE = 'aspnetcore.rate_limiting.request.time_in_queue' as const;\n\n/**\n * The duration of rate limiting lease held by requests on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION = 'aspnetcore.rate_limiting.request_lease.duration' as const;\n\n/**\n * Number of requests that tried to acquire a rate limiting lease.\n * \n * @note Requests could be:\n * \n *   - Rejected by global or endpoint rate limiting policies\n *   - Canceled while waiting for the lease.\n * \n * Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS = 'aspnetcore.rate_limiting.requests' as const;\n\n/**\n * Number of requests that were attempted to be matched to an endpoint.\n * \n * @note Meter name: `Microsoft.AspNetCore.Routing`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS = 'aspnetcore.routing.match_attempts' as const;\n\n/**\n * Duration of HTTP client requests.\n */\nexport const METRIC_HTTP_CLIENT_REQUEST_DURATION = 'http.client.request.duration' as const;\n\n/**\n * Duration of HTTP server requests.\n */\nexport const METRIC_HTTP_SERVER_REQUEST_DURATION = 'http.server.request.duration' as const;\n\n/**\n * Number of classes currently loaded.\n */\nexport const METRIC_JVM_CLASS_COUNT = 'jvm.class.count' as const;\n\n/**\n * Number of classes loaded since JVM start.\n */\nexport const METRIC_JVM_CLASS_LOADED = 'jvm.class.loaded' as const;\n\n/**\n * Number of classes unloaded since JVM start.\n */\nexport const METRIC_JVM_CLASS_UNLOADED = 'jvm.class.unloaded' as const;\n\n/**\n * Number of processors available to the Java virtual machine.\n */\nexport const METRIC_JVM_CPU_COUNT = 'jvm.cpu.count' as const;\n\n/**\n * Recent CPU utilization for the process as reported by the JVM.\n * \n * @note The value range is [0.0,1.0]. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/jdk.management/com/sun/management/OperatingSystemMXBean.html#getProcessCpuLoad()).\n */\nexport const METRIC_JVM_CPU_RECENT_UTILIZATION = 'jvm.cpu.recent_utilization' as const;\n\n/**\n * CPU time used by the process as reported by the JVM.\n */\nexport const METRIC_JVM_CPU_TIME = 'jvm.cpu.time' as const;\n\n/**\n * Duration of JVM garbage collection actions.\n */\nexport const METRIC_JVM_GC_DURATION = 'jvm.gc.duration' as const;\n\n/**\n * Measure of memory committed.\n */\nexport const METRIC_JVM_MEMORY_COMMITTED = 'jvm.memory.committed' as const;\n\n/**\n * Measure of max obtainable memory.\n */\nexport const METRIC_JVM_MEMORY_LIMIT = 'jvm.memory.limit' as const;\n\n/**\n * Measure of memory used.\n */\nexport const METRIC_JVM_MEMORY_USED = 'jvm.memory.used' as const;\n\n/**\n * Measure of memory used, as measured after the most recent garbage collection event on this pool.\n */\nexport const METRIC_JVM_MEMORY_USED_AFTER_LAST_GC = 'jvm.memory.used_after_last_gc' as const;\n\n/**\n * Number of executing platform threads.\n */\nexport const METRIC_JVM_THREAD_COUNT = 'jvm.thread.count' as const;\n\n/**\n * Number of connections that are currently active on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_ACTIVE_CONNECTIONS = 'kestrel.active_connections' as const;\n\n/**\n * Number of TLS handshakes that are currently in progress on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES = 'kestrel.active_tls_handshakes' as const;\n\n/**\n * The duration of connections on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_CONNECTION_DURATION = 'kestrel.connection.duration' as const;\n\n/**\n * Number of connections that are currently queued and are waiting to start.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_QUEUED_CONNECTIONS = 'kestrel.queued_connections' as const;\n\n/**\n * Number of HTTP requests on multiplexed connections (HTTP/2 and HTTP/3) that are currently queued and are waiting to start.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_QUEUED_REQUESTS = 'kestrel.queued_requests' as const;\n\n/**\n * Number of connections rejected by the server.\n * \n * @note Connections are rejected when the currently active count exceeds the value configured with `MaxConcurrentConnections`.\n * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_REJECTED_CONNECTIONS = 'kestrel.rejected_connections' as const;\n\n/**\n * The duration of TLS handshakes on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_TLS_HANDSHAKE_DURATION = 'kestrel.tls_handshake.duration' as const;\n\n/**\n * Number of connections that are currently upgraded (WebSockets). .\n * \n * @note The counter only tracks HTTP/1.1 connections.\n * \n * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_UPGRADED_CONNECTIONS = 'kestrel.upgraded_connections' as const;\n\n/**\n * Number of connections that are currently active on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS = 'signalr.server.active_connections' as const;\n\n/**\n * The duration of connections on the server.\n * \n * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_SIGNALR_SERVER_CONNECTION_DURATION = 'signalr.server.connection.duration' as const;\n\n"]}