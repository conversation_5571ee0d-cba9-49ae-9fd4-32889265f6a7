import fs from "fs";
import path from "path";
import * as cheerio from "cheerio";
import { writeToPath } from "fast-csv";

// Folder where HTML files are saved
const reportsDir = "./lighthouse-reports";
const outputFile = "./lighthouse-summary.csv";

const results = [];

fs.readdirSync(reportsDir).forEach((file) => {
    if (file.endsWith(".html")) {
        const filePath = path.join(reportsDir, file);
        const html = fs.readFileSync(filePath, "utf8");
        const $ = cheerio.load(html);

        const scores = {};
        const categories = [
            "performance",
            "accessibility",
            "best-practices",
            "seo",
            "pwa",
        ];

        try {
            // Extract the JSON data from the script tag
            const scriptContent = $("script")
                .filter((_, el) => {
                    return $(el).html().includes("window.__LIGHTHOUSE_JSON__");
                })
                .html();

            if (scriptContent) {
                // Extract JSON from the script content
                const jsonMatch = scriptContent.match(
                    /window\.__LIGHTHOUSE_JSON__\s*=\s*({.*?});/s
                );
                if (jsonMatch) {
                    const lighthouseData = JSON.parse(jsonMatch[1]);

                    // Extract scores from categories
                    if (lighthouseData.categories) {
                        categories.forEach((category) => {
                            const categoryKey =
                                category === "best-practices"
                                    ? "best-practices"
                                    : category;
                            if (
                                lighthouseData.categories[categoryKey] &&
                                lighthouseData.categories[categoryKey].score !==
                                    null
                            ) {
                                // Convert score from 0-1 range to 0-100 range
                                scores[category] = Math.round(
                                    lighthouseData.categories[categoryKey]
                                        .score * 100
                                );
                            } else {
                                scores[category] = null;
                            }
                        });
                    }
                }
            }

            // Fallback: if no scores were extracted, set all to null
            categories.forEach((category) => {
                if (scores[category] === undefined) {
                    scores[category] = null;
                }
            });
        } catch (error) {
            console.error(`Error processing ${file}:`, error.message);
            // Set all scores to null if there's an error
            categories.forEach((category) => {
                scores[category] = null;
            });
        }

        // Extract URL from the JSON data or fallback to title
        let url = "";
        try {
            const scriptContent = $("script")
                .filter((i, el) => {
                    return $(el).html().includes("window.__LIGHTHOUSE_JSON__");
                })
                .html();

            if (scriptContent) {
                const jsonMatch = scriptContent.match(
                    /window\.__LIGHTHOUSE_JSON__\s*=\s*({.*?});/s
                );
                if (jsonMatch) {
                    const lighthouseData = JSON.parse(jsonMatch[1]);
                    url =
                        lighthouseData.finalDisplayedUrl ||
                        lighthouseData.requestedUrl ||
                        "";
                }
            }
        } catch (error) {
            // Fallback to title extraction
            url = $("head title")
                .text()
                .replace("Lighthouse Report", "")
                .trim();
        }

        results.push({
            file: file,
            url: url,
            ...scores,
        });
    }
});

// Write to CSV
writeToPath(outputFile, results, { headers: true }).on("finish", () => {
    console.log(`✅ Consolidated report saved to: ${outputFile}`);
});
